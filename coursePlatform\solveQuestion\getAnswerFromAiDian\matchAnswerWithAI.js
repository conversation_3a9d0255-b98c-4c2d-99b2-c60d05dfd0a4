let aiChat = require('../aiChat.js');

// 获取提示词
function getPrompt(questionObj, item) {
    // 判断题的提示词
    let trueOrFalsePromptStr = `
        现在有一道判断题，我称之为“问题题目”：
        问题题目内容：${questionObj.content}
        问题题目类型：${questionObj.type}
        问题题目选项：${JSON.stringify(questionObj.options)}

        我从题库中找到一个相关的题目
        题库题目内容：${item.question}
        题库题目选项：${item.options}
        题库题目答案：${item.answer}

        首先通过题目内容的含义判断问题题目和题库题目是否为相同的题目。
        如果“问题题目”和“题库题目”不是同一个题目，直接回答'false'
        如果“问题题目”和“题库题目”是同一个题目，根据答案内容返回“A”或“B”，不要包含任何解释、分析或多余文字。
    `;

    // 选择题的提示词
    let selectPromptStr = `
        现在有一道题目，我称之为“问题题目”：
        问题题目内容：实施大规模定制的基本策略有（）。
        问题题目类型：多选题
        问题题目选项：["A:规模化","B:定制化","C:模块化","D:延迟化"]

        我从题库中找到一个相关的题目
        题库题目内容：实施大规模定制的基本策略有哪些？。
        题库题目选项：["A:延迟化","B:定制化","C:模块化","D:规模化"]
        题库题目答案：["AC"]

        首先通过题目内容的含义判断问题题目和题库题目是否为相同的题目 ：“实施大规模定制的基本策略有（）。”和 “实施大规模定制的基本策略有哪些？”是相同的题目，因为题目意思是一样的。如果两个题目意思不相同，直接回答'false'

        然后根据题库题目答案["AC"]，从题库题目选项中，获取对应的值：["A:延迟化","C:模块化"]
        用题库题目的正确选项，分别去匹配问题题目的选项

        用 "A:延迟化" 匹配 ["A:规模化","B:定制化","C:模块化","D:延迟化"] 得到答案D
        用 "C:模块化" 匹配 ["A:规模化","B:定制化","C:模块化","D:延迟化"] 得到答案C

        最后可以知道正确答案是CD

        返回CD

        
        现在有一道题目，我称之为“问题题目”：
        问题题目内容：${questionObj.content}
        问题题目类型：${questionObj.type}
        问题题目选项：${JSON.stringify(questionObj.options)}

        我从题库中找到一个相关的题目
        题库题目内容：${item.question}
        题库题目选项：${item.options}
        题库题目答案：${item.answer}
        按照以上的方法，现在你帮我判断这题的答案是什么。
    `;

    // 填空题的提示词
    let fillPromptStr = `
        现在有一道填空题，我称之为“问题题目”：
        问题题目内容：${questionObj.content}
        问题题目类型：${questionObj.type}

        我从题库中找到一个相关的题目
        题库题目内容：${item.question}
        题库题目答案：${item.answer}


        1.你首先通过题目含义判断“问题题目”和“题库题目”是否为同一个题目。
        2.如果“问题题目”和“题库题目”不是同一个题目，直接回答'false'
        3.如果“问题题目”和“题库题目”是同一个题目，你判断下我的填空题有几个空，然后根据题库题目的答案，返回对应的答案。
        答案格式要求：
            1. 答案中不能包含任何解释、分析或多余文字。
            2. 填空题如果有多个答案，答案之间用“|”符号连接，例如“答案1|答案2”
    `;

    // 简答题的提示词
    let defaultPromptStr = `
        现在有一道填空题，我称之为“问题题目”：
        问题题目内容：${questionObj.content}
        问题题目类型：${questionObj.type}

        我从题库中找到一个相关的题目
        题库题目内容：${item.question}
        题库题目答案：${item.answer}


        首先根据问题题目内容表达的意思和题库题目内容表达的意思来判断问题题目和题库题目是否为同一个题目。
        如果不是同一个题目，直接回答'false'
        如果是同一个题目，直接把题库题目的答案返回给我就行了。
    `;

    switch (questionObj.type) {
        case '判断题':
        case '单选题':
        case '多选题': {
            return selectPromptStr;
        }
        case '填空题': {
            return fillPromptStr;
        }
        default: {
            return defaultPromptStr;
        }
    }
}

async function matchAnswerWithAI(questionObj, serverRes) {
    let answers;
    for (let resObj of serverRes.qlist) {
        // 只有单选题多选题判断题，才能有选项
        if (!'单选题多选题判断题'.includes(questionObj.type)) {
            if (resObj.options.length > 0) {
                continue;
            }
        }
        // 获取提示词
        let promptStr = getPrompt(questionObj, resObj);
        // 获取AI回答结果
        let aiModel = 'o4-mini-2025-04-16';
        answers = await aiChat(aiModel, promptStr);
        // 对AI回答结果进行处理
        if (answers && answers != 'false') {
            break;
        }
    }
    if (answers && answers != 'false') {
        return answers;
    }
}

module.exports = matchAnswerWithAI;

// 测试
if (false) {
    let questionObj = {
        id: 'id-m25g1eno-czqkdv6toanswer400961834',
        content: '分析电力系统发生短路的原因',
        type: '简答题',
        options: [],
        platform: '超星学习通',
        courseName: '仪器分析',
    };
    let serverRes = {
        code: 1,
        msg: '获取成功',
        qlist: [
            {
                qid: 'kdCCLeLmJllnAFxiPTCZyttQh%2Bvk1LRyO5S4tVtt89QHWqc1jwFRzXmZRpeWp5SjBYdb2aNvvYAQOki3ZxP8S%2Bkh80pQCW%2B9zM0%2F%2Bx6YH%2F7gPdmOuFNUeCk5cPHtSzCAVbkrCOGX2A5pmgALGvWa8A%3D%3D',
                question: '分析电力系统发生短路的原因',
                options: [],
                answer: [
                    '①电气设备载流部分的绝缘损坏<br class="markdown_return">②操作人员违反安全操作规程而发生误操作<br class="markdown_return">③鸟兽跨越在裸露的相线之间或相线与接地物体之间，或咬坏设备、导线绝缘',
                ],
            },
            {
                qid: 'kdCCLeLmJllnAFxiPTCZykqjpB7RdxKNV9qZJSoq%2FDrViCBiArtGiK4v0PG0%2F7Xu0NvrzRMHYdx2JBaWWGKz3KfRBPP5R%2FT9Nyci3N16J8fzrdgT752m0A5GGUvTUa8wFlIH84%2Fv4EM0LS8YVheP3A%3D%3D',
                question: '电力系统发生短路的原因有（）',
                options: ['A、电气设备绝缘损坏', 'B、运行人员误操作', 'C、其他原因如鸟兽跨接导体造成短路等'],
                answer: ['ABC'],
            },
            {
                qid: 'kdCCLeLmJllnAFxiPTCZygLbxEPT5DdaRfCqRE5QLSYzKKfdMfGbTnbdglSR3FbqfH7kQ%2BCPvto6sQjuIlg33ODMPxp9q9XJRwAesguwOKVaGITpeQIbx9F4bVOYDmfDORSM%2BFFExDBj8eHP2QRQDw%3D%3D',
                question: '电力系统中发生短路的原因（ ）',
                options: ['电气设备绝缘损坏', '运行人员操作', '其他原因如鸟兽跨接导体造成短路'],
                answer: ['电气设备绝缘损坏', '运行人员操作', '其他原因如鸟兽跨接导体造成短路'],
            },
            {
                qid: 'kdCCLeLmJllnAFxiPTCZyjnBn%2FU2zc%2FFkA%2F48upLJKWr31zP50TLyoL5kUEfQW23BIvRk66L%2F3vLQLghTDyMOEeCW9ha%2Fey%2BgFkc8R7s6bI%2BXRVDRAmZovaTjR7ezMILFxi6ZLs8cpvlorh1ErTejw%3D%3D',
                question: '电力系统中发生短路的原因有（）。',
                options: ['A：电气设备绝缘损坏', 'B：运行人员误操作', 'C：其他原因如鸟兽跨接导体造成短路等'],
                answer: ['A, B, C'],
            },
            {
                qid: 'kdCCLeLmJllnAFxiPTCZypeS%2BVSlNQbVv2SJ7Pt2oMgx%2BJhagwXWTCzf3vQJIqJveR%2FVLnne%2Fvr9Q5hKkOHsMC9EQQh8%2FJWMsNtoHuYwVsGlgOWo0og7pxnPnNELCOdcPV3rtbgaavNWklPnPVTlug%3D%3D',
                question: '电力系统中发生短路的原因有（）',
                options: ['A．电气设备绝缘损坏', 'B．运行人员误操作', 'C．其他原因如鸟兽跨接导体造成短路等'],
                answer: ['ABC'],
            },
        ],
    };
    matchAnswerWithAI(questionObj, serverRes).then(r => console.log('r', r));
}
