let axiosIns = require('../../utils/axiosIns');
let { HttpsProxyAgent } = require('https-proxy-agent');
let querystring = require('querystring');
let qs = require('qs');
async function getAgent() {
    let res;
    let proxyInfo;
    try {
        // 多米代理
        res = await axiosIns({
            url: 'http://need1.dmdaili.com:7771/dmgetip.asp?apikey=c92e98bd&pwd=22193af90e7284bd39e440c8960e1d3f&getnum=1&httptype=1&geshi=2&fenge=1&fengefu=&Contenttype=2&operate=all',
            method: 'get',
            timeout: 5000,
        });

        proxyInfo = res.data[0];
        if (!proxyInfo) {
            throw new Error('代理1出错');
        }
    } catch (error) {
        // 用其他代理
        res = await axiosIns({
            url: 'http://ecs.hailiangip.com:8422/api/getIpEncrypt?dataType=0&encryptParam=SlDyzgfgDW12vuaMHmQkMz9pKEmWH7kDAoD1ZC4KkxpHXuvLm%2B3L9xWaasGPtq4TOj4RbltfYgoMC5a7FzqLLwNG8xQEizS3mHQLaScnz%2FbKRT6pSc9tBbFqufMkqx7tCKWVsT2h68WdIUX%2FicuP%2Bjb37eJ1ZK6mYsahgpXXq5IfFaJxyBo8680oTdrXHKDt0AOtabf92lu9Nd5cdhcIj9zbJ280PfgKxcbaM9RRtYAGY3mYgkn434O0KJtB9%2Fqe',
            method: 'get',
            timeout: 5000,
        });
        proxyInfo = res.data[0];
        if (!proxyInfo) {
            throw new Error('代理2出错');
        }
    }
    let agent = new HttpsProxyAgent(`http://${proxyInfo.ip}:${proxyInfo.port}`);
    return agent;
}

/**
 * ======================= 一、公共API =======================
 *
 * 1.验证码判断
 */

//验证码判断
exports.validateCode = imgBuffer => {
    let base64Image = imgBuffer.toString('base64');
    return axiosIns.post('http://upload.chaojiying.net/Upload/Processing.php', {
        user: 'mozhi0012',
        pass: 'rVYBbAw8vDF5@V6',
        softid: 953643,
        codetype: 1004,
        file_base64: base64Image,
    });
};
if (false) {
    let success = { err_no: 0, pic_str: '8934' };
    let fail = { err_no: -10061, err_str: '不是有效的图片文件', pic_str: '' };
}

// 我的验证码
exports.myValidateCode = async (base64Image) => {
    let config = {
        method: 'post',
        url: 'https://ddddocr.mozhi0012.top/ocr/classification',
        data: {
            beta: true,
            image_base64: base64Image,
            png_fix: false,
            probability: false,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        result: 'Hga2',
    };
}

//滑动验证码
exports.slideValidate = base64Image => {
    return axiosIns({
        url: 'http://www.fdyscloud.com.cn/tuling/predict',
        method: 'post',
        data: {
            username: 'mozhi0012',
            password: 'a11234566',
            b64: base64Image,
            ID: '05119180',
            version: '3.1.1',
        },
    });
};
if (false) {
    let success = { code: 1, message: '', data: { 滑块: { X坐标值: 34, Y坐标值: 83 }, 缺口: { X坐标值: 107, Y坐标值: 93 } } };
    let fail = { code: -1, message: '错误原因：该图未检测到关键要素，未扣积分，请您重新刷图上传。', data: {} };
}

//获取作业试题 ------（获取考试试题）
exports.getQuestionList = async (cookieStr, assignmentObj) => {
    let config = {
        url: 'https://exam.chinaedu.net/oxer/app/ots/TestActivity/StartAnswerPaper',
        headers: {
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            cookie: cookieStr,
        },
        data: `arrangementId=${assignmentObj.activityID}&resourcePackageId=${assignmentObj.studentId}`,
        method: 'POST',
    };
    let questionListRes = await axiosIns(config);
    return questionListRes;
};
//成功响应
if (false) {
    let obj = {
        data: {
            realpath: '/data/shareNfs/jlu-datafiles/ots/CJBZBE10112',
            faceCorpuscleInTest: false,
            subjectId: null,
            minimumCommitTime: 0,
            tempSaveAnswerTime: 300,
            timeLimitEnabled: true,
            testEndTime: '2024-06-30 17:49:00',
            takePhotoAutomaticSubmitTime: 0,
            closePageAfterSubmit: false,
            tenant: 'CJBZBE10112',
            resourcePackageId: '1754172689633797131',
            roomNo: null,
            iframeJs: 'http://**************:10038/venus/resources/venus/js/app/iframe.js',
            isEntranceExams: false,
            userFaceImageUrl: null, //用于人脸识别照片的对比照片
            delayPapersAutoGradePolicy: false,
            paperTime: 90,
            arrangementId: '588b2b41-95f0-4120-a286-5007c48c61ff',
            role: 'student',
            submitTips: false,
            canNotEnterOverTimes: 0,
            title: '23级化工仪表与自动化 作业1',
            strcode: null,
            scrollTopHtml: 'http://**************:10038/venus/scrollTo_top.html',
            studentTestActivityScoreId: '693edb74-eb5e-401c-9722-2599d9d6c44c',
            takePhotoInTest: false,
            forceDetails: '1',
            paper: {
                achievementType: 1,
                currentQuestionNum: 0,
                paperCategoryId: 'e90a144b-36e6-4217-b6a8-e91e1dd4dac6',
                answerTimeLength: null,
                creatorId: '76ea80a9-a11c-45de-8627-05361c8ff1ca',
                questionCategoryItemCodes: ['00053'],
                difficultyDegree: 3,
                showComment: true,
                isPrivate: 0,
                judgementmode: 1,
                showTotalScore: true,
                paperForm: 1,
                showEachScore: true,
                psOutputDto: [
                    // level1 是题干
                    {
                        paperQuestionList: null,
                        realEachScore: 0,
                        level: 1,
                        totalscore: '0.0',
                        eachQuestionScore: null,
                        sequencenumber: '',
                        usedsequence: '',
                        isSameScore: true,
                        questionTypeId: null,
                        answerMode: null,
                        parentId: '',
                        sequence: 1,
                        totalquestion: 0,
                        judgementModeCode: null,
                        name: null,
                        comment: null,
                        id: 'bce243e5-a189-4237-b8fd-b9f36c242a34',
                    },
                    // 单选题
                    {
                        paperQuestionList: [
                            {
                                solvingProcess: '',
                                sequenceNumber: '1',
                                uploadFile: 0,
                                questionId: '03f9412d-f41a-49a8-97d9-b24cd0a1678a',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 3,
                                usedsequence: '1',
                                answerArea: {
                                    optionList: [
                                        {
                                            id: '0',
                                            content: '<span> K </span>',
                                            sequence: 0,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '1',
                                            content: '<span> E </span>',
                                            sequence: 1,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '2',
                                            content: '<span> S </span>',
                                            sequence: 2,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '3',
                                            content: '<span> T </span>',
                                            sequence: 3,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '4',
                                            content: '<span>B</span>',
                                            sequence: 4,
                                            score: 0,
                                            isInput: false,
                                        },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 1,
                                score: 2,
                                judgmentMode: 1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [
                                    {
                                        courseName: '化工仪表及自动化',
                                        code: '00053001',
                                        courseCode: '00053',
                                        name: '化工仪表及自动化',
                                    },
                                ],
                                answerAreaStyle: '0,1,2,3,4',
                                stem: '镍铬－康铜热电偶的分度号是（）',
                                batchNumber: '03f9412d-f41a-49a8-97d9-b24cd0a1678a',
                            },
                        ],
                        realEachScore: 2,
                        level: 2,
                        totalscore: '40',
                        eachQuestionScore: '2',
                        sequencenumber: '一',
                        usedsequence: '一',
                        isSameScore: true,
                        questionTypeId: '1',
                        answerMode: 'SingleSelection',
                        parentId: 'bce243e5-a189-4237-b8fd-b9f36c242a34',
                        sequence: 1,
                        totalquestion: 20,
                        judgementModeCode: '1',
                        name: '单选题',
                        comment: null,
                        id: '8ab1db0c-8d3b-41cb-b30d-84320c77a61e',
                    },
                    // 多选题
                    {
                        paperQuestionList: [
                            {
                                solvingProcess: '',
                                sequenceNumber: '21',
                                uploadFile: 0,
                                questionId: 'd03c12e0-9819-4eb1-9429-a0179322c09c',
                                answerRecord: {},
                                questiontypename: '多选题',
                                difficultyDegree: 3,
                                usedsequence: '1',
                                answerArea: {
                                    optionList: [
                                        {
                                            id: '0',
                                            content: '<span> 系统的升级特性 </span>',
                                            sequence: 0,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '1',
                                            content: '<span> 互操作与互用性 </span>',
                                            sequence: 1,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '2',
                                            content: '<span> 现场设备的智能化 </span>',
                                            sequence: 2,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '3',
                                            content: '<span> 系统结构的高度分散性 </span>',
                                            sequence: 3,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '4',
                                            content: '<span>对现场环境的适应性</span>',
                                            sequence: 4,
                                            score: 0,
                                            isInput: false,
                                        },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'MultiSelection',
                                sequence: 1,
                                score: 4,
                                judgmentMode: 1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [
                                    {
                                        courseName: '化工仪表及自动化',
                                        code: '00053001',
                                        courseCode: '00053',
                                        name: '化工仪表及自动化',
                                    },
                                ],
                                answerAreaStyle: '0,1,2,3,4',
                                stem: '现场总线控制系统具有（）特性',
                                batchNumber: 'd03c12e0-9819-4eb1-9429-a0179322c09c',
                            },
                        ],
                        realEachScore: 4,
                        level: 2,
                        totalscore: '40',
                        eachQuestionScore: '4',
                        sequencenumber: '二',
                        usedsequence: '二',
                        isSameScore: true,
                        questionTypeId: '2',
                        answerMode: 'MultiSelection',
                        parentId: 'bce243e5-a189-4237-b8fd-b9f36c242a34',
                        sequence: 2,
                        totalquestion: 10,
                        judgementModeCode: '1',
                        name: '多选题',
                        comment: null,
                        id: '233fbb64-0402-41a7-b5d6-42c134ef6230',
                    },
                    // 判断题
                    {
                        paperQuestionList: [
                            {
                                solvingProcess: '',
                                sequenceNumber: '31',
                                uploadFile: 0,
                                questionId: 'fea5e5bd-0a9f-4f26-acfe-0e47e383ea30',
                                answerRecord: {},
                                questiontypename: '判断',
                                difficultyDegree: 3,
                                usedsequence: '1',
                                answerArea: {
                                    optionList: [
                                        {
                                            id: '1',
                                            content: 'T',
                                            sequence: 1,
                                            score: 0,
                                            isInput: true,
                                        },
                                        {
                                            id: '2',
                                            content: 'F',
                                            sequence: 2,
                                            score: 0,
                                            isInput: true,
                                        },
                                    ],
                                },
                                answerMode: 'Judgement',
                                sequence: 1,
                                score: 1,
                                judgmentMode: 1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [
                                    {
                                        courseName: '化工仪表及自动化',
                                        code: '00053001',
                                        courseCode: '00053',
                                        name: '化工仪表及自动化',
                                    },
                                ],
                                answerAreaStyle: null,
                                stem: '锅炉控制中，当负荷突然增加时，锅炉液位立即降低。',
                                batchNumber: 'fea5e5bd-0a9f-4f26-acfe-0e47e383ea30',
                            },
                        ],
                        realEachScore: 1,
                        level: 2,
                        totalscore: '20',
                        eachQuestionScore: '1',
                        sequencenumber: '三',
                        usedsequence: '三',
                        isSameScore: true,
                        questionTypeId: '4',
                        answerMode: 'Judgement',
                        parentId: 'bce243e5-a189-4237-b8fd-b9f36c242a34',
                        sequence: 3,
                        totalquestion: 20,
                        judgementModeCode: '1',
                        name: '判断',
                        comment: null,
                        id: '5b9c4ca4-a4b3-457d-a4be-15cd827e8c50',
                    },
                    // 阅读理解
                    {
                        paperQuestionList: [
                            {
                                solvingProcess: '',
                                sequenceNumber: '31',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '3759e43541a0445c89f1d696d9396375',
                                answerRecord: {},
                                questiontypename: '阅读理解',
                                difficultyDegree: 1,
                                usedsequence: '1',
                                answerArea: null,
                                answerMode: 'Composite',
                                sequence: 1,
                                score: 20,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: [
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '5a0c4ced202fc05c7b2d54589d86d611',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '1',
                                                    content: 'test the balloons',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: 'launch a house',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: 'shoot a hit movie',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '4',
                                                    content: 'prepare for breaking a record',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 1,
                                        score: 4,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2,3,4',
                                        stem: 'The adventurer flew across the English Channel to__________．',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: 'f36ead50d48054b60b0629437676b7c3',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '1',
                                                    content: '2500 miles',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: '18,000 feet',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: '25,000 feet',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '4',
                                                    content: '230 miles',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 2,
                                        score: 4,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2,3,4',
                                        stem: 'To finish the journey，he will fly a distance of__________．',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: 'f88db71df0dbdafff983232ba1949c4f',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '1',
                                                    content: 'When he will fly',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: 'How high he sill fly',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: 'How far he will fly',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '4',
                                                    content: 'How long it will take him',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 3,
                                        score: 4,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2,3,4',
                                        stem: 'About the ambitious journey，which is NOT mentioned in the passage?',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: 'ebf1434aaba21beea57f2d3956fe4888',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '1',
                                                    content: 'Two',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: 'Three',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: 'Four',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '4',
                                                    content: 'Five',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 4,
                                        score: 4,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2,3,4',
                                        stem: 'How many world records does Jonathan hold?',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '90a791f47494c94780b1cc37623a3cdf',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '1',
                                                    content: 'Trappe can’t sleep worrying about the adventure',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: 'Trappe was born to set world records',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: 'Trappe always keeps his ambition in mind',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '4',
                                                    content: 'Trappe never thought of crossing the Atlantic before',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 5,
                                        score: 4,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2,3,4',
                                        stem: 'What does he last paragraph imply?',
                                    },
                                ],
                                category: [
                                    {
                                        courseName: '大学英语（下）（2020）',
                                        code: '00858001',
                                        courseCode: '00858',
                                        name: '大学英语（下）（2020）',
                                    },
                                ],
                                answerAreaStyle: null,
                                stem: '&nbsp; &nbsp; &nbsp; Up，Up，and Away！<p>&nbsp; &nbsp; &nbsp; An adventurer who became the first person to fly across the English Channel on a cluster of balloons has launched a house into the sky just like in the hit movie Up-in reparation for a more ambitious journey and a new record．<span id="TLWUV798"></span></p>&nbsp; &nbsp; &nbsp; &nbsp;Fearless Trappe，from North Carolina，stepped into the cartoon themed home before flying above the Leon International Balloon Festival in Mexico more than a week ago．<p>&nbsp; &nbsp; &nbsp; The 38-year-old Trappe was using the event as a warm-up for his planned trans-Atlantic flight scheduled for next summer．He aims to complete the 2,500-mile journey in a seven-foot lifeboat carried by 365 huge helium balloons．</p>&nbsp; &nbsp; &nbsp; The brave man is learning to sail a lifeboat，in case he needs to ditch into the ocean during the danger-filled adventure．<p>&nbsp; &nbsp; &nbsp; He still fly at between 18,000 feet and 25,000 feet, beating his previous world altitude record of 21,600 feet, and must fly uninterrupted a distance ten times longer than his previous world record of 230 miles in order to succeed．</p>&nbsp; &nbsp; &nbsp;The adventurer Trappe，who holds records for crossing the Alps，flying the most cluster balloons，and the longest distance，has spent his entire career，building up to this ambitious plan．<p>&nbsp; &nbsp; &nbsp; “I didn’t wake up one day and think:‘I’m going to fly across the Atlantic，’”he said．“Every attempt before this was prepared for this flight, I’ve been training for a long time”.</p>',
                                batchNumber: '3759e43541a0445c89f1d696d9396375',
                            },
                        ],
                        realEachScore: 20,
                        level: 2,
                        totalscore: '40',
                        eachQuestionScore: '20',
                        sequencenumber: '二',
                        usedsequence: '二',
                        isSameScore: true,
                        questionTypeId: '23',
                        answerMode: 'Composite',
                        parentId: 'c98ab9f7-fac0-45d6-b1d5-74996ce81ab8',
                        sequence: 2,
                        totalquestion: 2,
                        judgementModeCode: '2',
                        name: '阅读理解',
                        comment: '',
                        id: 'bdc0cda4-e9eb-46bc-80a9-82ff8cf61fb4',
                    },
                ],
                number: '2404261430481714113048528',
                score: 100,
                name: '23级化工仪表与自动化 作业1',
                questionCategoryId: '2',
                showQuestionCount: true,
                lastSubmitQuestionTime: 0,
                comprehensive: 0,
                usedCount: 1,
            },
            useTime: 4812,
            startTime: '2024-05-17 16:33:09',
            multiCourse: false,
            paperOrder: 0,
            tempSaveAnswer: false,
            viewStructure: true,
            tempSaveAnswerExpire: 72,
            detection: 'false',
            startPaperResponse: false,
            stayInPage: 'false',
            currentStatus: '正在作答',
            needCheckCode: false,
            arrangementName: '23级化工仪表与自动化 作业1',
            answerPaperReentrantLimit: 0,
            paperLayoutMode: 0,
            answerPaperRecordId: 'af56cd0c-9a35-4202-994b-37667663342b',
            banCopy: false,
            answerPaperReentrant: 0,
            iframeHtml: 'http://**************:10038/venus/resources/venus/js/app/iframe.html',
            preventCheat: 0,
            takePhotoInMinimumCommitTime: 0,
            delayTime: 0,
        },
        error: null,
        status: 1,
    };
}

//获取作业试题 ------（获取考试试题）
exports.getQuestionListWithPhoto = async (cookieStr, assignmentObj) => {
    let agent = await getAgent();
    let data = new URLSearchParams();
    data.append('arrangementId', assignmentObj.activityID);
    data.append('resourcePackageId', assignmentObj.studentId);
    let config = {
        url: 'https://exam.chinaedu.net/oxer/app/ots/TestActivity/StartAnswerPaperWithPhoto',
        headers: {
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            cookie: cookieStr,
        },
        method: 'POST',
        // data: data,
        data: `arrangementId=${assignmentObj.activityID}&resourcePackageId=${assignmentObj.studentId}`,
        httpsAgent: agent,
    };
    let questionListRes = await axiosIns(config);
    return questionListRes;
};
if (false) {
    res = {
        data: {
            realpath: '/data/shareNfs/jlu-datafiles/ots/CJBZBE10094',
            faceCorpuscleInTest: false,
            photoBeginTest: true,
            subjectId: null,
            minimumCommitTime: 60,
            tempSaveAnswerTime: 60,
            timeLimitEnabled: true,
            testEndTime: '2024-06-30 21:00:00',
            takePhotoAutomaticSubmitTime: 0,
            closePageAfterSubmit: true,
            tenant: 'CJBZBE10094',
            resourcePackageId: '1792032651671003146',
            roomNo: null,
            iframeJs: 'http://**************:10038/venus/resources/venus/js/app/iframe.js',
            isEntranceExams: false,
            videoAndAudioPlaybackTimes: 0,
            userFaceImageUrl: 'https://otsstatic.chinaedu.net/ots/CJBZBE10094/baseImage/4a/db/4adb20bf-b1b9-4c58-b916-b8b8775093af/20240622202221.JPG',
            delayPapersAutoGradePolicy: false,
            paperTime: 150,
            arrangementId: 'eb35fba3-d824-4e07-95c3-cc6333982403',
            role: 'student',
            submitTips: false,
            autoPhotoTimes: 3,
            canNotEnterOverTimes: 0,
            title: '大学英语（上）（2020）统考试卷',
            photoBeginAboveThreeTimes: false,
            strcode: null,
            scrollTopHtml: 'http://**************:10038/venus/scrollTo_top.html',
            studentTestActivityScoreId: 'a513d33b-0b9a-4df6-9976-47c870d39025',
            takePhotoInTest: true,
            soundRecordingQuestionAnswerTimes: 0,
            forceDetails: 0,
            paper: {
                achievementType: 1,
                currentQuestionNum: 0,
                paperCategoryId: 'da8022c4-ca85-464e-82bd-8afb9fb9497d',
                answerTimeLength: 150,
                creatorId: 'bb80aa98-b60d-4416-8c74-cc22b735a464',
                questionCategoryItemCodes: ['00857'],
                difficultyDegree: 1,
                showComment: true,
                isPrivate: 0,
                judgementmode: 2,
                showTotalScore: true,
                paperForm: 2,
                showEachScore: true,
                psOutputDto: [
                    {
                        paperQuestionList: null,
                        realEachScore: null,
                        level: 1,
                        totalscore: '0.0',
                        eachQuestionScore: null,
                        sequencenumber: '',
                        usedsequence: '',
                        isSameScore: true,
                        questionTypeId: null,
                        answerMode: null,
                        parentId: '',
                        sequence: 1,
                        totalquestion: 0,
                        judgementModeCode: null,
                        name: null,
                        comment: null,
                        id: 'c80fa7fe-e8d9-4a76-8070-6883dd61171d',
                    },
                    {
                        paperQuestionList: [
                            {
                                solvingProcess: '',
                                sequenceNumber: '1',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'c56eb39eca8b8705c916485eed881510',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '1',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> considerable </span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> consideration </span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> consider </span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> considerate </span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 1,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: 'Please give the problem a careful _____.\u003cspan id\u003d\u0022SVYYB272\u0022\u003e\u003c/span\u003e',
                                batchNumber: 'c56eb39eca8b8705c916485eed881510',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '2',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'cff91e0e69d6abb7c942ed776477e168',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '2',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> How was your break</span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> How is your grandma</span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> Where did you go for holiday</span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> What did you do in your holiday</span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 2,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: '\u003cspan id\u003d\u0022HCIMF767\u0022\u003e\u003c/span\u003e——Hey,\u0026nbsp;Kelin.\u0026nbsp;Happy\u0026nbsp;new\u0026nbsp;year!\u0026nbsp;(\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;)?\u003cbr/\u003e——Ok,\u0026nbsp;I\u0026nbsp;guess.\u0026nbsp;My\u0026nbsp;grandma\u0026nbsp;kept\u0026nbsp;cooking\u0026nbsp;and\u0026nbsp;cooking,\u0026nbsp;so\u0026nbsp;I\u0026nbsp;just\u0026nbsp;kept\u0026nbsp;eating.',
                                batchNumber: 'cff91e0e69d6abb7c942ed776477e168',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '3',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'c9dfa7c0b80aa8af0e65b2109e4bc15d',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '3',
                                answerArea: {
                                    optionList: [
                                        {
                                            id: '0',
                                            content: '<span> Hello. How can I find Emergency Room? </span>',
                                            sequence: 0,
                                            score: 0,
                                            isInput: false,
                                        },
                                        { id: '1', content: '<span> Miss, is this Holiday Inn? </span>', sequence: 1, score: 0, isInput: false },
                                        {
                                            id: '2',
                                            content: '<span> Excuse me. Do you sell bathing suits? </span>',
                                            sequence: 2,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '3',
                                            content: '<span> Excuse me. Where are your cosmetics, please.</span>',
                                            sequence: 3,
                                            score: 0,
                                            isInput: false,
                                        },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 3,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: '\u003cp\u003e\u003cspan\u003eCustomer: ______\u003cspan id\u003d\u0022SYCFN944\u0022\u003e\u003c/span\u003e\u003c/span\u003e\u003c/p\u003e\u003cp\u003e\u003cspan\u003eSaleswoman: Yes, they are on the third floor.\u003c/span\u003e\u003c/p\u003e',
                                batchNumber: 'c9dfa7c0b80aa8af0e65b2109e4bc15d',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '4',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '7cc33139cfce9b31f5bd39b9f86ad01d',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '4',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> It is certain. </span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> It sure is. </span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> It must be. </span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> It is real.</span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 4,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: '\u003cp\u003e\u003cspan\u003e Roger: Hi, Frank. \u003cspan id\u003d\u0022YCTGL928\u0022\u003e\u003c/span\u003e\u003c/span\u003e\u003c/p\u003e\u003cp\u003e\u003cspan\u003eFrank: Hi, Roger. The party is really nice, isn’t it?\u003c/span\u003e\u003c/p\u003e\u003cp\u003e\u003cspan\u003eRoger: ______. Lots of food and drinks!\u003c/span\u003e\u003c/p\u003e',
                                batchNumber: '7cc33139cfce9b31f5bd39b9f86ad01d',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '5',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '91d6f37dcbfd7d2c0cc97ced348d5543',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '5',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> Take your time. </span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> Take it easy. </span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> Don’t worry. </span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> Be relaxed. </span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 5,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: '\u003cp\u003e\u003cspan\u003eHairdresser: How would you like to do your hair today? The same style as usual?\u003cspan id\u003d\u0022AGYFR921\u0022\u003e\u003c/span\u003e\u003c/span\u003e\u003c/p\u003e\u003cp\u003e\u003cspan\u003eMrs. Lee: I have a special party to attend tonight, and I’d like to change styles.\u003c/span\u003e\u003c/p\u003e\u003cp\u003e\u003cspan\u003eHairdresser: Very well. You’re not in a hurry, are you?\u003c/span\u003e\u003c/p\u003e\u003cp\u003e\u003cspan\u003eMrs. Lee: No. _____.\u003c/span\u003e\u003c/p\u003e',
                                batchNumber: '91d6f37dcbfd7d2c0cc97ced348d5543',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '6',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'b820ddbb8393522471296856f7bf6f0e',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '6',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> relative </span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> relates </span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> relate </span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> related </span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 6,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: ' The members of an extended family are ____ by blood or by marriage. \u003cspan id\u003d\u0022GMPXA693\u0022\u003e\u003c/span\u003e',
                                batchNumber: 'b820ddbb8393522471296856f7bf6f0e',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '7',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '78bfb8808cd05b542c56aacc1804708c',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '7',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> recalled</span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span>  remembered</span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> recollected</span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> reminded</span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 7,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: '\u003cspan id\u003d\u0022VFNCA935\u0022\u003e\u003c/span\u003eThe\u0026nbsp;scene\u0026nbsp;in\u0026nbsp;the\u0026nbsp;movie\u0026nbsp;(\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;)\u0026nbsp;him\u0026nbsp;of\u0026nbsp;his\u0026nbsp;childhood.',
                                batchNumber: '78bfb8808cd05b542c56aacc1804708c',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '8',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '6d7aee55e35cc7af36ac6a5339c478f5',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '8',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> make out of </span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> make for</span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> make at</span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> make up for</span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 8,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: 'Her\u0026nbsp;beauty\u0026nbsp;can\u0026nbsp;not\u0026nbsp;(\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;)\u0026nbsp;her\u0026nbsp;stupidity.\u003cspan id\u003d\u0022TWKHS24\u0022\u003e\u003c/span\u003e',
                                batchNumber: '6d7aee55e35cc7af36ac6a5339c478f5',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '9',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'e804b38638ec847db3d66bc3939de580',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '9',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> of </span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> at </span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> in </span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> by </span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 9,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: 'There are trees ____ all shapes on the hill.\u003cspan id\u003d\u0022DTSNS337\u0022\u003e\u003c/span\u003e',
                                batchNumber: 'e804b38638ec847db3d66bc3939de580',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '10',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '497c68b9ac3cd93bc3768d10adabd96e',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '10',
                                answerArea: {
                                    optionList: [
                                        {
                                            id: '1',
                                            content: '<span style="font-size:19px;font-family: 宋体;color:#333333">cooking</span>',
                                            sequence: 0,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '2',
                                            content: '<span style="font-size:19px;font-family: 宋体;color:#333333">to cook</span>',
                                            sequence: 0,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '3',
                                            content: '<span style="font-size:19px;font-family: 宋体;color:#333333">cook</span>',
                                            sequence: 0,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '4',
                                            content: '<span style="font-size:19px;font-family: 宋体;color:#333333">with</span>',
                                            sequence: 0,
                                            score: 0,
                                            isInput: false,
                                        },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 10,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '1,2,3,4',
                                stem: '\u003cspan style\u003d\u0022font-size:19px;font-family: 宋体;color:#333333\u0022\u003eFather is busy ____the meal while I am busy with the homework.\u003cspan id\u003d\u0022FHOWP372\u0022\u003e\u003c/span\u003e\u003c/span\u003e',
                                batchNumber: '497c68b9ac3cd93bc3768d10adabd96e',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '11',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '8fa77f61a94b9752d2ff257604ccfbab',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '11',
                                answerArea: {
                                    optionList: [
                                        {
                                            id: '0',
                                            content: '<span> Can you tell me the money I need to post a letter? </span>',
                                            sequence: 0,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '1',
                                            content: '<span> How much is a letter to South Africa? </span>',
                                            sequence: 1,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '2',
                                            content: '<span> Do you do international delivery? </span>',
                                            sequence: 2,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '3',
                                            content: '<span> Do you happen to know how fast airmails are?</span>',
                                            sequence: 3,
                                            score: 0,
                                            isInput: false,
                                        },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 11,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: '\u003cp\u003e\u003cspan\u003eCustomer: Excuse me. ______\u003cspan id\u003d\u0022MMRHB279\u0022\u003e\u003c/span\u003e\u003c/span\u003e\u003c/p\u003e\u003cp\u003e\u003cspan\u003eClerk: The rate for airmails is two dollars.\u003c/span\u003e\u003c/p\u003e',
                                batchNumber: '8fa77f61a94b9752d2ff257604ccfbab',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '12',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'd644f920fefcc06b8b1dd708a27826f8',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '12',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> passing </span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> to pass </span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> of passing </span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> pass </span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 12,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: 'This car is capable _____ most others on the road.\u003cspan id\u003d\u0022UWVUG866\u0022\u003e\u003c/span\u003e',
                                batchNumber: 'd644f920fefcc06b8b1dd708a27826f8',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '13',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '5942e74afbb7a924070776ef3a5f2907',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '13',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> situations </span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> things </span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> cases </span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> areas </span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 13,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: 'In most _____ heavy rains in this area will cause problems.\u003cspan id\u003d\u0022DNVXF114\u0022\u003e\u003c/span\u003e',
                                batchNumber: '5942e74afbb7a924070776ef3a5f2907',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '14',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '8ff32b135755ff390d86ba5e6dcbb265',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '14',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> position</span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> occupation</span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> profession</span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> career</span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 14,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: 'Mr.\u0026nbsp;Brown\u0026nbsp;had\u0026nbsp;an\u0026nbsp;unusual\u0026nbsp;(\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;):\u0026nbsp;he\u0026nbsp;was\u0026nbsp;first\u0026nbsp;an\u0026nbsp;office\u0026nbsp;clerk,\u0026nbsp;then\u0026nbsp;a\u0026nbsp;sailor,\u0026nbsp;and\u003cbr/\u003eended\u0026nbsp;up\u0026nbsp;as\u0026nbsp;a\u0026nbsp;school\u0026nbsp;teacher.\u003cspan id\u003d\u0022YWTPY673\u0022\u003e\u003c/span\u003e',
                                batchNumber: '8ff32b135755ff390d86ba5e6dcbb265',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '15',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '5005f72e4f5bf8c4824ed913ab1c3195',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '15',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> compliment</span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> appreciate</span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> thank</span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> attach</span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 15,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: 'What\u0026nbsp;we\u0026nbsp;would\u0026nbsp;like\u0026nbsp;you\u0026nbsp;to\u0026nbsp;know\u0026nbsp;is\u0026nbsp;that\u0026nbsp;we\u0026nbsp;greatly\u0026nbsp;(\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;)\u0026nbsp;your\u0026nbsp;timely\u0026nbsp;help.\u003cspan id\u003d\u0022VDSJL145\u0022\u003e\u003c/span\u003e',
                                batchNumber: '5005f72e4f5bf8c4824ed913ab1c3195',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '16',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '584c954e2d8530f89b5dfee982286acb',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '16',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> It’s nothing </span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> That’s all right </span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> Yes, I’ll do it </span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> Not at all </span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 16,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: '\u003cp\u003e\u003cspan\u003e Kate: Do you mind opening the door for me? \u003cspan id\u003d\u0022XRWGU975\u0022\u003e\u003c/span\u003e\u003c/span\u003e\u003c/p\u003e\u003cp\u003e\u003cspan\u003eBob: _______.\u003c/span\u003e\u003c/p\u003e',
                                batchNumber: '584c954e2d8530f89b5dfee982286acb',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '17',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'a761f707b2d32c9d0942f58693bad85d',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '17',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> You may only have one. </span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> Yours is lovely, too. </span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> Oh, really? Nice to meet you. </span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> Very well, thank you. </span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 17,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: '\u003cp\u003e\u003cspan\u003eA: Susan, this is my boyfriend Sam.\u003cspan id\u003d\u0022IYMLE876\u0022\u003e\u003c/span\u003e\u003c/span\u003e\u003c/p\u003e\u003cp\u003e\u003cspan\u003eB: ______\u003c/span\u003e\u003c/p\u003e\u003cp\u003e\u003cspan\u003eC: Nice to meet you, too.\u003c/span\u003e\u003c/p\u003e',
                                batchNumber: 'a761f707b2d32c9d0942f58693bad85d',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '18',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '7ef2f1de54ada87cb8e16f5f2e1dacab',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '18',
                                answerArea: {
                                    optionList: [
                                        {
                                            id: '1',
                                            content: '<span style="font-size:19px;font-family: 宋体;color:#333333">fee</span>',
                                            sequence: 0,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '2',
                                            content: '<span style="font-size:19px;font-family: 宋体;color:#333333">tip</span>',
                                            sequence: 0,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '3',
                                            content: '<span style="font-size:19px;font-family: 宋体;color:#333333">fare</span>',
                                            sequence: 0,
                                            score: 0,
                                            isInput: false,
                                        },
                                        {
                                            id: '4',
                                            content: '<span style="font-size:19px;font-family: 宋体;color:#333333">cost</span>',
                                            sequence: 0,
                                            score: 0,
                                            isInput: false,
                                        },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 18,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '1,2,3,4',
                                stem: '\u003cspan style\u003d\u0022font-size:19px;font-family: 宋体;color:#333333\u0022\u003eWhat is the train _____ to Birmingham?\u003cspan id\u003d\u0022XUSVV539\u0022\u003e\u003c/span\u003e\u003c/span\u003e',
                                batchNumber: '7ef2f1de54ada87cb8e16f5f2e1dacab',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '19',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '55a5a06ebdcfb5d6d75e1acf5b6f20fc',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '19',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> would meet</span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> will meet</span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> am going to meet</span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> would have met</span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 19,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: '\u003cspan id\u003d\u0022FLXCW770\u0022\u003e\u003c/span\u003eIf\u0026nbsp;\u0026nbsp;John\u0026nbsp;had\u0026nbsp;told\u0026nbsp;me\u0026nbsp;in\u0026nbsp;advance,\u0026nbsp;I\u0026nbsp;(\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;)\u0026nbsp;him\u0026nbsp;at\u0026nbsp;the\u0026nbsp;airport.',
                                batchNumber: '55a5a06ebdcfb5d6d75e1acf5b6f20fc',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '20',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '26110f139b543490d731ec05146a6f84',
                                answerRecord: {},
                                questiontypename: '单选题',
                                difficultyDegree: 1,
                                usedsequence: '20',
                                answerArea: {
                                    optionList: [
                                        { id: '0', content: '<span> in return</span>', sequence: 0, score: 0, isInput: false },
                                        { id: '1', content: '<span> in turn</span>', sequence: 1, score: 0, isInput: false },
                                        { id: '2', content: '<span> in hand</span>', sequence: 2, score: 0, isInput: false },
                                        { id: '3', content: '<span> in vain</span>', sequence: 3, score: 0, isInput: false },
                                    ],
                                    reorder: false,
                                },
                                answerMode: 'SingleSelection',
                                sequence: 20,
                                score: 1.0,
                                judgmentMode: 1,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: '0,1,2,3',
                                stem: 'I\u0026nbsp;gave\u0026nbsp;John\u0026nbsp;a\u0026nbsp;present\u0026nbsp;but\u0026nbsp;he\u0026nbsp;gave\u0026nbsp;me\u0026nbsp;nothing\u0026nbsp;(\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;).\u003cspan id\u003d\u0022WHORO407\u0022\u003e\u003c/span\u003e',
                                batchNumber: '26110f139b543490d731ec05146a6f84',
                            },
                        ],
                        realEachScore: 1.0,
                        level: 2,
                        totalscore: '20',
                        eachQuestionScore: '1',
                        sequencenumber: '一',
                        usedsequence: '一',
                        isSameScore: true,
                        questionTypeId: '1',
                        answerMode: 'SingleSelection',
                        parentId: 'c80fa7fe-e8d9-4a76-8070-6883dd61171d',
                        sequence: 1,
                        totalquestion: 20,
                        judgementModeCode: '1',
                        name: '单选题',
                        comment: null,
                        id: '93945e81-46f3-4b52-8d44-c95315b3dbde',
                    },
                    {
                        paperQuestionList: [
                            {
                                solvingProcess: '',
                                sequenceNumber: '21',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '3d08ca9043629fe869b6b894eedb73bc',
                                answerRecord: {},
                                questiontypename: '翻译',
                                difficultyDegree: 1,
                                usedsequence: '1',
                                answerArea: { blankLine: 4 },
                                answerMode: 'EssayQuestion',
                                sequence: 1,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cspan style\u003d\u0022font-size:19px;font-family:宋体;color:#333333\u0022\u003e她不玩游戏。\u003cspan id\u003d\u0022ONNAI888\u0022\u003e\u003c/span\u003e\u003c/span\u003e',
                                batchNumber: '3d08ca9043629fe869b6b894eedb73bc',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '22',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'e7c765dded5ba213d3086f46071ea25a',
                                answerRecord: {},
                                questiontypename: '翻译',
                                difficultyDegree: 1,
                                usedsequence: '2',
                                answerArea: { blankLine: 4 },
                                answerMode: 'EssayQuestion',
                                sequence: 2,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cspan style\u003d\u0022font-size:19px;font-family: 宋体;color:#333333\u0022\u003ePlease tell me where I can find these books.\u003cspan id\u003d\u0022XHXKG197\u0022\u003e\u003c/span\u003e\u003c/span\u003e',
                                batchNumber: 'e7c765dded5ba213d3086f46071ea25a',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '23',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'cfbe29e42d5d5ed8d983d275da96e679',
                                answerRecord: {},
                                questiontypename: '翻译',
                                difficultyDegree: 2,
                                usedsequence: '3',
                                answerArea: null,
                                answerMode: 'EssayQuestion',
                                sequence: 3,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cspan id\u003d\u0022XXUQD665\u0022\u003e\u003c/span\u003e你最好不要玩火，会伤到自己的。',
                                batchNumber: 'cfbe29e42d5d5ed8d983d275da96e679',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '24',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'b19ec34f34ff99a348010d79a9195279',
                                answerRecord: {},
                                questiontypename: '翻译',
                                difficultyDegree: 1,
                                usedsequence: '4',
                                answerArea: { blankLine: 4 },
                                answerMode: 'EssayQuestion',
                                sequence: 4,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cspan style\u003d\u0022font-size:19px;font-family: 宋体;color:#333333\u0022\u003eCall me tomorrow if you have time.\u003cspan id\u003d\u0022JHJLW199\u0022\u003e\u003c/span\u003e\u003c/span\u003e',
                                batchNumber: 'b19ec34f34ff99a348010d79a9195279',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '25',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '40a49ee0bc815e0f6e2930508b90f92c',
                                answerRecord: {},
                                questiontypename: '翻译',
                                difficultyDegree: 2,
                                usedsequence: '5',
                                answerArea: null,
                                answerMode: 'EssayQuestion',
                                sequence: 5,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '每一种药物都有副作用，对健康可能造成损害。\u003cspan id\u003d\u0022EBRLK532\u0022\u003e\u003c/span\u003e',
                                batchNumber: '40a49ee0bc815e0f6e2930508b90f92c',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '26',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '7e93eefa97c357c379e8701ac6ad3660',
                                answerRecord: {},
                                questiontypename: '翻译',
                                difficultyDegree: 1,
                                usedsequence: '6',
                                answerArea: { blankLine: 4 },
                                answerMode: 'EssayQuestion',
                                sequence: 6,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cspan style\u003d\u0022font-size:19px;font-family: 宋体;color:#333333\u0022\u003eMy parents got married in 1954.\u003cspan id\u003d\u0022VUBVF29\u0022\u003e\u003c/span\u003e\u003c/span\u003e',
                                batchNumber: '7e93eefa97c357c379e8701ac6ad3660',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '27',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '53f8d17469d053a59b4ddfd7a7361031',
                                answerRecord: {},
                                questiontypename: '翻译',
                                difficultyDegree: 2,
                                usedsequence: '7',
                                answerArea: null,
                                answerMode: 'EssayQuestion',
                                sequence: 7,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '他从图书馆借了一些参考书而不是侦探小说。\u003cspan id\u003d\u0022VCKUP68\u0022\u003e\u003c/span\u003e',
                                batchNumber: '53f8d17469d053a59b4ddfd7a7361031',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '28',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '67220cd43109312fb4eb59d5da780483',
                                answerRecord: {},
                                questiontypename: '翻译',
                                difficultyDegree: 1,
                                usedsequence: '8',
                                answerArea: { blankLine: 4 },
                                answerMode: 'EssayQuestion',
                                sequence: 8,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cspan style\u003d\u0022font-size:18px;font-family: 宋体;color:#333333\u0022\u003eI\u0026#39;ll stay at home and watch TV.\u003cspan id\u003d\u0022VSXOH363\u0022\u003e\u003c/span\u003e\u003c/span\u003e',
                                batchNumber: '67220cd43109312fb4eb59d5da780483',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '29',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'ce063cb8a0108a499640fb6965f23885',
                                answerRecord: {},
                                questiontypename: '翻译',
                                difficultyDegree: 1,
                                usedsequence: '9',
                                answerArea: { blankLine: 4 },
                                answerMode: 'EssayQuestion',
                                sequence: 9,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cspan style\u003d\u0022font-size:18px;font-family: 宋体;color:#333333\u0022\u003eDon\u0026#39;t tell me what to do!\u003cspan id\u003d\u0022NTAHU976\u0022\u003e\u003c/span\u003e\u003c/span\u003e',
                                batchNumber: 'ce063cb8a0108a499640fb6965f23885',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '30',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '4cdff229d4181245d73dde28a80c3804',
                                answerRecord: {},
                                questiontypename: '翻译',
                                difficultyDegree: 1,
                                usedsequence: '10',
                                answerArea: { blankLine: 4 },
                                answerMode: 'EssayQuestion',
                                sequence: 10,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cspan style\u003d\u0022font-size:18px;font-family: 宋体;color:#333333\u0022\u003eWhat\u0026#39;s your plan for the summer holiday?\u003cspan id\u003d\u0022BVOHF475\u0022\u003e\u003c/span\u003e\u003c/span\u003e',
                                batchNumber: '4cdff229d4181245d73dde28a80c3804',
                            },
                        ],
                        realEachScore: 1.0,
                        level: 2,
                        totalscore: '10',
                        eachQuestionScore: '1',
                        sequencenumber: '二',
                        usedsequence: '二',
                        isSameScore: true,
                        questionTypeId: '20',
                        answerMode: 'EssayQuestion',
                        parentId: 'c80fa7fe-e8d9-4a76-8070-6883dd61171d',
                        sequence: 2,
                        totalquestion: 10,
                        judgementModeCode: '2',
                        name: '翻译',
                        comment: null,
                        id: 'cc385712-1b97-4440-9b65-4e75d51cb9cf',
                    },
                    {
                        paperQuestionList: [
                            {
                                solvingProcess: '',
                                sequenceNumber: '31',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'f39f1ae354392ffb55c6810862ad08a7',
                                answerRecord: {},
                                questiontypename: '完形填空',
                                difficultyDegree: 1,
                                usedsequence: '1',
                                answerArea: null,
                                answerMode: 'Composite',
                                sequence: 1,
                                score: 10.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: [
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '4863bb0c22978ecde0b93e83f5dc222a',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '1',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">carpet</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">kind</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">that</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '4',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">first</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '5',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">for</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 1,
                                        score: 2.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2,3,4,5',
                                        stem: '(1)',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: 'c06f87091fdc6bb1fe5ca8a68d093ddc',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '1',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">carpet</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">kind</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">that</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '4',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">first</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '5',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">for</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 2,
                                        score: 2.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2,3,4,5',
                                        stem: '(2)',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: 'b0268b0f08aedfd1802ec38a5c616baf',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '1',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">carpet</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">kind</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">that</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '4',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">first</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '5',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">for</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 3,
                                        score: 2.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2,3,4,5',
                                        stem: '(3)',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '7b4049fe02d926ed35b8224fc8389993',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '1',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">carpet</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">kind</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">that</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '4',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">first</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '5',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">for</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 4,
                                        score: 2.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2,3,4,5',
                                        stem: '(4)',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '8f96ebe2e6873bef0f01249a764fc882',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '1',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">carpet</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">kind</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">that</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '4',
                                                    content: '<span style="font-size:19px;font-family: 宋体;color:#333333">for</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 5,
                                        score: 2.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2,3,4',
                                        stem: '(5)',
                                    },
                                ],
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cp style\u003d\u0022margin-top: 0px; margin-bottom: 10px; white-space: normal; text-indent: 32px; background: white;\u0022\u003e\u003cspan style\u003d\u0022color: #333333; font-family: 宋体, SimSun; font-size: 20px;\u0022\u003eIt was a large house that stood among other identical large houses in a tree-shaded square. At (1) I wondered if there could be some mistake. But the address in the advertisement was clear enough, so I rang the doorbell. A colored maid answered it. When I said I was looking (2) a room she nodded pleasantly, and led me up four flights of stairs.\u003cspan id\u003d\u0022WXMDJ396\u0022\u003e\u003c/span\u003e\u003c/span\u003e\u003c/p\u003e\u003cp style\u003d\u0022white-space: normal; text-indent: 2em;\u0022\u003e\u003cspan style\u003d\u0022color: #333333; font-family: 宋体, SimSun; font-size: 20px;\u0022\u003eThe carpets were thick and red, and wall decorations were of a (3) I had only seen in Hollywood films. I had a feeling that I was about to be shown a very expensive flat and (4) I should feel very foolish explaining that I was looking for something about ten times as cheap. But she led me up a final narrow flight of stairs with no (5) , and showed me a tiny room with a gas-fire, a single bed, an armchair and a table. It was icy cold.\u003c/span\u003e\u003c/p\u003e\u003cp\u003e\u003cbr/\u003e\u003c/p\u003e',
                                batchNumber: 'f39f1ae354392ffb55c6810862ad08a7',
                            },
                        ],
                        realEachScore: 10.0,
                        level: 2,
                        totalscore: '10',
                        eachQuestionScore: '10',
                        sequencenumber: '三',
                        usedsequence: '三',
                        isSameScore: true,
                        questionTypeId: '22',
                        answerMode: 'Composite',
                        parentId: 'c80fa7fe-e8d9-4a76-8070-6883dd61171d',
                        sequence: 3,
                        totalquestion: 1,
                        judgementModeCode: '2',
                        name: '完形填空',
                        comment: null,
                        id: '87e7257f-8692-441c-a6ff-719cd24b536b',
                    },
                    {
                        paperQuestionList: [
                            {
                                solvingProcess: '',
                                sequenceNumber: '32',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '4da89883ab32b97803482c5d1636e95a',
                                answerRecord: {},
                                questiontypename: '阅读理解',
                                difficultyDegree: 2,
                                usedsequence: '1',
                                answerArea: null,
                                answerMode: 'Composite',
                                sequence: 1,
                                score: 5.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: [
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '3ec8284e6ad5f250487caaed5e8da9f1',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                { id: '1', content: 'T', sequence: 0, score: 0, isInput: false },
                                                { id: '2', content: 'F', sequence: 0, score: 0, isInput: false },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 1,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2',
                                        stem: '\u003cspan style\u003d\u0022font-size:19px;font-family: 宋体;color:#333333\u0022\u003eFrom this passage, we can know oil is a source of energy.\u003c/span\u003e',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: 'f1f3d4d1e6423ff0e2e8673a5afb5584',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                { id: '1', content: 'T', sequence: 0, score: 0, isInput: false },
                                                { id: '2', content: 'F', sequence: 0, score: 0, isInput: false },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 2,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2',
                                        stem: '\u003cspan style\u003d\u0022font-size:19px;font-family: 宋体;color:#333333\u0022\u003eAccording to the passage, we can tell that oil is found only under water.\u003c/span\u003e',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: 'ab9be17fd5a0539226e5a99bc556d498',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                { id: '1', content: 'T', sequence: 0, score: 0, isInput: false },
                                                { id: '2', content: 'F', sequence: 0, score: 0, isInput: false },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 3,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2',
                                        stem: '\u003cspan style\u003d\u0022font-size:19px;font-family: 宋体;color:#333333\u0022\u003eSound waves travel through different kinds of rocks at different speeds.\u003c/span\u003e',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '604a87e1995f6bf5f53bab8d47e53ab0',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                { id: '1', content: 'T', sequence: 0, score: 0, isInput: false },
                                                { id: '2', content: 'F', sequence: 0, score: 0, isInput: false },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 4,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2',
                                        stem: '\u003cspan style\u003d\u0022font-size:19px;font-family: 宋体;color:#333333\u0022\u003eMany things are made from oil.\u003c/span\u003e',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '609c4bb67a893c1794d5b1e7207d6d9a',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                { id: '1', content: 'T', sequence: 0, score: 0, isInput: false },
                                                { id: '2', content: 'F', sequence: 0, score: 0, isInput: false },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 5,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '1,2',
                                        stem: '\u003cspan style\u003d\u0022font-size:19px;font-family: 宋体;color:#333333\u0022\u003eThe main idea of the whole story is that oil is important for us but hard to find.\u003c/span\u003e',
                                    },
                                ],
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cp style\u003d\u0022margin: 0px 0px 10px; background: white; text-indent: 2em;\u0022\u003e\u003cspan style\u003d\u0022font-size: 19px; color: #333333; font-family: 宋体, SimSun;\u0022\u003eOil is so important that it is sometimes called \u0026quot;black gold\u0026quot;. Almost half of our energy comes from oil. We use it to run our cars and factories and to heat our homes, offices and schools. Many everyday things are made from oil. Your shirt may have oil in the material. The soap you wash your hands with might also be made from oil. Your favorite plastic toy is made from oil.\u003cspan id\u003d\u0022NXXNA167\u0022\u003e\u003c/span\u003e\u003c/span\u003e\u003c/p\u003e\u003cp style\u003d\u0022margin: 0px 0px 10px; background: white; text-indent: 2em;\u0022\u003e\u003cspan style\u003d\u0022font-size: 19px; color: #333333; font-family: 宋体, SimSun;\u0022\u003eOil is hard to find, because it is trapped deep under the earth. Once the only way people knew there was oil someplace if it leaked out of the ground. Today, however, we have many ways of finding oil. One tool measures the pull of gravity. Places where gravity is weaker are more likely to have oil. Another tool is sound waves. Sound waves travel through different kinds of rocks at different speeds. We can use them to find the rocks that have oil in them.\u003c/span\u003e\u003c/p\u003e\u003cp style\u003d\u0022text-indent: 2em;\u0022\u003e\u003cspan style\u003d\u0022font-size: 19px; color: #333333; font-family: 宋体, SimSun;\u0022\u003eWe need a lot of oil, and we are using up the oil wells we know about. Soon we must find new ways of looking for this \u0026quot;black gold\u0026quot;.\u003c/span\u003e\u003c/p\u003e',
                                batchNumber: '4da89883ab32b97803482c5d1636e95a',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '33',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'f28f01fdcd9582e4ba971d1d41a0d2a3',
                                answerRecord: {},
                                questiontypename: '阅读理解',
                                difficultyDegree: 1,
                                usedsequence: '2',
                                answerArea: null,
                                answerMode: 'Composite',
                                sequence: 2,
                                score: 5.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: [
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: 'eb0fa387ff8c6387aee1e205357c823f',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '0',
                                                    content: '<span> Hunting is very important in human civilization.</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '1',
                                                    content: '<span> Sporting activities satisfy the desire of modern society.</span>',
                                                    sequence: 1,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: '<span> It’s hunting that provides human beings with food.</span>',
                                                    sequence: 2,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: '<span> The importance of sporting activities in modern society.</span>',
                                                    sequence: 3,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 1,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'What\u0026nbsp;is\u0026nbsp;the\u0026nbsp;main\u0026nbsp;idea\u0026nbsp;of\u0026nbsp;the\u0026nbsp;passage?\u003cbr/\u003e  \u0026nbsp;',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: 'f603d5628f1ac268d0a7d1dced56a09a',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '0',
                                                    content: '<span> are essential to the survival</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '1',
                                                    content: '<span> have actually developed from hunting</span>',
                                                    sequence: 1,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: '<span> evolve as biological development</span>',
                                                    sequence: 2,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                { id: '3', content: '<span> are football games </span>', sequence: 3, score: 0, isInput: false },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 2,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'According\u0026nbsp;to\u0026nbsp;the\u0026nbsp;author,\u0026nbsp;sporting\u0026nbsp;activities______.',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '81c9ab99f699dc9a9d4a4a2ff9259622',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                { id: '0', content: '<span> skillful sportsmen</span>', sequence: 0, score: 0, isInput: false },
                                                { id: '1', content: '<span> successful farmers</span>', sequence: 1, score: 0, isInput: false },
                                                { id: '2', content: '<span> runners and jumpers</span>', sequence: 2, score: 0, isInput: false },
                                                { id: '3', content: '<span> cooperating hunters</span>', sequence: 3, score: 0, isInput: false },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 3,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'For\u0026nbsp;over\u0026nbsp;a\u0026nbsp;million\u0026nbsp;years,\u0026nbsp;our\u0026nbsp;forefathers\u0026nbsp;were\u0026nbsp;basically______.\u003cbr/\u003e  ',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: 'b5dc4826ac4bfdde43c7e3c8467f0c11',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                { id: '0', content: '<span> sports activities</span>', sequence: 0, score: 0, isInput: false },
                                                { id: '1', content: '<span> hunting</span>', sequence: 1, score: 0, isInput: false },
                                                { id: '2', content: '<span> farmers</span>', sequence: 2, score: 0, isInput: false },
                                                { id: '3', content: '<span> prey killing </span>', sequence: 3, score: 0, isInput: false },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 4,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'The\u0026nbsp;word\u0022operation\u0022\u0026nbsp;(Par.\u0026nbsp;4)refers\u0026nbsp;to\u0026nbsp;______\u0026nbsp;.\u003cbr/\u003e ',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '8467eb3b422d60ae3b8e54c175c5aa22',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '0',
                                                    content: '<span> The goalmouth is equal to the weapon in hunting.</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '1',
                                                    content: '<span> Without hunting our forefathers couldn’t live.</span>',
                                                    sequence: 1,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: '<span> After our forefathers became farmers they still hunted for food.</span>',
                                                    sequence: 2,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content:
                                                        '<span> Farmers are satisfied with stable lives and they didn’t have enthusiasm for hunting any more.  </span>',
                                                    sequence: 3,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 5,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'Which\u0026nbsp;of\u0026nbsp;the\u0026nbsp;following\u0026nbsp;is\u0026nbsp;true\u0026nbsp;according\u0026nbsp;to\u0026nbsp;the\u0026nbsp;passage?\u003cbr/\u003e ',
                                    },
                                ],
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cp style\u003d\u0022text-indent: 2em;\u0022\u003eSporting\u0026nbsp;activities\u0026nbsp;are\u0026nbsp;essentially\u0026nbsp;modified\u0026nbsp;forms\u0026nbsp;of\u0026nbsp;hunting\u0026nbsp;behavior.\u0026nbsp;Viewed\u0026nbsp;biologically,\u0026nbsp;the\u0026nbsp;modern\u0026nbsp;footballer\u0026nbsp;is\u0026nbsp;in\u0026nbsp;reality\u0026nbsp;a\u0026nbsp;member\u0026nbsp;of\u0026nbsp;a\u0026nbsp;hunting\u0026nbsp;group.\u0026nbsp;His\u0026nbsp;killing\u0026nbsp;weapon\u0026nbsp;has\u0026nbsp;turned\u0026nbsp;into\u0026nbsp;a\u0026nbsp;harmless\u0026nbsp;football\u0026nbsp;and\u0026nbsp;his\u0026nbsp;prey\u0026nbsp;into\u0026nbsp;a\u0026nbsp;goalmouth.\u0026nbsp;If\u0026nbsp;his\u0026nbsp;aim\u0026nbsp;is\u0026nbsp;accurate\u0026nbsp;and\u0026nbsp;he\u0026nbsp;scores\u0026nbsp;a\u0026nbsp;goal,\u0026nbsp;he\u0026nbsp;enjoys\u0026nbsp;the\u0026nbsp;hunter’s\u0026nbsp;triumph\u0026nbsp;of\u0026nbsp;killing\u0026nbsp;his\u0026nbsp;prey.\u0026nbsp;\u0026nbsp; \u003cbr/\u003e\u003cspan id\u003d\u0022SHMUS877\u0022\u003e\u003c/span\u003e\u003c/p\u003e\u003cp style\u003d\u0022text-indent: 2em;\u0022\u003eTo\u0026nbsp;understand\u0026nbsp;how\u0026nbsp;this\u0026nbsp;transformation\u0026nbsp;has\u0026nbsp;taken\u0026nbsp;place\u0026nbsp;we\u0026nbsp;must\u0026nbsp;briefly\u0026nbsp;look\u0026nbsp;back\u0026nbsp;at\u0026nbsp;our\u0026nbsp;forefathers.\u0026nbsp;They\u0026nbsp;spent\u0026nbsp;over\u0026nbsp;a\u0026nbsp;million\u0026nbsp;years\u0026nbsp;evolving(进化)\u0026nbsp;as\u0026nbsp;Cooperative\u0026nbsp;hunters.\u0026nbsp;Their\u0026nbsp;very\u0026nbsp;survival\u0026nbsp;depended\u0026nbsp;on\u0026nbsp;success\u0026nbsp;in\u0026nbsp;the\u0026nbsp;hunting\u0026nbsp;field.\u0026nbsp;Under\u0026nbsp;this\u0026nbsp;pressure\u0026nbsp;their\u0026nbsp;whole\u0026nbsp;way\u0026nbsp;of\u0026nbsp;life,\u0026nbsp;even\u0026nbsp;their\u0026nbsp;bodies,\u0026nbsp;became\u0026nbsp;greatly\u0026nbsp;changed:\u0026nbsp;They\u0026nbsp;became\u0026nbsp;chasers,\u0026nbsp;runners,\u0026nbsp;jumpers,\u0026nbsp;aimers,\u0026nbsp;throwers\u0026nbsp;and\u0026nbsp;prey\u0026nbsp;killers.\u0026nbsp;They\u0026nbsp;cooperated\u0026nbsp;as\u0026nbsp;skillful\u0026nbsp;male\u0026nbsp;group\u0026nbsp;attack. \u003cbr/\u003e\u003c/p\u003e\u003cp style\u003d\u0022text-indent: 2em;\u0022\u003eThen\u0026nbsp;about\u0026nbsp;ten\u0026nbsp;thousand\u0026nbsp;years\u0026nbsp;ago,\u0026nbsp;after\u0026nbsp;this\u0026nbsp;immensely\u0026nbsp;long\u0026nbsp;period\u0026nbsp;of\u0026nbsp;hunting\u0026nbsp;the\u0026nbsp;food,\u0026nbsp;they\u0026nbsp;became\u0026nbsp;farmers.\u0026nbsp;Their\u0026nbsp;improved\u0026nbsp;intelligence,\u0026nbsp;so\u0026nbsp;vital\u0026nbsp;to\u0026nbsp;their\u0026nbsp;old\u0026nbsp;hunting\u0026nbsp;life\u0026nbsp;was\u0026nbsp;put\u0026nbsp;to\u0026nbsp;a\u0026nbsp;new\u0026nbsp;use——that\u0026nbsp;of\u0026nbsp;controlling\u0026nbsp;and\u0026nbsp;domesticating\u0026nbsp;(驯养)\u0026nbsp;their\u0026nbsp;prey.\u0026nbsp;The\u0026nbsp;hunt\u0026nbsp;became\u0026nbsp;suddenly\u0026nbsp;out\u0026nbsp;of\u0026nbsp;date.\u0026nbsp;The\u0026nbsp;food\u0026nbsp;was\u0026nbsp;there\u0026nbsp;on\u0026nbsp;the\u0026nbsp;farms,\u0026nbsp;awaiting\u0026nbsp;their\u0026nbsp;needs.\u0026nbsp;The\u0026nbsp;risks\u0026nbsp;and\u0026nbsp;uncertainties\u0026nbsp;of\u0026nbsp;the\u0026nbsp;hunt\u0026nbsp;were\u0026nbsp;no\u0026nbsp;longer\u0026nbsp;essential\u0026nbsp;for\u0026nbsp;survival.\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp; \u003cbr/\u003e\u003c/p\u003e\u003cp style\u003d\u0022text-indent: 2em;\u0022\u003eThe\u0026nbsp;skills\u0026nbsp;and\u0026nbsp;thirst\u0026nbsp;for\u0026nbsp;hunting\u0026nbsp;remained,\u0026nbsp;however,\u0026nbsp;and\u0026nbsp;demanded\u0026nbsp;new\u0026nbsp;outlets.\u0026nbsp;Hunting\u0026nbsp;for\u0026nbsp;sport\u0026nbsp;replaced\u0026nbsp;hunting\u0026nbsp;for\u0026nbsp;necessity.\u0026nbsp;This\u0026nbsp;new\u0026nbsp;activity\u0026nbsp;involved\u0026nbsp;all\u0026nbsp;the\u0026nbsp;original\u0026nbsp;hunting\u0026nbsp;sequences(后果),\u0026nbsp;but\u0026nbsp;the\u0026nbsp;aim\u0026nbsp;of\u0026nbsp;the\u0026nbsp;operation\u0026nbsp;was\u0026nbsp;no\u0026nbsp;longer\u0026nbsp;to\u0026nbsp;avoid\u0026nbsp;starvation.\u0026nbsp;Instead\u0026nbsp;the\u0026nbsp;sportsmen\u0026nbsp;set\u0026nbsp;off\u0026nbsp;to\u0026nbsp;test\u0026nbsp;their\u0026nbsp;skill\u0026nbsp;against\u0026nbsp;prey\u0026nbsp;that\u0026nbsp;was\u0026nbsp;no\u0026nbsp;longer\u0026nbsp;essential\u0026nbsp;to\u0026nbsp;their\u0026nbsp;survival.\u0026nbsp;To\u0026nbsp;be\u0026nbsp;sure,\u0026nbsp;the\u0026nbsp;kill\u0026nbsp;may\u0026nbsp;have\u0026nbsp;been\u0026nbsp;eaten,\u0026nbsp;but\u0026nbsp;there\u0026nbsp;were\u0026nbsp;other\u0026nbsp;purposes,\u0026nbsp;much\u0026nbsp;simpler\u0026nbsp;of\u0026nbsp;obtaining\u0026nbsp;a\u0026nbsp;meaty\u0026nbsp;meal.\u003c/p\u003e',
                                batchNumber: 'f28f01fdcd9582e4ba971d1d41a0d2a3',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '34',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '2433a33daf20ff4d3af0f1aa2673fcb0',
                                answerRecord: {},
                                questiontypename: '阅读理解',
                                difficultyDegree: 1,
                                usedsequence: '3',
                                answerArea: null,
                                answerMode: 'Composite',
                                sequence: 3,
                                score: 5.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: [
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '6a96c34aee32d15a3d0f12b90736da12',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '0',
                                                    content: '<span> The careers that bring us money</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                { id: '1', content: '<span> The causes we support</span>', sequence: 1, score: 0, isInput: false },
                                                {
                                                    id: '2',
                                                    content: '<span> The dreams that only money can make real</span>',
                                                    sequence: 2,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                { id: '3', content: '<span> Money itself</span>', sequence: 3, score: 0, isInput: false },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 1,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'Which\u0026nbsp;of\u0026nbsp;the\u0026nbsp;following\u0026nbsp;is\u0026nbsp;NOT\u0026nbsp;discussed\u0026nbsp;when\u0026nbsp;we\u0026nbsp;talk\u0026nbsp;about\u0026nbsp;money?\u0026nbsp;',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: 'e4174a632c96f55bba5263c9504bb041',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '0',
                                                    content: '<span> Money itself can interest us and bring us happiness</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '1',
                                                    content: '<span> The more money we earn, the more we should know about it</span>',
                                                    sequence: 1,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: '<span> It is a shame that people talk too much about money</span>',
                                                    sequence: 2,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: '<span> We should know more about money itself rather than avoid talking about it</span>',
                                                    sequence: 3,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 2,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'What\u0026nbsp;can\u0026nbsp;we\u0026nbsp;know\u0026nbsp;from\u0026nbsp;the\u0026nbsp;second\u0026nbsp;paragraph?',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '79e2ae052ac3317dc5a92f2bada2a6cb',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '0',
                                                    content: '<span> People should know the value of money</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '1',
                                                    content: '<span> People should know how to use money to increase their power</span>',
                                                    sequence: 1,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: '<span> People should learn to control their money</span>',
                                                    sequence: 2,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: '<span> People should learn how to make money</span>',
                                                    sequence: 3,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 3,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'What\u0026nbsp;does\u0026nbsp;the\u0026nbsp;writer\u0026nbsp;want\u0026nbsp;to\u0026nbsp;say\u0026nbsp;in\u0026nbsp;the\u0026nbsp;third\u0026nbsp;paragraph?\u003cbr/\u003e  ',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: 'ff9f3cdf45bb60919ac3ed18d2e50335',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '0',
                                                    content: '<span> the more you talk about money, the more you can control it</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '1',
                                                    content: '<span> we should learn to be a good master of our money if we want to be powerful in life</span>',
                                                    sequence: 1,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: '<span> we should not be so worried about money if we want to have a free life</span>',
                                                    sequence: 2,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: '<span> the more money you have, the more powerful you are</span>',
                                                    sequence: 3,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 4,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'The\u0026nbsp;writer’s\u0026nbsp;advice\u0026nbsp;that（\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;).',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '049f7c22b16b33251dde14e0fd889bbb',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                { id: '0', content: '<span> The importance of money</span>', sequence: 0, score: 0, isInput: false },
                                                {
                                                    id: '1',
                                                    content: '<span> Money, power and security</span>',
                                                    sequence: 1,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: '<span> The other steps for people to control money</span>',
                                                    sequence: 2,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: '<span> The steps for people to make money</span>',
                                                    sequence: 3,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 5,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'What\u0026nbsp;will\u0026nbsp;the\u0026nbsp;writer\u0026nbsp;probably\u0026nbsp;talk\u0026nbsp;about\u0026nbsp;after\u0026nbsp;the\u0026nbsp;last\u0026nbsp;paragraph?',
                                    },
                                ],
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cspan id\u003d\u0022SXPFC770\u0022\u003e\u003c/span\u003e\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;What\u0026nbsp;do\u0026nbsp;we\u0026nbsp;talk\u0026nbsp;about\u0026nbsp;when\u0026nbsp;we\u0026nbsp;talk\u0026nbsp;about\u0026nbsp;money?\u0026nbsp;We\u0026nbsp;often\u0026nbsp;think\u0026nbsp;about\u0026nbsp;what\u0026nbsp;we\u0026nbsp;can\u0026nbsp;buy\u0026nbsp;with\u0026nbsp;the\u0026nbsp;money\u0026nbsp;we\u0026nbsp;have,\u0026nbsp;what\u0026nbsp;we\u0026nbsp;can’t\u0026nbsp;buy\u0026nbsp;because\u0026nbsp;we\u0026nbsp;don’t\u0026nbsp;have\u0026nbsp;enough\u0026nbsp;and\u0026nbsp;what\u0026nbsp;we’re\u0026nbsp;planning\u0026nbsp;to\u0026nbsp;buy\u0026nbsp;when\u0026nbsp;we\u0026nbsp;have\u0026nbsp;more.\u0026nbsp;We\u0026nbsp;discuss\u0026nbsp;the\u0026nbsp;careers\u0026nbsp;that\u0026nbsp;bring\u0026nbsp;us\u0026nbsp;money\u0026nbsp;and\u0026nbsp;the\u0026nbsp;expenses\u0026nbsp;that\u0026nbsp;take\u0026nbsp;it\u0026nbsp;away.\u0026nbsp;We\u0026nbsp;talk\u0026nbsp;about\u0026nbsp;our\u0026nbsp;favourite\u0026nbsp;shops\u0026nbsp;and\u0026nbsp;restaurants,\u0026nbsp;the\u0026nbsp;causes\u0026nbsp;we\u0026nbsp;support,\u0026nbsp;the\u0026nbsp;places\u0026nbsp;we’ve\u0026nbsp;been\u0026nbsp;and\u0026nbsp;seen.\u0026nbsp;We\u0026nbsp;share\u0026nbsp;dreams\u0026nbsp;that\u0026nbsp;only\u0026nbsp;money\u0026nbsp;can\u0026nbsp;make\u0026nbsp;real.\u003cbr/\u003e\u0026nbsp;\u0026nbsp;\u0026nbsp;In\u0026nbsp;short,\u0026nbsp;we\u0026nbsp;talk\u0026nbsp;about\u0026nbsp;everything\u0026nbsp;but\u0026nbsp;money\u0026nbsp;itself.\u0026nbsp;In\u0026nbsp;daily\u0026nbsp;life,\u0026nbsp;money\u0026nbsp;is\u0026nbsp;still\u0026nbsp;a\u0026nbsp;major\u0026nbsp;conversational\u0026nbsp;taboo.\u0026nbsp;This\u0026nbsp;is\u0026nbsp;a\u0026nbsp;shame,\u0026nbsp;because\u0026nbsp;money\u0026nbsp;is\u0026nbsp;as\u0026nbsp;interesting\u0026nbsp;as\u0026nbsp;the\u0026nbsp;things\u0026nbsp;it\u0026nbsp;does\u0026nbsp;and\u0026nbsp;buys,\u0026nbsp;and\u0026nbsp;the\u0026nbsp;more\u0026nbsp;you\u0026nbsp;know\u0026nbsp;about\u0026nbsp;it,\u0026nbsp;the\u0026nbsp;more\u0026nbsp;interesting\u0026nbsp;it\u0026nbsp;is.\u003cbr/\u003e\u0026nbsp;\u0026nbsp;\u0026nbsp;As\u0026nbsp;a\u0026nbsp;financial\u0026nbsp;advisor,\u0026nbsp;I’ve\u0026nbsp;seen\u0026nbsp;hundreds\u0026nbsp;of\u0026nbsp;people\u0026nbsp;learn\u0026nbsp;to\u0026nbsp;control\u0026nbsp;their\u0026nbsp;money\u0026nbsp;instead\u0026nbsp;of\u0026nbsp;letting\u0026nbsp;it\u0026nbsp;control\u0026nbsp;them\u0026nbsp;and\u0026nbsp;watched\u0026nbsp;as\u0026nbsp;they\u0026nbsp;increased\u0026nbsp;as\u0026nbsp;they\u0026nbsp;increased\u0026nbsp;their\u0026nbsp;freedom,\u0026nbsp;power\u0026nbsp;and\u0026nbsp;security\u0026nbsp;by\u0026nbsp;handling\u0026nbsp;money\u0026nbsp;consciously.\u0026nbsp;Wouldn’t\u0026nbsp;you\u0026nbsp;like\u0026nbsp;to\u0026nbsp;know\u0026nbsp;that\u0026nbsp;you’ll\u0026nbsp;always\u0026nbsp;have\u0026nbsp;enough\u0026nbsp;money\u0026nbsp;to\u0026nbsp;live\u0026nbsp;exactly\u0026nbsp;as\u0026nbsp;you\u0026nbsp;want\u0026nbsp;to?',
                                batchNumber: '2433a33daf20ff4d3af0f1aa2673fcb0',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '35',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'b3b112147370f39c29b83f42f2dd6083',
                                answerRecord: {},
                                questiontypename: '阅读理解',
                                difficultyDegree: 1,
                                usedsequence: '4',
                                answerArea: null,
                                answerMode: 'Composite',
                                sequence: 4,
                                score: 5.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: [
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '1adce53f8584cb585cc06aaf25a43443',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                { id: '0', content: '<span> modern transportation</span>', sequence: 0, score: 0, isInput: false },
                                                { id: '1', content: '<span> new technology</span>', sequence: 1, score: 0, isInput: false },
                                                { id: '2', content: '<span> the changes of the world</span>', sequence: 2, score: 0, isInput: false },
                                                { id: '3', content: '<span> a peace agreement</span>', sequence: 3, score: 0, isInput: false },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 1,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'News\u0026nbsp;spreads\u0026nbsp;fast\u0026nbsp;because\u0026nbsp;of\u0026nbsp;______.\u003cbr/\u003e',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '0e52441356ff8a8635c58a6d3d0e7834',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                { id: '0', content: '<span> fast communication</span>', sequence: 0, score: 0, isInput: false },
                                                { id: '1', content: '<span> modern technology</span>', sequence: 1, score: 0, isInput: false },
                                                { id: '2', content: '<span> the news</span>', sequence: 2, score: 0, isInput: false },
                                                { id: '3', content: '<span> new ideas</span>', sequence: 3, score: 0, isInput: false },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 2,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'According\u0026nbsp;to\u0026nbsp;this\u0026nbsp;passage,\u0026nbsp;______is\u0026nbsp;very\u0026nbsp;important\u0026nbsp;to\u0026nbsp;people\u0026nbsp;in\u0026nbsp;a\u0026nbsp;disaster\u0026nbsp;area.',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '6b50ed95e86dd06b7b3ecb99be6e31ac',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                {
                                                    id: '0',
                                                    content: '<span> The world now seems smaller because of faster communication.</span>',
                                                    sequence: 0,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '1',
                                                    content: '<span> The world is actually smaller today.</span>',
                                                    sequence: 1,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '2',
                                                    content: '<span> The world is changing in size.</span>',
                                                    sequence: 2,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                                {
                                                    id: '3',
                                                    content: '<span> The distant between England and America has changed since the War of 1812.</span>',
                                                    sequence: 3,
                                                    score: 0,
                                                    isInput: false,
                                                },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 3,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'Which\u0026nbsp;of\u0026nbsp;the\u0026nbsp;following\u0026nbsp;statements\u0026nbsp;is\u0026nbsp;TRUE\u0026nbsp;based\u0026nbsp;on\u0026nbsp;the\u0026nbsp;text?\u003cbr/\u003e',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '9f5b8c8c2de1be460e5c5eed10c949e0',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                { id: '0', content: '<span> by telephone</span>', sequence: 0, score: 0, isInput: false },
                                                { id: '1', content: '<span> by land</span>', sequence: 1, score: 0, isInput: false },
                                                { id: '2', content: '<span> by air</span>', sequence: 2, score: 0, isInput: false },
                                                { id: '3', content: '<span> by sea</span>', sequence: 3, score: 0, isInput: false },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 4,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'Two\u0026nbsp;hundred\u0026nbsp;years\u0026nbsp;ago,\u0026nbsp;news\u0026nbsp;between\u0026nbsp;the\u0026nbsp;continents\u0026nbsp;was\u0026nbsp;carried\u0026nbsp;______.\u003cbr/\u003e ',
                                    },
                                    {
                                        solvingProcess: null,
                                        sequenceNumber: null,
                                        uploadFile: 0,
                                        studentSoundRecordingQuestionAnswerTimes: -1,
                                        questionId: '19a6912b61236d059e5306457c3b7c90',
                                        answerRecord: {},
                                        questiontypename: '单选题',
                                        answerArea: {
                                            optionList: [
                                                { id: '0', content: '<span> by both sides</span>', sequence: 0, score: 0, isInput: false },
                                                { id: '1', content: '<span> in time</span>', sequence: 1, score: 0, isInput: false },
                                                { id: '2', content: '<span> in America</span>', sequence: 2, score: 0, isInput: false },
                                                { id: '3', content: '<span> in England</span>', sequence: 3, score: 0, isInput: false },
                                            ],
                                            reorder: false,
                                        },
                                        answerMode: 'SingleSelection',
                                        sequence: 5,
                                        score: 1.0,
                                        studentVideoAndAudioPlaybackTimes: -1,
                                        answer: null,
                                        operationQuestion: 0,
                                        uploadImg: 1,
                                        answerAreaStyle: '0,1,2,3',
                                        stem: 'The\u0026nbsp;New\u0026nbsp;Orleans\u0026nbsp;Battle\u0026nbsp;could\u0026nbsp;have\u0026nbsp;been\u0026nbsp;avoided\u0026nbsp;if\u0026nbsp;the\u0026nbsp;peace\u0026nbsp;agreement\u0026nbsp;had\u0026nbsp;been\u0026nbsp;signed\u0026nbsp;______.',
                                    },
                                ],
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;Telephone,\u0026nbsp;television,\u0026nbsp;radio\u0026nbsp;and\u0026nbsp;the\u0026nbsp;Internet\u0026nbsp;help\u0026nbsp;people\u0026nbsp;communicate\u0026nbsp;with\u0026nbsp;each\u0026nbsp;other.\u0026nbsp;Because\u0026nbsp;of\u0026nbsp;these\u0026nbsp;devices,\u0026nbsp;ideas\u0026nbsp;and\u0026nbsp;news\u0026nbsp;of\u0026nbsp;events\u0026nbsp;spread\u0026nbsp;quickly\u0026nbsp;all\u0026nbsp;over\u0026nbsp;the\u0026nbsp;world.\u0026nbsp;For\u0026nbsp;example,\u0026nbsp;within\u0026nbsp;seconds,\u0026nbsp;people\u0026nbsp;can\u0026nbsp;know\u0026nbsp;the\u0026nbsp;results\u0026nbsp;of\u0026nbsp;an\u0026nbsp;election\u0026nbsp;in\u0026nbsp;another\u0026nbsp;country.\u0026nbsp;An\u0026nbsp;international\u0026nbsp;football\u0026nbsp;match\u0026nbsp;comes\u0026nbsp;into\u0026nbsp;the\u0026nbsp;homes\u0026nbsp;of\u0026nbsp;everyone\u0026nbsp;with\u0026nbsp;a\u0026nbsp;television\u0026nbsp;set.\u0026nbsp;News\u0026nbsp;of\u0026nbsp;a\u0026nbsp;disaster,\u0026nbsp;such\u0026nbsp;as\u0026nbsp;a\u0026nbsp;flood,\u0026nbsp;can\u0026nbsp;bring\u0026nbsp;help\u0026nbsp;from\u0026nbsp;distant\u0026nbsp;countries.\u0026nbsp;With\u0026nbsp;in\u0026nbsp;hours,\u0026nbsp;help\u0026nbsp;is\u0026nbsp;on\u0026nbsp;the\u0026nbsp;way.\u0026nbsp;This\u0026nbsp;is\u0026nbsp;because\u0026nbsp;modern\u0026nbsp;technology\u0026nbsp;information\u0026nbsp;travels\u0026nbsp;fast.\u003cbr/\u003e\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;How\u0026nbsp;has\u0026nbsp;this\u0026nbsp;speed\u0026nbsp;of\u0026nbsp;communication\u0026nbsp;changed\u0026nbsp;the\u0026nbsp;world?\u0026nbsp;To\u0026nbsp;many\u0026nbsp;people,\u0026nbsp;the\u0026nbsp;world\u0026nbsp;has\u0026nbsp;become\u0026nbsp;smaller.\u0026nbsp;Of\u0026nbsp;course,\u0026nbsp;this\u0026nbsp;does\u0026nbsp;not\u0026nbsp;mean\u0026nbsp;that\u0026nbsp;the\u0026nbsp;world\u0026nbsp;is\u0026nbsp;actually\u0026nbsp;physically\u0026nbsp;smaller.\u0026nbsp;It\u0026nbsp;means\u0026nbsp;that\u0026nbsp;the\u0026nbsp;world\u0026nbsp;seems\u0026nbsp;smaller.\u0026nbsp;Two\u0026nbsp;hundred\u0026nbsp;years\u0026nbsp;ago,\u0026nbsp;communication\u0026nbsp;between\u0026nbsp;the\u0026nbsp;continents\u0026nbsp;took\u0026nbsp;a\u0026nbsp;long\u0026nbsp;time.\u0026nbsp;All\u0026nbsp;news\u0026nbsp;was\u0026nbsp;carried\u0026nbsp;on\u0026nbsp;ships\u0026nbsp;that\u0026nbsp;took\u0026nbsp;weeks\u0026nbsp;or\u0026nbsp;even\u0026nbsp;months\u0026nbsp;to\u0026nbsp;cross\u0026nbsp;the\u0026nbsp;oceans.\u0026nbsp;In\u0026nbsp;the\u0026nbsp;seventeenth\u0026nbsp;and\u0026nbsp;eighteenth\u0026nbsp;centuries,\u0026nbsp;it\u0026nbsp;took\u0026nbsp;six\u0026nbsp;weeks\u0026nbsp;for\u0026nbsp;news\u0026nbsp;from\u0026nbsp;Europe\u0026nbsp;to\u0026nbsp;reach\u0026nbsp;the\u0026nbsp;Americas.\u0026nbsp;This\u0026nbsp;time\u0026nbsp;difference\u0026nbsp;influenced\u0026nbsp;people’s\u0026nbsp;actions.\u0026nbsp;For\u0026nbsp;example,\u0026nbsp;a\u0026nbsp;few\u0026nbsp;battles\u0026nbsp;in\u0026nbsp;the\u0026nbsp;war\u0026nbsp;of\u0026nbsp;1812\u0026nbsp;between\u0026nbsp;England\u0026nbsp;and\u0026nbsp;the\u0026nbsp;United\u0026nbsp;States\u0026nbsp;could\u0026nbsp;have\u0026nbsp;been\u0026nbsp;avoided.\u0026nbsp;A\u0026nbsp;peace\u0026nbsp;agreement\u0026nbsp;had\u0026nbsp;already\u0026nbsp;been\u0026nbsp;signed.\u0026nbsp;Peace\u0026nbsp;was\u0026nbsp;made\u0026nbsp;in\u0026nbsp;England,\u0026nbsp;but\u0026nbsp;the\u0026nbsp;news\u0026nbsp;of\u0026nbsp;peace\u0026nbsp;took\u0026nbsp;six\u0026nbsp;weeks\u0026nbsp;to\u0026nbsp;reach\u0026nbsp;America.\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;\u0026nbsp;During\u0026nbsp;these\u0026nbsp;six\u0026nbsp;weeks,\u0026nbsp;the\u0026nbsp;large\u0026nbsp;and\u0026nbsp;serious\u0026nbsp;Battle\u0026nbsp;of\u0026nbsp;New\u0026nbsp;Orleans\u0026nbsp;was\u0026nbsp;fought.\u0026nbsp;Many\u0026nbsp;people\u0026nbsp;lost\u0026nbsp;their\u0026nbsp;lives\u0026nbsp;after\u0026nbsp;a\u0026nbsp;peace\u0026nbsp;treaty\u0026nbsp;had\u0026nbsp;been\u0026nbsp;signed.\u0026nbsp;They\u0026nbsp;would\u0026nbsp;not\u0026nbsp;have\u0026nbsp;died\u0026nbsp;if\u0026nbsp;news\u0026nbsp;had\u0026nbsp;come\u0026nbsp;in\u0026nbsp;time.\u0026nbsp;In\u0026nbsp;the\u0026nbsp;past,\u0026nbsp;communication\u0026nbsp;took\u0026nbsp;much\u0026nbsp;more\u0026nbsp;time\u0026nbsp;than\u0026nbsp;it\u0026nbsp;does\u0026nbsp;now.\u0026nbsp;There\u0026nbsp;was\u0026nbsp;a\u0026nbsp;good\u0026nbsp;reason\u0026nbsp;why\u0026nbsp;the\u0026nbsp;world\u0026nbsp;seemed\u0026nbsp;so\u0026nbsp;much\u0026nbsp;larger\u0026nbsp;than\u0026nbsp;it\u0026nbsp;does\u0026nbsp;today.\u003cspan id\u003d\u0022EOFWS151\u0022\u003e\u003c/span\u003e',
                                batchNumber: 'b3b112147370f39c29b83f42f2dd6083',
                            },
                        ],
                        realEachScore: 5.0,
                        level: 2,
                        totalscore: '20',
                        eachQuestionScore: '5',
                        sequencenumber: '四',
                        usedsequence: '四',
                        isSameScore: true,
                        questionTypeId: '23',
                        answerMode: 'Composite',
                        parentId: 'c80fa7fe-e8d9-4a76-8070-6883dd61171d',
                        sequence: 4,
                        totalquestion: 4,
                        judgementModeCode: '2',
                        name: '阅读理解',
                        comment: '',
                        id: 'bf98f347-5fa2-4003-bf16-22fc2bc6dfef',
                    },
                    {
                        paperQuestionList: [
                            {
                                solvingProcess: '',
                                sequenceNumber: '36',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '9f6b3d96ebfcbc0b23a5044503765cde',
                                answerRecord: {},
                                questiontypename: '填空',
                                difficultyDegree: 1,
                                usedsequence: '1',
                                answerArea: {
                                    inputAreaList: [
                                        { id: '1', sequence: 0, score: 0 },
                                        { id: '2', sequence: 0, score: 0 },
                                    ],
                                },
                                answerMode: 'BlankFilling',
                                sequence: 1,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: 'Our ____ \u0026nbsp;\u003cspan id\u003d\u0022XYEOP187\u0022\u003e\u003c/span\u003e\u003c/var\u003e\u0026nbsp;（目的地）\u0026nbsp;is\u0026nbsp;London.\u0026nbsp;But\u0026nbsp;the\u0026nbsp;plane\u0026nbsp;took\u0026nbsp;us\u0026nbsp;to\u0026nbsp;Paris.\u0026lt;br/\u0026gt;',
                                batchNumber: '6c52c1deba9d4c626e3af90fcc68d0fd',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '37',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '0e627610e182129955f50b05288cae98',
                                answerRecord: {},
                                questiontypename: '填空',
                                difficultyDegree: 1,
                                usedsequence: '2',
                                answerArea: {
                                    inputAreaList: [
                                        { id: '1', sequence: 0, score: 0 },
                                        { id: '2', sequence: 0, score: 0 },
                                    ],
                                },
                                answerMode: 'BlankFilling',
                                sequence: 2,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: 'Careful\u0026nbsp;planning\u0026nbsp;and\u0026nbsp;hard\u0026nbsp;work\u0026nbsp;will ____ \u003cspan id\u003d\u0022BJNMC183\u0022\u003e\u003c/span\u003e\u003c/var\u003e（确保）\u0026nbsp;our\u0026nbsp;final\u0026nbsp;success.',
                                batchNumber: '54462a5d6b44e8a594b8b93fbc43b786',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '38',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '1d88436f0aa8fc7c4c2d1197bb7482a2',
                                answerRecord: {},
                                questiontypename: '填空',
                                difficultyDegree: 1,
                                usedsequence: '3',
                                answerArea: { inputAreaList: [{ id: '1', sequence: 0, score: 0 }] },
                                answerMode: 'BlankFilling',
                                sequence: 3,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cspan id\u003d\u0022VQOOY654\u0022\u003e\u003c/span\u003eYou\u0026nbsp;should\u0026nbsp;keep\u0026nbsp;calm\u0026nbsp;during\u0026nbsp;your\u0026nbsp; ____ （口语）\u0026nbsp;test.\u0026nbsp;Do\u0026nbsp;not\u0026nbsp;be\u0026nbsp;nervous.',
                                batchNumber: '9e21355002f7d7055b5c3cb53e3f883d',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '39',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '82051c88268b907bdf0d12a8fd15bca1',
                                answerRecord: {},
                                questiontypename: '填空',
                                difficultyDegree: 1,
                                usedsequence: '4',
                                answerArea: { inputAreaList: [{ id: '1', sequence: 0, score: 0 }] },
                                answerMode: 'BlankFilling',
                                sequence: 4,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cspan id\u003d\u0022JECPM581\u0022\u003e\u003c/span\u003eThat\u0026nbsp;rich\u0026nbsp;man\u0026nbsp;has\u0026nbsp;been\u0026nbsp;dead\u0026nbsp;for\u0026nbsp;a\u0026nbsp;long\u0026nbsp;time\u0026nbsp;but\u0026nbsp;his\u0026nbsp; ____ （遗嘱）\u0026nbsp;is\u0026nbsp;still\u0026nbsp;not\u0026nbsp;known\u0026nbsp;to\u0026nbsp;the\u0026nbsp;public.',
                                batchNumber: '255f290fb3c26a2dfc7010ae82eed1cc',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '40',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '9635c050edb62ed37873098c45c3cb43',
                                answerRecord: {},
                                questiontypename: '填空',
                                difficultyDegree: 1,
                                usedsequence: '5',
                                answerArea: {
                                    inputAreaList: [
                                        { id: '1', sequence: 0, score: 0 },
                                        { id: '2', sequence: 0, score: 0 },
                                    ],
                                },
                                answerMode: 'BlankFilling',
                                sequence: 5,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: 'Generally\u0026nbsp;speaking,\u0026nbsp;there\u0026nbsp;is\u0026nbsp;always\u0026nbsp;a\u0026nbsp;generation\u0026nbsp; ____ \u003cspan id\u003d\u0022KBPYT885\u0022\u003e\u003c/span\u003e\u003c/var\u003e（鸿沟）\u0026nbsp;in\u0026nbsp;every\u0026nbsp;country.\u0026nbsp;',
                                batchNumber: '5b24c28364db8b4298d39c1f544106d9',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '41',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'e1855070bf97f7fcf5ce2c3c41e57194',
                                answerRecord: {},
                                questiontypename: '填空',
                                difficultyDegree: 1,
                                usedsequence: '6',
                                answerArea: { inputAreaList: [{ id: '1', sequence: 0, score: 0 }] },
                                answerMode: 'BlankFilling',
                                sequence: 6,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: '\u003cspan id\u003d\u0022YSQBR707\u0022\u003e\u003c/span\u003eWhen\u0026nbsp;you\u0026nbsp;take\u0026nbsp;a\u0026nbsp;picture,\u0026nbsp;you\u0026nbsp;should\u0026nbsp; ____ \u0026nbsp;（对焦）carefully\u0026nbsp;to\u0026nbsp;get\u0026nbsp;a\u0026nbsp;sharp\u0026nbsp;picture.',
                                batchNumber: '793ca126d0c8caee7b282fd96617da65',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '42',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '54fff30398f4d4b302cda812ef298cce',
                                answerRecord: {},
                                questiontypename: '填空',
                                difficultyDegree: 1,
                                usedsequence: '7',
                                answerArea: {
                                    inputAreaList: [
                                        { id: '1', sequence: 0, score: 0 },
                                        { id: '2', sequence: 0, score: 0 },
                                    ],
                                },
                                answerMode: 'BlankFilling',
                                sequence: 7,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: 'He ____ \u0026nbsp;\u003cspan id\u003d\u0022VFDKJ867\u0022\u003e\u003c/span\u003e\u003c/var\u003e\u0026nbsp;（道歉）\u0026nbsp;to\u0026nbsp;his\u0026nbsp;roommate\u0026nbsp;for\u0026nbsp;being\u0026nbsp;so\u0026nbsp;rude\u0026nbsp;yesterday.',
                                batchNumber: '5f256425ebefe1041ed79c450e29b150',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '43',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: 'ae7165adbeb4dfda6dd3ebb9af635de1',
                                answerRecord: {},
                                questiontypename: '填空',
                                difficultyDegree: 1,
                                usedsequence: '8',
                                answerArea: {
                                    inputAreaList: [
                                        { id: '1', sequence: 0, score: 0 },
                                        { id: '2', sequence: 0, score: 0 },
                                    ],
                                },
                                answerMode: 'BlankFilling',
                                sequence: 8,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: 'Our\u0026nbsp;guide\u0026nbsp;gave\u0026nbsp;us\u0026nbsp;a\u0026nbsp;detailed\u0026nbsp; ____ \u0026nbsp;\u003cspan id\u003d\u0022FDEFY411\u0022\u003e\u003c/span\u003e\u003c/var\u003e\u0026nbsp;\u0026nbsp;（讲解）of\u0026nbsp;this\u0026nbsp;painting\u0026nbsp;but\u0026nbsp;we\u0026nbsp;still\u0026nbsp;do\u0026nbsp;not\u0026nbsp;understand.\u0026lt;br/\u0026gt;',
                                batchNumber: 'a216cd82132943c9bcf346d094174c69',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '44',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '003163d954a072eebe29eaffba1716d4',
                                answerRecord: {},
                                questiontypename: '填空',
                                difficultyDegree: 1,
                                usedsequence: '9',
                                answerArea: { inputAreaList: [{ id: '1', sequence: 0, score: 0 }] },
                                answerMode: 'BlankFilling',
                                sequence: 9,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: 'Young\u0026nbsp;as\u0026nbsp;her\u0026nbsp;sister\u0026nbsp;is,\u0026nbsp;\u0026nbsp;she\u0026nbsp;is\u0026nbsp;pretty\u0026nbsp; ____ (有经验).\u003cspan id\u003d\u0022YJEIP898\u0022\u003e\u003c/span\u003e',
                                batchNumber: 'aedd30684d63296da899a0f3206a46aa',
                            },
                            {
                                solvingProcess: '',
                                sequenceNumber: '45',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '3c8c15b54d17ce19630641e06b3e9df9',
                                answerRecord: {},
                                questiontypename: '填空',
                                difficultyDegree: 1,
                                usedsequence: '10',
                                answerArea: { inputAreaList: [{ id: '1', sequence: 0, score: 0 }] },
                                answerMode: 'BlankFilling',
                                sequence: 10,
                                score: 1.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: 'The\u0026nbsp; ____ (剩余)\u0026nbsp;money\u0026nbsp;belongs\u0026nbsp;to\u0026nbsp;you.\u003cspan id\u003d\u0022YKPMF545\u0022\u003e\u003c/span\u003e',
                                batchNumber: '795e712acff0cae283ca62053a2f3613',
                            },
                        ],
                        realEachScore: 1.0,
                        level: 2,
                        totalscore: '10',
                        eachQuestionScore: '1',
                        sequencenumber: '五',
                        usedsequence: '五',
                        isSameScore: true,
                        questionTypeId: '3',
                        answerMode: 'BlankFilling',
                        parentId: 'c80fa7fe-e8d9-4a76-8070-6883dd61171d',
                        sequence: 5,
                        totalquestion: 10,
                        judgementModeCode: '2',
                        name: '填空',
                        comment: null,
                        id: '0de0914c-1669-4ff4-9ca7-12d002e2083d',
                    },
                    {
                        paperQuestionList: [
                            {
                                solvingProcess: '',
                                sequenceNumber: '46',
                                uploadFile: 0,
                                studentSoundRecordingQuestionAnswerTimes: -1,
                                questionId: '48714381b63592c7df5bd254ff3b06e1',
                                answerRecord: {},
                                questiontypename: '作文题',
                                difficultyDegree: 3,
                                usedsequence: '1',
                                answerArea: { blankLine: 4 },
                                answerMode: 'EssayQuestion',
                                sequence: 1,
                                score: 30.0,
                                judgmentMode: 2,
                                studentVideoAndAudioPlaybackTimes: -1,
                                answer: null,
                                operationQuestion: 0,
                                uploadImg: 1,
                                subqustionList: null,
                                category: [{ courseName: '大学英语（上）（2020）', code: '00857001', courseCode: '00857', name: '大学英语（上）（2020）' }],
                                answerAreaStyle: null,
                                stem: 'For\u0026nbsp;this\u0026nbsp;part,\u0026nbsp;you\u0026nbsp;should\u0026nbsp;write\u0026nbsp;a\u0026nbsp;short\u0026nbsp;essay\u0026nbsp;on\u0026nbsp;the\u0026nbsp;topic:\u0026nbsp;Online\u0026nbsp;Shopping.\u0026nbsp;You\u0026nbsp;should\u0026nbsp;write\u0026nbsp;at\u0026nbsp;least\u0026nbsp;120-180\u0026nbsp;words\u0026nbsp;following\u0026nbsp;the\u0026nbsp;outline\u0026nbsp;given\u0026nbsp;below.\u003cp\u003e1．现在流行网上购物。\u003cspan id\u003d\u0022TFMFE506\u0022\u003e\u003c/span\u003e\u003c/p\u003e2．网上购物有很多好处，也有一些问题。\u003cp\u003e3．我的建议。\u003c/p\u003e',
                                batchNumber: '48714381b63592c7df5bd254ff3b06e1',
                            },
                        ],
                        realEachScore: 30.0,
                        level: 2,
                        totalscore: '30',
                        eachQuestionScore: '30',
                        sequencenumber: '六',
                        usedsequence: '六',
                        isSameScore: true,
                        questionTypeId: '9',
                        answerMode: 'EssayQuestion',
                        parentId: 'c80fa7fe-e8d9-4a76-8070-6883dd61171d',
                        sequence: 6,
                        totalquestion: 1,
                        judgementModeCode: '2',
                        name: '作文题',
                        comment: null,
                        id: '095b40c7-2741-45ef-bcfb-e57f937afa96',
                    },
                ],
                number: '2406200851451718844705792',
                score: 100.0,
                name: '大学英语（上）（2020）统考试卷',
                questionCategoryId: '2',
                showQuestionCount: true,
                lastSubmitQuestionTime: 0,
                comprehensive: 0,
                usedCount: 1,
            },
            useTime: 1,
            startTime: '2024-06-22 20:22:20',
            multiCourse: false,
            paperOrder: 0,
            tempSaveAnswer: true,
            viewStructure: true,
            tempSaveAnswerExpire: 72,
            detection: 'false',
            startPaperResponse: false,
            stayInPage: 'false',
            currentStatus: '正在作答',
            needCheckCode: false,
            arrangementName: '2024年春季学期大学英语（上）（2020）期末统考',
            answerPaperReentrantLimit: 0,
            paperLayoutMode: 0,
            photoEndTest: true,
            answerPaperRecordId: '00b0b7a4-99f0-4f34-ade6-837a7c463b2c',
            banCopy: false,
            answerPaperReentrant: 0,
            iframeHtml: 'http://**************:10038/venus/resources/venus/js/app/iframe.html',
            preventCheat: 0,
            takePhotoInMinimumCommitTime: 0,
            delayTime: 0,
            QuestionCountMode: '0',
            photoInPocessTest: 0,
        },
        error: null,
        status: 1,
    };
}

//查看作业试卷 ---（查看考试试题）
// https://exam.chinaedu.net/oxer/page/ots/OTS-UniverDetail.html?AnswerId=${assignmentObj.recordID}
exports.viewPaper = async (cookieStr, recordID) => {
    let config = {
        url: 'https://exam.chinaedu.net/oxer/app/ots/TestActivity/QueryAnswerPaperDetails',
        method: 'post',
        headers: {
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            cookie: cookieStr,
        },
        data: `answerPaperRecordId=${recordID}`,
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        data: {
            achievementType: 1,
            scoreid: '693edb74-eb5e-401c-9722-2599d9d6c44c',
            realpath: '/data/shareNfs/jlu-datafiles/ots/CJBZBE10112',
            achievement: 0,
            arrangementEndTime: '2024-06-30 17:49:00',
            tenant: 'CJBZBE10112',
            activityTypeId: '083c998e-bef0-4a71-92ba-fa826e34828d',
            arrangementid: '588b2b41-95f0-4120-a286-5007c48c61ff',
            iframeJs: 'http://**************:10038/venus/resources/venus/js/app/iframe.js',
            viewJudgeContent: false,
            paperStructures: [
                {
                    sequenceNumber: '1',
                    questionQuantity: 0,
                    realEachScore: null,
                    judgeview: 0,
                    blankLineNum: null,
                    questions: [],
                    scale: null,
                    eachAnswerScore: null,
                    isSameScore: true,
                    questionTypeId: null,
                    usedsequence: '',
                    parentId: null,
                    sequence: 1,
                    score: 0,
                    judgementModeCode: null,
                    name: null,
                    judgementRuleCode: null,
                    comment: null,
                    id: 'bce243e5-a189-4237-b8fd-b9f36c242a34',
                },
                {
                    sequenceNumber: '一',
                    questionQuantity: 20,
                    realEachScore: 2,
                    judgeview: 0,
                    blankLineNum: 0,
                    questions: [
                        {
                            isleave: 0,
                            judgeview: 0,
                            answerScore: 0,
                            difficultyDegree: '中等',
                            subQuestions: [],
                            answerCorrect: false,
                            judgementmode: '1',
                            answerArea: {
                                optionList: [
                                    {
                                        id: '0',
                                        content: '<span> K </span>',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '1',
                                        content: '<span> E </span>',
                                        sequence: 1,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '2',
                                        content: '<span> S </span>',
                                        sequence: 2,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '3',
                                        content: '<span> T </span>',
                                        sequence: 3,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '4',
                                        content: '<span>B</span>',
                                        sequence: 4,
                                        score: 0,
                                        isInput: false,
                                    },
                                ],
                                reorder: false,
                            },
                            score: 2,
                            assistJudgeScore: null,
                            number: 'DX20021215814832046046d03',
                            answerContent: null,
                            judgecontent: null,
                            sequenceinstructure: 1,
                            id: '03f9412d-f41a-49a8-97d9-b24cd0a1678a',
                            solvingProcess: '',
                            sequenceNumber: '1',
                            answerStatus: 2,
                            previousQuestionId: '',
                            usedsequence: '1',
                            answerMode: 'SingleSelection',
                            questionTypeName: '单选题',
                            answer: {
                                id: '1',
                            },
                            assistJudgeComment: null,
                            category: {
                                mainLevelItemName: null,
                                code: '00053001',
                                display: '课程结构',
                                value: '化工仪表及自动化',
                                mainLevelItemCode: null,
                            },
                            stem: '镍铬－康铜热电偶的分度号是（）',
                            actualJudgementMode: 1,
                        },
                    ],
                    scale: null,
                    eachAnswerScore: null,
                    isSameScore: true,
                    questionTypeId: '1',
                    usedsequence: '一',
                    answerMode: 'SingleSelection',
                    parentId: 'bce243e5-a189-4237-b8fd-b9f36c242a34',
                    sequence: 1,
                    score: 40,
                    judgementModeCode: '1',
                    name: '单选题',
                    judgementRuleCode: null,
                    comment: null,
                    id: '8ab1db0c-8d3b-41cb-b30d-84320c77a61e',
                },
                {
                    isleave: 0,
                    judgeview: 0,
                    answerScore: 0,
                    difficultyDegree: '容易',
                    subQuestions: [
                        {
                            solvingProcess: '根据第一段中的for a more ambitious journey and a new record可知，此次飞行时为一次新的计划做准备。',
                            isleave: 0,
                            judgeview: 0,
                            answerScore: 0,
                            difficultyDegree: '容易',
                            answerCorrect: false,
                            judgementmode: '1',
                            answerArea: {
                                optionList: [
                                    {
                                        id: '1',
                                        content: 'test the balloons',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '2',
                                        content: 'launch a house',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '3',
                                        content: 'shoot a hit movie',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '4',
                                        content: 'prepare for breaking a record',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                ],
                                reorder: false,
                            },
                            answerMode: 'SingleSelection',
                            score: 4,
                            questionTypeName: '单选题',
                            assistJudgeScore: null,
                            transferContent: null,
                            answer: {
                                id: '4',
                            },
                            answerContent: null,
                            transferResult: null,
                            sequenceinstructure: 1,
                            judgecontent: null,
                            assistJudgeComment: null,
                            id: 'ed7ad69e-6cb5-4d4e-83ce-bad2e39b188b',
                            stem: 'The adventurer flew across the English Channel to__________．',
                        },
                        {
                            solvingProcess: '根据第四段第二句中的the 2,500-mile journey可知，他要完成2500英里的旅程。',
                            isleave: 0,
                            judgeview: 0,
                            answerScore: 0,
                            difficultyDegree: '容易',
                            answerCorrect: false,
                            judgementmode: '1',
                            answerArea: {
                                optionList: [
                                    {
                                        id: '1',
                                        content: '2500 miles',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '2',
                                        content: '18,000 feet',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '3',
                                        content: '25,000 feet',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '4',
                                        content: '230 miles',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                ],
                                reorder: false,
                            },
                            answerMode: 'SingleSelection',
                            score: 4,
                            questionTypeName: '单选题',
                            assistJudgeScore: null,
                            transferContent: null,
                            answer: {
                                id: '1',
                            },
                            answerContent: null,
                            transferResult: null,
                            sequenceinstructure: 2,
                            judgecontent: null,
                            assistJudgeComment: null,
                            id: '39447adc-cbfd-4397-b871-7ef0e4a39cfc',
                            stem: 'To finish the journey，he will fly a distance of__________．',
                        },
                        {
                            solvingProcess: '文中提到了飞行的时间、距离及其高度，只有飞行要花费的时间没有提及，所以选D。',
                            isleave: 0,
                            judgeview: 0,
                            answerScore: 0,
                            difficultyDegree: '容易',
                            answerCorrect: false,
                            judgementmode: '1',
                            answerArea: {
                                optionList: [
                                    {
                                        id: '1',
                                        content: 'When he will fly',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '2',
                                        content: 'How high he sill fly',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '3',
                                        content: 'How far he will fly',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '4',
                                        content: 'How long it will take him',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                ],
                                reorder: false,
                            },
                            answerMode: 'SingleSelection',
                            score: 4,
                            questionTypeName: '单选题',
                            assistJudgeScore: null,
                            transferContent: null,
                            answer: {
                                id: '4',
                            },
                            answerContent: null,
                            transferResult: null,
                            sequenceinstructure: 3,
                            judgecontent: null,
                            assistJudgeComment: null,
                            id: '7029b041-c3e0-4ef9-b739-65983067930e',
                            stem: 'About the ambitious journey，which is NOT mentioned in the passage?',
                        },
                        {
                            solvingProcess: '根据第二段和倒数第二段可知选C。',
                            isleave: 0,
                            judgeview: 0,
                            answerScore: 0,
                            difficultyDegree: '容易',
                            answerCorrect: false,
                            judgementmode: '1',
                            answerArea: {
                                optionList: [
                                    {
                                        id: '1',
                                        content: 'Two',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '2',
                                        content: 'Three',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '3',
                                        content: 'Four',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '4',
                                        content: 'Five',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                ],
                                reorder: false,
                            },
                            answerMode: 'SingleSelection',
                            score: 4,
                            questionTypeName: '单选题',
                            assistJudgeScore: null,
                            transferContent: null,
                            answer: {
                                id: '3',
                            },
                            answerContent: null,
                            transferResult: null,
                            sequenceinstructure: 4,
                            judgecontent: null,
                            assistJudgeComment: null,
                            id: 'dc128b08-b150-4e08-8f03-c85942e8128e',
                            stem: 'How many world records does Jonathan hold?',
                        },
                        {
                            solvingProcess: '通过最后一段中冒险家的话可知，他不是在梦想，而是在为此实实在在地做准备。也就是说，他一直在筹划他的冒险计划。',
                            isleave: 0,
                            judgeview: 0,
                            answerScore: 0,
                            difficultyDegree: '容易',
                            answerCorrect: false,
                            judgementmode: '1',
                            answerArea: {
                                optionList: [
                                    {
                                        id: '1',
                                        content: 'Trappe can’t sleep worrying about the adventure',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '2',
                                        content: 'Trappe was born to set world records',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '3',
                                        content: 'Trappe always keeps his ambition in mind',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                    {
                                        id: '4',
                                        content: 'Trappe never thought of crossing the Atlantic before',
                                        sequence: 0,
                                        score: 0,
                                        isInput: false,
                                    },
                                ],
                                reorder: false,
                            },
                            answerMode: 'SingleSelection',
                            score: 4,
                            questionTypeName: '单选题',
                            assistJudgeScore: null,
                            transferContent: null,
                            answer: {
                                id: '3',
                            },
                            answerContent: null,
                            transferResult: null,
                            sequenceinstructure: 5,
                            judgecontent: null,
                            assistJudgeComment: null,
                            id: '4cd23b5c-e230-4503-aa72-cc34e707c6f8',
                            stem: 'What does he last paragraph imply?',
                        },
                    ],
                    answerCorrect: false,
                    judgementmode: '2',
                    answerArea: null,
                    score: 20,
                    assistJudgeScore: null,
                    number: 'YDT20092416009288099130e90',
                    transferContent: null,
                    answerContent: null,
                    transferResult: null,
                    judgecontent: null,
                    sequenceinstructure: 1,
                    id: '0c0510b2-e19c-4e15-82c0-1c5a027992a0',
                    solvingProcess: null,
                    sequenceNumber: '31',
                    answerStatus: 0,
                    previousQuestionId: '',
                    usedsequence: '1',
                    answerMode: 'Composite',
                    questionTypeName: '阅读理解',
                    answer: null,
                    assistJudgeComment: null,
                    category: {
                        mainLevelItemName: null,
                        code: '00858001',
                        display: '课程结构',
                        value: '大学英语（下）（2020）',
                        mainLevelItemCode: null,
                    },
                    stem: '&nbsp; &nbsp; &nbsp; Up，Up，and Away！<p>&nbsp; &nbsp; &nbsp; An adventurer who became the first person to fly across the English Channel on a cluster of balloons has launched a house into the sky just like in the hit movie Up-in reparation for a more ambitious journey and a new record．</p>&nbsp; &nbsp; &nbsp; &nbsp;Fearless Trappe，from North Carolina，stepped into the cartoon themed home before flying above the Leon International Balloon Festival in Mexico more than a week ago．<p>&nbsp; &nbsp; &nbsp; The 38-year-old Trappe was using the event as a warm-up for his planned trans-Atlantic flight scheduled for next summer．He aims to complete the 2,500-mile journey in a seven-foot lifeboat carried by 365 huge helium balloons．</p>&nbsp; &nbsp; &nbsp; The brave man is learning to sail a lifeboat，in case he needs to ditch into the ocean during the danger-filled adventure．<p>&nbsp; &nbsp; &nbsp; He still fly at between 18,000 feet and 25,000 feet, beating his previous world altitude record of 21,600 feet, and must fly uninterrupted a distance ten times longer than his previous world record of 230 miles in order to succeed．</p>&nbsp; &nbsp; &nbsp;The adventurer Trappe，who holds records for crossing the Alps，flying the most cluster balloons，and the longest distance，has spent his entire career，building up to this ambitious plan．<p>&nbsp; &nbsp; &nbsp; “I didn’t wake up one day and think:‘I’m going to fly across the Atlantic，’”he said．“Every attempt before this was prepared for this flight, I’ve been training for a long time”.</p>',
                    actualJudgementMode: 1,
                },
            ],
            showQuestionCount: true,
            canViewAnswer: true,
            judgeId: null,
            status: 'marked',
            viewDetail: null,
            expandAnswer: false,
            assistJudge: 0,
            title: '23级化工仪表与自动化 作业1',
            showTotalScore: true,
            showEachScore: true,
            scrollTopHtml: 'http://**************:10038/venus/scrollTo_top.html',
            assistJudgeScore: null,
            timelimitenable: true,
            paperScore: 100,
            useTime: 5400,
            compositeObjectiveView: '0',
            viewStructure: true,
            showTrueAndFalse: '0',
            stayInPage: null,
            judgeUserName: null,
            judgeRealName: null,
            display: 'category,solvingprocess,answer,score',
            quessubmittime: '2024-05-31 13:13:47',
            calculateJudgeTime: false,
            autoFillScore: 0,
            paperLayoutMode: 0,
            quesResponseMode: 'answerResults,solution,analysis,totalScore,scoreStatByLevels,scoreByQuestion',
            realname: '周晓峰',
            questionJudgeContent: 'true',
            judgeContent: '',
            viewCorrection: 'true',
            answerPaperRecordId: 'e08610f7-de16-4b15-bcd0-4bb8924321a0',
            recordStatus: '试卷已评阅',
            arrangementname: '23级化工仪表与自动化 作业1',
            scorePercent: 0,
            iframeHtml: 'http://**************:10038/venus/resources/venus/js/app/iframe.html',
            comprehensive: false,
            quesstarttime: '2024-05-31 11:43:25',
            username: '2023060163',
            studentInfoDisabled: 'false',
        },
        error: null,
        status: 1,
    };
}

// 人脸识别
exports.faceCompare = async (pageCookieStr, data) => {
    let result = querystring.stringify(data);
    let config = {
        url: 'https://exam.chinaedu.net/oxer/app/ots/Public/faceCompare',
        method: 'post',
        headers: {
            cookie: pageCookieStr,
            // "content-type": "application/json",
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        },
        data: result,
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        data: {
            photoBeginTimes: 1,
            isPassed: true,
            autoPhotoTimes: 3,
            photoBeginTest: false,
            faceCorpuscleInTest: false,
            type: '1',
            photoBeginAboveThreeTimes: false,
            faceImage: 'https://otsstatic.chinaedu.net/ots/CJBZBE10094/faceImage/4a/db/4adb20bf-b1b9-4c58-b916-b8b8775093af/20240622202245.png',
            nowTime: '2024-06-22 20:22:45',
            takePhotoInTest: true,
            photoEndTimes: 0,
            photoEndTest: true,
            useTime: 0,
            delayTime: 0,
            timeLimitEnabled: true,
            testEndTime: '2024-06-30 21:00:00',
            answerPaperId: '00b0b7a4-99f0-4f34-ade6-837a7c463b2c',
            isReset: 1,
            paperTime: 150,
            photoInPocessTest: 0,
        },
        error: null,
        status: 1,
    };
}

// 保存考试

/**
 * ======================= 一、课程API =======================
 *
 * 1.获取课程列表
 */

//1.获取课程列表
exports.getCourseList = async globalStore => {
    let courseListRes = await axiosIns({
        url: 'https://czzycj.sccchina.net/student/student/coursestudy/getlist',
        method: 'post',
        headers: {
            'content-type': 'application/json',
            cookie: globalStore.cookieStr,
        },
        data: '{"data":"aggregation"}',
    });
    return courseListRes;
};
if (false) {
    let getCourseListRes = {
        items: [
            {
                bussinessType: 1,
                bussinessTypeName: '成教',
                bussinessCaseId: '1754263628863767563',
                bussinessCaseName: '成教专科应用化工技术',
                courseVersionID: '1655866475903189001',
                imagePath: 'https://czzycj.sccchina.net/web-public-file/10112/courseversion/1660017808396779528/416f9a5f81e14c00885d3164e6c621e3.jpg',
                versionName: '化工仪表及自动化', //课程名称
                versionCode: 'dz0330',
                credit: '1.0',
                semesterName: '三',
                courseGrade: null,
                teacherName: '汪蓓蓓',
                status: 1, //状态，1表示完成 2表示不可看
                courseUrl: '00623',
                forumUrl: null,
                boardsID: '89',
                userName: '2023060163',
                score: null,
                examType: null,
                teacherID: '1694467890787385354',
                resultType: 4,
                realCoursewarePlayNum: ' 1 / -- ', // 观看次数
                realCoursewarePlayTime: ' 1181.6 / 700 ', // 已看分钟 / 总分钟
                realPostsNum: ' 0 / -- ',
                semesterId: '1721101681988468809',
                opicDiscussionNum: '0 / -- ',
                questionnaireNum: '0 / -- ',
                downloadResourcesNum: ' 0 / -- ',
                textResourcesNum: ' 0 / -- ',
                upToStandard: '--',
                mainpostsnum: 0,
                replynum: 0,
                buyVersionCode: '00623',
                teachplanCourseVersionId: '1754170487752140810',
                sign: '848501AD2133C9A9C38902B23D47E14F',
                teachplanId: '1754170487652488202',
                stationId: '1655866524047507465',
                positionType: '3',
                courseScopeStatus: '1',
                degreeCourseId: null,
                degreeCourseFlag: 0,
                userAccountId: '1754263628863767563',
                userId: null,
                relativeSemesterValue: 3,
                eleBookCode: '',
                studentId: '1754172689633797131',
                clickOrNot: '1',
                studentFileStatus: 1,
                eleBookUseFlag: true,
                coursewareUseFlag: true,
                bbsUseFlag: true,
                homeWorkUseFlag: true,
                documentUseFlag: true,
                eleBookDisplayFlag: true,
                coursewareDisplayFlag: true,
                bbsDisplayFlag: true,
                homeWorkDisplayFlag: true,
                documentDisplayFlag: true,
                entireTestScoreOrNot: null,
                showScore: null,
                startTime: *************, //课程开始时间
                endTime: *************, //课程结束时间
                currentSemester: 1,
                httpsDomain: null,
                httpsToken: null,
                courseName: '化工仪表及自动化',
                eleBoookStatus: 0,
                eleMsg: '',
                goodsId: null,
                coursewareLearningScore: '40',
                studyScore: null,
                totalDuration: '1637.92',
                coursewareLearningProgress: '100%',
                auditStatus: '0',
                watchCoursePopupFlag: 0,
                watchCoursePopupMsgCode: '0',
                teachingMode: null,
                isShowTeachingMode: '0',
                lmsSwitch: 0,
                practiceUseFlag: true,
                practiceDisplayFlag: true,
                examStatus: null,
            },
            {
                bussinessType: 1,
                bussinessTypeName: '成教',
                bussinessCaseId: '1754263628863767563',
                bussinessCaseName: '成教专科应用化工技术',
                courseVersionID: '1655866474908090377',
                imagePath: 'https://czzycj.sccchina.net/web-public-file/10112/courseversion/1660017808396779528/85d207e6809d490ba8e69b6e7635a9e6.jpg',
                versionName: '高等数学',
                versionCode: 'dz0105',
                credit: '1.0',
                semesterName: '一',
                courseGrade: null,
                teacherName: null,
                status: 2,
                courseUrl: '00663',
                forumUrl: null,
                boardsID: '41',
                userName: '2023060163',
                score: '100',
                examType: '1',
                teacherID: null,
                resultType: 4,
                realCoursewarePlayNum: ' 0 / -- ',
                realCoursewarePlayTime: ' 0.0 / -- ',
                realPostsNum: ' 0 / -- ',
                semesterId: '1688208721866391559',
                opicDiscussionNum: '0 / -- ',
                questionnaireNum: '0 / -- ',
                downloadResourcesNum: ' 0 / -- ',
                textResourcesNum: ' 0 / -- ',
                upToStandard: '--',
                mainpostsnum: 0,
                replynum: 0,
                buyVersionCode: '00663',
                teachplanCourseVersionId: '1754170487752132618',
                sign: '725B3153737FEFB1E3915542F6F88117',
                teachplanId: '1754170487652488202',
                stationId: '1655866524047507465',
                positionType: '3',
                courseScopeStatus: '1',
                degreeCourseId: null,
                degreeCourseFlag: 0,
                userAccountId: '1754263628863767563',
                userId: null,
                relativeSemesterValue: 1,
                eleBookCode: '',
                studentId: '1754172689633797131',
                clickOrNot: '1',
                studentFileStatus: 1,
                eleBookUseFlag: false,
                coursewareUseFlag: true,
                bbsUseFlag: false,
                homeWorkUseFlag: false,
                documentUseFlag: false,
                eleBookDisplayFlag: true,
                coursewareDisplayFlag: true,
                bbsDisplayFlag: true,
                homeWorkDisplayFlag: true,
                documentDisplayFlag: true,
                entireTestScoreOrNot: '0',
                showScore: '100',
                startTime: *************,
                endTime: *************,
                currentSemester: 0,
                httpsDomain: null,
                httpsToken: null,
                courseName: '高等数学',
                eleBoookStatus: 0,
                eleMsg: '',
                goodsId: null,
                coursewareLearningScore: '0',
                studyScore: null,
                totalDuration: '1523.2',
                coursewareLearningProgress: '--',
                auditStatus: '0',
                watchCoursePopupFlag: 0,
                watchCoursePopupMsgCode: '0',
                teachingMode: null,
                isShowTeachingMode: '0',
                lmsSwitch: 0,
                practiceUseFlag: false,
                practiceDisplayFlag: true,
                examStatus: '1',
            },
        ],
        pageSize: 0,
        totalPage: 1,
        totalCount: 0,
        currentPage: 0,
    };
}

// 进一步获取课程信息，url,token
exports.getcoursewareaddress = async (globalStore, courseObj) => {
    let data = {
        data: {
            roleType: '1',
            teachplanCourseVersionId: courseObj.teachplanCourseVersionId,
            courseVersionId: courseObj.courseVersionID,
            sign: courseObj.sign,
        },
    };
    let config = {
        url: 'https://czzycj.sccchina.net/student/common/common/getcoursewareaddress',
        method: 'post',
        headers: {
            cookie: globalStore.cookieStr,
            'content-type': 'application/json',
        },
        data: JSON.stringify(data),
    };
    let coursewareaddressRes = await axiosIns(config);
    return coursewareaddressRes;
};
if (false) {
    let res = {
        code: 1,
        message: '',
        data: {
            coursewareUrl:
                'https://fjshare.chinaedu.net/course/courseWare.do?orgCode=010051&courseCode=00623&userId=E89762BB574CF7BAD7A1CB1E0849C3DC&timestamp=1715828990202&sign=6ECBB06F2D62E9254D333F2276B800EC&connectionPlayKey=1754263628863767563_1655866475903189001&teachingVersion=1655866475903189001&protocolType=https&centerId=ffb9ce6f96904d02ad55e7b7c048454c&sourceType=1&learnSessionId=958eec99bddb4359bf9d9eab21f2700b',
            lmsSwitch: '0',
            encode: '1',
            thirdType: '0',
            token: '********************************|3e4433b10d0bd30399fc75f342c664c4',
        },
        extendedData: null,
        tag: null,
        errorCode: 0,
    };
}

//刷新视频进度
exports.addDuration = async (globalStore, courseObj) => {
    let body = {
        data: {
            courseVersionId: courseObj.courseVersionID,
            studyDuration: 30,
            token: courseObj.token,
        },
    };
    body = JSON.stringify(body);
    let config = {
        url: 'https://czzycj.sccchina.net/student/student/coursestudyrecord/adddurationpc',
        method: 'post',
        headers: {
            'content-type': 'application/json',
            cookie: globalStore.cookieStr,
        },
        data: body,
    };
    let addDurationRes = axiosIns(config);
    return addDurationRes;
};

// 获取作业列表
exports.getAssignmentList = async (cookieStr, courseVersionID) => {
    let assignmentListRes = await axiosIns({
        url: 'https://czzycj.sccchina.net/student/student/otsresult/getlist',
        method: 'post',
        headers: {
            'content-type': 'application/json',
            cookie: cookieStr,
        },
        data: `{"data":${courseVersionID}}`,
    });
    return assignmentListRes;
};
// 成功响应
if (false) {
    let obj = {
        items: [
            //已答题1次，分数0分
            {
                id: '1799012525041254505', //作业id 不变
                extendObject: {},
                activityName: '23级化工仪表与自动化 作业1',
                activityTypeName: '在线作业',
                answerTime: '0',
                answerCount: '1', //答题次数
                submitTime: '2024-05-14 15:33:59',
                markingStatus: '1',
                score: '0.00', //分数
                otsUrl: null,
                activityID: '588b2b41-95f0-4120-a286-5007c48c61ff', //作业id 不变
                courseVersionID: '1655866475903189001', //课程id
                answer: '1/0', //答题次数 已经提交次数/次数限制
                versionCode: 'dz0330',
                startTime: '2024-05-01 17:49:00', // 作业开始时间
                endTime: '2024-06-30 17:49:00', //作业截至时间
                recordID: 'bf52321e-c87c-4c19-8763-7e9041ec5b19', //记录id ，每次都不一样
                checkStatus: 1,
                isExpired: 0,
                studentId: '1754172689633797131',
            },
            // 答题0次，分数0分
            {
                id: null,
                extendObject: {},
                activityName: '23级化工仪表与自动化 作业3',
                activityTypeName: '在线作业',
                answerTime: '0',
                answerCount: null,
                submitTime: '',
                markingStatus: null,
                score: null,
                otsUrl: null,
                activityID: '59b1aabe-2be5-47f3-b5a8-2d800c5379f7',
                courseVersionID: '1655866475903189001',
                answer: '0/0',
                versionCode: 'dz0330',
                startTime: '2024-05-01 17:55:00',
                endTime: '2024-06-30 17:55:00',
                recordID: null,
                checkStatus: 1,
                isExpired: 0,
                studentId: '1754172689633797131',
            },
        ],
        pageSize: 20,
        totalPage: 1,
        totalCount: 12,
        currentPage: 1,
    };
}

//获取作业url
exports.getotsurl = async (cookieStr, assignmentObj) => {
    let config = {
        url: 'https://czzycj.sccchina.net/student/student/otsresult/getotsurl',
        method: 'post',
        headers: {
            cookie: cookieStr,
            'content-type': 'application/json',
        },
        data: `{"data":{"activityID":"${assignmentObj.activityID}","versionCode":"${assignmentObj.versionCode}"}}`,
    };
    let getotsurlRes = await axiosIns(config);
    return getotsurlRes;
};
//成功响应
if (false) {
    let obj = {
        code: 1,
        message:
            'https://exam.chinaedu.net/oxer/auth/ots?sso=checkmd5&userId=2023060163&realName=%E5%91%A8%E6%99%93%E5%B3%B0&tenant=CJBZBE10112&role=student&action=startTestNormal&timestamp=1715938530620&st=61d8157fe37c60d84b8e702e0f529d92&codes=dz0330&arrangementId=588b2b41-95f0-4120-a286-5007c48c61ff&resourcePackageId=1754172689633797131',
        data: 'https://exam.chinaedu.net/oxer/auth/ots?sso=checkmd5&userId=2023060163&realName=%E5%91%A8%E6%99%93%E5%B3%B0&tenant=CJBZBE10112&role=student&action=startTestNormal&timestamp=1715938530620&st=61d8157fe37c60d84b8e702e0f529d92&codes=dz0330&arrangementId=588b2b41-95f0-4120-a286-5007c48c61ff&resourcePackageId=1754172689633797131',
        extendedData: null,
        tag: null,
        errorCode: 0,
    };
}

// 保存考试
exports.saveAssignment = async (cookieStr, data) => {
    // content必须是一个字符串
    function stringifyContent(obj) {
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                if (key === 'content' && typeof obj[key] === 'object' && obj[key] !== null) {
                    // 如果是 content 属性并且值是对象，进行 JSON.stringify
                    obj[key] = JSON.stringify(obj[key]);
                } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                    // 如果是对象或数组，递归调用
                    stringifyContent(obj[key]);
                }
            }
        }
        return obj;
    }
    data = stringifyContent(data);
    data.paperAnswerResult = JSON.stringify(data.paperAnswerResult);
    let str = qs.stringify(data, { encode: true });

    let result = querystring.stringify(data);
    let config = {
        url: 'https://exam.chinaedu.net/oxer/app/ots/TestActivity/TempSaveAnswerPaper',
        method: 'post',
        headers: {
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
            cookie: cookieStr,
        },
        data: str,
    };
    let res = await axiosIns(config);
    return res;
};

//提交作业 （提交考试）
exports.submitAssignment = async (cookieStr, data) => {
    // content必须是一个字符串
    function stringifyContent(obj) {
        for (let key in obj) {
            if (obj.hasOwnProperty(key)) {
                if (key === 'content' && typeof obj[key] === 'object' && obj[key] !== null) {
                    // 如果是 content 属性并且值是对象，进行 JSON.stringify
                    obj[key] = JSON.stringify(obj[key]);
                } else if (typeof obj[key] === 'object' && obj[key] !== null) {
                    // 如果是对象或数组，递归调用
                    stringifyContent(obj[key]);
                }
            }
        }
        return obj;
    }
    data = stringifyContent(data);
    data.paperAnswerResult = JSON.stringify(data.paperAnswerResult);
    let str = qs.stringify(data, { encode: true });
    // console.log('str',str)
    let config = {
        url: 'https://exam.chinaedu.net/oxer/app/ots/TestActivity/SubmitAnswerPaper',
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded; charset=UTF-8',
            cookie: cookieStr,
        },
        data: str,
    };
    let res = await axiosIns(config);
    // console.log('res',res)
    return res;
};
if (false) {
    res = {
        data: {
            achievementType: 1,
            alreadySubmitted: false,
            finalScore: 8,
            resourcePackageId: null,
            returnAchievement: true,
            currentScore: 0,
            userTime: 3310,
            resourceFlag: null,
            calculateJudgeTime: false,
            answerPaperRecordId: '84d225b8-0942-42f8-812c-550cdfb3164d',
            sourceIp: '*************',
            multiScoreCac: 1,
            success: 'true',
            validSubmit: true,
            useDeviceType: '',
        },
        error: null,
        status: 1,
    };
}

/**
 * ======================= 一、考试API =======================
 *
 * 1.获取课程列表
 */

// 获取考试列表
exports.getExamList = async cookie => {
    let res = await axiosIns({
        url: 'https://czzycj.sccchina.net/student/student/onlineexamactivity/getlist',
        method: 'post',
        headers: {
            'content-type': 'application/json',
            cookie,
        },
        //-1表示所有学期
        data: '{"data":"-1"}',
    });
    return res;
};
if (false) {
    res = {
        items: [
            {
                id: '7230d35b-31a6-4516-a511-860fd52d9239',
                extendObject: {},
                activityName: '2022级大专《会计学原理》毕业总补考',
                paperForm: '1',
                startTime: 1716940800000,
                endTime: 1717516799000,
                effectiveTime: '2024/05/29 08:00:00 - 2024/06/04 23:59:59',
                answerTime: '3',
                answerCount: '1/3',
                submitTime: '2024-05-29 08:13:20',
                markingStatus: '1',
                beginDate: 1716940800000,
                endDate: 1717516799000,
                score: '97.00',
                examBatchId: '1799194785254211715',
                activityID: '7230d35b-31a6-4516-a511-860fd52d9239',
                versionCode: 'dz0209',
                recordId: 'e4f9cfc8-5c4e-4bf2-bd17-e1c0781f0ded',
                startTestNormalUrl: 'startTestNormal',
                answerDetailUrl: 'AnswerDetail',
                publishStatus: null,
                courseVersionID: '1655866475152408585',
                canTest: '1',
                status: '0',
                signstatus: '0',
                examStatus: '1',
                onlineExamCache: 0,
                responseTime: '2024-05-29 08:08:48',
                studentId: '1721186722971144202',
                paperTime: '90',
                isFaceRecognition: '0',
                onlineexamMode: '0',
                batchName: '2022级第一次毕业总补考',
                key: null,
            },
            {
                id: '10bd07b7-c1ba-4f11-b004-77fc37e393b5',
                extendObject: {},
                activityName: '2022级大专《全国成人高等专科英语(2)》毕业总补考',
                paperForm: '1',
                startTime: 1716940800000,
                endTime: 1717516799000,
                effectiveTime: '2024/05/29 08:00:00 - 2024/06/04 23:59:59',
                answerTime: '3',
                answerCount: '1/3',
                submitTime: '2024-05-29 08:16:44',
                markingStatus: '1',
                beginDate: 1716940800000,
                endDate: 1717516799000,
                score: '99.00',
                examBatchId: '1799194785254211715',
                activityID: '10bd07b7-c1ba-4f11-b004-77fc37e393b5',
                versionCode: 'dz0204',
                recordId: '26885b41-7b42-4187-a199-34a1fd3c055b',
                startTestNormalUrl: 'startTestNormal',
                answerDetailUrl: 'AnswerDetail',
                publishStatus: null,
                courseVersionID: '1655866475011899401',
                canTest: '1',
                status: '0',
                signstatus: '0',
                examStatus: '1',
                onlineExamCache: 0,
                responseTime: '2024-05-29 08:09:00',
                studentId: '1721186722971144202',
                paperTime: '90',
                isFaceRecognition: '0',
                onlineexamMode: '0',
                batchName: '2022级第一次毕业总补考',
                key: null,
            },
            {
                id: '25abd3ac-fad0-446c-9a5d-185dfa7293b7',
                extendObject: {},
                activityName: '2022级大专《财政与金融》毕业总补考',
                paperForm: '1',
                startTime: 1716940800000,
                endTime: 1717516799000,
                effectiveTime: '2024/05/29 08:00:00 - 2024/06/04 23:59:59',
                answerTime: '3',
                answerCount: '1/3',
                submitTime: '2024-05-29 08:07:49',
                markingStatus: '1',
                beginDate: 1716940800000,
                endDate: 1717516799000,
                score: '89.00',
                examBatchId: '1799194785254211715',
                activityID: '25abd3ac-fad0-446c-9a5d-185dfa7293b7',
                versionCode: 'dz0307',
                recordId: '5e6d5027-4970-440f-989d-7e802739bff6',
                startTestNormalUrl: 'startTestNormal',
                answerDetailUrl: 'AnswerDetail',
                publishStatus: null,
                courseVersionID: '1655866475432378377',
                canTest: '1',
                status: '0',
                signstatus: '0',
                examStatus: '1',
                onlineExamCache: 0,
                responseTime: '2024-05-29 08:02:15',
                studentId: '1721186722971144202',
                paperTime: '90',
                isFaceRecognition: '0',
                onlineexamMode: '0',
                batchName: '2022级第一次毕业总补考',
                key: null,
            },
            {
                id: '3cbadc6c-b66e-407a-a832-bc6afb3f3c4f',
                extendObject: {},
                activityName: '2022级大专《毛泽东思想和中国特色社会主义理论体系概论》毕业总补考',
                paperForm: '1',
                startTime: 1716940800000,
                endTime: 1717516799000,
                effectiveTime: '2024/05/29 08:00:00 - 2024/06/04 23:59:59',
                answerTime: '3',
                answerCount: '1/3',
                submitTime: '2024-05-29 08:12:45',
                markingStatus: '1',
                beginDate: 1716940800000,
                endDate: 1717516799000,
                score: '97.00',
                examBatchId: '1799194785254211715',
                activityID: '3cbadc6c-b66e-407a-a832-bc6afb3f3c4f',
                versionCode: 'dz0203',
                recordId: '6ce9a376-5021-498b-bcba-59b46454a723',
                startTestNormalUrl: 'startTestNormal',
                answerDetailUrl: 'AnswerDetail',
                publishStatus: null,
                courseVersionID: '1655866474991976457',
                canTest: '1',
                status: '0',
                signstatus: '0',
                examStatus: '1',
                onlineExamCache: 0,
                responseTime: '2024-05-29 08:07:57',
                studentId: '1721186722971144202',
                paperTime: '90',
                isFaceRecognition: '0',
                onlineexamMode: '0',
                batchName: '2022级第一次毕业总补考',
                key: null,
            },
        ],
        pageSize: 20,
        totalPage: 1,
        totalCount: 4,
        currentPage: 1,
    };
}

// 获取考试url
exports.getExamUrl = async (cookie, courseObj) => {
    let config = {
        url: 'https://czzycj.sccchina.net/student/student/onlineexamactivity/getsurl',
        method: 'post',
        headers: {
            cookie,
            'content-type': 'application/json',
        },
        data: `{"data":{"versionCode":"${courseObj.versionCode}","activityID":"${courseObj.activityID}","recordId":"${courseObj.recordId}","answerDetailUrl":"${courseObj.answerDetailUrl}","startTestNormalUrl":"${courseObj.startTestNormalUrl}","studentId":"${courseObj.studentId}","examBatchId":"${courseObj.examBatchId}"}}`,
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        code: 1,
        message:
            'https://exam.chinaedu.net/oxer/auth/ots?sso=checkmd5&userId=2022021719&realName=%E9%BB%84%E5%A8%9F&tenant=CJBZBE10112&role=student&action=startTestNormal&timestamp=1717316020279&st=0e40c9d462b5a1a3fcad10ef67d523e8&codes=dz0307&arrangementId=25abd3ac-fad0-446c-9a5d-185dfa7293b7&AnswerId=5e6d5027-4970-440f-989d-7e802739bff6&resourcePackageId=1721186722971144202',
        data: 'https://exam.chinaedu.net/oxer/auth/ots?sso=checkmd5&userId=2022021719&realName=%E9%BB%84%E5%A8%9F&tenant=CJBZBE10112&role=student&action=startTestNormal&timestamp=1717316020279&st=0e40c9d462b5a1a3fcad10ef67d523e8&codes=dz0307&arrangementId=25abd3ac-fad0-446c-9a5d-185dfa7293b7&AnswerId=5e6d5027-4970-440f-989d-7e802739bff6&resourcePackageId=1721186722971144202',
        extendedData: null,
        tag: null,
        errorCode: 0,
    };
}

// 获取考试结束后的url
exports.getExamEndUrl = async (cookie, courseObj, answerPaperRecordId) => {
    let config = {
        url: 'https://czzycj.sccchina.net/student/student/onlineexamactivity/getaurl',
        method: 'post',
        headers: {
            cookie,
            'content-type': 'application/json',
        },
        data: `{"data":{"versionCode":"${courseObj.versionCode}","activityID":"${courseObj.activityID}","recordId":"${answerPaperRecordId}","answerDetailUrl":"startTestNormalUrl","startTestNormalUrl":"startTestNormal","studentId":"${courseObj.studentId}"}}`,
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        code: 1,
        message:
            'https://exam.chinaedu.net/oxer/auth/ots?sso=checkmd5&userId=24002486&realName=%E9%82%B5%E8%90%A5%E7%9B%88&tenant=CJBZBE10151&role=student&action=AnswerDetail&timestamp=1727052527731&st=3ec867fb32c75f319a62089ff1d0ff14&codes=k581&arrangementId=0e78a3b0-2254-4192-884d-31655141daf5&AnswerId=b448d85d-7b11-49e5-9b18-d0569e2303e5&resourcePackageId=1787033972595778571',
        data: 'https://exam.chinaedu.net/oxer/auth/ots?sso=checkmd5&userId=24002486&realName=%E9%82%B5%E8%90%A5%E7%9B%88&tenant=CJBZBE10151&role=student&action=AnswerDetail&timestamp=1727052527731&st=3ec867fb32c75f319a62089ff1d0ff14&codes=k581&arrangementId=0e78a3b0-2254-4192-884d-31655141daf5&AnswerId=b448d85d-7b11-49e5-9b18-d0569e2303e5&resourcePackageId=1787033972595778571',
        extendedData: null,
        tag: null,
        errorCode: 0,
    };
}
