const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('gjkfdx_experiment', {
    id: {
      type: DataTypes.STRING(255),
      allowNull: false,
      primaryKey: true
    },
    course_name: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    task_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "也是taskObj的id，也就是课件id"
    },
    task_name: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    experiment_name: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "一个课件可以有多个实验"
    },
    app_key: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    task_steps: {
      type: DataTypes.JSON,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'gjkfdx_experiment',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
};
