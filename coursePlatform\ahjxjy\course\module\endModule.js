let api = require('../../utils/api.js');

async function endModule(infoLogger, globalStore, taskObj) {
    let finalResult = [];
    let courseList;
    //重新获取课程列表
    try {
        courseList = await api.getCourseList(globalStore, taskObj);
    } catch (error) {
        await infoLogger('获取课程列表出错', 'red'); //记录日志
        throw error; //抛出错误，结束进程池
    }

    // 如果有课程名筛选条件，就筛选课程
    if (taskObj.coursename) {
        courseList = courseList.filter(item => item.courseName.includes(taskObj.coursename));
    }

    //获取进度
    for (let i = 0; i < courseList.length; i++) {
        let courseObj = courseList[i];
        // let coursePercent = ((courseObj.completed * 1) / courseObj.totalCount) * 1;
        finalResult.push({
            courseName: courseObj.courseName,
            // progress: Math.floor(coursePercent * 100),
            progress:courseObj.schedule,
        });
    }

    //返回数据
    return {
        finalResult,
        warningMessage: globalStore.warningMessage,
    };
}

module.exports = endModule;
