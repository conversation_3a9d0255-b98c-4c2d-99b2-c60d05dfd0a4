let Model = require('../../../../../config/sequelize.config.js');
let { v4 } = require('uuid');
async function collectionQuestion(questionList,courseName) {
    let saveCount = 0;
    for (questionObj of questionList) {
        if (!questionObj.answers) {
            await infoLogger(`${assignmentLogStr}，没有答案，${questionObj.content}`);
            continue;
        }

        let answersContent = [];
        switch (questionObj.type) {
            case '填空题': {
                answersContent.push(questionObj.answers);
                break;
            }
            case '判断题': {
                answersContent.push(questionObj.answers);
                questionObj.answers = questionObj.answers == '对' ? 'A' : 'B';
                break;
            }
            case '单选题':
            case '多选题': {
                for (let str of questionObj.answers) {
                    let ansIndex = { A: 0, B: 1, C: 2, D: 3, E: 4, F: 5, G: 6, H: 7 }[str];
                    let queOption = questionObj.options[ansIndex];
                    // let [queIndex, queValue] = queOption.split(':');
                    let [queIndex, queValue] = queOption.split(/:(.+)/);
                    answersContent.push(queValue);
                    answersContent = [...answersContent].sort((x, y) => x.localeCompare(y, 'zh-Hans-CN'));
                }
                break;
            }
            default: {
                questionObj.answers = questionObj.answers.slice(0, 3000).replaceAll('答案要点','答：')
                answersContent.push(questionObj.answers);
                break;
            }
        }

        let existAnswer = await Model.bank.findOne({
            where: {
                content: questionObj.content,
                type: questionObj.type,
                answers_content: JSON.stringify(answersContent),
            },
        });

        // 如果题库中存在的题目，不是继续教育，柠檬文采，就重新收集
        if (!existAnswer) {
            await Model.bank.create({
                id: v4(),
                content: questionObj.content,
                options: JSON.stringify(questionObj.options),
                type: questionObj.type,
                answers: questionObj.answers,
                answers_content: JSON.stringify(answersContent),
                course_name: courseName,
                platform: '柠檬文采学堂',
                add_time: new Date(),
            });
            saveCount++;
        }
        // await new Promise((r) => setTimeout(r, ********));
    }

    return saveCount;
}

module.exports = collectionQuestion;
