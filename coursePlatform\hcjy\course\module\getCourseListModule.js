let api = require('../../utils/api');
async function getCourseListModule(taskObj, mainPage, globalStore, infoLogger) {
    //1.从服务器获取原始课程列表数据
    let courseListRes = await api.getCourseList(globalStore);
    let courseList = courseListRes.items;

    courseList = courseList.filter(item => item.status == 1);

    //2.获取课程对象的其他信息，例如token，url，已完成时间，总共时间等，为了刷时长做准备
    let baseUrl = mainPage.url();

    for (let i = 0; i < courseList.length; i++) {
        let courseObj = courseList[i];
        //获取课程已播放时间，总共时间，剩余事件 " 5.0 / 700 " "0.0 / -- "
        let realCoursewarePlayTime = courseObj.realCoursewarePlayTime.replace(/\s/g, '');
        let [playingTime, totalTime] = realCoursewarePlayTime.split('/').map(item => item * 1);
        totalTime += 20;

        //给无效课程添加标记
        if (isNaN(totalTime)) {
            courseObj.isInvalid = true;
            continue;
        }

        //1.已经播放时间
        courseObj.playingTime = playingTime;
        //2.总共时间
        courseObj.totalTime = totalTime;
        //3.剩余时间
        courseObj.timeLeft = (totalTime - playingTime).toFixed(1);

        //获取课程的url和token
        let coursewareaddressRes = await api.getcoursewareaddress(globalStore, courseObj);

        // 这里失败的返回值是我猜的
        if (coursewareaddressRes.code == 1) {
            courseObj.coursewareUrl = coursewareaddressRes.data.coursewareUrl;
            courseObj.token = coursewareaddressRes.data.token;
        } else {
            courseObj.isInvalid = true;
            continue;
        }

        //获取课程的作业列表
        let AssignmentListRes = await api.getAssignmentList(globalStore.cookieStr, courseObj.courseVersionID);
        courseObj.assignmentList = AssignmentListRes.items;
    }

    courseList = courseList.filter(item => !item.isInvalid);

    // 过滤课程
    if (taskObj.coursename) {
        courseList = courseList.filter(item => item.courseName.includes(taskObj.coursename));
    }

    if (courseList.length === 0) {
        await infoLogger('有效课程列表为空', 'red');
        throw new Error('有效课程列表为空');
    } else {
        await infoLogger(`获取课程列表成功，共${courseList.length}门课程`);
    }

    return courseList;

    //获取作业列表

    // //3.收集作业url列表
    // let assignmentList = [];
    // //点击所有课程的作业列表，弘成的作业url是动态生成的
    // await mainPage.evaluate(() => {
    //     let assignmentList = document.querySelectorAll(
    //         "#content > div > div.cource-list.clear-after > div.in-courcelist-b > div > div.in-c-main.clear-after > div.in-c-el-right > div.in-c-bt-box > a.in-c-link-bt.work-bt"
    //     );
    //     assignmentList.forEach((assignmentButton) => assignmentButton.click());
    // });
    // //获取所有“做作业”按钮
    // await new Promise((r) => setTimeout(r, 2000));
    // let openworkList = await mainPage.$$("div> table > tbody > tr > td:nth-child(8) > a.openwork");
    // for (let i = 0; i < openworkList.length; i++) {
    //     let openworkHandle = openworkList[i];
    //     // 监听新页面的打开
    //     let newPagePromise = new Promise((x) => browser.once("targetcreated", (target) => x(target.page())));
    //     //点击 做作业 按钮，会打开一个新的页面
    //     await openworkHandle.evaluate((el) => el.click());
    //     //获取新页面实例
    //     let newPage = await newPagePromise;
    //     await new Promise((r) => setTimeout(r, 2000));
    //     // 收集作业页面的url
    //     let assignmentUrl = newPage.url();
    //     assignmentList.push(assignmentUrl);
    //     //关闭作业页面
    //     await newPage.close();
    // }
}

module.exports = getCourseListModule;
