// ========= 私有模块 =========
let pageTools = require('../../utils/pageTools.js');
let getMainPage = require('../../utils/getMainPage.js');

// 考试模块
let signinModule = require('../course/module/signinModule.js');
let getCourseListModule = require('./module/getCourseListModule.js');
let runExamModule = require('./module/runExamModule.js');
let endModule = require('./module/endModule.js');

// ========= 任务开始 ====  =====
async function ahjxjyExam(taskObj, taskOptions) {
    // taskOptions.isHeadless = false;
    let infoLogger = pageTools.getExamInfoLogger(taskObj.id, false);
    let globalStore = { taskObj: taskObj };
    let { mainPage, browser } = await getMainPage(taskOptions);

    try {
        // 一、登录
        await signinModule(infoLogger, mainPage, globalStore, taskObj);

        if (!process.env.HAS_PERMISSION) {
            return;
        }

        // 二、获取课程列表
        let courseList = await getCourseListModule(infoLogger, globalStore, taskObj);
        if (courseList.length == 0) {
            await infoLogger('课程数量为0', 'red');
            await browser.close();
            browser = null;
            return {
                finalResult: [{ courseName: '课程数量为0', progress: 0 }],
                warningMessage: '',
            };
        }

        if (!taskObj.others.includes('跳过考试')) {
            // 三、开始考试
            await runExamModule(infoLogger, globalStore, courseList);
        }

        // 五、收尾工作
        let finalResult = await endModule(infoLogger, globalStore, taskObj);
        await browser.close();
        return { finalResult, warningMessage: '' };
    } catch (error) {
        await browser.close();
        browser = null;
        throw error;
    } finally {
        if (browser) {
            await browser.close();
            browser = null;
        }
    }
}

module.exports = ahjxjyExam;

// 单元测试
if (false) {
    //测试用
    let taskOptions = {
        isVideoEnabled: true, //视频
        isAssignmentEnabled: true, //作业
        isHeadless: 'new', //浏览器模式 false ,'new'
    };
    let taskObj = {
        username: '341124199205055424',
        password: '055424',
        runCourse: true,
        runAssignment: true,
        schoolname: '安徽国际商务职业学院',
    };

    ahjxjyExam(taskObj, taskOptions).then(
        val => console.log(val),
        err => {
            console.log(err);
        }
    );
}