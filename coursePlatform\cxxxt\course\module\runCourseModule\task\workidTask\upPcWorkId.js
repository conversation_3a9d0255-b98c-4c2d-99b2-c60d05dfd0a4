let Model = require("../../../../../../../config/sequelize.config.js");
let { v4 } = require("uuid");

async function upWorkId(mainPage, courseStore, infoLogger, attachment, attachLogStr,mArgJson) {
    await infoLogger(`[${attachLogStr}]开始收集题目：${attachment.property.title}`);
    //从web页面收集题目以及答案
    let questionList = await mainPage.evaluate((courseId) => {
        function handleImgs(s) {
            let imgEs = s.match(/(<img([^>]*)>)/gi);
            if (imgEs) {
                for (let j = 0, k = imgEs.length; j < k; j++) {
                    let urls = imgEs[j].match(/http[s]?:\/\/(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+/),
                        url;
                    if (urls) {
                        url = urls[0].replace(/http[s]?:\/\//, "");
                        s = s.replaceAll(imgEs[j], url);
                    }
                }
            }
            return s;
        }

        function trim(s) {
            return (
                s
                    //删除多余字符串
                    .replace(/(<([^>]+)>)/gi, "") //去掉所有的html标记 <a>xx<a>
                    .replace(/^【.*?】\s*/, "") //删除所有【xx】的内容
                    .replaceAll("&nbsp;", "")
                    .replace(/\s{2,}/g, " ") //删除两个及以上的空格，保留单个空格
                    .trim()
            );
        }

        //生成一个唯一id 'id-ltv1dsq7-lz192kwa9'
        function generateUniqueId() {
            return "id-" + new Date().getTime().toString(36) + "-" + Math.random().toString(36).substr(2, 9);
        }

        let TiMuList = document.querySelectorAll(".CeYan .TiMu");
        let questionList = [];
        for (let i = 0; i < TiMuList.length; i++) {
            let timuDom = TiMuList[i];
            //题目内容原始字符串
            let questionFull = timuDom.querySelector(".Zy_TItle.clearfix > div.clearfix").innerHTML
            // questionFull='<span class="newZy_TItle">【单选题】</span>下列属于微观经济学研究对象的是（ &nbsp; &nbsp;）'

            //题目类型
            let type = questionFull.match(/【(.*?)】/)[1];

            //题目内容
            let content = trim(handleImgs(questionFull));

            //处理答案，有可能没有答案
            let answers;
            try {
                answers = timuDom.querySelector(".correctAnswer .answerCon").innerText;
            } catch (error) {
                continue
            }

            //处理选项
            let options = [];
            switch (type) {
                case "单选题":
                case "多选题":
                    let optionList = timuDom.querySelectorAll("li");
                    for (let j = 0; j < optionList.length; j++) {
                        let optionContent = trim(handleImgs(optionList[j].innerHTML));
                        optionContent = optionContent.replace("、", ":");
                        options.push(optionContent);
                    }
                    break;
                case "判断题":
                    options = ["A:对", "B:错"];
                    break;
                default:
                    options = [];
                    break;
            }

            //收集题目对象
            questionList.push({
                content: content,
                type: type,
                id: generateUniqueId(),
                options: options,
                platform: "超星学习通",
                courseid: courseId,
                answers: answers,
            });
        }
        return questionList;
    }, courseStore.courseId);

    let upCount = 0;

    //上传答案
    for (let i = 0; i < questionList.length; i++) {
        let questionObj = questionList[i];
        let defaultObj = {
            id: v4(),
            content: questionObj.content,
            options: questionObj.options,
            type: questionObj.type,
            platform: questionObj.platform,
            answers: questionObj.answers,
            courseid: questionObj.courseid,
        };
        let [instance, created] = await Model.bank.findOrCreate({
            where: {
                content: questionObj.content,
                type: questionObj.type,
                platform: questionObj.platform,
                courseid: questionObj.courseid,
            },
            defaults: defaultObj,
        });
        if (created) {
            upCount++;
        }
    }

    await infoLogger(`[${attachLogStr}]题目收集完成，共收集${upCount}道题目：${attachment.property.title}`);
}

module.exports = upWorkId;