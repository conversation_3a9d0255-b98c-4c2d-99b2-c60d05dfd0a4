const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('exam_info_log', {
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    exam_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
      references: {
        model: 'exam',
        key: 'id'
      }
    },
    log_time: {
      type: DataTypes.DATE,
      allowNull: true
    },
    log_message: {
      type: DataTypes.STRING(2000),
      allowNull: true
    },
    timestamp: {
      type: DataTypes.BIGINT,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'exam_info_log',
    timestamps: false,
    indexes: [
      {
        name: "PRIMAR<PERSON>",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "exam_id",
        using: "BTREE",
        fields: [
          { name: "exam_id" },
        ]
      },
    ]
  });
};
