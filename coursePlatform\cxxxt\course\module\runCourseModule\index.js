// 其他模块
let pageTools = require('../../../../utils/pageTools.js');
let skipFacecheck = require('./skipFacecheck.js');

// 引入新的章节处理模块
let handleChapter = require('./handleChapter.js');

// 嵌套逻辑：课程->章节->页面->任务，任务分为不同类型
async function runCourseModule(infoLogger, globalStore, mainPage, courseList) {
    let taskObj = globalStore.taskObj;
    // 遍历课程列表
    loopCourse: for (let courseIndex = 0; courseIndex < courseList.length; courseIndex++) {
        // 1.取出一个课程对象courseObj
        let courseObj = courseList[courseIndex];
        let courseLogStr = `课程[${courseIndex + 1}/${courseList.length}]`;

        if (courseObj.progress == '100') {
            await infoLogger(`${courseLogStr}：${courseObj.courseName}，当前课程已完成，跳过`, 'green');
            continue loopCourse;
        }

        await infoLogger(`${courseLogStr}：${courseObj.courseName}-${courseObj.progress}，课程开始`, 'blue');

        //2.进入新版课程界面 course
        await mainPage.goto(courseObj.courseUrl, { waitUntil: 'networkidle0' });

        try {
            // 进入课程页面的时候会提示 人脸识别，这里进行处理，进行认证也能直接进入课程
            // 注意：强行绕过会出现：学生课程中采集的人脸图像与本人档案图像匹配度低于50%”，教师可操作清除学习进度或取消学习成绩
            await skipFacecheck(mainPage, courseLogStr, infoLogger);
        } catch (error) {
            continue loopCourse;
        }

        // await new Promise(resolve => setTimeout(resolve, 200000)); //等待加载完成

        await new Promise(resolve => setTimeout(resolve, 2000));

        // 3.获取课程页面数据 courseObj
        // 获取课程页面cookie 通过页面cookie获取课程的 $uid $fid
        let cookieStr = await pageTools.getPageCookies(mainPage);
        let $uid = pageTools.getCookieValue(cookieStr, 'UID') || pageTools.getCookieValue(cookieStr, '_uid');
        let $fid = pageTools.getCookieValue(cookieStr, 'fid') || '666';
        //根据url获取课程信息 classid courseId cpi coursename origin
        let $s = await mainPage.evaluate(() => {
            let obj = Object.fromEntries(new URLSearchParams(window.location.search));
            // 如果进入课程需要人脸识别，这里就无法通过window.document.title获取courseName
            obj.courseName = window.document.title.replace('-首页', '');
            obj.$siteHost = window.location.origin;
            obj.oldenc = oldenc.value; // 确保 oldenc 在页面上下文中存在
            return obj;
        });
        if (false) {
            // 保留原有的调试代码块
            let $s_debug = {
                courseid: '210383912',
                clazzid: '92210804',
                cpi: '323008885',
                enc: '52d2af9b041de7cd19bb04595d6c3431',
                t: '1710574515630',
                pageHeader: '0',
                v: '2',
                courseName: '概率论与数理统计',
                $siteHost: 'https://mooc2-ans.chaoxing.com',
            };
        }

        //设置一个对象，储存当前课程所有信息
        let courseStore = {
            //课程信息
            $uid: $uid, //用户id，通过页面cookie获取
            $fid: $fid, //通过页面cookie获取
            classId: $s['clazzid'] || $s['classid'] || $s['classId'] || courseObj.classId, //班级id，通过页面url获取
            courseId: $s['courseid'] || $s['courseId'] || courseObj.courseId, //课程id，通过页面url获取
            cpi: $s['cpi'], //课程cpi，通过页面url获取
            courseName: $s['courseName'] || courseObj.courseName, //课程名称，通过页面title获取
            $siteHost: $s.$siteHost || 'https://mooc1.chaoxing.com', //课程域名，通过页面url获取：'https://mooc1.chaoxing.com'
            enc: $s.enc,
            t: $s.t,
            oldenc: $s.oldenc,

            //视频设置
            beisu: taskObj.videoRate, //视频播放倍速
            vgqtlv: 85, //题目正确率
            videoV: '**************',

            //其他
            need: false, //是否需要二次循环
            $version: '099',
            chuangguan: false, //是否是闯关模式

            //防沉迷
            timelong: {}, //今天学习时长
            timeTold: false, //学习时长是否超过18个小时
            cookieStr: cookieStr, // 当前课程主页的cookie
        };
        globalStore.courseStore = courseStore; // 将当前课程信息存入全局，方便其他模块访问

        // 二、调用新的章节处理模块
        try {
            await handleChapter(globalStore, infoLogger, mainPage, courseLogStr);
        } catch (error) {
            // 如果 loopChapter 内部抛出如验证码之类的严重错误，在这里捕获并决定如何处理
            // 例如，记录错误并继续下一个课程，或者直接向上抛出中断整个脚本
            await infoLogger(`${courseLogStr} 处理章节时发生严重错误: ${error.message}, 跳过当前课程`, 'red');
            // // 根据实际需求决定是否 continue loopCourse 或 throw error;
            // if (error.message && error.message.includes('操作异常，需要输入验证码')) {
            //     await infoLogger('检测到验证码，终止当前所有任务。', 'red');
            //     // 可以选择在这里抛出错误，中断整个 runCourseModule
            //     throw error; // 或者 return; 来结束当前所有课程的处理
            // }
            continue loopCourse; // 默认继续下一个课程
        }
    }
}

module.exports = runCourseModule;
