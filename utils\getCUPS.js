// 通过 readline 模块 创建一个命令行窗口，让用户输入线程数量
const readline = require('readline');

let originCpus = require('os').cpus().length;
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout,
});
function askQuestion(query) {
    return new Promise(resolve => {
        rl.question(query, resolve);
    });
}
async function main() {
    try {
        // 使用 await 来等待用户输入
        const cpus = await askQuestion(`请输入线程数量(默认值:${originCpus}，线程数跟cpu核心以及内存有关，如果设置太大会导致卡死)：  `);
        return cpus;
    } catch (err) {
        console.error('输入出错:', err);
    } finally {
        // 关闭 readline 接口
        rl.close();
    }
}

module.exports = main;