let api = require("../../utils/api.js");
async function endModule(infoLogger,globalStore) {
    let {mainPageCookieStr,schoolName}=globalStore
    let finalResult = [];

    // await infoLogger(`等三十秒，因为成绩更新有延迟`);
    await new Promise(r => setTimeout(r, 30000));

    let courseRes = await api.getStudentLearnInfo(schoolName, mainPageCookieStr, globalStore.termCode);

    let courseList = courseRes.debugData.courseInfoList;
    courseList.forEach((item) => {
        finalResult.push({
            courseName: item.courseName,
            progress: item.onlineScore,
        });
    });
    return finalResult;
}

module.exports = endModule;