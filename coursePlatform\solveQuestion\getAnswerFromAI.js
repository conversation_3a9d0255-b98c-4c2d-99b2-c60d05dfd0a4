let aiChat = require('./aiChat.js');
let { validateAnswer } = require('./format.js');
let collectQuestion = require('./collectQuestion.js');

// 根据题目类型获取提示词
function generatePromptStr(questionObj) {
    //  填空题 提示词
    let fillPromptStr = `
    这是一道填空题，题目内容：${questionObj.content}。
    首先，你先分析下这个填空题一共有几个空，每个空对应一个答案。
    现在需要你把答案发给我，多个答案用"|"分隔，如"答案1|答案2"。
    不要包含任何解释、分析或多余文字，如果无法回答，请回答'false'。
    `;

    // 简答题 提示词
    let shortPromptStr = `
    这是一道简答题，题目内容：${questionObj.content}。
    请根据题目要求直接给出答案，不要包含任何解释、分析或多余文字。
    如果无法回答，请回答'false'。
    `;

    // 判断题 提示词
    let judgePromptStr = `
    这是一道判断题，题目内容：${questionObj.content}。
    如果题目表述是对，就回答“A”，如果题目表述是错，就回答“B”。
    直接回答A或者B，不要包含任何解释、分析或多余文字。
    `;

    // 单选题 提示词
    let singlePromptStr = `
    这是一道单选题，题目内容：${questionObj.content}，题目选项：${questionObj.options}。
    直接回答一个大写字母，如"A"，不要包含任何解释、分析或多余文字。
    `;

    // 多选题 提示词
    let multiplePromptStr = `
    这是一道多选题，题目内容：${questionObj.content}，题目选项：${questionObj.options}。
    直接回答多个大写字母，如"ABCD"，不要包含任何解释、分析或多余文字。
    `;

    switch (questionObj.type) {
        case '填空题':
            return fillPromptStr;
        case '简答题':
            return shortPromptStr;
        case '判断题':
            return judgePromptStr;
        case '单选题':
            return singlePromptStr;
        case '多选题':
            return multiplePromptStr;
        default:
            return shortPromptStr;
    }
}

async function getAnswerFromAI(questionObj) {
    let promptStr = generatePromptStr(questionObj);

    // let model = 'deepseek-chat';
    // model = 'deepseek-reasoner';
    model = 'o4-mini-2025-04-16';
    let answers = await aiChat(model, promptStr);

    // 校验题目类型
    let isFormat = validateAnswer(answers, questionObj.type);
    if (!isFormat) {
        return;
    }

    // 收集题目
    if (answers && answers != 'false') {
        await collectQuestion(questionObj, answers, 'getAnswerFromAI');
    }

   
    return answers;
}

module.exports = getAnswerFromAI;

if (false) {
    // let questionObj = {
    //     id: 'id-maqmgd04-mitnaupijsigleQuestionDiv_884682952',
    //     content:
    //         'Plain Text下列程序的运行结果?publicclassTest{publicstaticvoidmain(Stringa[]){intx=3,y=4,z=5;if(x>3){if(y<2)System.out.println("showone");elseSystem.out.println("showtwo");}else{if(z>4)System.out.println("showthree");elseSystem.out.println("showfour");}}}',
    //     type: '单选题',
    //     options: ['A:show one', 'B:show two', 'C:show three', 'D:show four'],
    //     platform: '超星学习通',
    //     courseName: '2025第1学期Java语言程序设计期末考试',
    // };
    let questionObj = {
        id: 'id-mauhzh41-oxmvmi6a3sigleQuestionDiv_884682970',
        content: 'Plain Text下列叙述中，正确的是？',
        type: '多选题',
        options: ['A:声明变量时必须指定一个类型', 'B:java认为变量number与Number相同', 'C:Java中唯一的注释方式是"//"', 'D:源文件中public类可以有0或多个'],
        platform: '超星学习通',
        courseName: '2025第1学期Java语言程序设计期末考试',
    };

    getAnswerFromAI(questionObj).then(r => console.log('最终答案', r));
}
