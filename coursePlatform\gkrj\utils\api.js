let axiosIns = require('../../utils/axiosIns');

function formatDate(date) {
    const options = {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false, // 24 小时制
    };

    return date.toLocaleString('zh-CN', options).replace(/\//g, '-');
}

//验证码判断
exports.validateCode = base64Image => {
    // let base64Image = imgBuffer.toString('base64');
    return axiosIns.post('http://upload.chaojiying.net/Upload/Processing.php', {
        user: 'mozhi0012',
        pass: 'rVYBbAw8vDF5@V6',
        softid: 953643,
        codetype: 1004,
        file_base64: base64Image,
    });
};
if (false) {
    let success = { err_no: 0, pic_str: '8934' };
    let fail = { err_no: -10061, err_str: '不是有效的图片文件', pic_str: '' };
}

// 获取课程列表
exports.getCourseList = async (studentNumber, cookieStr) => {
    let config = {
        url: `http://test2024b.souchn.cn/api/exams/1/6?studentNumber=${studentNumber}`,
        method: 'get',
        headers: {
            accept: 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'cache-control': 'no-cache',
            pragma: 'no-cache',
            'proxy-connection': 'keep-alive',
            cookie: cookieStr,
            Referer: 'http://test2024b.souchn.cn/',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
        },
    };
    let res = await axiosIns(config);

    return res;
};
if (false) {
    res = {
        code: 200,
        message: '请求成功！',
        data: {
            records: [
                {
                    examCode: 202310035,
                    description: 'Oracle数据库编程',
                    name: '24126',
                    source: 'Oracle数据库编程',
                    paperId: null,
                    examDate: '2024-12-05 13:18:26',
                    endDate: '2025-01-03 20:00:00',
                    totalTime: 60,
                    grade: null,
                    term: null,
                    major: null,
                    institute: '全选,国家开放大学软件学院',
                    totalScore: 100,
                    selectScore: 1,
                    number: 20,
                    numPart: null,
                    tips: null,
                },
            ],
            total: 6,
            size: 6,
            current: 1,
            orders: [],
            searchCount: true,
            pages: 1,
        },
    };
}

// 获取课程详细信息
exports.getCourseInfo = async (examCode, cookieStr) => {
    let config = {
        url: `http://test2024b.souchn.cn/api/exam/${examCode}`,
        method: 'get',
        headers: {
            accept: 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'cache-control': 'no-cache',
            pragma: 'no-cache',
            'proxy-connection': 'keep-alive',
            cookie: cookieStr,
            Referer: 'http://test2024b.souchn.cn/',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
        },
    };
    let res = await axiosIns(config);

    return res;
};
if (false) {
    res = {
        code: 200,
        message: '请求成功！',
        data: {
            examCode: 202310035,
            description: 'Oracle数据库编程',
            name: '24126',
            source: 'Oracle数据库编程',
            paperId: null,
            examDate: '2024-12-05 13:18:26',
            endDate: '2025-01-03 20:00:00',
            totalTime: 60,
            grade: null,
            term: null,
            major: null,
            institute: '全选,国家开放大学软件学院',
            totalScore: 100,
            selectScore: 1,
            number: 20,
            numPart: 20,
            tips: null,
        },
    };
}

// 获取试题列表
exports.getQuestionList = async (studentNumber, cookieStr) => {
    let config = {
        url: `http://test2024b.souchn.cn/api/paper/${examCode}/${num}`,
        method: 'get',
        headers: {
            accept: 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'cache-control': 'no-cache',
            pragma: 'no-cache',
            'proxy-connection': 'keep-alive',
            cookie: cookieStr,
            Referer: 'http://test2024b.souchn.cn/',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        1: [
            {
                questionId: 10626,
                subject: 'Oracle数据库编程',
                section: null,
                type: 1,
                answerA: '数据结构',
                answerB: '数据库管理系统',
                answerC: '操作系统',
                answerD: '数据模型',
                answerE: null,
                answerF: null,
                question: '数据库系统的基础是（     ）。',
                level: '1',
                rightAnswer: 'D',
                analysis: null,
                score: 2,
            },
        ],
        2: [
            {
                questionId: 10741,
                subject: 'Oracle数据库编程',
                section: null,
                type: 2,
                answerA: '通过Database Configuration Assistant工具创建',
                answerB: '通过命令行创建',
                answerC: '通过SQL*PlUS创建',
                answerD: '通过自定义批处理脚本创建',
                answerE: null,
                answerF: null,
                question: '创建Oracle数据库的方式包括：（       ）',
                level: '1',
                rightAnswer: 'A,C,D',
                analysis: null,
                score: 4,
            },
        ],
        3: [
            {
                questionId: 10925,
                subject: 'Oracle数据库编程',
                question: '采集是指数据的获取',
                answer: 'T',
                level: '1',
                section: null,
                score: 2,
                type: 3,
                analysis: null,
            },
        ],
    };
}

// 提交分数
exports.submitScore = async (courseObj, userId, cookieStr, score) => {
    let config = {
        url: `http://test2024b.souchn.cn/api/scoreUpload`,
        method: 'post',
        headers: {
            cookie: cookieStr,
        },
        data: {
            examCode: courseObj.examCode, //考试编号
            studentId: userId, //学号
            subject: courseObj.source, //课程名称
            score: score, //答题成绩
            answerDate: formatDate(new Date()), //答题日期
        },
    };
    let res = await axiosIns(config);
    return res;
};

// 获取最终结果
exports.getScore = async (cookieStr, userId) => {
    let config = {
        url: `http://test2024b.souchn.cn/api/score/1/10/${userId}`,
        method: 'get',
        headers: {
            cookie: cookieStr,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        code: 200,
        message: '根据ID查询成绩',
        data: {
            records: [
                {
                    scoreId: 203350,
                    examCode: 202310035,
                    studentId: 2390101451120,
                    subject: 'Oracle数据库编程',
                    ptScore: null,
                    etScore: null,
                    score: 88,
                    number: 1,
                    answerDate: '2024-12-13 13:56:21',
                },
            ],
            total: 1,
            size: 10,
            current: 1,
            orders: [],
            searchCount: true,
            pages: 1,
        },
    };
}

fetch('http://test2024b.souchn.cn/api/scoreUpload', {
    headers: {
        accept: 'application/json, text/plain, */*',
        'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
        'cache-control': 'no-cache',
        'content-type': 'application/json;charset=UTF-8',
        pragma: 'no-cache',
        'proxy-connection': 'keep-alive',
        cookie: 'cname=%E4%BF%9E%E6%B3%BD%E4%B8%9C; cid=2390101451120',
        Referer: 'http://test2024b.souchn.cn/',
        'Referrer-Policy': 'strict-origin-when-cross-origin',
    },
    body: '{"examCode":202310035,"studentId":"2390101451120","subject":"Oracle数据库编程","score":93,"answerDate":"2024-12-13 17:12:60"}',
    method: 'POST',
})
    .then(r => r.json())
    .then(v => console.log(v));
