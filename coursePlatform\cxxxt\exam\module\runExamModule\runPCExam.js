let PCfaceCompare = require('./PCfaceCompare');
let getQuestionList = require('./getQuestionList');
let solveQuestion = require('../../../../solveQuestion/index');
let saveQuestion = require('./saveQuestion');
let collectQuestion = require('./collectQuestion');
let handleSlideValidate = require('./handleSlideValidate');
let pageTools = require('../../../../utils/pageTools.js');

// 把url中的查询字符串转为对象
let url = require('url');
function getQueryParams(urlString) {
    // 解析 URL
    const parsedUrl = new URL(urlString);

    // 获取查询参数
    const queryParams = {};
    for (const [key, value] of parsedUrl.searchParams) {
        queryParams[key] = value;
    }

    return queryParams;
}

// 把对象作为查询字符串添加到url后面
function addQueryParamsToUrl(baseUrl, params) {
    // 创建一个 URL 对象
    const url = new URL(baseUrl);

    // 遍历对象的属性
    for (const [key, value] of Object.entries(params)) {
        // 将每个键值对添加到 URL 的查询参数中
        url.searchParams.append(key, value);
    }

    // 返回完整的 URL 字符串
    return url.toString();
}

async function runPCExam(mainPage, taskObj, courseObj, infoLogger, finalResult, courseLogStr) {
    // 跳转到考试界面 （确认界面）
    await mainPage.goto(courseObj.examUrl, { waitUntil: 'networkidle0' });
    await new Promise(r => setTimeout(r, 2000));
    let temUrl = await mainPage.url();

    // 一、考试已经交卷
    if (temUrl.includes('/exam-ans/exam/test/look')) {
        let statObj;
        try {
            statObj = await mainPage.evaluate(() => {
                let stateTag = document.querySelector('body > div.result_Main   div.textCenter > div[aria-label]');
                // 本次考试允许重考3次，已重考1次
                let stateStr = stateTag.getAttribute('aria-label');
                let obj = {
                    allowCount: stateStr.match(/允许重考(\d+)次，已重考(\d+)次/)[1],
                    submitCount: stateStr.match(/允许重考(\d+)次，已重考(\d+)次/)[2],
                };
                return obj;
            });
        } catch (error) {
            await infoLogger(`${courseLogStr}，考试已经参加，没有考试机会`);
            return;
        }
        let restCount = statObj.allowCount * 1 - statObj.submitCount * 1;

        await infoLogger(`${courseLogStr}，考试已经参加${statObj.submitCount}次，还剩余${restCount}次机会`);

        if (restCount > 1) {
            await infoLogger(`接下来开始重新考试`);
            await new Promise(r => setTimeout(r, 2000));

            // 在查看界面，点击“重考”
            await mainPage.evaluate(() => jumpRetest());
            await new Promise(r => setTimeout(r, 2000));

            // 在准备界面点击 开始重考
            await mainPage.evaluate(async () => {
                let divTag = document.querySelector('body > div.main1200 > div > div > div > div.face_agreement');
                divTag.click();
                await new Promise(r => setTimeout(r, 1000));
                preEnterExam();
            });
            await new Promise(r => setTimeout(r, 2000));

            // 在弹出 的提示 界面 点击 去顶重考
            await mainPage.evaluate(() => reTestActionCallBack());
            await new Promise(r => setTimeout(r, 2000));

            await main();
            return;
        }

        if (restCount == 1) {
            await infoLogger(`考试结束`);
            return;
        }
    }

    // 二、可以考试
    if (temUrl.includes('/exam-ans/exam/test/examcode/examnotes')) {
        await infoLogger(`${courseLogStr}，考试开始`);
        // 进入考试
        await mainPage.evaluate(() => enterExam());
        await new Promise(r => setTimeout(r, 2000));

        await main();
        return;
    }

    async function main() {
        // 验证码识别 (通过页面元素判断) #cx_image_margin
        let needSlideValidate = false;
        try {
            await mainPage.waitForSelector('#cx_image_margin', { timeout: 3000 });
            needSlideValidate = true;
            await infoLogger(`${courseLogStr} 需要滑动验证`);
        } catch (error) {
            await infoLogger(`${courseLogStr} 不需要滑动验证`);
        }
        if (needSlideValidate) {
            await infoLogger(`${courseLogStr} 开始滑动验证码`);
            await handleSlideValidate(mainPage, infoLogger);
            await infoLogger(`${courseLogStr} 滑动验证结束`);
            await new Promise(r => setTimeout(r, 2000));
        }
        await new Promise(r => setTimeout(r, 2000));

        //切换到预览模式
        await mainPage.evaluate(() => {
            topreview();
        });
        await new Promise(r => setTimeout(r, 2000));

        // 获取题目列表
        let questionList = await getQuestionList(mainPage, courseObj, taskObj);
        if (Array.isArray(questionList) && questionList.length > 0) {
            await infoLogger(`${courseLogStr} 获取题目成功，共有${questionList.length}道题目`, 'green');
        } else {
            await infoLogger(`${courseLogStr} 获取题目失败`, 'red');
            throw new Error(`${courseLogStr} 获取题目失败`); //结束进程池任务
        }

        // 查找答案
        let answerCount = 0;
        for (let i = 0; i < questionList.length; i++) {
            //1.取出一个问题对象
            let questionObj = questionList[i];

            let answers;
            try {
                answers = await solveQuestion(questionObj);
            } catch (error) {
                await infoLogger(`${courseLogStr} 查找答案出错`, 'red');
                await infoLogger(`当前题目：${JSON.stringify(questionObj)}`, 'gray');
                continue;
            }

            if (answers) {
                questionObj.answers = answers;
                answerCount++;
            }
        }
        if (answerCount == 0) {
            await infoLogger(`${courseLogStr} 一题都没找到答案`);
            return;
        }
        await infoLogger(`${courseLogStr} ，共找到${answerCount}/${questionList.length}题答案`);
        await new Promise(r => setTimeout(r, 2000));

        //回答题目
        await saveQuestion(infoLogger, mainPage, questionList, taskObj);
        await infoLogger(`${courseLogStr} 答题成功`, 'green');

        // 随机产生一个31-45的整数
        let waitMin = pageTools.getRandomInt(31, 45);

        // waitMin = 10
        for (let i = 0; i < waitMin; i++) {
            await infoLogger(`等待${waitMin - i}分钟后交卷`);
            await new Promise(resolve => setTimeout(resolve, 1 * 60 * 1000)); //等待加载完成
        }

        // 交卷
        await mainPage.evaluate(() => {
            finalSubmit(1);
        });
        await infoLogger(`${courseLogStr} 交卷成功`, 'green');

        // 考完之后会跳转到这个页面
        // https://mooc1.chaoxing.com/exam-ans/exam/test/look?courseId=*********&classId=*********&examId=5434666&examAnswerId=*********&cpi=*********&qbanksystem=1
        await new Promise(r => setTimeout(r, 2000));
        let temUrl = await mainPage.url();
        let paramsObj = getQueryParams(temUrl);

        //获取分数
        // body > div.examBox > div.exam_content > h2 > b
        let score = await mainPage.evaluate(() => {
            let scoreTag = document.querySelector('body > div.result_Main > div > h2.result_number > b');
            if (!scoreTag) {
                return undefined;
            }
            let score = scoreTag.innerText;
            return score;
        });
        await infoLogger(`${courseLogStr} 最终分数为：${score}`, 'green');

        // 给错误题目增加标记，有的学校不允许查看，可以考虑通过url拼接方式查看
        // https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionPaperMarkContentNew?courseId=*********&classId=*********&p=1&id=*********
        let baseUrl = 'https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionPaperMarkContentNew';
        let finalParams = {
            courseId: paramsObj.courseId,
            classId: paramsObj.classId,
            id: paramsObj.examAnswerId,
            newMooc: true,
        };
        let finalUrl = addQueryParamsToUrl(baseUrl, finalParams);
        await infoLogger(`${courseLogStr}  查看答题试卷：${finalUrl}`);
        // try {
        //     await infoLogger(`${courseLogStr}  收集错题`);
        //     await mainPage.goto(finalUrl, { waitUntil: 'networkidle0' });
        //     await new Promise(r => setTimeout(r, 2000));
        //     let { collectCount, updateCount } = await collectQuestion(mainPage, courseObj.courseName);
        //     await infoLogger(`${courseLogStr}  标记${collectCount}道题目，更新${updateCount}道题目`);
        // } catch (error) {
        //     await infoLogger(`${courseLogStr}  不支持查看试卷，${error.message}`);
        // }
    }
}

module.exports = runPCExam;
