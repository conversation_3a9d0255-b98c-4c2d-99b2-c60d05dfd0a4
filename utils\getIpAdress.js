let axios = require('axios');
// 封装及格常见的获取IP地址的方法
async function getIpAdress() {
    let ipAdress;
    let location;
    let ipchaxunRes;
    let ipcnRes;

    try {
        ipchaxunRes = await axios.get('https://2024.ipchaxun.com');
        let obj = { ret: 'ok', ip: '**************', data: ['中国', '安徽', '滁州', '南谯', '电信', '239000', '0550'] };
        if (ipchaxunRes.data && ipchaxunRes.data.ip) {
            ipAdress = ipchaxunRes.data.ip;
            location = ipchaxunRes.data.data.join('-');
        }
    } catch (error) {
        ipAdress = null;
        location = null;
    }

    // 基于go-geoip开源项目，自己部署 https://github.com/deanxv/go-geoip
    if (!ipAdress) {
        ipcnRes = await axios.get('http://origin.mozhi0012.top:7099/ip');
        let obj = {
            code: 0,
            message: 'success',
            data: {
                addr: '*************/24',
                as: '中国电信',
                country: '中国',
                ip: '**************',
                latitude: 34.7732,
                longitude: 113.722,
                subdivisions: null,
                province: '安徽省',
                city: '滁州市',
                district: '南谯区',
                registered_country: '中国',
            },
        };
        ipcnRes=ipcnRes.data
        if (ipcnRes&& ipcnRes.code == 0) {
            ipAdress = ipcnRes.data.ip
            location = `${ipcnRes.data.country}-${ipcnRes.data.province}-${ipcnRes.data.city}-${ipcnRes.data.as}`;
        } else {
            throw new Error('无法解析IP地址，请联系管理员 ');
        }
    }

    return { ipAdress, location };
}

module.exports = getIpAdress;