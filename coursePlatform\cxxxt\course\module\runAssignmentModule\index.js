let doHomeWork = require('./doHomeWork.js');
let pageTools = require('../../../../utils/pageTools.js');
let skipFacecheck = require('../runCourseModule/skipFacecheck.js');

async function runAssignmentModule(infoLogger, globalStore, mainPage, courseList) {
    let taskObj = globalStore.taskObj;
    // let courseStore = globalStore.courseStore;
    for (let i = 0; i < courseList.length; i++) {
        let courseObj = courseList[i];
        let courseLogStr = `课程[${i + 1}/${courseList.length}]${courseObj.courseName}`;
        await infoLogger(`${courseLogStr}：开始作业`, 'blue');

        //进入新版课程界面
        await mainPage.goto(courseObj.courseUrl, { waitUntil: 'networkidle0' });
        await new Promise(resolve => setTimeout(resolve, 3000)); //等待加载完成

        try {
            // 进入课程页面的时候会提示 人脸识别，这里进行处理，进行认证也能直接进入课程
            // 注意：强行绕过会出现：学生课程中采集的人脸图像与本人档案图像匹配度低于50%”，教师可操作清除学习进度或取消学习成绩
            await skipFacecheck(mainPage, courseLogStr, infoLogger);
        } catch (error) {
            continue;
        }

        let cookieStr = await pageTools.getPageCookies(mainPage);
        let $uid = pageTools.getCookieValue(cookieStr, 'UID') || pageTools.getCookieValue(cookieStr, '_uid');
        let $fid = pageTools.getCookieValue(cookieStr, 'fid') || '666';
        //根据url获取课程信息 classid courseId cpi coursename origin
        let $s = await mainPage.evaluate(() => {
            let obj = Object.fromEntries(new URLSearchParams(window.location.search));
            // 如果进入课程需要人脸识别，这里就无法通过window.document.title获取courseName
            obj.courseName = window.document.title.replace('-首页', '');
            obj.$siteHost = window.location.origin;
            return obj;
        });

        //设置一个对象，储存当前课程所有信息
        let courseStore = {
            //课程信息
            $uid: $uid, //用户id，通过页面cookie获取
            $fid: $fid, //通过页面cookie获取
            classId: $s['clazzid'] || $s['classid'] || $s['classId'] || $s['classId'], //班级id，通过页面url获取
            courseId: $s['courseid'] || $s['courseId'], //课程id，通过页面url获取
            cpi: $s['cpi'], //课程cpi，通过页面url获取
            courseName: $s['courseName'] || courseObj.courseName, //课程名称，通过页面title获取
            $siteHost: 'https://mooc1.chaoxing.com', //课程域名，通过页面url获取：'https://mooc1.chaoxing.com'
            enc: $s.enc,
            t: $s.t,

            //视频设置
            beisu: taskObj.videoRate, //视频播放倍速
            vgqtlv: 85, //题目正确率
            videoV: '**************',

            //其他
            need: false, //是否需要二次循环
            $version: '099',
            chuangguan: false, //是否是闯关模式

            //防沉迷
            timelong: {}, //今天学习时长
            timeTold: false, //学习时长是否超过18个小时
            cookieStr: cookieStr,
        };
        globalStore.courseStore = courseStore;

        //获取作业页面数据
        let workEnc;
        try {
            workEnc = await mainPage.evaluate(() => {
                return document.getElementById('workEnc').value;
            });
        } catch (error) {
            await infoLogger(`${courseLogStr}：获取作业workenc失败，${courseObj.courseUrl}`, 'red');
            continue;
        }

        //进入到作业页面
        let homeWorkListUrlObj = {
            courseId: courseStore.courseId,
            classId: courseStore.classId,
            cpi: courseStore.cpi,
            ut: 's',
            enc: workEnc,
        };
        let homeWorkListUrlBase = 'https://mooc1.chaoxing.com/mooc2/work/list';
        let homeWorkListUrl = pageTools.buildUrl(homeWorkListUrlBase, homeWorkListUrlObj);
        await infoLogger(`${courseLogStr}：进入作业列表页面:${homeWorkListUrl}`);
        // await mainPage.goto(homeWorkListUrl, { waitUntil: 'networkidle0' });
        await pageTools.gotoWithRetry(mainPage, homeWorkListUrl, { waitUntil: 'networkidle0' }, 3, infoLogger);

        //获取作业列表
        let homeWorkList = await mainPage.evaluate(() => {
            let homeWorkList = [];
            let homeWorkTagList = document.querySelectorAll('body > div.box > div > div > div.has-content > div.bottomList > ul > li');
            homeWorkTagList.forEach(liTag => {
                let homeWorkName = liTag.querySelector('.overHidden2').textContent;
                let homeWorkStatus = liTag.querySelector('.status').textContent;
                let homeWorkUrl = liTag.getAttribute('data');
                homeWorkList.push({
                    name: homeWorkName,
                    status: homeWorkStatus,
                    url: homeWorkUrl,
                });
            });
            return homeWorkList;
        });
        await infoLogger(`${courseLogStr}：获取作业列表成功，共${homeWorkList.length}门作业`);

        //开始做作业
        for (let k = 0; k < homeWorkList.length; k++) {
            let homeWorkObj = homeWorkList[k];
            let homeWorkLogStr = `${courseLogStr}-作业[${k + 1}/${homeWorkList.length}]`;
            // if (homeWorkObj.status == '已完成') {
            //     await infoLogger(`${homeWorkLogStr}，当前作业已完成`);
            //     await uploadHomeWork(mainPage, infoLogger, courseStore, homeWorkObj, k, i, totalCourse, homeWorkList);
            //     continue;
            // }
            if (homeWorkObj.status == '待批阅') {
                await infoLogger(`${homeWorkLogStr}，当前待批阅，跳过`);
                continue;
            }
            if (homeWorkObj.status == '未交') {
                await infoLogger(`${homeWorkLogStr}，开始答题`);
                await doHomeWork(infoLogger, globalStore, mainPage, courseObj, homeWorkObj, homeWorkLogStr);
                // await new Promise(r => setTimeout(r, 3000));
                // await uploadHomeWork(mainPage, infoLogger, courseStore, homeWorkObj, k, i, totalCourse, homeWorkList);
            }
        }
    }
}

module.exports = runAssignmentModule;
