async function getQuestionList(mainPage, courseObj, taskObj) {
    let questionList;
    let courseName=courseObj.courseName;

    if (taskObj.others.includes("电脑端")) {
        questionList = await mainPage.evaluate((courseName) => {

            function handleImgs(s) {
                // 匹配所有 img 标签，捕获 src 属性的 URL
                let imgEs = s.match(/<img\s+[^>]*src\s*=\s*['"]([^'"]+)['"][^>]*>/gi);
            
                if (imgEs) {
                    for (let j = 0; j < imgEs.length; j++) {
                        // 匹配 img 标签中的 src 属性
                        let urlMatch = imgEs[j].match(/src\s*=\s*['"]([^'"]+)['"]/i);
                        if (urlMatch && urlMatch[1]) {
                            let url = urlMatch[1]; // 获取完整 URL
                            // 用 [URL] 格式替换 img 标签为 URL
                            s = s.replace(imgEs[j], `[URL]${url}[/URL]`);
                        }
                    }
                }
            
                return s;
            };
            let str='<p><img src="http://p.ananas.chaoxing.com/star3/origin/e26a7f1c81ec0813d1298e05022b7753.png" data-original="http://p.ananas.chaoxing.com/star3/origin/e26a7f1c81ec0813d1298e05022b7753.png"></p>'
            console.log(handleImgs(str));

            function trim(s) {
                return (
                    s
                        //删除多余字符串
                        .replace(/(<([^>]+)>)/gi, "") //去掉所有的html标记 <a> </a>
                        .replace(/^\d+[\.、]/, "") //删除所有1.、1、的内容
                        .replace(/\(.{3}\)/, "") //删除所有（xx题）的内容
                        .replaceAll("&nbsp;", "")
                        .replaceAll("\n", "") //删除所有的换行符
                        .replace(/\s{2,}/g, " ") //删除两个及以上的空格，保留单个空格
                        .trim()
                );
            }

            //生成一个唯一id  #fanyaMarking > div.marking_content.ans-cc > div.mark_table > div.whiteDiv > div.questionLi
            function generateUniqueId() {
                return "id-" + new Date().getTime().toString(36) + "-" + Math.random().toString(36).substr(2, 9);
            }

            let questionList = [];
            let TimuList = document.querySelectorAll("#fanyaMarking > div.marking_content.ans-cc > div.mark_table > div.whiteDiv > div.questionLi");
            
            for (let i = 0; i < TimuList.length; i++) {
                let timuDom = TimuList[i];
                // 题目内容
                let questionFull = timuDom.querySelector(":scope>h3>div").innerHTML;
                let content = trim(handleImgs(questionFull));

                //题目类型
                let type = timuDom.querySelector(":scope>h3>span").innerHTML;
                type = type.match(/\(([^,]+),/)[1];

                //获取题目id
                let questionId = timuDom.getAttribute("id");

                //4.获取题目选项
                let options = [];
                switch (type) {
                    case "单选题":
                    case "多选题":
                        let optionList = timuDom.querySelectorAll(".answerBg");
                        for (let j = 0; j < optionList.length; j++) {
                            let optionKey = optionList[j].querySelector("span").innerText;
                            let optionContent = optionList[j].querySelector("div").innerHTML;
                            optionContent = optionKey + ":" + trim(handleImgs(optionContent));
                            options.push(optionContent);
                        }
                        break;
                    case "判断题":
                        options = ["A:对", "B:错"];
                        break;
                    default:
                        options = [];
                        break;
                }

                //收集题目对象
                questionList.push({
                    id: generateUniqueId() + questionId, //id-ltf3k9bz-qnqw2bq3qanswer401584769
                    content: content,
                    type: type,
                    options: options,
                    platform: "超星学习通",
                    courseName: courseName,
                });
            }

            return questionList;
        }, courseName);
    }

    if (taskObj.others.includes("手机端")) {
        questionList = await mainPage.evaluate((courseName) => {
            function handleImgs(s) {
                let imgEs = s.match(/(<img([^>]*)>)/gi);
                if (imgEs) {
                    for (let j = 0, k = imgEs.length; j < k; j++) {
                        let urls = imgEs[j].match(/http[s]?:\/\/(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+/),
                            url;
                        if (urls) {
                            url = urls[0].replace(/http[s]?:\/\//, "");
                            s = s.replaceAll(imgEs[j], url);
                        }
                    }
                }
                return s;
            }

            function trim(s) {
                return (
                    s
                        //删除多余字符串
                        .replace(/(<([^>]+)>)/gi, "") //去掉所有的html标记 <a> </a>
                        .replace(/^\d+[\.、]/, "") //删除所有1.、1、的内容
                        .replace(/\(.{3}\)/, "") //删除所有（xx题）的内容
                        .replaceAll("&nbsp;", "")
                        .replaceAll("\n", "") //删除所有的换行符
                        .replace(/\s{2,}/g, " ") //删除两个及以上的空格，保留单个空格
                        .trim()
                );
            }

            function getLastTextNode(titElement) {
                // 获取所有子节点
                const childNodes = titElement.childNodes;
                
                // 过滤并保存文本节点
                const textNodes = Array.from(childNodes).filter(node => 
                    node.nodeType === Node.TEXT_NODE && node.nodeValue.trim() !== ''
                );
                
                // 获取最后一个文本节点并返回其修剪后的内容
                if (textNodes.length > 0) {
                    return textNodes[textNodes.length - 1].nodeValue.trim();
                }
                
                return '';
            }

            //生成一个唯一id  #fanyaMarking > div.marking_content.ans-cc > div.mark_table > div.whiteDiv > div.questionLi
            function generateUniqueId() {
                return "id-" + new Date().getTime().toString(36) + "-" + Math.random().toString(36).substr(2, 9);
            }

            let questionList = [];
            // #ext-gen1001 > div.answerMain.previewPage > div > div > div.questionWrap
            let TimuList = document.querySelectorAll("#ext-gen1001 > div.answerMain.previewPage > div > div > div.questionWrap");
            for (let i = 0; i < TimuList.length; i++) {
                let timuDom = TimuList[i];

                // 题目内容
                let titDiv=timuDom.querySelector(":scope>form>div.tit")
                let questionFull =getLastTextNode(titDiv)
                let content = trim(handleImgs(questionFull));

                //题目类型
                let type = timuDom.querySelector(":scope>form>div.tit>h3").innerHTML;
                type = type.match(/^(.*?)（.*?分）$/)[1];

                //获取题目id
                let questionId = timuDom.getAttribute("data");

                //4.获取题目选项
                let options = [];
                switch (type) {
                    case "单选题":
                    case "多选题":
                        let optionList = timuDom.querySelectorAll(":scope>form>div.answerList");
                        for (let j = 0; j < optionList.length; j++) {
                            let optionKey  = optionList[j].querySelector("span").innerText;
                            let optionContent= optionList[j].querySelector("cc").innerText;
                            optionContent = optionKey + ":" + trim(handleImgs(optionContent));
                            options.push(optionContent);
                        }
                        break;
                    case "判断题":
                        options = ["A:对", "B:错"];
                        break;
                    default:
                        options = [];
                        break;
                }

                //收集题目对象
                questionList.push({
                    id: generateUniqueId() + questionId, //id-ltf3k9bz-qnqw2bq3qanswer401584769
                    content: content,
                    type: type,
                    options: options,
                    platform: "超星学习通",
                    courseName: courseName,
                });
            }

            return questionList;
        }, courseId);
    }

    return questionList;
}

module.exports = getQuestionList;
