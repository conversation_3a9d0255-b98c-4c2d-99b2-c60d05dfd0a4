let api = require('../../../../utils/api.js');
async function documentTask(infoLogger, globalStore, courseObj, cellObj, cellLogStr) {
    if (cellObj.status) {
        await infoLogger(`${cellLogStr}：当前${cellObj.icon}课件已经完成，不需要重复 《${cellObj.title}》`);
        return;
    }

    await infoLogger(`${cellLogStr}：开始${cellObj.icon}文档任务 《${cellObj.title}》`);

    let documentUrl = `https://main.ahjxjy.cn/study/html/content/studying/?courseOpenId=${courseObj.courseOpenId}&cellId=${cellObj.id}&schoolCode=${globalStore.schoolCode}`;

    //发送文档请求
    let studyingRes;
    try {
        studyingRes = await api.startDocument(globalStore, documentUrl);
    } catch {}
    await new Promise(r => setTimeout(r, 2000));

    // 遇到这种错误，所以采用trycatch：{"stack":"Error: 请求超时\n at C:\\snapshot\\courseMain\\coursePlatform\\utils\\axiosIns.js:44:35\n at async Axios.request (C:\\snapshot\\courseMain\\node_modules\\.store\\axios@1.7.9\\node_modules\\axios\\dist\\node\\axios.cjs:4247:14)\n at async Object.finishChapter (C:\\snapshot\\courseMain\\coursePlatform\\ahjxjy\\utils\\api.js:358:22)\n at async documentTask (C:\\snapshot\\courseMain\\coursePlatform\\ahjxjy\\course\\module\\runCourseModule\\courseTask\\documentTask.js:20:5)\n at async runCourseModule (C:\\snapshot\\courseMain\\coursePlatform\\ahjxjy\\course\\module\\runCourseModule\\index.js:36:21)\n at async ahjxjyCourse (C:\\snapshot\\courseMain\\coursePlatform\\ahjxjy\\course\\index.js:41:13)\n at async process.workProcessHandle (C:\\snapshot\\courseMain\\main\\workProcessHandle.js:72:29)","message":"请求超时"}
    //更改进度
    // await api.finishChapter(globalStore, courseObj, cellObj);
    try {
        await api.finishChapter(globalStore, courseObj, cellObj);
    } catch (e) {
        await infoLogger(`${cellLogStr}：${cellObj.icon}文档任务失败，原因：${e.message} 《${cellObj.title}》`, 'red');
        return;
    }

    await infoLogger(`${cellLogStr}：${cellObj.icon}文档任务完成 《${cellObj.title}》`, 'green');
}

module.exports = documentTask;
