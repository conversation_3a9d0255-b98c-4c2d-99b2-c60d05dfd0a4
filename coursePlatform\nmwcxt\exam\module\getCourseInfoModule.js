let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api.js');
let { encryptData, decryptData, decodeRequestData, encodeRequestData } = require('../../utils/aesF.js');

async function getCourseInfoModule(mainPage,taskObj, globalStore, infoLogger){
    let { courseList,cookieStr } = globalStore;
    let schoolName=taskObj.schoolurl.match(/net\/(.*)\/consol/)[1]
    //获取当前考试对象
    let examCourseObj = courseList.find(item => item.courseName == taskObj.coursename);
    if (!examCourseObj) {
        await infoLogger(`获取考试信息失败，考试列表没有当前考试科目，当前考试科目为：${taskObj.coursename}`, 'red');
        throw new Error('考试列表没有当前考试科目'); //结束进程池任务
    }

    //从考试对象中提取urserInfoVo，用户信息，后面经常用到
    let userInfoVo = examCourseObj.userInfoVo;
    //4.从服务器获取考试对象信息 examModular_exam_info.action
    // let cookieStr = await pageTools.getPageCookies(mainPage);
    let desktopInfoRes = await api.getDesktopInfo(schoolName, cookieStr);
    userInfoVo.userId = desktopInfoRes.debugData.learningUserId;
    userInfoVo.school = taskObj.schoolurl.replace('console/', '');

    // console.log(userInfoVo)
    // await new Promise(r=>setTimeout(r,1000000));
    // let params=encryptData(JSON.stringify(userInfoVo));
    // console.log(`https://learning.wencaischool.net/openlearning/separation/exam/index.html?info=${params}`)
    // await new Promise(r=>setTimeout(r,1000000));


    let examModuleDate = { user_info: JSON.stringify(userInfoVo) };
    let preUrl = 'https://learning.wencaischool.net/openlearning/separation/exam/index.html?';
    let courseExamUrl = encodeRequestData({ info: userInfoVo });
    let examUrl = preUrl + courseExamUrl;
    let examModuleRes = await api.examInfo(examModuleDate, examUrl);
    let examModuleStr = decryptData(examModuleRes.data);
    // console.log('examModuleStr',examModuleStr)
    let examModuleArr = JSON.parse(examModuleStr);
    let examModuleObj = examModuleArr[0];

    // console.log(JSON.stringify(examModuleObj))
    // await new Promise(r=>setTimeout(r,1000000));

    //处理考试对象信息
    if (examModuleObj.buttonType == 3) {
        await infoLogger(`获取考试信息失败，没有考试权限，当前分数为：${examModuleObj.finalScore}`, 'red');
        throw new Error('没有考试权限'); //结束进程池任务
    }else{
        await infoLogger(`获取考试信息成功`, 'green');
    }



    globalStore.userInfoVo = userInfoVo;
    globalStore.examModuleObj = examModuleObj;
    globalStore.desktopInfoRes = desktopInfoRes;
    globalStore.examModuleDate = examModuleDate;
    globalStore.schoolName=schoolName
    globalStore.examUrl=examUrl
}

module.exports = getCourseInfoModule;
