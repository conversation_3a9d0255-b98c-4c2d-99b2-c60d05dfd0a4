{"name": "course<PERSON>in", "version": "1.0.0", "description": "", "main": "server.js", "bin": "server.js", "scripts": {"seqq": "sequelize-auto -c ./config/sequelize-auto.config.js"}, "pkg": {"assets": ["node_modules/axios/dist/node/axios.cjs", "node_modules/puppeteer-extra-plugin-stealth/", "node_modules/.store/@ffprobe-installer+ffprobe@1.4.2/node_modules/@ffprobe-installer/win32-x64/**", "node_modules/.store/@ffprobe-installer+ffprobe@1.4.2/node_modules/@ffprobe-installer/win32-x64/ffprobe.exe", "node_modules/.store/@ffprobe-installer+ffprobe@1.4.2/node_modules/@ffprobe-installer/ffprobe/node_modules/@ffprobe-installer/win32-x64/ffprobe.exe", "node_modules/@jimp/**", "node_modules/.store/jimp@1.6.0"]}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@ffprobe-installer/ffprobe": "^2.1.2", "@sinonjs/text-encoding": "^0.7.3", "axios": "^1.7.7", "axios-cookiejar-support": "^5.0.3", "chalk": "^4.1.2", "cheerio": "^1.0.0", "cookie": "^1.0.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.13", "execa": "^9.4.1", "ffprobe-static": "^3.1.0", "fontkit": "^2.0.4", "form-data": "^4.0.0", "get-video-duration": "^4.1.0", "highlight.js": "^11.10.0", "html-entities": "^2.5.2", "https-proxy-agent": "^7.0.5", "is-stream": "^4.0.1", "jimp": "^1.6.0", "jsdom": "^24.1.1", "marked": "^15.0.3", "music-metadata": "^10.5.1", "mysql2": "^3.11.0", "online_mp4_duration": "^2.0.8", "puppeteer": "^23.1.1", "puppeteer-extra": "^3.3.6", "puppeteer-extra-plugin-anonymize-ua": "^2.4.6", "puppeteer-extra-plugin-stealth": "^2.11.2", "puppeteer-extra-plugin-user-data-dir": "^2.4.1", "puppeteer-extra-plugin-user-preferences": "^2.4.1", "qs": "^6.13.0", "readline": "^1.3.0", "sequelize": "^6.37.3", "sequelize-auto": "^0.8.8", "tough-cookie": "^5.0.0", "user-agents": "^1.1.340", "uuid": "^10.0.0", "ws": "^8.18.0"}}