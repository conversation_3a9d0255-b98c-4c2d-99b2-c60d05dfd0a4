let dayjs = require('dayjs');
let Model = require('../../config/sequelize.config.js');

// 辅助函数：转义HTML特殊字符
function escapeHtml(unsafe) {
    return unsafe
         .replace(/&/g, "&amp;")
         .replace(/</g, "&lt;")
         .replace(/>/g, "&gt;")
         .replace(/"/g, "&quot;")
         .replace(/'/g, "&#039;");
}

//生成一个记录信息日志的函数【所有模块都能用到】
exports.getCourseInfoLogger = function (taskId, showLog = true) {
    return async function (message, color = 'black') {
        let date = new Date();
        let time = dayjs(date).format('MM-DD HH:mm:ss');
        if (showLog) console.log(`[${time}]： ${message}`);
        let safeMessage = escapeHtml(message); // 转义message内容
        let msgTag = `<p style="color:${color}">[${time}]：${safeMessage}</p>`;
        await Model.course_info_log.create({
            course_id: taskId,
            log_message: msgTag,
            log_time: date,
            timestamp: new Date().getTime(),
        });
    };
};

//生成一个记录信息日志的函数【所有模块都能用到】
exports.getExamInfoLogger = function (taskId, showLog = true) {
    return async function (message, color = 'black') {
        let date = new Date();
        let time = dayjs(date).format('MM-DD HH:mm:ss');
        if (showLog) console.log(`[${time}]： ${message}`);
        let safeMessage = escapeHtml(message); // 转义message内容
        let msgTag = `<p style="color:${color}">[${time}]：${safeMessage}</p>`;
        await Model.exam_info_log.create({
            exam_id: taskId,
            log_message: msgTag,
            log_time: date,
            timestamp: new Date().getTime(),
        });
    };
};

//获取页面cookie 【所有模块都能用到】
exports.getPageCookies = async function getPageCookies(page) {
    // 获取所有的 cookies
    const cookies = await page.cookies();
    // 将 cookies 格式化为一个字符串
    return cookies.map(cookie => `${cookie.name}=${cookie.value}`).join('; ');
};

//获取根据cookie的key获取cookie的value 【学习通考试】
exports.getCookieValue = function getCookieValue(cookie, key) {
    let value = '';
    cookie.split('; ').forEach(item => {
        if (item.startsWith(key)) {
            value = item.split('=')[1];
        }
    });
    return value;
};

// 路径和查询字符串对象拼接成完整的url 【学习通网课】
exports.buildUrl = (base, params) => {
    const queryString = Object.keys(params)
        .map(key => `${encodeURIComponent(key)}=${encodeURIComponent(params[key])}`)
        .join('&');
    return `${base}?${queryString}`;
};

// 根据url获取查询字符串对象 
exports.getQueryString = (url) => {
    const urlObj = new URL(url);
    const queryString = urlObj.searchParams;
    return Object.fromEntries(queryString);
};


// 重写 page.goto方法 柠檬文采学堂作业用到这个模块
exports.gotoWithRetry = async function gotoWithRetry(page, url, options = {}, retries = 1, infoLogger = async () => Promise.resolve()) {
    for (let attempt = 0; attempt <retries; attempt++) {
        try {
            await page.goto(url, options);
            return; // 如果成功，退出函数
        } catch (error) {
            await infoLogger(`页面打开失败，2分钟后重试,url为：${url}`, 'gray');
            await new Promise(r => setTimeout(r, 2 * 60 * 1000));
            if (attempt == retries) {
                await infoLogger(`${retries}次尝试后依然失败：${url}`, 'red');
                throw error; // 所有尝试都失败后，抛出错误
            }
        }
    }
};

// 内存监控 国家开放大学课程，runCouseModule用到这个模块
let v8 = require('v8'); // 引入 v8 模块
exports.monitorMemoryUsage = function (process) {
    let memoryUsage = process.memoryUsage();
    // let usageReport = {
    //     rss: (memoryUsage.rss / 1024 / 1024).toFixed(2), // 常驻内存大小
    //     heapTotal: (memoryUsage.heapTotal / 1024 / 1024).toFixed(2), // 动态分配的可用内存
    //     heapUsed: (memoryUsage.heapUsed / 1024 / 1024).toFixed(2), // 已使用的堆大小
    //     external: (memoryUsage.external / 1024 / 1024).toFixed(2), // V8引擎内部的C++对象占用的内存
    //     arrayBuffers: (memoryUsage.arrayBuffers / 1024 / 1024).toFixed(2), // 分配给ArrayBuffers的内存，包括Node.js Buffer
    // };
    let rss = (memoryUsage.rss / 1024 / 1024).toFixed(2);
    let heapUsed = (memoryUsage.heapUsed / 1024 / 1024).toFixed(2);
    let heapSizeLimit = (v8.getHeapStatistics().heap_size_limit / 1024 / 1024).toFixed(2);

    // return `rss:${rss}Mb,heapUsed:${heapUsed}Mb,heapSizeLimit:${heapSizeLimit}Mb`
    return {
        rss,
        heapUsed,
        heapSizeLimit,
    };
};

/**
 * 返回一个位于 [min, max] 之间的随机整数。
 *
 * @param {number} min - 随机数的最小值（含）
 * @param {number} max - 随机数的最大值（含）
 * @returns {number}    - 生成的随机整数
 */
exports.getRandomInt = function getRandomInt(min, max) {
    // 确保 min、max 为整数
    min = Math.ceil(min);
    max = Math.floor(max);

    // Math.random() 产生 [0, 1) 的随机数
    // Math.random() * (max - min + 1)：相当于把随机小数的范围从 [0, 1) 转换到 [0, max - min + 1).
    return Math.floor(Math.random() * (max - min + 1)) + min;
};

/**
 * 将字符串中的 http:// 替换为 https://
 * @param {string} str - 原始字符串
 * @return {string} - 替换后的字符串
 */
function convertHttpToHttps(str) {
    // 使用正则，匹配所有 http://，替换为 https://
    return str.replace(/http:\/\//g, 'https://');
}

/**
 * 将字符串中的 http:// 替换为 https://
 * @param {string} str - 原始字符串
 * @return {string} - 替换后的字符串
 * 相关模块：柠檬文采学堂
 */
function convertHttpToHttps(str) {
    // 使用正则，匹配所有 http://，替换为 https://
    return str.replace(/http:\/\//g, 'https://');
}