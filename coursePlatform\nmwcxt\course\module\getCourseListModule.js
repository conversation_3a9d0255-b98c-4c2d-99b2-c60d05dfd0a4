let api = require('../../utils/api.js');

async function getCourseListModule(infoLogger, globalStore, taskObj) {
    //1.获取页面数据，获取cookie
    let mainPageCookieStr = globalStore.mainPageCookieStr;
    let schoolName = taskObj.schoolurl.match(/net\/(.*)\/consol/)[1];

    //2.从服务器获取desktopInfo
    let desktopInfoRes = await api.getDesktopInfo(schoolName, mainPageCookieStr);

    //3.从服务器获取termCode
    let termCode;
    if (taskObj.term) {
        termCode = taskObj.year+''+(taskObj.term-1);
    } else {
        let getTermRes = await api.getTerm(schoolName, mainPageCookieStr);
        getTermRes.debugData.forEach(termObj => {
            if (termObj.isCurrentTerm) {
                termCode = termObj.termCode;
                return;
            }
        });
    }

    globalStore.termCode = termCode;
    globalStore.desktopInfoRes = desktopInfoRes;
    globalStore.schoolName = schoolName;

    //4.从服务器获取课程列表
    let getStudentLearnInforRes = await api.getStudentLearnInfo(schoolName, mainPageCookieStr, termCode);
    // console.log('termCode',termCode)
    let courseList = getStudentLearnInforRes.debugData.courseInfoList;
    courseList.forEach(item => {
        // \u003d 是等号的 Unicode 编码。 \u0026 是&的Unicode编码
        item.filePath = decodeURIComponent(item.filePath.replace(/\\u003d/g, '=').replace(/\\u0026/g, '&'));
    });

    if (taskObj.coursename) {
        courseList = courseList.filter(item => {
            return item.courseName === taskObj.coursename;
        });
    }

    if (courseList.length === 0) {
        await infoLogger('获取课程列表失败，课程列表为空');
        throw new Error('获取课程列表失败，课程列表为空');
    }

    await infoLogger(`获取课程列表成功，共有${courseList.length}门课程`);
    globalStore.courseList = courseList;
}

module.exports = getCourseListModule;
