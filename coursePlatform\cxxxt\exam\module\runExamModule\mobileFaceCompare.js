let axiosIns = require('../../../utils/axios');
let FormData = require('form-data');
let path = require('path');
let Model = require('../../../../../config/sequelize.config.js');

let pageTools = require('../../../../utils/pageTools.js');

async function mobileFaceCompare(mainPage, taskObj, courseObj, infoLogger) {
    await infoLogger('当前考试需要人脸识别，接下来进行人脸识别');

    // 从当前页面中获取数据

    // 获取当前页面cookies
    let cookieStr = await pageTools.getPageCookies(mainPage);

    // 1.构建formData
    let formData = new FormData();

    // 把照片数据添加到formData
    let picRes = await Model.photo.findOne({
        where: {
            username: taskObj.username,
            platform: taskObj.platform,
            school: taskObj.schoolname,
        },
    });
    picRes = picRes.get();
    let fileBuffer = Buffer.from(picRes.photo_base64, 'base64');
    formData.append('file', fileBuffer, {
        filename: new Date().getTime() + '.jpg',
        contentType: 'image/jpeg',
    });
    // 把userId添加到formData
    let parsedUrl = new URL(courseObj.examUrl);
    let queryParams = Object.fromEntries(parsedUrl.searchParams);
    formData.append('puid', queryParams.userId);

    // 获取用户token，用于拼接上传url
    let userValidRes = await axiosIns({
        method: 'get',
        url: 'https://pan-yz.chaoxing.com/api/token/uservalid',
        headers: {
            cookie: cookieStr,
        },
    });
    let userValid = userValidRes._token;

    // 3.开始上传
    let updateBase64Res;
    try {
        updateBase64Res = await axiosIns({
            url: `https://pan-yz.chaoxing.com/upload?_token=${userValid}`,
            method: 'post',
            data: formData,
            headers: {
                'Content-Type': 'multipart/form-data',
                cookie: cookieStr,
            },
        });
    } catch (error) {
        await infoLogger('上传照片出错', 'red');
        throw new Error(error); //结束进程池任务
    }
    if (false) {
        let updateBase64Res = {
            result: true,
            msg: 'success',
            crc: '01ec3d785f12a8752289afa6f4ee86b0',
            objectId: '3f93ca13043021d8b4b97851f86774cf',
            resid: 1021055930115125248,
            puid: 284968523,
            data: {
                disableOpt: false,
                resid: 1021055930115125248,
                encryptedId: '5dfd014aa703e4d2d777f0eff9ee126bafb110f14522640b',
                crc: '01ec3d785f12a8752289afa6f4ee86b0',
                puid: 284968523,
                isfile: true,
                pantype: 'USER_PAN',
                size: 46930,
                name: '1721368302119.jpg',
                objectId: '3f93ca13043021d8b4b97851f86774cf',
                restype: 'RES_TYPE_NORMAL',
                uploadDate: 1721368304029,
                modifyDate: 1721368304029,
                uploadDateFormat: '2024-07-19',
                residstr: '1021055930115125248',
                suffix: 'jpg',
                preview: 'https://pan-yz.chaoxing.com/preview/showpreview_1021055930115125248.html?v=1721368304050&enc=2a1d9903a04b6ba9763ea6cb02325f8d',
                thumbnail: 'https://pan-yz.chaoxing.com/thumbnail/origin/3f93ca13043021d8b4b97851f86774cf?type=img',
                creator: 284968523,
                duration: 0,
                isImg: true,
                isOffice: false,
                isMirror: false,
                previewUrl: 'https://p.ananas.chaoxing.com/star3/origin/3f93ca13043021d8b4b97851f86774cf.jpg',
                filetype: '',
                filepath: '',
                sort: 0,
                topsort: 0,
                resTypeValue: 3,
                extinfo: '',
            },
        };
    }

    //3.处理上传结果
    if (!updateBase64Res.result) {
        await infoLogger('上传照片失败', 'red');
        throw new Error('上传照片失败'); //结束进程池任务
    } else {
        await infoLogger('上传照片成功', 'green');
    }

    //4.开始人脸识别
    // 获取课程信息
    let pageObj = await mainPage.evaluate(() => {
        var classId = $('#classId').val();
        var courseId = $('#courseId').val();
        var cpi = $('#cpi').val();
        var testPaperId = $('#testPaperId').val();
        var testUserRelationId = $('#testUserRelationId').val();
        return { classId, courseId, cpi, testPaperId, testUserRelationId };
    });

    // var tk = cxcid + '_' + cxtime + '_' + signToken;
    let pareObj = {
        relationid: pageObj.testPaperId,
        courseId: pageObj.courseId,
        classId: pageObj.classId,
        currentFaceId: updateBase64Res.objectId,
        liveDetectionStatus: '0',
        collectStatus: '1',
        // tk: '9f02261b4b0d948e2ddcff2e7f5b0b6b284968523_1740571577309_78dbc2bc67331c29ddab0cc2c7aeb967',
        cpi: pageObj.cpi,
    };
    let pareUrl = pageTools.buildUrl('https://mooc1-api.chaoxing.com/exam-ans/exam/phone/to-face-compare', pareObj);

    // 跳转到人脸识别页面
    // https://mooc1-api.chaoxing.com/exam-ans/exam/phone/to-face-compare?courseId=226495783&classId=114276401&cpi=327328741&relationid=6547469&currentFaceId=5d29edf0c797198418492c25c6054bd5&liveDetectionStatus=0&collectStatus=1&tk=9f02261b4b0d948e2ddcff2e7f5b0b6b284968523_1740571577309_78dbc2bc67331c29ddab0cc2c7aeb967
    // let faceCompareUrl = `https://mooc1-api.chaoxing.com/exam-ans/exam/phone/to-face-compare?courseId=${courseId}&classId=${classId}&cpi=${cpi}&relationid=${testPaperId}&currentFaceId=${updateBase64Res.objectId}&liveDetectionStatus=1`;
    await mainPage.goto(pareUrl, { waitUntil: 'networkidle0' });
    await new Promise(r => setTimeout(r, 3 * 1000));
    cookieStr = await pageTools.getPageCookies(mainPage);

    // var tk = cxcid + '_' + cxtime + '_' + signToken;
    let faceCompareObj = {
        relationid: pageObj.testPaperId,
        courseId: pageObj.courseId,
        classId: pageObj.classId,
        currentFaceId: updateBase64Res.objectId,
        liveDetectionStatus: '0',
        collectStatus: '1',
        // tk: '9f02261b4b0d948e2ddcff2e7f5b0b6b284968523_1740571577309_78dbc2bc67331c29ddab0cc2c7aeb967',
        cpi: pageObj.cpi,
        _d: 'mobile',
        _signcode: '3',
        _signc: '0',
        _signe: '3-1',
        signk: '',
        cxcid: '',
        cxtime: '',
        signt: '',
    };
    
    let faceCompareUrl = pageTools.buildUrl('https://mooc1-api.chaoxing.com/exam-ans/exam/phone/face-compare', faceCompareObj);
    //向服务器发送人脸识别请求 
    let faceCompareRes;
    try {
        faceCompareRes = await axiosIns({
            // url: 'https://mooc1-api.chaoxing.com/exam-ans/exam/phone/face-compare',
            url: `https://mooc1-api.chaoxing.com/exam-ans/exam/phone/face-compare?relationid=6547469&courseId=226495783&classId=114276401&currentFaceId=${updateBase64Res.objectId}&liveDetectionStatus=0&collectStatus=1&tk=9f02261b4b0d948e2ddcff2e7f5b0b6b284968523_1740571577309_78dbc2bc67331c29ddab0cc2c7aeb967&cpi=327328741&_d=mobile&_signcode=3&_signc=0&_signe=3-1&signk=&cxcid=&cxtime=&signt=`,
            method: 'get',
            // params: {
            //     relationid: pageObj.testPaperId,
            //     courseId: pageObj.courseId,
            //     classId: pageObj.classId,
            //     currentFaceId: updateBase64Res.objectId,
            //     liveDetectionStatus: '0',
            //     collectStatus: '1',
            //     tk: '9f02261b4b0d948e2ddcff2e7f5b0b6b284968523_1740571577309_78dbc2bc67331c29ddab0cc2c7aeb967', //?
            //     cpi: pageObj.cpi,
            //     _d: 'mobile',
            //     _signcode: '3',
            //     _signc: '0',
            //     _signe: '3-1',
            //     signk: '',
            //     cxcid: '',
            //     cxtime: '',
            //     signt: '',
            // },
            headers: {
                cookie: cookieStr,
                'User-Agent':
                    'Mozilla/5.0 (Linux; Android 11; MI 9 Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/133.0.6943.121 Mobile Safari/537.36 (schild:3bdb747d7e6fb03a5cf7cec875aa50e7) (device:MI 9) Language/zh_CN com.chaoxing.mobile/ChaoXingStudy_3_6.4.8_android_phone_10834_264 (@Kalimdor)_91de89da3d6f40fea30d894695bab73c',
            },
        });
    } catch (error) {
        await infoLogger('人脸识别服务器出错', 'red');
        throw new Error(error); //结束进程池任务
    }
    if (false) {
        faceCompareRes = {
            data: {
                msg: 'SUCCESS',
                score: 85.04,
                code: 1,
                facekey: '5ae33a7bca96e6a49243ed5d695b17db',
                origin: { msg: '调用成功', code: 200, data: { score: 0.8504, hitStatus: 1 } },
                threshold: 65,
                logId: '7592643653dcb32b086c0cd14672674a',
                detail: {
                    faceObjectId: 'f310e339f287f0e66024a0f2f490e375',
                    collectObjectId: '423f7691f63a747a0de9a65534fdaba6',
                    liveDetectionStatus: 1,
                    score: '85.04',
                },
                enc: '259b16af42a4604e2cb90f8b12efa2d0',
                isSimilar: true,
            },
            status: true,
        };
    }

    // 等待30秒
    await new Promise(r => setTimeout(r, 3000 * 1000));

    //处理服务器结果
    if (faceCompareRes.data.code == 1 && faceCompareRes.data.isSimilar) {
        await infoLogger('人脸识别成功', 'green');
    } else {
        await infoLogger('人脸识别失败', 'red');
        throw new Error('人脸识别失败'); //结束进程池任务
    }

    let examUrlParams = {
        // _v: "0.8023971542080244",
        // captchavalidate: "",
        classId: classId,
        courseId: courseId,
        cpi: cpi,
        examAnswerId: testUserRelationId,
        examId: testPaperId,
        // faceDetection: "1",
        // faceDetectionResult: `{"collectedFaceId":"${faceCompareRes.data.detail.collectObjectId}","currentFaceId":"${faceCompareRes.data.detail.faceObjectId}"}`,
        facekey: faceCompareRes.data.facekey,
        // imei: "693480ab70044c5e8137c1de8f594768",
        // jt: "1",
        // keyboardDisplayRequiresUserAction: "1",
        // source: "0",
    };

    // 将对象转换为查询字符串
    let queryString = Object.entries(examUrlParams)
        .map(([key, value]) => `${encodeURIComponent(key)}=${encodeURIComponent(value)}`)
        .join('&');

    let examBaseUrl = 'https://mooc1-api.chaoxing.com/exam-ans/exam/phone/start';

    // 将查询字符串添加到 URL
    let examUrl = `${examBaseUrl}?${queryString}`;

    return examUrl;
    // await mainPage.goto(examUrl, { waitUntil: "networkidle0" });
    // await new Promise((r) => setTimeout(r, 20000000));
}

module.exports = mobileFaceCompare;
