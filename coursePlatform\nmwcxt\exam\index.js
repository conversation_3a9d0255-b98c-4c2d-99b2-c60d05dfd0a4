// ========= 组成模块 =========
let signinModule = require('../course/module/signinModule');
let getCourseListModule = require('./module/getCourseListModule.js');
let getCourseInfoModule = require('./module/getCourseInfoModule.js');
let faceCompareModule = require('./module/faceCompareModule.js');
let permissionModule = require('./module/permissionModule.js');
let collectQuestionModule = require('./module/collectQuestionModule.js');
let saveExamModule = require('./module/saveExamModule.js');
let submitExamModule = require('./module/submitExamModule.js');
let endModule = require('./module/endModule.js');

// 工具模块
let pageTools = require('../../utils/pageTools.js');
let getMainPage = require('../../utils/getMainPage.js');

async function nmwcxtExam(taskObj, taskOptions) {
    // 全局变量
    // taskOptions.isHeadless=false // 强制开启窗口
    //创建页面实例
    let { mainPage, browser } = await getMainPage(taskOptions);
    let globalStore = {};
    let infoLogger = pageTools.getExamInfoLogger(taskObj.id, false);
    let warningMessage = '';

    // 主要进程
    try {
        // 一、登录 mainPageCookieStr,mainUrl
        await signinModule(infoLogger, mainPage, taskObj, globalStore);

        if (!process.env.HAS_PERMISSION) {
            return;
        }

        // 二、获取考试列表 cookieStr,courseList
        await getCourseListModule(mainPage, taskObj, globalStore, infoLogger);

        // 三、获取当前考试课程信息userInfoVo examModuleObj desktopInfoRes,examModuleDate,schoolName,examUrl
        await getCourseInfoModule(mainPage, taskObj, globalStore, infoLogger);

        // 可以考虑跳过人脸识别，直接进入考试

        // 四、人脸识别
        await faceCompareModule(taskObj, globalStore, infoLogger);

        // 五、考前承诺
        await permissionModule(globalStore, infoLogger);

        // 六、获取题目列表 收集题目 itemInfoList
        await collectQuestionModule(globalStore, taskObj, infoLogger);

        // 七、保存试卷
        await saveExamModule(globalStore, infoLogger);

        // 八、提交试卷
        await submitExamModule(globalStore, infoLogger);

        // 九、结束
        let finalResult = await endModule(globalStore, taskObj, infoLogger);
        await browser.close();
        return { warningMessage, finalResult };
    } catch (error) {
        await browser.close();
        browser = null;
        throw error;
    } finally {
        if (browser) {
            await browser.close();
            browser = null;
        }
    }
}

module.exports = nmwcxtExam;
