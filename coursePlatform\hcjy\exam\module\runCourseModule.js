let api = require('../../utils/api.js');
let pageTools = require('../../../utils/pageTools.js');
let Model = require('../../../../config/sequelize.config.js');
let { handleQuestion, collectQuestion } = require('../../utils/handleQuestion.js');
let axios = require('axios');

// 把对象拼接成查询字符串，
function convertToQueryString(obj) {
    const paperAnswerResultJSON = JSON.stringify(obj.paperAnswerResult);
    const encodedPaperAnswerResult = encodeURIComponent(paperAnswerResultJSON);

    const queryParams = [];
    for (const key in obj) {
        if (key !== 'paperAnswerResult') {
            queryParams.push(`${key}=${obj[key]}`);
        }
    }
    queryParams.push(`paperAnswerResult=${encodedPaperAnswerResult}`);

    return queryParams.join('&');
}

// 提取Url中的查询字符串，返回对象
function parseQueryString(url) {
    // 创建一个空对象来存储查询参数
    const queryParams = {};
    // 尝试从 URL 中提取查询字符串
    const queryString = url.split('?')[1];
    // 如果没有查询字符串，直接返回空对象
    if (!queryString) {
        return queryParams;
    }
    // 将查询字符串分割成键值对
    const pairs = queryString.split('&');
    // 遍历每个键值对
    for (const pair of pairs) {
        // 分割键和值
        const [key, value] = pair.split('=');
        // 解码键和值，并将它们添加到对象中
        queryParams[decodeURIComponent(key)] = decodeURIComponent(value || '');
    }
    return queryParams;
}

// 把path和对象，拼接成完成的url
function buildUrlWithQuery(url, queryParams = {}) {
    // 创建一个 URL 对象
    const urlObj = new URL(url);
    // 遍历查询参数对象
    Object.entries(queryParams).forEach(([key, value]) => {
        // 使用 URLSearchParams 的 append 方法添加查询参数
        urlObj.searchParams.append(encodeURIComponent(key), encodeURIComponent(value));
    });
    // 返回完整的 URL 字符串
    return urlObj.toString();
}

async function runCourseModule(infoLogger, mainPage, courseList, globalStore, taskObj) {
    mainPage.resolve = {};
    mainPage.apiInfo = {};
    mainPage.promise = {};

    // 通过拦截的方式获取题目列表
    mainPage.on('requestfinished', async request => {
        let url = request.url();

        if (url.includes('https://exam.chinaedu.net/oxer/app/ots/TestActivity/StartAnswerPaperWithPhoto')) {
            //处理响应体
            let response = await request.response();
            let responseData = await response.json();

            mainPage.apiInfo.getQuestionList = {
                request: {
                    method: request.method(),
                    url: request.url(),
                    headers: request.headers(),
                    postData: request.postData(),
                    resourceType: request.resourceType(),
                },
                response: responseData,
            };
            mainPage.resolve.getQuestionListResolve();
        }
    });

    let temUrl = mainPage.url();

    for (let i = 0; i < courseList.length; i++) {
        let courseObj = courseList[i];
        let examLogStr = `[课程${i + 1}/${courseList.length}] <${courseObj.activityName}>`;
        await infoLogger(`${examLogStr} 考试开始`, 'blue');

        // 通过分数
        let passScore = 80;

        async function enterExamWithoutPhoto(examUrl) {
            // 跳转到考试url界面 这里涉及多次重定向，多次重定向不能用 waitUntil: 'networkidle0'，
            await mainPage.goto(examUrl, { waitUntil: 'networkidle0' });
            await new Promise(r => setTimeout(r, 2000));
            try {
                await mainPage.waitForSelector('#tika_helpLink');
                await infoLogger(`进入考试界面成功`);
            } catch (error) {
                await infoLogger(`${examLogStr} 进入考试界面失败 ${examUrl}`, 'red');
                throw new Error('进入考试界面失败');
            }
        }

        async function enterExamWithPhoto(examUrl) {
            // 写在前面，本来正常应该用 api.getQuestionListWithPhoto获取数据，但是这个请求只能请求一次，后续请求全部失败，我也不知道为什么。所以没办法，只能用拦截了
            mainPage.getListPromise = new Promise(resolve => {
                mainPage.resolve.getQuestionListResolve = resolve;
            });

            // 跳转到考试验证界面，不是考试界面，但是这个界面可以获取cookie
            await mainPage.goto(examUrl, { waitUntil: 'networkidle0' });

            // 本场考前终端环境验证 这里要跳转两次页面，所以等待6秒
            await new Promise(r => setTimeout(r, 6000));
            try {
                await mainPage.click('#ok1');
                await mainPage.click('#ok2');
                await mainPage.click('#okBtn');
                await mainPage.click('#okBtn');
                await new Promise(r => setTimeout(r, 10 * 1000));
            } catch (error) {}

            // await new Promise(r => setTimeout(r, 3000000));

            // 非法操作处理 确定按钮：#btn_ok 非法操作div #confirm_dialog > div > div.IsDel_Title
            try {
                // #confirm_dialog > div > div.IsDel_Title > div
                await mainPage.waitForSelector('#confirm_dialog > div > div.IsDel_Title > div', { timeout: 3000 });
                await infoLogger(`${examLogStr} 非法操作，重新进入`, 'red');
                mainPage.getListPromise = new Promise(resolve => {
                    mainPage.resolve.getQuestionListResolve = resolve;
                });
                await mainPage.reload({ waitUntil: 'networkidle0' });
                await new Promise(r => setTimeout(r, 10 * 1000));
            } catch (error) {}

            try {
                await mainPage.waitForSelector('#tika_helpLink');
                await infoLogger(`进入考试界面成功`);
            } catch (error) {
                await infoLogger(`${examLogStr} 进入考试界面失败 ${examUrl}`, 'red');
                throw new Error('进入考试界面失败');
            }

            //等待拦截成功
            await mainPage.promise.getListPromise;
            let getQuestionListRes = mainPage.apiInfo.getQuestionList.response;

            mainPage.getListPromise = new Promise(resolve => {
                mainPage.resolve.getQuestionListResolve = resolve;
            });

            return getQuestionListRes;
        }

        async function doExamWithPhoto(examUrl) {
            // 跳转到考试url界面
            let getQuestionListRes = await enterExamWithPhoto(examUrl);

            // 获取页面cookie
            let pageCookieStr = await pageTools.getPageCookies(mainPage);

            // 跳转到其他页面，避免抓拍
            await mainPage.goto('https://nwujxjy.sccchina.net/student/', {
                waitUntil: 'networkidle0',
            });

            // 进行人脸识别
            await infoLogger(`${examLogStr} 进行人脸识别`);
            let imgBase64 = await Model.photo.findOne({
                where: {
                    username: taskObj.username,
                    school: taskObj.schoolname,
                    platform: taskObj.platform,
                },
            });
            imgBase64 = imgBase64.get();
            imgBase64 = imgBase64.photo_base64;
            let entryFaceConfig = {
                arrangementId: getQuestionListRes.data.arrangementId,
                imageStr: imgBase64,
                extName: 'png',
                answerPaperId: getQuestionListRes.data.answerPaperRecordId,
                type: 1,
                basePhoto: getQuestionListRes.data.userFaceImageUrl,
            };

            let entryFaceCompareRes = await api.faceCompare(pageCookieStr, entryFaceConfig);
            if (entryFaceCompareRes.status == 1) {
                await infoLogger(`${examLogStr} 人脸识别成功`, 'green');
            } else {
                await infoLogger(`${examLogStr} 人脸识别失败,${JSON.stringify(entryFaceCompareRes)}`, 'red');
                throw new Error('人脸识别失败');
            }

            //格式化题目
            let submitData = await handleQuestion(getQuestionListRes.data, courseObj.activityName, infoLogger);

            await infoLogger(`${examLogStr} 开始保存试卷`);
            submitData.temp = '1';
            let saveRes = await api.saveAssignment(pageCookieStr, submitData, infoLogger);
            if (saveRes.status == 1) {
                await infoLogger(`${examLogStr} 试卷保存成功，等待10分钟`);
            } else {
                await infoLogger(`${examLogStr} 试卷保存失败`, 'red');
            }

            await new Promise(r => setTimeout(r, 10 * 60 * 1000));

            // 考试统一都不收集题目了，如果分数不够，再考一次，会自动收集题目，防止一次性把考试用完

            // // 等待90钟后，开始收集题目
            // let waitTime = 100;
            // for (let i = 0; i < waitTime; i++) {
            //     await infoLogger(`${examLogStr} 等待${waitTime - i}分钟后收集题目`);
            //     await new Promise(r => setTimeout(r, 60 * 1000));
            // }

            // //回答问题，这里并不是真的提交试卷，只是为了获取试卷ID
            // // 不能直接交卷 1.有时间限制 2.需要人脸识别 这两个问题解决之后才可以直接交卷，否则就等时间到了自动交卷
            // let submitRes = await api.submitAssignment(pageCookieStr, submitData);
            // let answerPaperRecordId = submitRes.data.answerPaperRecordId;

            // // 获取已答试卷url
            // let examEndUrlRes = await api.getExamEndUrl(pageCookieStr, courseObj, answerPaperRecordId);
            // let examEndUrl = examEndUrlRes.message;
            // await infoLogger(`${examLogStr} 试卷记录为：${examEndUrl}`);

            // await mainPage.goto(examEndUrl, { waitUntil: 'networkidle0' });
            // await new Promise(r => setTimeout(r, 10*1000));
            // let pageUrlCookieStr = await pageTools.getPageCookies(mainPage);
            // let paperRes = await api.viewPaper(pageUrlCookieStr, answerPaperRecordId);
            // let { collectCount, updateCont } = await collectQuestion(paperRes, courseObj.courseVersionID);

            // // 结束
            // await infoLogger(
            //     `${examLogStr} 题目收集结束，共收集${collectCount}道题目，共更新${updateCont}道题目，答题记录：https://exam.chinaedu.net/oxer/page/ots/OTS-UniverDetail.html?AnswerId=${answerPaperRecordId}`
            // );

            return;
        }

        async function doExamWithoutPhoto(examUrl) {
            // 跳转到考试url界面，之前跳转失败是因为浏览器窗口尺寸跟页面视口尺寸不匹配，让平台认为我开起了开发者控制台
            await enterExamWithoutPhoto(examUrl);

            // 获取页面cookie
            let pageCookieStr = await pageTools.getPageCookies(mainPage);

            // 跳转到其他页面，避免抓拍
            await mainPage.goto('https://nwujxjy.sccchina.net/student/', {
                waitUntil: 'networkidle0',
            });

            //获取试题列表
            let getQuestionListRes = await api.getQuestionList(pageCookieStr, courseObj);
            // let psOutputDto = getQuestionListRes.data.paper.psOutputDto;
            // console.log('getQuestionListRes',getQuestionListRes)

            //格式化题目
            let submitData = await handleQuestion(getQuestionListRes.data, courseObj.activityName, infoLogger);

            // 等待20分钟
            let waitTime = 30;
            for (let i = 0; i < waitTime; i++) {
                await infoLogger(`${examLogStr} 等待${waitTime - i}分钟后交卷`);
                await new Promise(r => setTimeout(r, 60 * 1000));
            }

            //回答问题，提交试卷
            let submitRes = await api.submitAssignment(pageCookieStr, submitData);
            // console.log("submitRes", submitRes);
            await infoLogger(`${examLogStr} 答题结束，分数：${submitRes.data.currentScore}`);

            // 考试统一都不收集题目了，如果分数不够，再考一次，会自动收集题目，防止一次性把考试用完

            // // 收集题目
            // let answerPaperRecordId = submitRes.data.answerPaperRecordId;

            // // 获取已答试卷url
            // let examEndUrl = await api.getExamEndUrl(pageCookieStr, courseObj, answerPaperRecordId);
            // await infoLogger(`${examLogStr} 试卷记录为：${examEndUrl}`);

            // //查看考试试卷
            // // let paperUrl = `https://exam.chinaedu.net/oxer/page/ots/OTS-UniverDetail.html?AnswerId=${answerPaperRecordId}`;
            // await mainPage.goto(examEndUrl, { waitUntil: 'networkidle0' });
            // await new Promise(r => setTimeout(r, 10000));

            // //收集题目
            // let pageUrlCookieStr = await pageTools.getPageCookies(mainPage);
            // let paperRes = await api.viewPaper(pageUrlCookieStr, answerPaperRecordId);
            // let { collectCount, updateCont } = await collectQuestion(paperRes, courseObj.courseVersionID);

            // // 结束
            // await infoLogger(`${examLogStr} 题目收集结束，共收集${collectCount}道题目，共更新${updateCont}道题目，答题记录：${examEndUrl}`);

            return;
        }

        async function doExam() {
            // 获取考试url
            let examUrlRes = await api.getExamUrl(globalStore.cookieStr, courseObj);
            let examUrl = examUrlRes.data;

            if (taskObj.others.includes('人脸识别')) {
                await infoLogger(`需要人脸识别`);
                await doExamWithPhoto(examUrl);
            } else {
                await infoLogger(`不需要人脸识别`);
                await doExamWithoutPhoto(examUrl);
            }
        }

        async function startExam() {
            // console.log(courseObj);
            // await new Promise((r) => setTimeout(r, 300000000));

            // 没有回答过题目
            if (courseObj.submitCount == 0) {
                await infoLogger(`${examLogStr} 第一次答题`);
                await doExam();
                return;
            }

            // 答已经答题，题次数用完
            if (courseObj.submitCount >= courseObj.totalCount) {
                await infoLogger(`${examLogStr} ${courseObj.totalCount}次答题机会已经用完，无法答题`);

                if (courseObj.score * 1 < passScore) {
                    // 分数不够
                    await infoLogger(`分数${courseObj.score}，开始收集题目,${temUrl}`);

                    await mainPage.goto(temUrl, { waitUntil: 'networkidle0' });

                    let pageCookieStr = await pageTools.getPageCookies(mainPage);

                    await infoLogger(`courseObj.recordId   ${courseObj.recordId}`);

                    // 1.获取已答试卷url
                    let examEndUrlRes = await api.getExamEndUrl(pageCookieStr, courseObj, courseObj.recordId);
                    let examEndUrl = examEndUrlRes.message;

                    //2.查看考试试卷
                    await infoLogger(`上次考试试卷为：${examEndUrl}`);
                    await mainPage.goto(examEndUrl, { waitUntil: 'networkidle0' });
                    await infoLogger(`已经进入试卷界面`);
                    await new Promise(r => setTimeout(r, 10 * 1000));
                    pageCookieStr = await pageTools.getPageCookies(mainPage);

                    // 3.收集题目
                    let paperRes = await api.viewPaper(pageCookieStr, courseObj.recordId);
                    let { collectCount, updateCont } = await collectQuestion(paperRes, courseObj.activityName);
                    await infoLogger(`${examLogStr} 题目收集结束，共收集${collectCount}道题目，共更新${updateCont}道题目`);
                } else {
                    // 分数够
                    await infoLogger(`分数${courseObj.score}，结束`);
                }

                return;
            }

            // 已经答题，题次数没用完，分数够90
            if (courseObj.score * 1 >= passScore) {
                await infoLogger(`${examLogStr}，当前分数为：${courseObj.score}分，已经合格，跳过`);
                return;
            }

            // 已经答题，题次数没用完，分数不够90
            if (courseObj.score * 1 < passScore) {
                await infoLogger(`${examLogStr}，当前分数为：${courseObj.score}分，需要重新答题`);

                let pageCookieStr = await pageTools.getPageCookies(mainPage);

                // 1.获取已答试卷url
                let examEndUrlRes = await api.getExamEndUrl(pageCookieStr, courseObj, courseObj.recordId);
                let examEndUrl = examEndUrlRes.message;

                //2.查看考试试卷
                await infoLogger(`上次考试试卷为：${examEndUrl}`);
                await mainPage.goto(examEndUrl, { waitUntil: 'networkidle0' });
                await infoLogger(`已经进入试卷界面`);
                await new Promise(r => setTimeout(r, 10 * 1000));
                pageCookieStr = await pageTools.getPageCookies(mainPage);

                // 3.收集题目
                let paperRes = await api.viewPaper(pageCookieStr, courseObj.recordId);
                let { collectCount, updateCont } = await collectQuestion(paperRes, courseObj.activityName);
                await infoLogger(`${examLogStr} 题目收集结束，共收集${collectCount}道题目，共更新${updateCont}道题目`);

                // await new Promise(r => setTimeout(r, 1000 * 1000));

                // 4.开始答题
                await doExam();

                return;
            }
        }

        await startExam();
    }
}

module.exports = runCourseModule;
