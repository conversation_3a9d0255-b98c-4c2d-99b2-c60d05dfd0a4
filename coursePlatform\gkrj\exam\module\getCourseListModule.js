let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api.js');


async function getCourseListModule(infoLogger, mainPage,globalStore, taskObj){
    let cookieStr=await pageTools.getPageCookies(mainPage)

    let getCourseListRes=await api.getCourseList(taskObj.username,cookieStr)

    let courseList=getCourseListRes.data.records



    await infoLogger(`获取考试列表成功，共有${courseList.length}门课程`)

    return courseList
}

module.exports=getCourseListModule;