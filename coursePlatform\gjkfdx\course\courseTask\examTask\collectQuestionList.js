let { v4 } = require('uuid');
let Model = require('../../../../../config/sequelize.config.js');
let { lettersToNumbers, numbersToLetters, handleImgs, getSelectAnswerContent } = require('../../../../solveQuestion/format.js');

// 题型常量
const subjectTypeMap = {
    single_selection: '单选题',
    multiple_selection: '多选题',
    true_or_false: '判断题',
    fill_in_blank: '填空题',
    short_answer: '简答题',
    text: '文本题',
    analysis: '分析题',
    matching: '匹配题',
    random: '随机题',
    cloze: '完形填空题',
    analysis_fill: '分析填空题', //自己添加，意思是分析题下面嵌套填空题
};

function trim(s) {
    return (
        s
            //删除多余字符串
            .replace(/(<([^>]+)>)/gi, '') //删除html标签 <span>
            .replaceAll('\n', '') //删除所有的换行符
            .replaceAll('&nbsp;', '')
            .replace(/\s{2,}/g, ' ') //删除两个及以上的空格，保留单个空格
            .trim()
    );
}

// 结合 handleImgs 和 trim，处理题目内容
function handleContent(str) {
    return trim(handleImgs(str)).slice(0, 4500);
}

// 收集正确答案
async function collectQuestionList(viewPageRes, courseName, collectType) {
    let collectNum = 0;
    let updateNum = 0;
    let subjectList;
    let collectList;
    if (collectType == '收集正确答案') {
        if (viewPageRes.correct_answers_data.length == 0) {
            return { collectNum, updateNum };
        }
        subjectList = viewPageRes.subjects_data.subjects; //题目
        collectList = viewPageRes.correct_answers_data.correct_answers; // 官方答案
    }
    if (collectType == '收集我的答案') {
        subjectList = viewPageRes.subjects_data.subjects; //题目
        collectList = viewPageRes.submission_data.subjects; // 我的答案
    }

    // text代表题干
    subjectList = subjectList.filter(questionObj => questionObj.type !== 'text');

    // 遍历题目
    subjectLoop: for (let questionObj of subjectList) {
        let questionId = questionObj.id;
        let questionType = subjectTypeMap[questionObj.type];

        // 格式化题目
        let formatQuestion = {
            id: v4(),
            content: handleContent(questionObj.description),
            type: subjectTypeMap[questionObj.type],
            course_name: courseName,
            platform: '国家开放大学',
            add_time: new Date(),
            comment: '收集正确答案',
            options: JSON.stringify([]),
            // options: JSON.stringify(questionObj.newOptions),
            // answers: questionObj.answers,
            // answers_content: JSON.stringify(answersContent),
        };

        if (collectType == '收集我的答案') {
            formatQuestion.comment = '收集我的答案';
        }

        // 嵌套类型题目
        if (questionType == '分析题' || questionType == '完形填空题') {
            let answerArr = [];
            // 遍历所有子问题对象
            for (let subQuestionObj of questionObj.sub_subjects) {
                // 遍历所有答案
                for (let collectObj of collectList) {
                    // 如果ID相同，说明是同一个题目
                    if (collectObj.subject_id == subQuestionObj.id) {
                        // 没有答案
                        if (collectObj.answer_option_ids.length == 0) continue subjectLoop;
                        // 遍历子题目的所有options
                        for (let optionObj of subQuestionObj.options) {
                            if (optionObj.id == collectObj.answer_option_ids[0]) {
                                answerArr.push({
                                    subjectContent: handleContent(subQuestionObj.description),
                                    answerContent: handleContent(optionObj.content),
                                });
                            }
                        }
                    }
                }
            }
            formatQuestion.answers = JSON.stringify(answerArr);
            formatQuestion.answers_content = JSON.stringify(answerArr);
        }

        if (questionType == '匹配题') {
            // 答题界面的题目数据question.options为空数组
            questionObj.options = questionObj.sub_subjects.map(item => item.options[0]);
            let answerArr = [];

            // 遍历所有答案对象
            for (let collectObj of collectList) {
                // 遍历问题对象的sub_subjects
                for (let subObj of questionObj.sub_subjects) {
                    // ID相同，说明当前collectObj就是当前答案
                    if (subObj.id == collectObj.subject_id) {
                        // 遍历subObj的options
                        for (let optionObj of questionObj.options) {
                            if (optionObj.id == collectObj.answer_option_ids[0]) {
                                answerArr.push({
                                    subjectContent: handleContent(subObj.description),
                                    answerContent: handleContent(optionObj.content),
                                });
                            }
                        }
                    }
                }
            }
            formatQuestion.answers = JSON.stringify(answerArr);
            formatQuestion.answers_content = JSON.stringify(answerArr);
        }

        // 扁平类题目
        if (questionType == '判断题') {
            // 遍历所有答案
            for (let collectObj of collectList) {
                // 如果ID相同，说明是同一个题目
                if (questionId == collectObj.subject_id) {
                    // 没有答案
                    if (collectObj.answer_option_ids.length == 0) continue subjectLoop;

                    // 答案
                    let answerArr = [];
                    // 遍历题目所有选项
                    for (let questionOption of questionObj.options) {
                        // 判断题答案肯定只有一个，这里跟选择题处理逻辑不一样
                        if (collectObj.answer_option_ids[0] == questionOption.id) {
                            let sort = questionOption.sort;
                            answerArr.push(numbersToLetters(sort + ''));
                        }
                    }

                    formatQuestion.answers = answerArr.join('');
                    formatQuestion.answers_content = formatQuestion.answers == 'A' ? '["对"]' : '["错"]';
                    formatQuestion.options = JSON.stringify(['A:对', 'B:错']);
                }
            }
        }

        if (questionType == '单选题' || questionType == '多选题') {
            // 遍历所有答案
            for (let collectObj of collectList) {
                // 如果ID相同，说明是同一个题目
                if (questionId == collectObj.subject_id) {
                    // 没有答案
                    if (collectObj.answer_option_ids.length == 0) continue subjectLoop;

                    // 选项
                    let newOptions = questionObj.options.map((option, index) => {
                        let content = handleContent(option.content);
                        let key = numbersToLetters(index + '');
                        return `${key}:${content}`;
                    });
                    formatQuestion.options = JSON.stringify(newOptions);

                    // 答案
                    let answerArr = [];
                    for (let answerOptiondId of collectObj.answer_option_ids) {
                        for (let questionOption of questionObj.options) {
                            if (answerOptiondId == questionOption.id) {
                                let sort = questionOption.sort;
                                answerArr.push(numbersToLetters(sort + ''));
                            }
                        }
                    }
                    formatQuestion.answers = answerArr.join('');
                    // 答案内容
                    formatQuestion.answers_content = getSelectAnswerContent(formatQuestion.answers, newOptions);
                }
            }
        }

        if (questionType == '填空题') {
            // 遍历所有答案
            for (let collectObj of collectList) {
                // 如果ID相同，说明是同一个题目
                if (questionId == collectObj.subject_id) {
                    if (collectType == '收集我的答案') {
                        // 没有答案
                        if (!collectObj.answers) continue subjectLoop;
                        if (collectObj.answers.length == 0) continue subjectLoop;

                        // 答案
                        let answerArr = [];
                        collectObj.answers.forEach(item => {
                            answerArr.push(item.content);
                        });
                        formatQuestion.answers = answerArr.join('|');
                        // 答案内容
                        formatQuestion.answers_content = answerArr.join('|');
                    }
                    if (collectType == '收集正确答案') {
                        // 没有答案
                        if (!collectObj.correct_answers) continue subjectLoop;
                        if (collectObj.correct_answers.length == 0) continue subjectLoop;

                        // 答案
                        let answerArr = [];
                        collectObj.correct_answers.forEach(item => {
                            answerArr.push(item.content);
                        });
                        formatQuestion.answers = answerArr.join('|');
                        // 答案内容
                        formatQuestion.answers_content = answerArr.join('|');
                    }
                }
            }
        }

        if (questionType == '简答题') {
            // 遍历所有答案
            for (let collectObj of collectList) {
                // 如果ID相同，说明是同一个题目
                if (questionId == collectObj.subject_id) {
                    // 没有答案
                    if (!collectObj.answer) continue subjectLoop;
                    // 选项
                    formatQuestion.options = JSON.stringify([]);
                    // 答案
                    formatQuestion.answers = collectObj.answer
                        .replace(/\s{2,}/g, ' ')
                        .trim()
                        .slice(0, 4900);
                    // 答案内容
                    formatQuestion.answers_content = JSON.stringify([formatQuestion.answers]);
                }
            }
        }

        // 收集题目
        if (formatQuestion.answers) {
            // 收集到服务器
            let existingRecord = await Model.bank.findOne({
                where: {
                    content: formatQuestion.content,
                    type: formatQuestion.type,
                    platform: '国家开放大学',
                    course_name: courseName,
                },
            });
            if (existingRecord) {
                if (existingRecord.comment == '收集正确答案') continue subjectLoop;
                // 如果找到记录，则更新记录
                await Model.bank.update(
                    {
                        answers: formatQuestion.answers,
                        answers_content: formatQuestion.answers_content,
                        comment: collectType,
                    },
                    {
                        where: {
                            id: existingRecord.id,
                        },
                    }
                );
                updateNum++;
            }
            if (!existingRecord) {
                // 如果未找到记录，则创建新记录
                await Model.bank.create(formatQuestion);
                collectNum++;
            }
        }
    }

    return { collectNum, updateNum };
}

module.exports = collectQuestionList;

if (false) {
    let viewPageRes = {
        auto_mark: true,
        correct_answers_data: {
            correct_answers: [
                {
                    answer_option_ids: [***********],
                    point: '4.0',
                    subject_id: ***********,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [***********],
                    point: '4.0',
                    subject_id: ***********,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [***********],
                    point: '4.0',
                    subject_id: ***********,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10044165309],
                    point: '4.0',
                    subject_id: 10016847884,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10044165597],
                    point: '4.0',
                    subject_id: 10016848017,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10044165587],
                    point: '4.0',
                    subject_id: 10016848008,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10044165326],
                    point: '4.0',
                    subject_id: 10016847888,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10044166104],
                    point: '4.0',
                    subject_id: 10016848279,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10044166129],
                    point: '4.0',
                    subject_id: 10016848292,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10044166306],
                    point: '4.0',
                    subject_id: 10016848361,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10044166324],
                    point: '4.0',
                    subject_id: 10016848367,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10044166081],
                    point: '4.0',
                    subject_id: 10016848268,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10044166135],
                    point: '4.0',
                    subject_id: 10016848296,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10044166121],
                    point: '4.0',
                    subject_id: 10016848287,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10044166435],
                    point: '4.0',
                    subject_id: 10016848415,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10044166047],
                    point: '4.0',
                    subject_id: 10016848256,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10044165630],
                    point: '2.0',
                    subject_id: 10016848039,
                    type: 'true_or_false',
                },
                {
                    answer_option_ids: [10044165712],
                    point: '2.0',
                    subject_id: 10016848084,
                    type: 'true_or_false',
                },
                {
                    answer_option_ids: [10044165745],
                    point: '2.0',
                    subject_id: 10016848100,
                    type: 'true_or_false',
                },
                {
                    answer_option_ids: [10044165904],
                    point: '2.0',
                    subject_id: 10016848190,
                    type: 'true_or_false',
                },
                {
                    answer_option_ids: [10044165825],
                    point: '2.0',
                    subject_id: 10016848138,
                    type: 'true_or_false',
                },
                {
                    answer_option_ids: [10044165944],
                    point: '2.0',
                    subject_id: 10016848204,
                    type: 'true_or_false',
                },
                {
                    answer_option_ids: [10044165957],
                    point: '2.0',
                    subject_id: 10016848212,
                    type: 'true_or_false',
                },
                {
                    correct_answers: [
                        {
                            alternates: [],
                            content: 'A',
                            sort: 0,
                            uuid: 1647446728528,
                        },
                        {
                            alternates: [],
                            content: 'A',
                            sort: 1,
                            uuid: 1647446728528,
                        },
                        {
                            alternates: [],
                            content: 'B',
                            sort: 2,
                            uuid: 1647446728528,
                        },
                        {
                            alternates: [],
                            content: 'C',
                            sort: 3,
                            uuid: 1647446728528,
                        },
                        {
                            alternates: [],
                            content: 'D',
                            sort: 4,
                            uuid: 1647446728528,
                        },
                        {
                            alternates: [],
                            content: 'E',
                            sort: 5,
                            uuid: 1647446728528,
                        },
                        {
                            alternates: [],
                            content: 'F',
                            sort: 6,
                            uuid: 1647446728528,
                        },
                    ],
                    point: '14.0',
                    settings: {
                        case_sensitive: true,
                        required: false,
                        unordered: false,
                    },
                    subject_id: 10016848998,
                    type: 'fill_in_blank',
                },
                {
                    correct_answers: [
                        {
                            alternates: [],
                            content: 'D',
                            sort: 0,
                            uuid: 1647446728640,
                        },
                        {
                            alternates: [],
                            content: 'A',
                            sort: 1,
                            uuid: 1647446728640,
                        },
                    ],
                    point: '8.0',
                    settings: {
                        case_sensitive: true,
                        required: false,
                        unordered: false,
                    },
                    subject_id: 10016849052,
                    type: 'fill_in_blank',
                },
            ],
        },
        is_simulated: false,
        score: 78,
        subjects_data: {
            subjects: [
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p><strong>一、单选题（每题4分，共计16题）</strong></p>',
                    difficulty_level: 'medium',
                    id: 10016858097,
                    last_updated_at: '2024-08-07T16:23:12Z',
                    note: null,
                    options: [],
                    parent_id: null,
                    point: '0.0',
                    settings: {
                        case_sensitive: true,
                        required: false,
                        unordered: false,
                    },
                    sort: 0,
                    sub_subjects: [],
                    type: 'text',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>为了对紧急进程或重要进程进行调度，调度算法应采用（    ）。</p>',
                    difficulty_level: 'medium',
                    id: ***********,
                    last_updated_at: '2024-08-07T16:17:03Z',
                    note: null,
                    options: [
                        {
                            content: '<p><span>先来先服务法</span></p>',
                            id: 10044165541,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p><span>优先级法</span></p>',
                            id: ***********,
                            is_answer: true,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p><span>短作业优先法 </span></p>',
                            id: 10044165543,
                            is_answer: false,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p><span>时间片轮转法</span></p>',
                            id: 10044165544,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 1,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>作业调度的关键在于（    ）。</p>',
                    difficulty_level: 'medium',
                    id: ***********,
                    last_updated_at: '2024-08-07T16:17:00Z',
                    note: null,
                    options: [
                        {
                            content: '<p>选择恰当的进程管理程序</p>',
                            id: 10044165383,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p>选择恰当的作业调度算法</p>',
                            id: ***********,
                            is_answer: true,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p>用户作业准备充分</p>',
                            id: 10044165385,
                            is_answer: false,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p>有一个较好的操作环境</p>',
                            id: 10044165386,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 2,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>作业调度程序从处于（    ）状态的队列中选取适当的作业调入主存运行。</p>',
                    difficulty_level: 'medium',
                    id: ***********,
                    last_updated_at: '2024-08-07T16:16:59Z',
                    note: null,
                    options: [
                        {
                            content: '<p>执行</p>',
                            id: 10044165335,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p>提交</p>',
                            id: 10044165336,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p>完成</p>',
                            id: 10044165337,
                            is_answer: false,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p>后备</p>',
                            id: ***********,
                            is_answer: true,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 3,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>操作系统中必不可少的调度是（    ）。</p>',
                    difficulty_level: 'medium',
                    id: 10016847884,
                    last_updated_at: '2024-08-07T16:16:59Z',
                    note: null,
                    options: [
                        {
                            content: '<p>作业调度</p>',
                            id: 10044165307,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p>中级调度</p>',
                            id: 10044165308,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p>进程调度</p>',
                            id: 10044165309,
                            is_answer: true,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p>对换</p>',
                            id: 10044165310,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 4,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>Linux系统中的shell是负责（    ）的模块。</p>',
                    difficulty_level: 'medium',
                    id: 10016848017,
                    last_updated_at: '2024-08-07T16:17:04Z',
                    note: null,
                    options: [
                        {
                            content: '<p><span>解释并执行来自终端的命令</span></p>',
                            id: 10044165597,
                            is_answer: true,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p><span>解释并执行来自终端的内部命令</span></p>',
                            id: 10044165598,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p><span>解释并执行来自终端的外部命令</span></p>',
                            id: 10044165599,
                            is_answer: false,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p><span>进行系统调用</span></p>',
                            id: 10044165600,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 5,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>下列中断类型中，属于自愿性中断事件的是（    ）。</p>',
                    difficulty_level: 'medium',
                    id: 10016848008,
                    last_updated_at: '2024-08-07T16:17:04Z',
                    note: null,
                    options: [
                        {
                            content: '<p><span>硬件故障中断</span></p>',
                            id: 10044165585,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p><span>程序性中断</span></p>',
                            id: 10044165586,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p><span>访管中断</span></p>',
                            id: 10044165587,
                            is_answer: true,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p><span>外部中断</span></p>',
                            id: 10044165588,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 6,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>作业生存期共经历四个状态，它们是提交、后备、（    ）和完成。</p>',
                    difficulty_level: 'medium',
                    id: 10016847888,
                    last_updated_at: '2024-08-07T16:16:59Z',
                    note: null,
                    options: [
                        {
                            content: '<p>等待</p>',
                            id: 10044165323,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p>就绪</p>',
                            id: 10044165324,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p>开始</p>',
                            id: 10044165325,
                            is_answer: false,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p>执行</p>',
                            id: 10044165326,
                            is_answer: true,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 7,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>固定分区中各分区的大小是（    ）。</p>',
                    difficulty_level: 'medium',
                    id: 10016848279,
                    last_updated_at: '2024-08-07T16:17:16Z',
                    note: null,
                    options: [
                        {
                            content: '<p>相同的</p>',
                            id: 10044166103,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p>相同或者不同，但预先固定</p>',
                            id: 10044166104,
                            is_answer: true,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p>根据进程要求确定</p>',
                            id: 10044166105,
                            is_answer: false,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p>随进程个数而定</p>',
                            id: 10044166106,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 8,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>可重定位分区存储管理采用的地址转换公式是（    ）。</p>',
                    difficulty_level: 'medium',
                    id: 10016848292,
                    last_updated_at: '2024-08-07T16:17:16Z',
                    note: null,
                    options: [
                        {
                            content: '<p>绝对地址=界限寄存器值+逻辑地址</p>',
                            id: 10044166127,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p>绝对地址=下限寄存器值+逻辑地址</p>',
                            id: 10044166128,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p> 绝对地址=基址寄存器值+逻辑地址</p>',
                            id: 10044166129,
                            is_answer: true,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p>绝对地址=块号×块长+页内地址</p>',
                            id: 10044166130,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 9,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>实现虚拟存储器的目的是（    ）。</p>',
                    difficulty_level: 'medium',
                    id: 10016848361,
                    last_updated_at: '2024-08-07T16:17:19Z',
                    note: null,
                    options: [
                        {
                            content: '<p>实现存储保护</p>',
                            id: 10044166303,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p>实现程序浮动</p>',
                            id: 10044166304,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p>扩充辅存容量</p>',
                            id: 10044166305,
                            is_answer: false,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p>扩充主存容量</p>',
                            id: 10044166306,
                            is_answer: true,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 10,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>虚拟存储器的最大容量（    ）。</p>',
                    difficulty_level: 'medium',
                    id: 10016848367,
                    last_updated_at: '2024-08-07T16:17:19Z',
                    note: null,
                    options: [
                        {
                            content: '<p>为内外存容量之和</p>',
                            id: 10044166323,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p>由计算机的地址结构决定</p>',
                            id: 10044166324,
                            is_answer: true,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p>是任意大的</p>',
                            id: 10044166325,
                            is_answer: false,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p>由作业的地址空间决定</p>',
                            id: 10044166326,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 11,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>下列存储管理方案中，不采用动态重定位的是（    ）。</p>',
                    difficulty_level: 'medium',
                    id: 10016848268,
                    last_updated_at: '2024-08-07T16:17:15Z',
                    note: null,
                    options: [
                        {
                            content: '<p>页式管理</p>',
                            id: 10044166079,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p>可变分区</p>',
                            id: 10044166080,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p>固定分区</p>',
                            id: 10044166081,
                            is_answer: true,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p>段式管理</p>',
                            id: 10044166082,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 12,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>最先适应分配算法把空闲区（    ）</p>',
                    difficulty_level: 'medium',
                    id: 10016848296,
                    last_updated_at: '2024-08-07T16:17:16Z',
                    note: null,
                    options: [
                        {
                            content: '<p>按地址顺序从小到大登记在空闲区表中</p>',
                            id: 10044166135,
                            is_answer: true,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content:
                                "<p>按地址顺序从大到小登记在空闲区表中 e � 97@�V �?# indent:-18.0pt;mso-list: l0 level1 lfo1;tab-stops:list 39.75pt'&gt;A． 按地址顺序从小到大登记在空闲区表中</p>\n<p> 按地址顺序从大到小登记在空闲区表中</p>",
                            id: 10044166136,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p>按长度以递增顺序登记在空闲区表中</p>',
                            id: 10044166137,
                            is_answer: false,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p>按长度以递减顺序登记在空闲区表中</p>',
                            id: 10044166138,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 13,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>在存储管理中，为实现地址映射，硬件应提供两个寄存器，一个是基址寄存器。另一个是（    ）。</p>',
                    difficulty_level: 'medium',
                    id: 10016848287,
                    last_updated_at: '2024-08-07T16:17:16Z',
                    note: null,
                    options: [
                        {
                            content: '<p>控制寄存器</p>',
                            id: 10044166119,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p>程序状态字寄存器</p>',
                            id: 10044166120,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p>限长寄存器</p>',
                            id: 10044166121,
                            is_answer: true,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p>通用寄存器</p>',
                            id: 10044166122,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 14,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>下述页面置换算法中会产生Belady现象的算法是（   ）。</p>',
                    difficulty_level: 'medium',
                    id: 10016848415,
                    last_updated_at: '2024-08-07T16:17:21Z',
                    note: null,
                    options: [
                        {
                            content: '<p>先进先出法</p>',
                            id: 10044166435,
                            is_answer: true,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p>最近最少使用置换法</p>',
                            id: 10044166436,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p>最近未使用置换法</p>',
                            id: 10044166437,
                            is_answer: false,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p>最佳置换法</p>',
                            id: 10044166438,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 15,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>在目标程序装入内存时，一次性完成地址修改的方式是（    ）。</p>',
                    difficulty_level: 'medium',
                    id: 10016848256,
                    last_updated_at: '2024-08-07T16:17:15Z',
                    note: null,
                    options: [
                        {
                            content: '<p>静态重定位</p>',
                            id: 10044166047,
                            is_answer: true,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '<p>动态重定位</p>',
                            id: 10044166048,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '<p>静态连接</p>',
                            id: 10044166049,
                            is_answer: false,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '<p>动态连接</p>',
                            id: 10044166050,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '4.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 16,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p><strong>二、判断题（每题2分，共计7题）</strong></p>',
                    difficulty_level: 'medium',
                    id: 10016857988,
                    last_updated_at: '2024-08-07T16:23:06Z',
                    note: null,
                    options: [],
                    parent_id: null,
                    point: '0.0',
                    settings: {
                        case_sensitive: true,
                        required: false,
                        unordered: false,
                    },
                    sort: 17,
                    sub_subjects: [],
                    type: 'text',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>作业调度选中一个作业后，与该作业相关的进程即占有CPU运行。（  ）</p>',
                    difficulty_level: 'medium',
                    id: 10016848039,
                    last_updated_at: '2024-08-07T16:17:05Z',
                    note: null,
                    options: [
                        {
                            content: '对',
                            id: 10044165629,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '错',
                            id: 10044165630,
                            is_answer: true,
                            sort: 1,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '2.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 18,
                    sub_subjects: [],
                    type: 'true_or_false',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>中断处理一般分为中断响应和中断处理两个步骤，前者由软件实施，后者由硬件实施。（   ）</p>',
                    difficulty_level: 'medium',
                    id: 10016848084,
                    last_updated_at: '2024-08-07T16:17:07Z',
                    note: null,
                    options: [
                        {
                            content: '对',
                            id: 10044165711,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '错',
                            id: 10044165712,
                            is_answer: true,
                            sort: 1,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '2.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 19,
                    sub_subjects: [],
                    type: 'true_or_false',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>Linux系统针对不同类别的进程提供了3种不同的调度策略。（   ）</p>',
                    difficulty_level: 'medium',
                    id: 10016848100,
                    last_updated_at: '2024-08-07T16:17:08Z',
                    note: null,
                    options: [
                        {
                            content: '对',
                            id: 10044165745,
                            is_answer: true,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '错',
                            id: 10044165746,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '2.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 20,
                    sub_subjects: [],
                    type: 'true_or_false',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>虚拟存储空间实际上就是辅存空间。（    ）</p>',
                    difficulty_level: 'medium',
                    id: 10016848190,
                    last_updated_at: '2024-08-07T16:17:12Z',
                    note: null,
                    options: [
                        {
                            content: '对',
                            id: 10044165903,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '错',
                            id: 10044165904,
                            is_answer: true,
                            sort: 1,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '2.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 21,
                    sub_subjects: [],
                    type: 'true_or_false',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>磁带设备的主要用途是作为文件系统的后备，存放不常用的信息或用做系统间传送信息的介质。（   ）</p>',
                    difficulty_level: 'medium',
                    id: 10016848138,
                    last_updated_at: '2024-08-07T16:17:09Z',
                    note: null,
                    options: [
                        {
                            content: '对',
                            id: 10044165825,
                            is_answer: true,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '错',
                            id: 10044165826,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '2.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 22,
                    sub_subjects: [],
                    type: 'true_or_false',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>虚拟存储器实际上是一种设计技巧，使主存物理容量得到扩大。（    ）</p>',
                    difficulty_level: 'medium',
                    id: 10016848204,
                    last_updated_at: '2024-08-07T16:17:12Z',
                    note: null,
                    options: [
                        {
                            content: '对',
                            id: 10044165943,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '错',
                            id: 10044165944,
                            is_answer: true,
                            sort: 1,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '2.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 23,
                    sub_subjects: [],
                    type: 'true_or_false',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description: '<p>Linux系统采用了请求分页存储管理技术和对换技术。（   ）</p>',
                    difficulty_level: 'medium',
                    id: 10016848212,
                    last_updated_at: '2024-08-07T16:17:13Z',
                    note: null,
                    options: [
                        {
                            content: '对',
                            id: 10044165957,
                            is_answer: true,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '错',
                            id: 10044165958,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '2.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 24,
                    sub_subjects: [],
                    type: 'true_or_false',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p><span>三、应用题（共22分，2道题，第1题14分，第2题8分）</span></p>',
                    difficulty_level: 'medium',
                    id: 10016857991,
                    last_updated_at: '2024-08-07T16:23:06Z',
                    note: null,
                    options: [],
                    parent_id: null,
                    point: '0.0',
                    settings: {
                        case_sensitive: true,
                        required: false,
                        unordered: false,
                    },
                    sort: 25,
                    sub_subjects: [],
                    type: 'text',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 7,
                    correct_answers: [
                        {
                            alternates: [],
                            content: 'A',
                            sort: 0,
                            uuid: 1647446728528,
                        },
                        {
                            alternates: [],
                            content: 'A',
                            sort: 1,
                            uuid: 1647446728528,
                        },
                        {
                            alternates: [],
                            content: 'B',
                            sort: 2,
                            uuid: 1647446728528,
                        },
                        {
                            alternates: [],
                            content: 'C',
                            sort: 3,
                            uuid: 1647446728528,
                        },
                        {
                            alternates: [],
                            content: 'D',
                            sort: 4,
                            uuid: 1647446728528,
                        },
                        {
                            alternates: [],
                            content: 'E',
                            sort: 5,
                            uuid: 1647446728528,
                        },
                        {
                            alternates: [],
                            content: 'F',
                            sort: 6,
                            uuid: 1647446728528,
                        },
                    ],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description:
                        '<p>（14分）某分时系统的进程出现如图所示的状态变化。</p>\n<p style="text-align: center;"><img alt="" height="194" src="/api/uploads/1598811/in-rich-content?created_at=1647446728" width="394"></p>\n<p>（1）你认为该系统采用的进程调度算法是<span class="__blank__" contenteditable="false" data-id="1647446728528">&nbsp;&nbsp;&nbsp;<span class="circle-number">1</span>&nbsp;&nbsp;&nbsp;</span> 。</p>\n<p>A．时间片轮转法</p>\n<p>B．先来先服务法</p>\n<p>C．优先级法</p>\n<p>D．最佳置换法</p>\n<p>（2）根据以下A-F的提示，标识图中从①到⑥所示的每一个状态变化的原因。</p>\n<p>A：进程被选中，变成运行态；</p>\n<p>B：时间片到，运行的进程排入就绪队列尾部；</p>\n<p>C：运行的进程启动打印机，等待打印；</p>\n<p>D：打印工作结束，阻塞的进程排入就绪队列尾部；</p>\n<p>E：等待磁盘读文件工作；</p>\n<p>F：磁盘传输信息结束，阻塞的进程排入就绪队列尾部。</p>\n<p>①<span class="__blank__" contenteditable="false" data-id="1647446728528">&nbsp;&nbsp;&nbsp;<span class="circle-number">2</span>&nbsp;&nbsp;&nbsp;</span> ； ②<span class="__blank__" contenteditable="false" data-id="1647446728528">&nbsp;&nbsp;&nbsp;<span class="circle-number">3</span>&nbsp;&nbsp;&nbsp;</span> ； ③<span class="__blank__" contenteditable="false" data-id="1647446728528">&nbsp;&nbsp;&nbsp;<span class="circle-number">4</span>&nbsp;&nbsp;&nbsp;</span> ； ④<span class="__blank__" contenteditable="false" data-id="1647446728528">&nbsp;&nbsp;&nbsp;<span class="circle-number">5</span>&nbsp;&nbsp;&nbsp;</span> ； ⑤<span class="__blank__" contenteditable="false" data-id="1647446728528">&nbsp;&nbsp;&nbsp;<span class="circle-number">6</span>&nbsp;&nbsp;&nbsp;</span> ； ⑥<span class="__blank__" contenteditable="false" data-id="1647446728528">&nbsp;&nbsp;&nbsp;<span class="circle-number">7</span>&nbsp;&nbsp;&nbsp;</span> </p>',
                    difficulty_level: 'medium',
                    id: 10016848998,
                    last_updated_at: '2024-08-07T16:17:37Z',
                    note: null,
                    options: [],
                    parent_id: null,
                    point: '14.0',
                    settings: {
                        case_sensitive: true,
                        required: false,
                        unordered: false,
                    },
                    sort: 26,
                    sub_subjects: [],
                    type: 'fill_in_blank',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 2,
                    correct_answers: [
                        {
                            alternates: [],
                            content: 'D',
                            sort: 0,
                            uuid: 1647446728640,
                        },
                        {
                            alternates: [],
                            content: 'A',
                            sort: 1,
                            uuid: 1647446728640,
                        },
                    ],
                    data: {
                        is_conflict_subject: false,
                        is_random_subject: true,
                    },
                    description:
                        '<p>（每小题4分，共8分）考虑下述页面走向：1，2，3，4，2，1，5，6，2，1，2，3，7，6，3，2，1，2，3，6。所有内存块最初都是空的，所以，凡第一次用到的页面都产生一次缺页。</p>\n<p>（1）当内存块数量分别为3时，试问使用最佳置换算法（OPT）的缺页次数是<span class="__blank__" contenteditable="false" data-id="1647446728640">&nbsp;&nbsp;&nbsp;<span class="circle-number">1</span>&nbsp;&nbsp;&nbsp;</span> ；</p>\n<p>（2）当内存块数量分别为5时，试问使用最佳置换算法（OPT）的缺页次数是<span class="__blank__" contenteditable="false" data-id="1647446728640">&nbsp;&nbsp;&nbsp;<span class="circle-number">2</span>&nbsp;&nbsp;&nbsp;</span> 。</p>\n<p>A.7         B.10         C.9        D.11</p>',
                    difficulty_level: 'medium',
                    id: 10016849052,
                    last_updated_at: '2024-08-07T16:17:39Z',
                    note: null,
                    options: [],
                    parent_id: null,
                    point: '8.0',
                    settings: {
                        case_sensitive: true,
                        required: false,
                        unordered: false,
                    },
                    sort: 27,
                    sub_subjects: [],
                    type: 'fill_in_blank',
                    wrong_explanation: '',
                },
            ],
        },
        submission_comment_data: {},
        submission_data: {
            _fixed: true,
            progress: {},
            subjects: [
                {
                    answer_option_ids: [***********],
                    subject_id: ***********,
                    subject_updated_at: '2024-08-07T16:17:03Z',
                },
                {
                    answer_option_ids: [***********],
                    subject_id: ***********,
                    subject_updated_at: '2024-08-07T16:17:00Z',
                },
                {
                    answer_option_ids: [***********],
                    subject_id: ***********,
                    subject_updated_at: '2024-08-07T16:16:59Z',
                },
                {
                    answer_option_ids: [10044165309],
                    subject_id: 10016847884,
                    subject_updated_at: '2024-08-07T16:16:59Z',
                },
                {
                    answer_option_ids: [10044165597],
                    subject_id: 10016848017,
                    subject_updated_at: '2024-08-07T16:17:04Z',
                },
                {
                    answer_option_ids: [10044165587],
                    subject_id: 10016848008,
                    subject_updated_at: '2024-08-07T16:17:04Z',
                },
                {
                    answer_option_ids: [10044165326],
                    subject_id: 10016847888,
                    subject_updated_at: '2024-08-07T16:16:59Z',
                },
                {
                    answer_option_ids: [10044166104],
                    subject_id: 10016848279,
                    subject_updated_at: '2024-08-07T16:17:16Z',
                },
                {
                    answer_option_ids: [10044166129],
                    subject_id: 10016848292,
                    subject_updated_at: '2024-08-07T16:17:16Z',
                },
                {
                    answer_option_ids: [10044166306],
                    subject_id: 10016848361,
                    subject_updated_at: '2024-08-07T16:17:19Z',
                },
                {
                    answer_option_ids: [10044166324],
                    subject_id: 10016848367,
                    subject_updated_at: '2024-08-07T16:17:19Z',
                },
                {
                    answer_option_ids: [10044166081],
                    subject_id: 10016848268,
                    subject_updated_at: '2024-08-07T16:17:15Z',
                },
                {
                    answer_option_ids: [10044166135],
                    subject_id: 10016848296,
                    subject_updated_at: '2024-08-07T16:17:16Z',
                },
                {
                    answer_option_ids: [10044166121],
                    subject_id: 10016848287,
                    subject_updated_at: '2024-08-07T16:17:16Z',
                },
                {
                    answer_option_ids: [10044166435],
                    subject_id: 10016848415,
                    subject_updated_at: '2024-08-07T16:17:21Z',
                },
                {
                    answer_option_ids: [10044166047],
                    subject_id: 10016848256,
                    subject_updated_at: '2024-08-07T16:17:15Z',
                },
                {
                    answer_option_ids: [10044165630],
                    subject_id: 10016848039,
                    subject_updated_at: '2024-08-07T16:17:05Z',
                },
                {
                    answer_option_ids: [10044165712],
                    subject_id: 10016848084,
                    subject_updated_at: '2024-08-07T16:17:07Z',
                },
                {
                    answer_option_ids: [10044165745],
                    subject_id: 10016848100,
                    subject_updated_at: '2024-08-07T16:17:08Z',
                },
                {
                    answer_option_ids: [10044165904],
                    subject_id: 10016848190,
                    subject_updated_at: '2024-08-07T16:17:12Z',
                },
                {
                    answer_option_ids: [10044165825],
                    subject_id: 10016848138,
                    subject_updated_at: '2024-08-07T16:17:09Z',
                },
                {
                    answer_option_ids: [10044165944],
                    subject_id: 10016848204,
                    subject_updated_at: '2024-08-07T16:17:12Z',
                },
                {
                    answer_option_ids: [10044165957],
                    subject_id: 10016848212,
                    subject_updated_at: '2024-08-07T16:17:13Z',
                },
                {
                    answers: [
                        {
                            content: '1. A\n\n2. A\n3. B\n4. C\n5. D\n6. E\n7. F',
                            correct: false,
                            origin_content: '1. A\n\n2. A\n3. B\n4. C\n5. D\n6. E\n7. F',
                            sort: 0,
                        },
                    ],
                    subject_id: 10016848998,
                },
                {
                    answers: [
                        {
                            content: '9',
                            correct: false,
                            origin_content: '9',
                            sort: 0,
                        },
                        {
                            content: 'B',
                            correct: false,
                            origin_content: 'B',
                            sort: 1,
                        },
                    ],
                    subject_id: 10016849052,
                },
            ],
        },
        submission_score_data: {
            10016847884: '4.0',
            10016847888: '4.0',
            ***********: '4.0',
            ***********: '4.0',
            ***********: '4.0',
            10016848008: '4.0',
            10016848017: '4.0',
            10016848039: '2.0',
            10016848084: '2.0',
            10016848100: '2.0',
            10016848138: '2.0',
            10016848190: '2.0',
            10016848204: '2.0',
            10016848212: '2.0',
            10016848256: '4.0',
            10016848268: '4.0',
            10016848279: '4.0',
            10016848287: '4.0',
            10016848292: '4.0',
            10016848296: '4.0',
            10016848361: '4.0',
            10016848367: '4.0',
            10016848415: '4.0',
            10016848998: '0.0',
            10016849052: '0.0',
        },
    };
    (async () => {
        let res = await collectQuestionList(viewPageRes, 'test', '收集正确答案');
        console.log(res);
    })();
}
