let solveQuestion = require('../../../../solveQuestion/index.js');
let { lettersToNumbers, numbersToLetters, handleImgs } = require('../../../../solveQuestion/format.js');
let getAnswerFromAI = require('./getAnswerFromAI.js');
let getAnswerFromLocal = require('../../../../solveQuestion/getAnswerFromLocal.js');

// 题型常量
const subjectTypeMap = {
    single_selection: '单选题',
    multiple_selection: '多选题',
    true_or_false: '判断题',
    fill_in_blank: '填空题',
    short_answer: '简答题',
    text: '文本题',
    analysis: '分析题',
    matching: '匹配题',
    random: '随机题',
    cloze: '完形填空题',
    analysis_fill: '分析填空题', //自己添加，意思是分析题下面嵌套填空题
};

console.log(subjectTypeMap['analysis'])

function trim(s) {
    return (
        s
            //删除多余字符串
            .replace(/(<([^>]+)>)/gi, '') //删除html标签 <span>
            .replaceAll('\n', '') //删除所有的换行符
            .replaceAll('&nbsp;', '')
            .replace(/\s{2,}/g, ' ') //删除两个及以上的空格，保留单个空格
            .trim()
    );
}

// 结合 handleImgs 和 trim，处理题目内容
function handleContent(str) {
    return trim(handleImgs(str)).slice(0, 4500);
}

// 回答问题 把嵌套题目看作一个题目
async function handleQuestionList(questionList, courseName) {
    //去除掉题目类型的元素
    questionList = questionList.filter(questionObj => questionObj.type !== 'text');
    let subjects = [];

    questionLoop: for (let questionObj of questionList) {
        let questionType = subjectTypeMap[questionObj.type];

        if (!'判断题单选题多选题简答题填空题完形填空题分析题匹配题'.includes(questionType)) {
            throw new Error(`未知题型：${questionObj.type}`);
        }

        // 格式化题目
        let temObj = {
            type: questionType,
            content: handleContent(questionObj.description),
            options: [],
            // answers: questionObj.answers,
            // answers_content: JSON.stringify(answersContent),
            courseName: courseName,
            platform: '国家开放大学',
            // description: questionObj.description,
            // id: v4(),
            // comment: '收集',
            // add_time: new Date(),
        };

        // 每个支线比较复杂，就不用switch了
        // 判断题
        if (questionType == '判断题') {
            // 题目选项
            temObj.options = ['A:对', 'B:错'];

            // 查找答案
            let answers = await solveQuestion(temObj);
            temObj.answers = answers;
            if (!answers) continue questionLoop;

            // 生成响应内容 subjectObj
            let answer_option_ids = [];
            questionObj.options.forEach(option => {
                let letter = numbersToLetters(option.sort + '');
                if (letter == answers) {
                    answer_option_ids.push(option.id);
                }
            });
            let subjectObj = {
                subject_id: questionObj.id,
                subject_updated_at: questionObj.last_updated_at,
                answer_option_ids: answer_option_ids,
            };
            subjects.push(subjectObj);
        }

        // 单选题
        if (questionType == '单选题') {
            // 题目选项
            temObj.options = questionObj.options.map((option, index) => {
                let content = handleContent(option.content);
                let key = numbersToLetters(index + '');
                return `${key}:${content}`;
            });
            // 查找答案
            let answers = await solveQuestion(temObj);
            if (!answers) continue questionLoop;

            // 生成响应内容 subjectObj
            let answer_option_ids = [];
            questionObj.options.forEach(option => {
                let letter = numbersToLetters(option.sort + '');
                if (letter == answers) {
                    answer_option_ids.push(option.id);
                }
            });
            let subjectObj = {
                subject_id: questionObj.id,
                subject_updated_at: questionObj.last_updated_at,
                answer_option_ids: answer_option_ids,
            };
            subjects.push(subjectObj);
        }

        // 多选题
        if (questionType == '多选题') {
            // 题目选项
            temObj.options = questionObj.options.map((option, index) => {
                let content = handleContent(option.content);
                let key = numbersToLetters(index + '');
                return `${key}:${content}`;
            });

            // 查找答案
            let answers = await solveQuestion(temObj);
            if (!answers) continue questionLoop;

            // 生成响应内容 subjectObj
            let answer_option_ids = [];
            questionObj.options.forEach(option => {
                let letter = numbersToLetters(option.sort + '');
                for (let answer of answers) {
                    if (letter == answer) {
                        answer_option_ids.push(option.id);
                    }
                }
            });
            let subjectObj = {
                subject_id: questionObj.id,
                subject_updated_at: questionObj.last_updated_at,
                answer_option_ids: answer_option_ids,
            };
            subjects.push(subjectObj);
        }

        // 简答题
        if (questionType == '简答题') {
            // 题目选项
            // temObj.options = [];

            // 查找答案
            let answers = await solveQuestion(temObj);
            if (!answers) continue questionLoop;

            // 5.生成响应内容 subjectObj
            let subjectObj = {
                answer: `${answers}`,
                subject_id: questionObj.id,
                attachments: [],
            };

            subjects.push(subjectObj);
        }

        // 填空题（自己处理）
        if (questionType == '填空题') {
            // 先从本地查找答案
            let answers;
            answers = await getAnswerFromLocal(temObj);

            // 再用AI处理
            if (!answers) answers = await getAnswerFromAI(temObj);

            if (!answers) continue questionLoop;
            temObj.answers = answers;

            // 生成响应内容 subjectObj
            let subjectObj = {
                subject_id: questionObj.id,
                // subject_updated_at: questionObj.last_updated_at,
                answers: [],
            };
            let answerArr = answers.split('|');
            for (let i = 0; i < questionObj.answer_number; i++) {
                let answerItem = answerArr[i];
                if (!answerItem) answerItem = '';
                if (!answerItem) break;
                subjectObj.answers.push({
                    sort: i,
                    content: answerItem,
                });
            }
            subjects.push(subjectObj);
        }

        // 匹配题（嵌套，自己处理）
        if (questionType == '匹配题') {
            // 原始选项
            temObj.rawOptions = questionObj.options.map(optionObj => {
                let obj = {
                    id: optionObj.id,
                    content: handleContent(optionObj.content),
                };
                return obj;
            });
            // 嵌套题目
            temObj.sub_subjects = questionObj.sub_subjects.map(subQuestionObj => {
                return {
                    id: subQuestionObj.id,
                    content: handleContent(subQuestionObj.description),
                };
            });

            let answers;

            // 先从本地查找答案
            let localDbAnswerString = await getAnswerFromLocal(temObj);
            if (localDbAnswerString) {
                let parsedDbAnswers = JSON.parse(localDbAnswerString); // 解析数据库来的答案：[{subjectContent, answerContent}, ...]
                let submissionFormatAnswers = []; // 存储转换后的答案，格式：[{subject_id, answer_option_ids: [id]}, ...]

                // 遍历题目中的每一个子问题（左侧待匹配项）
                for (let sub of temObj.sub_subjects) {
                    // 在数据库答案中查找与当前子问题内容匹配的项
                    for (let dbAns of parsedDbAnswers) {
                        if (sub.content === dbAns.subjectContent) {
                            // 如果左侧内容匹配，则开始在右侧选项池中查找匹配的答案内容
                            for (let rawOpt of temObj.rawOptions) {
                                if (rawOpt.content === dbAns.answerContent) {
                                    submissionFormatAnswers.push({
                                        subject_id: sub.id, // 左侧待匹配项的ID
                                        answer_option_ids: [rawOpt.id], // 匹配到的右侧选项的ID
                                    });
                                    break; // 找到了当前sub的匹配项的rawOpt，跳出rawOptions循环
                                }
                            }
                            break; // 找到了当前sub的匹配项的dbAns，跳出parsedDbAnswers循环
                        }
                    }

                    if (submissionFormatAnswers.length > 0) {
                        // 将转换后的答案数组字符串化，以便后续和AI返回的格式统一
                        answers = JSON.stringify(submissionFormatAnswers);
                    } else {
                        // 如果本地答案无法转换或为空，则清空answers，以便后续尝试AI获取
                        answers = null;
                    }
                }
            }

            // 再用AI处理
            if (!answers) answers = await getAnswerFromAI(temObj);

            if (!answers) continue questionLoop;
            // 5.生成响应内容 subjectObj
            answers = JSON.parse(answers);
            answers.forEach(obj => (obj.subject_updated_at = questionObj.last_updated_at));
            subjects = subjects.concat(answers);
        }

        // 分析题 （嵌套，自己处理）
        if (questionType == '分析题' || questionType == '完形填空题') {
            // 处理嵌套子题目
            temObj.sub_subjects = questionObj.sub_subjects.map(subQuestionObj => {
                return {
                    id: subQuestionObj.id,
                    content: handleContent(subQuestionObj.description),
                    options: subQuestionObj.options.map(option => {
                        return {
                            id: option.id,
                            content: handleContent(option.content),
                        };
                    }),
                };
            });

            // 先从本地查找答案
            let answers;
            let localDbAnswerString = await getAnswerFromLocal(temObj); // 从数据库获取的答案字符串，格式如：'[{"subjectContent":"子题目描述","answerContent":"子题目答案文本"},...]'

            if (localDbAnswerString) {
                let parsedDbAnswers = JSON.parse(localDbAnswerString); // 解析数据库来的答案：[{subjectContent, answerContent}, ...]
                let submissionFormatAnswers = []; // 存储转换后的答案，格式：[{subject_id, answer_option_ids: [id]}, ...]

                // 遍历题目中的每一个子问题
                for (let sub of temObj.sub_subjects) {
                    // 在数据库答案中查找与当前子问题内容匹配的项
                    for (let dbAns of parsedDbAnswers) {
                        if (sub.content === dbAns.subjectContent) {
                            // 如果子题目内容匹配，则开始在该子题目的选项中查找匹配的答案内容
                            for (let opt of sub.options) {
                                if (opt.content === dbAns.answerContent) {
                                    submissionFormatAnswers.push({
                                        subject_id: sub.id, // 子题目的ID
                                        answer_option_ids: [opt.id], // 匹配到的选项的ID
                                    });
                                    break; // 找到了当前sub的匹配项的opt，跳出sub.options循环
                                }
                            }
                            break; // 找到了当前sub的匹配项的dbAns，跳出parsedDbAnswers循环
                        }
                    }
                }

                if (submissionFormatAnswers.length > 0) {
                    // 将转换后的答案数组字符串化，以便后续和AI返回的格式统一
                    answers = JSON.stringify(submissionFormatAnswers);
                } else {
                    // 如果本地答案无法转换或为空，则清空answers，以便后续尝试AI获取
                    answers = null;
                }
            }

            // 再用AI处理
            if (!answers) answers = await getAnswerFromAI(temObj);

            if (!answers) continue questionLoop;
            // 5.生成响应内容 subjectObj
            answers = JSON.parse(answers);
            answers.forEach(obj => (obj.subject_updated_at = questionObj.last_updated_at));
            subjects = subjects.concat(answers);
        }
    }
    //返回结果
    return subjects;
}

module.exports = handleQuestionList;

if (false) {
    let subjects = [
        // // 单选题
        // {
        //     answer_number: 0,
        //     data: null,
        //     description: '<p>钢结构的连接按照连接的方法主要分为焊缝连接、螺栓连接、铆钉连接和销轴连接，其中出现最早的是（ &nbsp; ）</p>',
        //     difficulty_level: 'medium',
        //     id: 70027435799,
        //     last_updated_at: '2025-02-14T04:20:54Z',
        //     note: null,
        //     options: [
        //         {
        //             content: '<p>焊缝连接</p>',
        //             id: 70073117210,
        //             sort: 0,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>螺栓连接</p>',
        //             id: 70073117211,
        //             sort: 1,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>铆钉连接</p>',
        //             id: 70073117212,
        //             sort: 2,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>销轴连接</p>',
        //             id: 70073117213,
        //             sort: 3,
        //             type: 'text',
        //         },
        //     ],
        //     parent_id: null,
        //     point: '2.0',
        //     settings: {
        //         case_sensitive: true,
        //         option_type: 'text',
        //         options_layout: 'vertical',
        //         required: false,
        //         unordered: false,
        //     },
        //     sort: 0,
        //     sub_subjects: [],
        //     type: 'single_selection',
        // },

        // // 单选题 带图片
        // {
        //     answer_number: 0,
        //     data: null,
        //     description:
        //         '<p class="a">直角角焊缝的强度计算公式<img alt="" height="65" src="/api/uploads/1491528/in-rich-content?created_at=1647283599" width="128">， 式中符号<img alt="" height="25" src="/api/uploads/1491529/in-rich-content?created_at=1647283599" width="20">表示（ &nbsp; &nbsp;）</p>',
        //     difficulty_level: 'medium',
        //     id: 70027435972,
        //     last_updated_at: '2025-02-14T04:20:56Z',
        //     note: null,
        //     options: [
        //         {
        //             content: '<p>垂直于焊缝长度方向的应力</p>',
        //             id: 70073117743,
        //             sort: 0,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>平行于焊缝长度方向的剪应力</p>',
        //             id: 70073117744,
        //             sort: 1,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>正面角焊缝的强度设计值增大系数</p>',
        //             id: 70073117745,
        //             sort: 2,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>角焊缝的强度设计值</p>',
        //             id: 70073117746,
        //             sort: 3,
        //             type: 'text',
        //         },
        //     ],
        //     parent_id: null,
        //     point: '2.0',
        //     settings: {
        //         case_sensitive: true,
        //         option_type: 'text',
        //         options_layout: 'vertical',
        //         required: false,
        //         unordered: false,
        //     },
        //     sort: 12,
        //     sub_subjects: [],
        //     type: 'single_selection',
        // },

        // // 判断题
        // {
        //     answer_number: 0,
        //     data: null,
        //     description:
        //         '<p><span style=\'font-family: "Times New Roman"; font-size: 10.5pt;\'><font>焊缝连接分为工厂焊接和现场焊接，其中现场焊接易于控制质量，而工厂焊接受施工条件、季节影响大，质量不易保证。</font></span>（）</p>',
        //     difficulty_level: 'medium',
        //     id: 70027436080,
        //     last_updated_at: '2025-02-14T04:20:57Z',
        //     note: null,
        //     options: [
        //         {
        //             content: '<p>对</p>',
        //             id: 70073118111,
        //             sort: 0,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>错</p>',
        //             id: 70073118112,
        //             sort: 1,
        //             type: 'text',
        //         },
        //     ],
        //     parent_id: null,
        //     point: '3.0',
        //     settings: {
        //         case_sensitive: true,
        //         option_type: 'text',
        //         options_layout: 'vertical',
        //         required: false,
        //         unordered: false,
        //     },
        //     sort: 21,
        //     sub_subjects: [],
        //     type: 'true_or_false',
        // },

        // // 判断题 带图片
        // {
        //     answer_number: 0,
        //     data: null,
        //     description:
        //         '<p class="a">角焊缝的焊脚尺寸宜满足<img alt="" height="28" src="/api/uploads/1491530/in-rich-content?created_at=1647283599" width="131">的构造要求，其中<img alt="" height="20" src="/api/uploads/1491531/in-rich-content?created_at=1647283599" width="15">（单位为mm）为较薄焊件厚度，为较厚焊件厚度。（ <em>&nbsp;&nbsp;</em>&nbsp;）</p><p>&nbsp;</p>',
        //     difficulty_level: 'medium',
        //     id: 70027436114,
        //     last_updated_at: '2025-02-14T04:20:58Z',
        //     note: null,
        //     options: [
        //         {
        //             content: '<p>对</p>',
        //             id: 70073118223,
        //             sort: 0,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>错</p>',
        //             id: 70073118224,
        //             sort: 1,
        //             type: 'text',
        //         },
        //     ],
        //     parent_id: null,
        //     point: '3.0',
        //     settings: {
        //         case_sensitive: true,
        //         option_type: 'text',
        //         options_layout: 'vertical',
        //         required: false,
        //         unordered: false,
        //     },
        //     sort: 24,
        //     sub_subjects: [],
        //     type: 'true_or_false',
        // },

        // // 多选题
        // {
        //     answer_number: 0,
        //     data: null,
        //     description: '<p>钢结构焊接连接方法的优点有____。</p>',
        //     difficulty_level: 'medium',
        //     id: 70027436213,
        //     last_updated_at: '2025-02-14T04:20:59Z',
        //     note: null,
        //     options: [
        //         {
        //             content: '<p>焊接间可以直接连接，构造简单，制作方便</p>',
        //             id: 70073118551,
        //             sort: 0,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>连接的密闭性好，结构的刚度大</p>',
        //             id: 70073118552,
        //             sort: 1,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>焊缝附近的热影响区内，钢材的金相组织发生改变，导致局部材质变脆</p>',
        //             id: 70073118553,
        //             sort: 2,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>可实现自动化操作，提高焊接结构的质量</p>',
        //             id: 70073118554,
        //             sort: 3,
        //             type: 'text',
        //         },
        //     ],
        //     parent_id: null,
        //     point: '5.0',
        //     settings: {
        //         case_sensitive: true,
        //         option_type: 'text',
        //         options_layout: 'vertical',
        //         point_rule: 'all_correct',
        //         point_rule_minimum_count: 0,
        //         required: false,
        //         unordered: false,
        //     },
        //     sort: 32,
        //     sub_subjects: [],
        //     type: 'multiple_selection',
        // },

        // 分析题 带图片
        // {
        //     answer_number: 0,
        //     data: null,
        //     description:
        //         '<p><span style="font-family: 宋体;">如图所示：已知焊缝承受的斜向静力荷载设计值</span><i><span style=\'font-family: "Times New Roman", serif;\'>F=</span></i><span style=\'font-family: "Times New Roman", serif;\'>230kN</span><span style="font-family: 宋体;">，</span><i><span style=\'font-family: "Times New Roman", serif;\'>θ</span></i><span style=\'font-family: "Times New Roman", serif;\'>=</span><span style=\'line-height: 150%; font-family: "Times New Roman", serif;\'>45</span><sup><span style="line-height: 150%; font-family: 宋体;">。</span></sup><span style="font-family: 宋体;">，偏心</span><i><span style=\'font-family: "Times New Roman", serif;\'>e</span></i><span style="font-family: 宋体;">为</span><span style=\'font-family: "Times New Roman", serif;\'>30mm</span><span style="font-family: 宋体;">，角焊缝的焊脚尺寸</span><i><span style=\'font-family: "Times New Roman", serif;\'>h</span></i><sub><span style=\'font-family: "Times New Roman", serif;\'>f</span></sub><span style=\'font-family: "Times New Roman", serif;\'>=8mm</span><span style="font-family: 宋体;">，实际长度</span><i><span style=\'font-family: "Times New Roman", serif;\'>l</span></i><span style=\'font-family: "Times New Roman", serif;\'>=220mm</span><span style="font-family: 宋体;">，钢材为</span><span style=\'font-family: "Times New Roman", serif;\'>Q235B</span><span style="font-family: 宋体;">，焊条为</span><span style=\'font-family: "Times New Roman", serif;\'>E43</span><span style="font-family: 宋体;">型<img alt="image.png" class="" data-id="12828605" height="67" src="https://lms.ouchn.cn:443/api/uploads/12828605/in-rich-content?created_at=2023-08-18T05:46:38Z" width="154"></span><span style="font-family: 宋体;">焊缝强度增大系数</span><i><span style=\'font-family: "Times New Roman", serif;\'>β</span></i><sub><span style=\'font-family: "Times New Roman", serif;\'>f</span></sub><span style="font-family: 宋体;">取</span><span style=\'font-family: "Times New Roman", serif;\'>1.22</span><span style="font-family: 宋体;">，验算直角角焊缝的强度。</span><br></p><p><img alt="image.png" class="" data-id="12828606" height="279" src="https://lms.ouchn.cn:443/api/uploads/12828606/in-rich-content?created_at=2023-08-18T05:48:46Z" width="382"><br></p>',
        //     difficulty_level: 'none',
        //     id: 70027436227,
        //     last_updated_at: '2025-02-14T04:20:59Z',
        //     note: null,
        //     options: [],
        //     parent_id: null,
        //     point: '15.0',
        //     settings: {
        //         case_sensitive: true,
        //         options_layout: 'vertical',
        //         required: false,
        //         unordered: false,
        //         uploads: [
        //             {
        //                 id: 12828605,
        //                 type: 'image',
        //             },
        //             {
        //                 id: 12828606,
        //                 type: 'image',
        //             },
        //         ],
        //     },
        //     sort: 33,
        //     sub_subjects: [
        //         {
        //             answer_number: 0,
        //             data: null,
        //             description:
        //                 '<p><span style="font-size: 10.5pt; font-family: 宋体;">选出求解本题所需要用到的公式（</span><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'> </span><span style="font-size: 10.5pt; font-family: 宋体;">）</span><br></p><p><span style="font-size: 10.5pt; font-family: 宋体;"><img alt="image.png" class="" data-id="12828607" height="275" src="https://lms.ouchn.cn:443/api/uploads/12828607/in-rich-content?created_at=2023-08-18T05:51:01Z" width="629"><br></span></p>',
        //             difficulty_level: 'medium',
        //             id: 70027436252,
        //             last_updated_at: '2025-02-14T04:21:00Z',
        //             note: null,
        //             options: [
        //                 {
        //                     content:
        //                         '<p><span style="font-size: 10.5pt; font-family: 宋体;">①</span><span style="font-size: 10.5pt; font-family: 宋体;">④⑤⑨⑩</span><br></p>',
        //                     id: 70073118677,
        //                     sort: 0,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content:
        //                         '<p><span style="font-size: 10.5pt; font-family: 宋体;">②④⑤</span><span style="font-size: 10.5pt; font-family: 宋体;">⑥</span><span style="font-size: 10.5pt; font-family: 宋体;">⑩</span><br></p>',
        //                     id: 70073118678,
        //                     sort: 1,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content:
        //                         '<p><span style="font-size: 10.5pt; font-family: 宋体;">①⑥</span><span style="font-size: 10.5pt; font-family: 宋体;">⑦⑧⑩</span><br></p>',
        //                     id: 70073118679,
        //                     sort: 2,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content:
        //                         '<p><span style="font-size: 10.5pt; font-family: 宋体;">①</span><span style="font-size: 10.5pt; font-family: 宋体;">③⑧⑨⑩</span><br></p>',
        //                     id: 70073118680,
        //                     sort: 3,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content:
        //                         '<p><span style="font-size: 10.5pt; font-family: 宋体;">②③</span><span style="font-size: 10.5pt; font-family: 宋体;">⑥</span><span style="font-size: 10.5pt; font-family: 宋体;">⑦⑧</span><br></p>',
        //                     id: 70073118681,
        //                     sort: 4,
        //                     type: 'text',
        //                 },
        //             ],
        //             parent_id: 70027436227,
        //             point: '5.0',
        //             settings: {
        //                 case_sensitive: true,
        //                 option_type: 'text',
        //                 options_layout: 'vertical',
        //                 required: false,
        //                 unordered: false,
        //                 uploads: [
        //                     {
        //                         id: 12828607,
        //                         type: 'image',
        //                     },
        //                 ],
        //             },
        //             sort: 0,
        //             sub_subjects: [],
        //             type: 'single_selection',
        //         },
        //         {
        //             answer_number: 0,
        //             data: null,
        //             description:
        //                 '<p><span style="font-size: 10.5pt; font-family: 宋体;">将</span><i><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>F</span></i><span style="font-size: 10.5pt; font-family: 宋体;">分解为垂直于焊缝和平行于焊缝的分力，两个分力分别为</span><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>_____kN</span><span style="font-size: 10.5pt; font-family: 宋体;">和</span><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>______kN</span><span style="font-size: 10.5pt; font-family: 宋体;">，焊缝受到弯矩为</span><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>______KN*m</span><span style="font-size: 10.5pt; font-family: 宋体;">。</span><br></p>',
        //             difficulty_level: 'medium',
        //             id: 70027436266,
        //             last_updated_at: '2025-02-14T04:21:00Z',
        //             note: null,
        //             options: [
        //                 {
        //                     content:
        //                         '<p><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>4.9</span><span style="font-size: 10.5pt; font-family: 宋体;">，</span><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>162.6</span><span style="font-size: 10.5pt; font-family: 宋体;">，</span><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>63.1</span><br></p>',
        //                     id: 70073118729,
        //                     sort: 0,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content:
        //                         '<p><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>162.6</span><span style="font-size: 10.5pt; font-family: 宋体;">，</span><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>4.9</span><span style="font-size: 10.5pt; font-family: 宋体;">，</span><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>71.2</span><br></p>',
        //                     id: 70073118730,
        //                     sort: 1,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content:
        //                         '<p><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>162.6</span><span style="font-size: 10.5pt; font-family: 宋体;">，</span><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>162.6</span><span style="font-size: 10.5pt; font-family: 宋体;">，</span><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>4.9</span><br></p>',
        //                     id: 70073118731,
        //                     sort: 2,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content:
        //                         '<p><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>71.2</span><span style="font-size: 10.5pt; font-family: 宋体;">，</span><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>63.1</span><br></p>',
        //                     id: 70073118732,
        //                     sort: 3,
        //                     type: 'text',
        //                 },
        //             ],
        //             parent_id: 70027436227,
        //             point: '5.0',
        //             settings: {
        //                 case_sensitive: true,
        //                 option_type: 'text',
        //                 options_layout: 'vertical',
        //                 required: false,
        //                 unordered: false,
        //             },
        //             sort: 1,
        //             sub_subjects: [],
        //             type: 'single_selection',
        //         },
        //         {
        //             answer_number: 0,
        //             data: null,
        //             description:
        //                 '<p><span style="font-family: 宋体;">折算应力为</span><span style=\'font-family: "Times New Roman", serif;\'>____N/mm</span><span style="font-family: 宋体;">，直角角焊缝的强度</span><span style=\'font-family: "Times New Roman", serif;\'>______</span><span style="font-family: 宋体;">要求。</span><br></p>',
        //             difficulty_level: 'medium',
        //             id: 70027436285,
        //             last_updated_at: '2025-02-14T04:21:00Z',
        //             note: null,
        //             options: [
        //                 {
        //                     content:
        //                         '<p><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>131.1</span><span style="font-size: 10.5pt; font-family: 宋体;">，满足</span><br></p>',
        //                     id: 70073118778,
        //                     sort: 0,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content:
        //                         '<p><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>131.1</span><span style="font-size: 10.5pt; font-family: 宋体;">，不满足</span><br></p>',
        //                     id: 70073118779,
        //                     sort: 1,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content:
        //                         '<p><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>151.1</span><span style="font-size: 10.5pt; font-family: 宋体;">，满足</span><br></p>',
        //                     id: 70073118780,
        //                     sort: 2,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content:
        //                         '<p><span style=\'font-size: 10.5pt; font-family: "Times New Roman", serif;\'>151.1</span><span style="font-size: 10.5pt; font-family: 宋体;">，不满足</span><br></p>',
        //                     id: 70073118781,
        //                     sort: 3,
        //                     type: 'text',
        //                 },
        //             ],
        //             parent_id: 70027436227,
        //             point: '5.0',
        //             settings: {
        //                 case_sensitive: true,
        //                 option_type: 'text',
        //                 options_layout: 'vertical',
        //                 required: false,
        //                 unordered: false,
        //             },
        //             sort: 2,
        //             sub_subjects: [],
        //             type: 'single_selection',
        //         },
        //     ],
        //     type: 'analysis',
        // },

        // 匹配题
        // {
        //     answer_number: 0,
        //     data: {
        //         is_conflict_subject: false,
        //         is_random_subject: true,
        //     },
        //     description:
        //         '<p>（每小题2分，共16分）设有无穷多个信息，输入进程把信息逐个写入缓冲区，输出进程逐个从缓冲区中取出信息。设缓冲区是环形的，编号为0～n-1，in和out分别是输入进程和输出进程使用的指针，初值都是0。为使两类进程实行同步操作，设置三个信号量：两个计数信号量full和empty，一个互斥信号量mutex。full表示放有信息的缓冲区数，其初值为0。empty表示可供使用的缓冲区数，其初值为n。mutex互斥信号量，初值为1。 根据下面输入、输出进程的同步算法，填写相应的P、V操作。</p><p>输入<span style="font-family: 宋体;">进程</span>Input<span style="font-family: 宋体;">：</span></p><p>while&nbsp;(TRUE) {</p><p>__(1)__;</p><p>_<span style="color: rgb(51, 51, 51); font-family: arial, sans-serif; font-size: 14px;">P(mutex)</span>__;</p><p>信息<span style="font-family: 宋体;">送往</span>buffer(in);&nbsp;</p><p>in=(in+1) mod n; &nbsp;&nbsp;&nbsp;/*<span style="font-family: 宋体;">以</span><span style="font-family: Calibri;">n</span><span style="font-family: 宋体;">为模</span><span style="font-family: Calibri;">*/</span></p><p>__(2)__;</p><p>__(3)__;</p><p>} &nbsp;</p><p>输出<span style="font-family: 宋体;">进程</span>Output<span style="font-family: 宋体;">：</span></p><p>while&nbsp;(TRUE){</p><p>__(4)__;</p><p>__(5)__;</p><p><span style="font-family: 宋体;">从</span>buffer(out)<span style="font-family: 宋体;">中取出</span><span style="font-family: 宋体;">信息</span>;&nbsp;</p><p>out=(out+1) mod n; &nbsp;&nbsp;/*<span style="font-family: 宋体;">以</span><span style="font-family: Calibri;">n</span><span style="font-family: 宋体;">为模</span><span style="font-family: Calibri;">*/</span></p><p>_<span style="color: rgb(51, 51, 51); font-family: arial, sans-serif; font-size: 14px;">V(mutex)</span>_;</p><p>__(6)__;</p><p>}</p>',
        //     difficulty_level: 'medium',
        //     id: 10016848931,
        //     last_updated_at: '2024-08-07T16:17:37Z',
        //     note: null,
        //     options: [
        //         {
        //             content: '<p>P(full)</p>',
        //             id: 10044168041,
        //             sort: 0,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>V(full)</p>',
        //             id: 10044168018,
        //             sort: 0,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>P(mutex)</p>',
        //             id: 10044168062,
        //             sort: 0,
        //             type: 'text',
        //         },
        //         {
        //             content: 'P(empty)',
        //             id: 10044167976,
        //             sort: 0,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>V(empty)</p>',
        //             id: 10044168083,
        //             sort: 0,
        //             type: 'text',
        //         },
        //         {
        //             content: '<p>V(mutex)</p>',
        //             id: 10044167997,
        //             sort: 0,
        //             type: 'text',
        //         },
        //     ],
        //     parent_id: null,
        //     point: '16.0',
        //     settings: {
        //         case_sensitive: true,
        //         option_type: 'text',
        //         options_layout: 'vertical',
        //         required: false,
        //         unordered: false,
        //     },
        //     sort: 29,
        //     sub_subjects: [
        //         {
        //             answer_number: 0,
        //             data: null,
        //             description: '选项1',
        //             difficulty_level: 'medium',
        //             id: 10016848937,
        //             last_updated_at: '2024-08-07T16:17:36Z',
        //             note: null,
        //             options: [],
        //             parent_id: 10016848931,
        //             point: '0.0',
        //             settings: {
        //                 case_sensitive: true,
        //                 option_type: 'text',
        //                 options_layout: 'vertical',
        //                 required: false,
        //                 unordered: false,
        //             },
        //             sort: 0,
        //             sub_subjects: [],
        //             type: 'single_selection',
        //         },
        //         {
        //             answer_number: 0,
        //             data: null,
        //             description: '<p>选项2</p>',
        //             difficulty_level: 'medium',
        //             id: 10016848944,
        //             last_updated_at: '2024-08-07T16:17:36Z',
        //             note: null,
        //             options: [],
        //             parent_id: 10016848931,
        //             point: '0.0',
        //             settings: {
        //                 case_sensitive: true,
        //                 option_type: 'text',
        //                 options_layout: 'vertical',
        //                 required: false,
        //                 unordered: false,
        //             },
        //             sort: 1,
        //             sub_subjects: [],
        //             type: 'single_selection',
        //         },
        //         {
        //             answer_number: 0,
        //             data: null,
        //             description: '<p>选项3</p>',
        //             difficulty_level: 'medium',
        //             id: 10016848952,
        //             last_updated_at: '2024-08-07T16:17:37Z',
        //             note: null,
        //             options: [],
        //             parent_id: 10016848931,
        //             point: '0.0',
        //             settings: {
        //                 case_sensitive: true,
        //                 option_type: 'text',
        //                 options_layout: 'vertical',
        //                 required: false,
        //                 unordered: false,
        //             },
        //             sort: 2,
        //             sub_subjects: [],
        //             type: 'single_selection',
        //         },
        //         {
        //             answer_number: 0,
        //             data: null,
        //             description: '<p>选项4</p>',
        //             difficulty_level: 'medium',
        //             id: 10016848960,
        //             last_updated_at: '2024-08-07T16:17:37Z',
        //             note: null,
        //             options: [],
        //             parent_id: 10016848931,
        //             point: '0.0',
        //             settings: {
        //                 case_sensitive: true,
        //                 option_type: 'text',
        //                 options_layout: 'vertical',
        //                 required: false,
        //                 unordered: false,
        //             },
        //             sort: 3,
        //             sub_subjects: [],
        //             type: 'single_selection',
        //         },
        //         {
        //             answer_number: 0,
        //             data: null,
        //             description: '<p>选项5</p>',
        //             difficulty_level: 'medium',
        //             id: 10016848967,
        //             last_updated_at: '2024-08-07T16:17:37Z',
        //             note: null,
        //             options: [],
        //             parent_id: 10016848931,
        //             point: '0.0',
        //             settings: {
        //                 case_sensitive: true,
        //                 option_type: 'text',
        //                 options_layout: 'vertical',
        //                 required: false,
        //                 unordered: false,
        //             },
        //             sort: 4,
        //             sub_subjects: [],
        //             type: 'single_selection',
        //         },
        //         {
        //             answer_number: 0,
        //             data: null,
        //             description: '<p>选项6</p>',
        //             difficulty_level: 'medium',
        //             id: 10016848974,
        //             last_updated_at: '2024-08-07T16:17:37Z',
        //             note: null,
        //             options: [],
        //             parent_id: 10016848931,
        //             point: '0.0',
        //             settings: {
        //                 case_sensitive: true,
        //                 option_type: 'text',
        //                 options_layout: 'vertical',
        //                 required: false,
        //                 unordered: false,
        //             },
        //             sort: 5,
        //             sub_subjects: [],
        //             type: 'single_selection',
        //         },
        //     ],
        //     type: 'matching',
        // },

        // 填空题 带图片
        {
            answer_number: 7,
            data: {
                is_conflict_subject: false,
                is_random_subject: true,
            },
            description:
                '<p>（14分）某分时系统的进程出现如图所示的状态变化。</p>\n<p style="text-align: center;"><img alt="" height="194" src="/api/uploads/1598811/in-rich-content?created_at=1647446728" width="394"></p>\n<p>（1）你认为该系统采用的进程调度算法是<span class="__blank__" contenteditable="false" data-id="1647446728528">&nbsp;&nbsp;&nbsp;<span class="circle-number">1</span>&nbsp;&nbsp;&nbsp;</span> 。</p>\n<p>A．时间片轮转法</p>\n<p>B．先来先服务法</p>\n<p>C．优先级法</p>\n<p>D．最佳置换法</p>\n<p>（2）根据以下A-F的提示，标识图中从①到⑥所示的每一个状态变化的原因。</p>\n<p>A：进程被选中，变成运行态；</p>\n<p>B：时间片到，运行的进程排入就绪队列尾部；</p>\n<p>C：运行的进程启动打印机，等待打印；</p>\n<p>D：打印工作结束，阻塞的进程排入就绪队列尾部；</p>\n<p>E：等待磁盘读文件工作；</p>\n<p>F：磁盘传输信息结束，阻塞的进程排入就绪队列尾部。</p>\n<p>①<span class="__blank__" contenteditable="false" data-id="1647446728528">&nbsp;&nbsp;&nbsp;<span class="circle-number">2</span>&nbsp;&nbsp;&nbsp;</span> ； ②<span class="__blank__" contenteditable="false" data-id="1647446728528">&nbsp;&nbsp;&nbsp;<span class="circle-number">3</span>&nbsp;&nbsp;&nbsp;</span> ； ③<span class="__blank__" contenteditable="false" data-id="1647446728528">&nbsp;&nbsp;&nbsp;<span class="circle-number">4</span>&nbsp;&nbsp;&nbsp;</span> ； ④<span class="__blank__" contenteditable="false" data-id="1647446728528">&nbsp;&nbsp;&nbsp;<span class="circle-number">5</span>&nbsp;&nbsp;&nbsp;</span> ； ⑤<span class="__blank__" contenteditable="false" data-id="1647446728528">&nbsp;&nbsp;&nbsp;<span class="circle-number">6</span>&nbsp;&nbsp;&nbsp;</span> ； ⑥<span class="__blank__" contenteditable="false" data-id="1647446728528">&nbsp;&nbsp;&nbsp;<span class="circle-number">7</span>&nbsp;&nbsp;&nbsp;</span> </p>',
            difficulty_level: 'medium',
            id: 10016848998,
            last_updated_at: '2024-08-07T16:17:37Z',
            note: null,
            options: [],
            parent_id: null,
            point: '14.0',
            settings: {
                case_sensitive: true,
                required: false,
                unordered: false,
            },
            sort: 26,
            sub_subjects: [],
            type: 'fill_in_blank',
        },

        // // 完形填空题
        // {
        //     answer_number: 0,
        //     data: {
        //         is_conflict_subject: false,
        //         is_random_subject: true,
        //     },
        //     description:
        //         '<p>二、阅读理解：阅读下面的短文，根据文章内容从A、B、C三个选项中选出一个最佳选项。</p>\n<p>Building a house costs quite a lot of money<span style="font-family: 宋体;">．</span>Suppose you plan to build a house<span style="font-family: 宋体;">．</span>Your first step will be to find a right piece of land<span style="font-family: 宋体;">．</span>Your choice will depend on many different things<span style="font-family: 宋体;">．</span>You will probably try to find a sunny place, with pleasant surroundings(<span style="font-family: 宋体;">环境</span>) near shops and bus stops, not too far from your friends and the place where you work<span style="font-family: 宋体;">．</span></p>\n<p>Next you will find a good builder, and together with the builder you will work out a plan<span style="font-family: 宋体;">．</span>The builder will draw the plan<span style="font-family: 宋体;">．</span>It will show the number of rooms, their position and size, and other parts, which must be noticed, such as windows, doors, and electric outlets<span style="font-family: 宋体;">．</span>The builder will work out how much money is needed to build your house<span style="font-family: 宋体;">．</span>He will work out the cost of the wood, bricks, the glass, and everything else that must be used in building the house<span style="font-family: 宋体;">．</span>Later on, when he starts to build, this estimate(<span style="font-family: 宋体;">预算</span>)must be corrected and revised(<span style="font-family: 宋体;">修订</span>)<span style="font-family: 宋体;">．</span>His estimate is based on existing prices, but prices of such things may change, and many other things may happen between the time when he makes the estimate and the time when he builds the house<span style="font-family: 宋体;">．</span></p>\n<p>When the builder gives his estimate, you may wish to change your plan<span style="font-family: 宋体;">．</span>(You may also wish to change your builder, if his estimate is too high!)You may find that the house you wanted at first costs too much, or that you can spend a little more and add something to your plan<span style="font-family: 宋体;">．</span>The builder\'s estimate depends on the plan, but the final plan depends on the builder\'s estimate<span style="font-family: 宋体;">．</span></p>\n<p> </p>\n<p>1．The best title of this passage would be “<span> <span class="__blank__" contenteditable="false" data-id="1647533990152">&nbsp;&nbsp;&nbsp;<span class="circle-number">1</span>&nbsp;&nbsp;&nbsp;</span> </span>”<span style="font-family: 宋体;">．</span></p>\n<p style="margin-left: 30px;">A<span style="font-family: 宋体;">．</span>Building a House Costs Much Money</p>\n<p style="margin-left: 30px;">B<span style="font-family: 宋体;">．</span>Estimate Is Important</p>\n<p style="margin-left: 30px;">C<span style="font-family: 宋体;">．</span>Planning a House</p>\n<p>2．The first thing for a person to build a house is <span> <span class="__blank__" contenteditable="false" data-id="1647533990152">&nbsp;&nbsp;&nbsp;<span class="circle-number">2</span>&nbsp;&nbsp;&nbsp;</span> </span><span style="font-family: 宋体;">．</span></p>\n<p style="margin-left: 30px;">A<span style="font-family: 宋体;">．</span>to get as much money as possible</p>\n<p style="margin-left: 30px;">B<span style="font-family: 宋体;">．</span>to find a suitable piece of land</p>\n<p style="margin-left: 30px;">C<span style="font-family: 宋体;">．</span>to work out a plan</p>\n<p>3<span style="font-family: 宋体;">．</span> The phrase “draw a plan” in this passage means <span> <span class="__blank__" contenteditable="false" data-id="1647533990152">&nbsp;&nbsp;&nbsp;<span class="circle-number">3</span>&nbsp;&nbsp;&nbsp;</span> </span><span style="font-family: 宋体;">．</span></p>\n<p style="margin-left: 30px;"> A<span style="font-family: 宋体;">．</span>making a picture of a building or a room</p>\n<p style="margin-left: 30px;"> B<span style="font-family: 宋体;">．</span>making a plan</p>\n<p style="margin-left: 30px;"> C<span style="font-family: 宋体;">．</span>working out a plan</p>\n<p>4．When the builder starts to build a house, his estimate will have to be corrected and revised because <span> <span class="__blank__" contenteditable="false" data-id="1647533990152">&nbsp;&nbsp;&nbsp;<span class="circle-number">4</span>&nbsp;&nbsp;&nbsp;</span> </span><span style="font-family: 宋体;">．</span></p>\n<p style="margin-left: 30px;">A<span style="font-family: 宋体;">．</span>it is wrongly worked out by a workman</p>\n<p style="margin-left: 30px;">B<span style="font-family: 宋体;">．</span>the future owner of the house thinks the estimate is so high that he cannot afford the building<span style="font-family: 宋体;">．</span></p>\n<p style="margin-left: 30px;">C<span style="font-family: 宋体;">．</span>The prices of building materials and the expenses(<span style="font-family: 宋体;">费用</span>)of labor may be different from the</p>\n<p>original prices and expenses</p>\n<p>5．What is the relationship(<span style="font-family: 宋体;">关系</span>)between the estimate and the plan?<span> <span class="__blank__" contenteditable="false" data-id="1647533990152">&nbsp;&nbsp;&nbsp;<span class="circle-number">5</span>&nbsp;&nbsp;&nbsp;</span> </span></p>\n<p style="margin-left: 30px;">A<span style="font-family: 宋体;">．</span>The plan depends on the estimate<span style="font-family: 宋体;">．</span></p>\n<p style="margin-left: 30px;">B<span style="font-family: 宋体;">．</span>The plan has nothing to do with the estimate<span style="font-family: 宋体;">．</span></p>\n<p style="margin-left: 30px;">C<span style="font-family: 宋体;">．</span>The estimate and the plan depend on each other<span style="font-family: 宋体;">．</span></p>',
        //     difficulty_level: 'medium',
        //     id: 10017353265,
        //     last_updated_at: '2024-08-07T22:48:12Z',
        //     note: null,
        //     options: [],
        //     parent_id: null,
        //     point: '50.0',
        //     settings: {
        //         case_sensitive: true,
        //         option_type: 'text',
        //         options_layout: 'vertical',
        //         required: false,
        //         unordered: false,
        //     },
        //     sort: 6,
        //     sub_subjects: [
        //         {
        //             answer_number: 0,
        //             data: null,
        //             description: '__1__',
        //             difficulty_level: 'medium',
        //             id: 10017353276,
        //             last_updated_at: '2024-08-07T22:48:12Z',
        //             note: null,
        //             options: [
        //                 {
        //                     content: 'A',
        //                     id: 10045423523,
        //                     sort: 0,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content: 'B',
        //                     id: 10045423524,
        //                     sort: 1,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content: 'C',
        //                     id: 10045423525,
        //                     sort: 2,
        //                     type: 'text',
        //                 },
        //             ],
        //             parent_id: 10017353265,
        //             point: '1.0',
        //             settings: {
        //                 case_sensitive: true,
        //                 option_type: 'text',
        //                 options_layout: 'vertical',
        //                 required: false,
        //                 unordered: false,
        //             },
        //             sort: 0,
        //             sub_subjects: [],
        //             type: 'single_selection',
        //         },
        //         {
        //             answer_number: 0,
        //             data: null,
        //             description: '__2__',
        //             difficulty_level: 'medium',
        //             id: 10017353287,
        //             last_updated_at: '2024-08-07T22:48:12Z',
        //             note: null,
        //             options: [
        //                 {
        //                     content: 'A',
        //                     id: 10045423549,
        //                     sort: 0,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content: 'B',
        //                     id: 10045423550,
        //                     sort: 1,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content: 'C',
        //                     id: 10045423551,
        //                     sort: 2,
        //                     type: 'text',
        //                 },
        //             ],
        //             parent_id: 10017353265,
        //             point: '1.0',
        //             settings: {
        //                 case_sensitive: true,
        //                 option_type: 'text',
        //                 options_layout: 'vertical',
        //                 required: false,
        //                 unordered: false,
        //             },
        //             sort: 1,
        //             sub_subjects: [],
        //             type: 'single_selection',
        //         },
        //         {
        //             answer_number: 0,
        //             data: null,
        //             description: '__3__',
        //             difficulty_level: 'medium',
        //             id: 10017353297,
        //             last_updated_at: '2024-08-07T22:48:12Z',
        //             note: null,
        //             options: [
        //                 {
        //                     content: 'A',
        //                     id: 10045423572,
        //                     sort: 0,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content: 'B',
        //                     id: 10045423573,
        //                     sort: 1,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content: 'C',
        //                     id: 10045423574,
        //                     sort: 2,
        //                     type: 'text',
        //                 },
        //             ],
        //             parent_id: 10017353265,
        //             point: '1.0',
        //             settings: {
        //                 case_sensitive: true,
        //                 option_type: 'text',
        //                 options_layout: 'vertical',
        //                 required: false,
        //                 unordered: false,
        //             },
        //             sort: 2,
        //             sub_subjects: [],
        //             type: 'single_selection',
        //         },
        //         {
        //             answer_number: 0,
        //             data: null,
        //             description: '__4__',
        //             difficulty_level: 'medium',
        //             id: 10017353306,
        //             last_updated_at: '2024-08-07T22:48:12Z',
        //             note: null,
        //             options: [
        //                 {
        //                     content: 'A',
        //                     id: 10045423592,
        //                     sort: 0,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content: 'B',
        //                     id: 10045423593,
        //                     sort: 1,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content: 'C',
        //                     id: 10045423594,
        //                     sort: 2,
        //                     type: 'text',
        //                 },
        //             ],
        //             parent_id: 10017353265,
        //             point: '1.0',
        //             settings: {
        //                 case_sensitive: true,
        //                 option_type: 'text',
        //                 options_layout: 'vertical',
        //                 required: false,
        //                 unordered: false,
        //             },
        //             sort: 3,
        //             sub_subjects: [],
        //             type: 'single_selection',
        //         },
        //         {
        //             answer_number: 0,
        //             data: null,
        //             description: '__5__',
        //             difficulty_level: 'medium',
        //             id: 10017353316,
        //             last_updated_at: '2024-08-07T22:48:12Z',
        //             note: null,
        //             options: [
        //                 {
        //                     content: 'A',
        //                     id: 10045423607,
        //                     sort: 0,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content: 'B',
        //                     id: 10045423608,
        //                     sort: 1,
        //                     type: 'text',
        //                 },
        //                 {
        //                     content: 'C',
        //                     id: 10045423609,
        //                     sort: 2,
        //                     type: 'text',
        //                 },
        //             ],
        //             parent_id: 10017353265,
        //             point: '1.0',
        //             settings: {
        //                 case_sensitive: true,
        //                 option_type: 'text',
        //                 options_layout: 'vertical',
        //                 required: false,
        //                 unordered: false,
        //             },
        //             sort: 4,
        //             sub_subjects: [],
        //             type: 'single_selection',
        //         },
        //     ],
        //     type: 'cloze',
        // },
    ];

    (async () => {
        let res = await handleQuestionList(subjects, 'test');
        console.log(JSON.stringify(res));
    })();
}
