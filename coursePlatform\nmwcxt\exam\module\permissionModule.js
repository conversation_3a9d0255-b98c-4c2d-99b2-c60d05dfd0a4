let api = require('../../utils/api.js');

async function permissionModule(globalStore, infoLogger) {
    let { examModuleObj, userInfoVo } = globalStore;

    //向服务器发送 考前承诺
    let examModularExamItemObj = { user_info: JSON.stringify(userInfoVo), exam_id: examModuleObj.examId, exam_status: '1' };
    let examModularExamItemRes = await api.examModularExamItemAction(examModularExamItemObj);

    // console.log(examModularExamItemRes)
    // console.log(examModularExamItemRes.code)
    // console.log(examModularExamItemRes.code == 1000)
    // await new Promise(r => setTimeout(r, 1000000));


    if(examModularExamItemRes.message.includes('您正在考试中')){
        await infoLogger('正在考试中,无需进行考前承诺');
        return
    }

    if (examModularExamItemRes.code == 1000) {
        await infoLogger(`考前承诺成功,${examModularExamItemRes.code}`, 'green');
    } else {
        await infoLogger(`考前承诺失败,${examModularExamItemRes.code}`, 'red');
        throw new Error('考前承诺失败');
    }

    // //先判断是否需要，再进行考前承诺
    // let getExamListDataObj = { user_info: JSON.stringify(userInfoVo), exam_id: examModuleObj.examId };
    // let getExamListRes = await api.getItemList(getExamListDataObj);

    // //如果没有题目，说明没有进行“考前承诺”
    // if (getExamListRes.message.includes('无题库数据')) {
    //     await infoLogger('无题库数据,进行考前承诺');
    //     //向服务器发送 考前承诺
    //     let examModularExamItemObj = { user_info: JSON.stringify(userInfoVo), exam_id: examModuleObj.examId, exam_status: '0' };
    //     let examModularExamItemRes = await api.examModularExamItemAction(examModularExamItemObj);
    //     if (examModularExamItemRes.code == 1000) {
    //         await infoLogger(`考前承诺成功,${examModularExamItemRes.code}`, 'green');
    //     } else {
    //         await infoLogger(`考前承诺失败,${examModularExamItemRes.code}`, 'red');
    //         throw new Error('考前承诺失败');
    //     }
    // } else {
    //     await infoLogger('已经进行过考前承诺');
    // }
}

module.exports = permissionModule;
