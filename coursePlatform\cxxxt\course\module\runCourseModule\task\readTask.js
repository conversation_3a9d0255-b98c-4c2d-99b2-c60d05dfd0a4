let pageTools = require('../../../../../utils/pageTools.js');
let api = require('../../../../utils/api.js');
async function documentTask(courseStore, infoLogger, attachment, mArgJson, attachLogStr, chapterCookieStr, chapterId) {
    //等待5秒，让文档加载完成
    await new Promise(r => setTimeout(r, 2000));

    let attachmentName = attachment.property.name || attachment.property.title;

    //获取jobid
    let jobid = attachment.jobid || attachment.property.jobid;

    let documentPath = 'https://mooc1.chaoxing.com/ananas/job/readv2';
    let documentParams = {
        jobid: jobid,
        knowledgeid: chapterId,
        courseid: courseStore.courseId,
        clazzid: courseStore.classId,
        jtoken: attachment.jtoken,
        _dc: String(Math.round(new Date())),
    };
    let documentUrl = pageTools.buildUrl(documentPath, documentParams);
    let doDocumentResult = await api.submitDocumentTask(documentUrl, chapterCookieStr);

    // api返回结果
    if (false) {
        doDocumentResult = { msg: '添加考核点成功', status: true };
    }

    if (doDocumentResult && doDocumentResult.status) {
        await infoLogger(`${attachLogStr}阅读文档完成：${attachmentName}`, 'green');
        return;
    } else {
        await infoLogger(`${attachLogStr}阅读文档失败，跳过任务：${attachmentName}`, 'red');
        return;
    }

    // //处理文档结果
    // if (!doDocumentResult) {
    //     await infoLogger(`${attachLogStr}阅读文档失败，跳过任务：${attachment.property.name}`, 'red');
    //     return;
    // }
    // try {
    //     let doDocumentJson = JSON.parse(doDocumentResult.responseText);
    //     if (doDocumentJson.status) {
    //         await infoLogger(`${attachLogStr}文档任务完成：${attachment.property.name}`, 'green');
    //         return;
    //     } else {
    //         await infoLogger(`${attachLogStr}文档任务失败：${attachment.property.name}`, 'red');
    //         return;
    //     }
    // } catch (e) {
    //     await infoLogger(`${attachLogStr}解析文档内容失败：${attachment.property.name},${documentUrl}`, 'red');
    //     return;
    // }
}

module.exports = documentTask;

// 对应的fecth，只有把文档拉到底才会触发
// fetch(
//     "https://mooc1.chaoxing.com/ananas/job/document?jobid=1648176139023803&knowledgeid=511752129&courseid=221858211&clazzid=96152734&jtoken=cd57ffa89e6cf7161e94be738f9b554f&checkMicroTopic=true&microTopicId=undefined&_dc=1717751456570",
//     {
//         headers: {
//             accept: "*/*",
//             "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
//             "sec-ch-ua": '"Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
//             "sec-ch-ua-mobile": "?0",
//             "sec-ch-ua-platform": '"Windows"',
//             "sec-fetch-dest": "empty",
//             "sec-fetch-mode": "cors",
//             "sec-fetch-site": "same-origin",
//             "x-requested-with": "XMLHttpRequest",
//             cookie: 'k8s-ed=1717491083.009.1707.398184; jrose=F9FA787C2A9C9C3B74DE2BCDA0E300FD.html-editor-a-3050727248-s1jwt; k8s=1717491073.353.1428.209036; route=f9c314690d8e5d436efa7770254d0199; writenote=yes; lv=0; source=""; thirdRegist=0; doubleSpeedValue=2; jrose=D141A969494CCFBE158D440D9C2D2B56.mooc-3869458785-h512b; orgfid=28919; registerCode=0001; videojs_id=2312133; tl=1; schoolId=119768; uname=2334102986; fid=133444; _uid=*********; uf=b2d2c93beefa90dc3cd7d6cedfaeeefd82f6d048c8c309449daea7b6272ba2e30ecc5e0cdb0af8edc070bbb7f1e7755be64bab884f65d48917a3131c5323ef26d54d32d139944a55fd68be96b6183b1a48d2a5fdd35040c407e733ddacacb7e6ff7167ffe2f9a8fa; _d=1717750118778; UID=*********; vc=80673E96AB51D14167C56CF5B54D668D; vc2=D9E3E5BFA01A689895A594518A1216F1; vc3=Z1qsEJZ2Qg9vHBgtP9CzCIYHBg3N9qus8mkEzBgtqAe72Wi4AV9Mw7kWW%2FenkXDLqbYHCeEsiByhjcf4r5ZGI44emVi5ytprGnPaOxOEHJt32Kpjoo3J3hlasPaqhDLdYtX%2BGlpV%2FcCcEZKr1bSfdQXjUzp0UZrH7GdXf%2BrF75E%3D6ced2a623e97aff21c842e449f4efb07; cx_p_token=8ea834aa1ebd8d45b4d279f6b5cf6d2f; p_auth_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1aWQiOiIyODM5Mzc1MTciLCJsb2dpblRpbWUiOjE3MTc3NTAxMTg3ODAsImV4cCI6MTcxODM1NDkxOH0.HRQvP9XjBB4QzVM3EyZ0TzPL1d7ECCr0YAH-wzaxeH4; xxtenc=716fc4a602f93034894ee129b77a4936; DSSTASH_LOG=C_38-UN_0-US_*********-T_1717750118780',
//             Referer: "https://mooc1.chaoxing.com/ananas/modules/pdf/index.html?v=**************",
//             "Referrer-Policy": "unsafe-url",
//         },
//         body: null,
//         method: "GET",
//     }
// );
