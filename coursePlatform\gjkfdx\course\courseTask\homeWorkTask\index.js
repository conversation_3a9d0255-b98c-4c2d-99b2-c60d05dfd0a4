let pageTools = require('../../../../utils/pageTools.js');
let api = require('../../../utils/api.js');
let collectHomework = require('./collectHomework.js');
let path = require('path');
let crypto = require('crypto');
let Model = require('../../../../../config/sequelize.config.js');
let uploadFile = require('./uploadFile.js');

async function homeWorkTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr) {
    let homeWorkLogStr = `${taskLogStr} <${taskObj.title}> ${taskObj.type}`;

    // 课件的唯一标识符
    // 刚开始用的courseid和taskid，后来发现这两个不是唯一标识符
    // let formatName = `${courseObj.name}-${courseObj.id}-${taskObj.title}-${taskObj.id}`;
    // 现在用的imported_track_id这个字段，但是有时候会是null
    let formatName;
    if (taskObj.imported_track_id) {
        formatName = `${taskObj.title}-${taskObj.imported_track_id}`;
    }
    // 如果imported_track_id为null，就用大作业描述
    if (!taskObj.imported_track_id && taskObj.data.description) {
        let descriptionHash = crypto.createHash('md5').update(taskObj.data.description).digest('hex');
        formatName = `${taskObj.title}-${descriptionHash}`;
    }
    // 如果imported_track_id为null，就用大作业描述
    if (!taskObj.imported_track_id && !taskObj.data.description) {
        await infoLogger(`${homeWorkLogStr} 没有课件的唯一标识符，跳过`, 'red');
        await infoLogger(`${homeWorkLogStr} 没有课件的唯一标识符，跳过`, 'red');
        await infoLogger(`${homeWorkLogStr} 没有课件的唯一标识符，跳过`, 'red');
        return;
    }
    // 在Windows系统中，文件名不能包含以下字符：< > : " | ? * \
    formatName = formatName.replace(/[<>:"|?*\\]/g, '_');
    formatName = formatName.trim();

    await infoLogger(`formatName:${formatName}`);

    // 获取本地存放文件目录 文件结构 专业/课程名称/课件名称id
    let basePath = `static/gkHomework/${globalStore.major}/${courseObj.name}/${formatName}`;
    let baseFilePath = process.pkg ? path.join(path.dirname(process.execPath), basePath) : path.resolve(basePath);

    let examId = taskObj.id;

    // // 打开课件页面
    // let taskUrl = `https://lms.ouchn.cn/course/${courseObj.id}/learning-activity/full-screen#/${taskObj.id}`;
    // await pageTools.gotoWithRetry(mainPage, taskUrl, { waitUntil: 'networkidle0' }, 3);
    // await new Promise(r => setTimeout(r, 3 * 1000));
    let cookieStr = await pageTools.getPageCookies(mainPage);

    // 重新获取大作业的taskObj对象
    taskObj = await api.getTaskInfo(taskObj.id, cookieStr);
    
    

    if (globalStore.others.includes('收集课程')) {
        await collectHomework(infoLogger, courseObj, cookieStr, examId, homeWorkLogStr, formatName, globalStore, baseFilePath);
        return;
    }

    if (taskObj.user_submit_count * 1 > 0) {
        await infoLogger(`${homeWorkLogStr}，大作业已经提交，跳过`);
        return;
    }

    let homeWorkRes = await Model.bank.findOne({
        where: {
            platform: '国家开放大学',
            type: '国开大作业',
            content: formatName,
        },
    });

    if (!homeWorkRes) {
        await infoLogger(`${homeWorkLogStr} 没有答案，跳过`, 'red');
        globalStore.warningMessage += `${courseObj.name}大作业没有答案`;
        return;
    }

    // 文本类大作业
    if (!homeWorkRes.answers == '上传类') {
        await infoLogger(`${homeWorkLogStr} 当前作业类型为：文本类`);
        let submitRes = await api.submitHomework(taskObj.id, homeWorkRes.answers, '', cookieStr);
        await infoLogger(`${homeWorkLogStr} 已经提交，60秒后进行下一个课件`, 'green');
        await new Promise(r => setTimeout(r, 60 * 1000));
        return;
    }

    // 上传类大作业
    if (homeWorkRes.answers == '上传类') {
        await infoLogger(`${homeWorkLogStr} 当前作业类型为：文件上传类`);
        await uploadFile(infoLogger, globalStore, taskObj, homeWorkLogStr, formatName, cookieStr, baseFilePath, courseObj);
        await infoLogger(`${homeWorkLogStr} 已经提交，60秒后进行下一个课件`, 'green');
        await new Promise(r => setTimeout(r, 60 * 1000));
        return;
    }
}

module.exports = homeWorkTask;
