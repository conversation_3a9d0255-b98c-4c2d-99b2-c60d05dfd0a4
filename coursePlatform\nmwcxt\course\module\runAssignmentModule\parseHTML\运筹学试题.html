<table id="tblDataList" onselectstart="return false" width="100%" cellpadding="3" _default_attr="1" cellspacing="1">
    <tbody>
        <tr id="tr_tblDataList_0" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_0');}">
            <td scoreid="437022263928160257" id="_DataListTD_tblDataList_0">
                <table width="100%" border="0" id="tblItem_419204695175725139" islabel="1" keylist="" style="">
                    <tbody>
                        <tr>
                            <td>
                                <script language="javascript">
                                    document.write($wapper.api.getDisplayFormat('<b>一、单选题<\/b><SPAN IsScoreRemark="1"><\/SPAN>'));
                                </script>
                                <b>一、单选题</b><span isscoreremark="1">&nbsp;&nbsp;<font color="gray">(第1-5题每题5分)</font></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="tr_tblDataList_1" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_1');}">
            <td scoreid="437022263928160258" id="_DataListTD_tblDataList_1">
                <table width="100%" border="0" id="tblItem_419204695175725113" islabel="0" keylist="lemonysoft_item_key_1046496_35637" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">1.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">互为对偶的两个问题存在关系(　 　 )<\/div><div style="line-height:20px;font-size:10pt"><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046496_35637_0 name="lemonysoft_item_key_1046496_35637" ><TD>(A)<TD><label for=lemonysoft_item_key_1046496_35637_0>原问题无可行解，对偶问题也无可行解<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046496_35637_1 name="lemonysoft_item_key_1046496_35637" ><TD>(B)<TD><label for=lemonysoft_item_key_1046496_35637_1>对偶问题有可行解，原问题也有可行解<\/label><TR><TD><input type=radio value="C" id=lemonysoft_item_key_1046496_35637_2 name="lemonysoft_item_key_1046496_35637" ><TD>(C)<TD><label for=lemonysoft_item_key_1046496_35637_2>原问题有最优解解，对偶问题可能没有最优解<\/label><TR><TD><input type=radio value="D" id=lemonysoft_item_key_1046496_35637_3 name="lemonysoft_item_key_1046496_35637" ><TD>(D)<TD><label for=lemonysoft_item_key_1046496_35637_3>原问题无界解，对偶问题无可行解<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">互为对偶的两个问题存在关系(　 　 )</div>
                                <div style="line-height: 20px; font-size: 10pt">
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046496_35637_0"
                                                            name="lemonysoft_item_key_1046496_35637"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td>
                                                        <label for="lemonysoft_item_key_1046496_35637_0">原问题无可行解，对偶问题也无可行解</label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046496_35637_1"
                                                            name="lemonysoft_item_key_1046496_35637"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td>
                                                        <label for="lemonysoft_item_key_1046496_35637_1">对偶问题有可行解，原问题也有可行解</label>
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="C"
                                                            id="lemonysoft_item_key_1046496_35637_2"
                                                            name="lemonysoft_item_key_1046496_35637"
                                                        />
                                                    </td>
                                                    <td>(C)</td>
                                                    <td>
                                                        <label for="lemonysoft_item_key_1046496_35637_2"
                                                            >原问题有最优解解，对偶问题可能没有最优解</label
                                                        >
                                                    </td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="D"
                                                            id="lemonysoft_item_key_1046496_35637_3"
                                                            name="lemonysoft_item_key_1046496_35637"
                                                        />
                                                    </td>
                                                    <td>(D)</td>
                                                    <td><label for="lemonysoft_item_key_1046496_35637_3">原问题无界解，对偶问题无可行解</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046496_35637", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="tr_tblDataList_2" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_2');}">
            <td scoreid="437022263928160259" id="_DataListTD_tblDataList_2">
                <table width="100%" border="0" id="tblItem_419204695175725114" islabel="0" keylist="lemonysoft_item_key_1046495_33992" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">2.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">X是线性规划的基本可行解则有(　 　 )<\/div><div style="line-height:20px;font-size:10pt"><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046495_33992_0 name="lemonysoft_item_key_1046495_33992" ><TD>(A)<TD><label for=lemonysoft_item_key_1046495_33992_0>X中的基变量非零，非基变量为零<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046495_33992_1 name="lemonysoft_item_key_1046495_33992" ><TD>(B)<TD><label for=lemonysoft_item_key_1046495_33992_1>X不一定满足约束条件<\/label><TR><TD><input type=radio value="C" id=lemonysoft_item_key_1046495_33992_2 name="lemonysoft_item_key_1046495_33992" ><TD>(C)<TD><label for=lemonysoft_item_key_1046495_33992_2>X中的基变量非负，非基变量为零<\/label><TR><TD><input type=radio value="D" id=lemonysoft_item_key_1046495_33992_3 name="lemonysoft_item_key_1046495_33992" ><TD>(D)<TD><label for=lemonysoft_item_key_1046495_33992_3>X是最优解<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">X是线性规划的基本可行解则有(　 　 )</div>
                                <div style="line-height: 20px; font-size: 10pt">
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046495_33992_0"
                                                            name="lemonysoft_item_key_1046495_33992"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046495_33992_0">X中的基变量非零，非基变量为零</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046495_33992_1"
                                                            name="lemonysoft_item_key_1046495_33992"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046495_33992_1">X不一定满足约束条件</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="C"
                                                            id="lemonysoft_item_key_1046495_33992_2"
                                                            name="lemonysoft_item_key_1046495_33992"
                                                        />
                                                    </td>
                                                    <td>(C)</td>
                                                    <td><label for="lemonysoft_item_key_1046495_33992_2">X中的基变量非负，非基变量为零</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="D"
                                                            id="lemonysoft_item_key_1046495_33992_3"
                                                            name="lemonysoft_item_key_1046495_33992"
                                                        />
                                                    </td>
                                                    <td>(D)</td>
                                                    <td><label for="lemonysoft_item_key_1046495_33992_3">X是最优解</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046495_33992", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="tr_tblDataList_3" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_3');}">
            <td scoreid="437022263928160260" id="_DataListTD_tblDataList_3">
                <table width="100%" border="0" id="tblItem_419204695175725116" islabel="0" keylist="lemonysoft_item_key_1046494_65440" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">3.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">线性规划可行域的顶点一定是(　 　 )<\/div><div style="line-height:20px;font-size:10pt"><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046494_65440_0 name="lemonysoft_item_key_1046494_65440" ><TD>(A)<TD><label for=lemonysoft_item_key_1046494_65440_0>基本可行解<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046494_65440_1 name="lemonysoft_item_key_1046494_65440" ><TD>(B)<TD><label for=lemonysoft_item_key_1046494_65440_1>非基本解<\/label><TR><TD><input type=radio value="C" id=lemonysoft_item_key_1046494_65440_2 name="lemonysoft_item_key_1046494_65440" ><TD>(C)<TD><label for=lemonysoft_item_key_1046494_65440_2>非可行解<\/label><TR><TD><input type=radio value="D" id=lemonysoft_item_key_1046494_65440_3 name="lemonysoft_item_key_1046494_65440" ><TD>(D)<TD><label for=lemonysoft_item_key_1046494_65440_3>最优解<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">线性规划可行域的顶点一定是(　 　 )</div>
                                <div style="line-height: 20px; font-size: 10pt">
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046494_65440_0"
                                                            name="lemonysoft_item_key_1046494_65440"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046494_65440_0">基本可行解</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046494_65440_1"
                                                            name="lemonysoft_item_key_1046494_65440"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046494_65440_1">非基本解</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="C"
                                                            id="lemonysoft_item_key_1046494_65440_2"
                                                            name="lemonysoft_item_key_1046494_65440"
                                                        />
                                                    </td>
                                                    <td>(C)</td>
                                                    <td><label for="lemonysoft_item_key_1046494_65440_2">非可行解</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="D"
                                                            id="lemonysoft_item_key_1046494_65440_3"
                                                            name="lemonysoft_item_key_1046494_65440"
                                                        />
                                                    </td>
                                                    <td>(D)</td>
                                                    <td><label for="lemonysoft_item_key_1046494_65440_3">最优解</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046494_65440", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="tr_tblDataList_4" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_4');}">
            <td scoreid="437022263928160261" id="_DataListTD_tblDataList_4">
                <table width="100%" border="0" id="tblItem_419204695175725117" islabel="0" keylist="lemonysoft_item_key_1046493_17033" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">4.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">有3个产地4个销地的平衡运输问题模型具有特征(　 　 )　 　　<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046493_17033_0 name="lemonysoft_item_key_1046493_17033" ><TD>(A)<TD><label for=lemonysoft_item_key_1046493_17033_0>有7个变量<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046493_17033_1 name="lemonysoft_item_key_1046493_17033" ><TD>(B)<TD><label for=lemonysoft_item_key_1046493_17033_1>有12个约束<\/label><TR><TD><input type=radio value="C" id=lemonysoft_item_key_1046493_17033_2 name="lemonysoft_item_key_1046493_17033" ><TD>(C)<TD><label for=lemonysoft_item_key_1046493_17033_2>有6约束<\/label><TR><TD><input type=radio value="D" id=lemonysoft_item_key_1046493_17033_3 name="lemonysoft_item_key_1046493_17033" ><TD>(D)<TD><label for=lemonysoft_item_key_1046493_17033_3>有6个基变量<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    有3个产地4个销地的平衡运输问题模型具有特征(　 　 )　 　　
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046493_17033_0"
                                                            name="lemonysoft_item_key_1046493_17033"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046493_17033_0">有7个变量</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046493_17033_1"
                                                            name="lemonysoft_item_key_1046493_17033"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046493_17033_1">有12个约束</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="C"
                                                            id="lemonysoft_item_key_1046493_17033_2"
                                                            name="lemonysoft_item_key_1046493_17033"
                                                        />
                                                    </td>
                                                    <td>(C)</td>
                                                    <td><label for="lemonysoft_item_key_1046493_17033_2">有6约束</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="D"
                                                            id="lemonysoft_item_key_1046493_17033_3"
                                                            name="lemonysoft_item_key_1046493_17033"
                                                        />
                                                    </td>
                                                    <td>(D)</td>
                                                    <td><label for="lemonysoft_item_key_1046493_17033_3">有6个基变量</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046493_17033", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="tr_tblDataList_5" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_5');}">
            <td scoreid="437022263928160262" id="_DataListTD_tblDataList_5">
                <table width="100%" border="0" id="tblItem_419204695175725118" islabel="0" keylist="lemonysoft_item_key_1046492_23520" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">5.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">原问题有5个变量3个约束，其对偶问题(　 　 )　 　 　<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046492_23520_0 name="lemonysoft_item_key_1046492_23520" ><TD>(A)<TD><label for=lemonysoft_item_key_1046492_23520_0>有3个变量5个约束<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046492_23520_1 name="lemonysoft_item_key_1046492_23520" ><TD>(B)<TD><label for=lemonysoft_item_key_1046492_23520_1>有5个变量3个约束<\/label><TR><TD><input type=radio value="C" id=lemonysoft_item_key_1046492_23520_2 name="lemonysoft_item_key_1046492_23520" ><TD>(C)<TD><label for=lemonysoft_item_key_1046492_23520_2>有5个变量5个约束<\/label><TR><TD><input type=radio value="D" id=lemonysoft_item_key_1046492_23520_3 name="lemonysoft_item_key_1046492_23520" ><TD>(D)<TD><label for=lemonysoft_item_key_1046492_23520_3>有3个变量3个约束<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    原问题有5个变量3个约束，其对偶问题(　 　 )　 　 　
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046492_23520_0"
                                                            name="lemonysoft_item_key_1046492_23520"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046492_23520_0">有3个变量5个约束</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046492_23520_1"
                                                            name="lemonysoft_item_key_1046492_23520"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046492_23520_1">有5个变量3个约束</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="C"
                                                            id="lemonysoft_item_key_1046492_23520_2"
                                                            name="lemonysoft_item_key_1046492_23520"
                                                        />
                                                    </td>
                                                    <td>(C)</td>
                                                    <td><label for="lemonysoft_item_key_1046492_23520_2">有5个变量5个约束</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="D"
                                                            id="lemonysoft_item_key_1046492_23520_3"
                                                            name="lemonysoft_item_key_1046492_23520"
                                                        />
                                                    </td>
                                                    <td>(D)</td>
                                                    <td><label for="lemonysoft_item_key_1046492_23520_3">有3个变量3个约束</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046492_23520", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="tr_tblDataList_6" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_6');}">
            <td scoreid="437022263928160263" id="_DataListTD_tblDataList_6">
                <table width="100%" border="0" id="tblItem_419204695175725141" islabel="1" keylist="" style="">
                    <script language="javascript">
                        var oSpan = document.getElementsByTagName("SPAN");
                        for (var i = oSpan.length - 1; i >= 0; i--) {
                            if (oSpan[i].getAttribute("IsScoreRemark") == "1") {
                                oSpan[i].innerHTML = "&nbsp;&nbsp;<font color=gray>(第1-5题每题5分)</font>";
                                break;
                            }
                        }
                    </script>
                    <tbody>
                        <tr>
                            <td>
                                <script language="javascript">
                                    document.write($wapper.api.getDisplayFormat('<b>二、判断题<\/b><SPAN IsScoreRemark="1"><\/SPAN>'));
                                </script>
                                <b>二、判断题</b><span isscoreremark="1">&nbsp;&nbsp;<font color="gray">(第1-15题每题5分)</font></span>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="tr_tblDataList_7" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_7');}">
            <td scoreid="437022263928160264" id="_DataListTD_tblDataList_7">
                <table width="100%" border="0" id="tblItem_419204695175725124" islabel="0" keylist="lemonysoft_item_key_1046488_28546" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">1.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">若原问题具有m个约束，则它的对偶问题具有m个变量<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046488_28546_0 name="lemonysoft_item_key_1046488_28546" ><TD>(A)<TD><label for=lemonysoft_item_key_1046488_28546_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046488_28546_1 name="lemonysoft_item_key_1046488_28546" ><TD>(B)<TD><label for=lemonysoft_item_key_1046488_28546_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    若原问题具有m个约束，则它的对偶问题具有m个变量
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046488_28546_0"
                                                            name="lemonysoft_item_key_1046488_28546"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046488_28546_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046488_28546_1"
                                                            name="lemonysoft_item_key_1046488_28546"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046488_28546_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046488_28546", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="tr_tblDataList_8" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_8');}">
            <td scoreid="437022263928160265" id="_DataListTD_tblDataList_8">
                <table width="100%" border="0" id="tblItem_419204695175725125" islabel="0" keylist="lemonysoft_item_key_1046487_7404" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">2.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">求极大值的目标值是各分枝的上界<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046487_7404_0 name="lemonysoft_item_key_1046487_7404" ><TD>(A)<TD><label for=lemonysoft_item_key_1046487_7404_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046487_7404_1 name="lemonysoft_item_key_1046487_7404" ><TD>(B)<TD><label for=lemonysoft_item_key_1046487_7404_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    求极大值的目标值是各分枝的上界
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046487_7404_0"
                                                            name="lemonysoft_item_key_1046487_7404"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046487_7404_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046487_7404_1"
                                                            name="lemonysoft_item_key_1046487_7404"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046487_7404_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046487_7404", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="tr_tblDataList_9" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_9');}">
            <td scoreid="437022263928160266" id="_DataListTD_tblDataList_9">
                <table width="100%" border="0" id="tblItem_419204695175725126" islabel="0" keylist="lemonysoft_item_key_1046486_12718" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">3.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">将指派问题效率表中的每一元素同时减去一个数后最优解不变<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046486_12718_0 name="lemonysoft_item_key_1046486_12718" ><TD>(A)<TD><label for=lemonysoft_item_key_1046486_12718_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046486_12718_1 name="lemonysoft_item_key_1046486_12718" ><TD>(B)<TD><label for=lemonysoft_item_key_1046486_12718_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    将指派问题效率表中的每一元素同时减去一个数后最优解不变
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046486_12718_0"
                                                            name="lemonysoft_item_key_1046486_12718"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046486_12718_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046486_12718_1"
                                                            name="lemonysoft_item_key_1046486_12718"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046486_12718_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046486_12718", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr
            id="tr_tblDataList_10"
            onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_10');}"
        >
            <td scoreid="437022263928160267" id="_DataListTD_tblDataList_10">
                <table width="100%" border="0" id="tblItem_419204695175725128" islabel="0" keylist="lemonysoft_item_key_1046485_50" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">4.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">人工变量出基后还可能再进基<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046485_50_0 name="lemonysoft_item_key_1046485_50" ><TD>(A)<TD><label for=lemonysoft_item_key_1046485_50_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046485_50_1 name="lemonysoft_item_key_1046485_50" ><TD>(B)<TD><label for=lemonysoft_item_key_1046485_50_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    人工变量出基后还可能再进基
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046485_50_0"
                                                            name="lemonysoft_item_key_1046485_50"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046485_50_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046485_50_1"
                                                            name="lemonysoft_item_key_1046485_50"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046485_50_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046485_50", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr
            id="tr_tblDataList_11"
            onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_11');}"
        >
            <td scoreid="437022263928160268" id="_DataListTD_tblDataList_11">
                <table width="100%" border="0" id="tblItem_419204695175725129" islabel="0" keylist="lemonysoft_item_key_1046484_18821" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">5.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">一对正负偏差变量至少一个等于零<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046484_18821_0 name="lemonysoft_item_key_1046484_18821" ><TD>(A)<TD><label for=lemonysoft_item_key_1046484_18821_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046484_18821_1 name="lemonysoft_item_key_1046484_18821" ><TD>(B)<TD><label for=lemonysoft_item_key_1046484_18821_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    一对正负偏差变量至少一个等于零
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046484_18821_0"
                                                            name="lemonysoft_item_key_1046484_18821"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046484_18821_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046484_18821_1"
                                                            name="lemonysoft_item_key_1046484_18821"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046484_18821_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046484_18821", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr
            id="tr_tblDataList_12"
            onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_12');}"
        >
            <td scoreid="437022263928160269" id="_DataListTD_tblDataList_12">
                <table width="100%" border="0" id="tblItem_419204695175725130" islabel="0" keylist="lemonysoft_item_key_1046483_19778" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">6.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">运输问题不一定存在最优解<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046483_19778_0 name="lemonysoft_item_key_1046483_19778" ><TD>(A)<TD><label for=lemonysoft_item_key_1046483_19778_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046483_19778_1 name="lemonysoft_item_key_1046483_19778" ><TD>(B)<TD><label for=lemonysoft_item_key_1046483_19778_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    运输问题不一定存在最优解
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046483_19778_0"
                                                            name="lemonysoft_item_key_1046483_19778"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046483_19778_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046483_19778_1"
                                                            name="lemonysoft_item_key_1046483_19778"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046483_19778_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046483_19778", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr
            id="tr_tblDataList_13"
            onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_13');}"
        >
            <td scoreid="437022263928160270" id="_DataListTD_tblDataList_13">
                <table width="100%" border="0" id="tblItem_419204695175725132" islabel="0" keylist="lemonysoft_item_key_1046482_27093" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">7.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">可行解是基本解<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046482_27093_0 name="lemonysoft_item_key_1046482_27093" ><TD>(A)<TD><label for=lemonysoft_item_key_1046482_27093_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046482_27093_1 name="lemonysoft_item_key_1046482_27093" ><TD>(B)<TD><label for=lemonysoft_item_key_1046482_27093_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    可行解是基本解
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046482_27093_0"
                                                            name="lemonysoft_item_key_1046482_27093"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046482_27093_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046482_27093_1"
                                                            name="lemonysoft_item_key_1046482_27093"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046482_27093_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046482_27093", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr
            id="tr_tblDataList_14"
            onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_14');}"
        >
            <td scoreid="437022263928160271" id="_DataListTD_tblDataList_14">
                <table width="100%" border="0" id="tblItem_419204695175725133" islabel="0" keylist="lemonysoft_item_key_1046480_1807" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">8.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">匈牙利法是对指派问题求最小值的一种求解方法<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046480_1807_0 name="lemonysoft_item_key_1046480_1807" ><TD>(A)<TD><label for=lemonysoft_item_key_1046480_1807_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046480_1807_1 name="lemonysoft_item_key_1046480_1807" ><TD>(B)<TD><label for=lemonysoft_item_key_1046480_1807_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    匈牙利法是对指派问题求最小值的一种求解方法
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046480_1807_0"
                                                            name="lemonysoft_item_key_1046480_1807"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046480_1807_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046480_1807_1"
                                                            name="lemonysoft_item_key_1046480_1807"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046480_1807_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046480_1807", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr
            id="tr_tblDataList_15"
            onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_15');}"
        >
            <td scoreid="437022263928160272" id="_DataListTD_tblDataList_15">
                <table width="100%" border="0" id="tblItem_419204695175725134" islabel="0" keylist="lemonysoft_item_key_1046479_55377" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">9.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">整数规划的最优解是先求相应的线性规划的最优解然后取整得到<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046479_55377_0 name="lemonysoft_item_key_1046479_55377" ><TD>(A)<TD><label for=lemonysoft_item_key_1046479_55377_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046479_55377_1 name="lemonysoft_item_key_1046479_55377" ><TD>(B)<TD><label for=lemonysoft_item_key_1046479_55377_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    整数规划的最优解是先求相应的线性规划的最优解然后取整得到
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046479_55377_0"
                                                            name="lemonysoft_item_key_1046479_55377"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046479_55377_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046479_55377_1"
                                                            name="lemonysoft_item_key_1046479_55377"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046479_55377_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046479_55377", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr
            id="tr_tblDataList_16"
            onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_16');}"
        >
            <td scoreid="437022263928160273" id="_DataListTD_tblDataList_16">
                <table width="100%" border="0" id="tblItem_419204695175725135" islabel="0" keylist="lemonysoft_item_key_1046478_57666" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">10.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">目标约束含有偏差变量<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046478_57666_0 name="lemonysoft_item_key_1046478_57666" ><TD>(A)<TD><label for=lemonysoft_item_key_1046478_57666_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046478_57666_1 name="lemonysoft_item_key_1046478_57666" ><TD>(B)<TD><label for=lemonysoft_item_key_1046478_57666_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    目标约束含有偏差变量
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046478_57666_0"
                                                            name="lemonysoft_item_key_1046478_57666"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046478_57666_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046478_57666_1"
                                                            name="lemonysoft_item_key_1046478_57666"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046478_57666_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046478_57666", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr
            id="tr_tblDataList_17"
            onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_17');}"
        >
            <td scoreid="437022263928160274" id="_DataListTD_tblDataList_17">
                <table width="100%" border="0" id="tblItem_419204695175725137" islabel="0" keylist="lemonysoft_item_key_1046477_30675" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">11.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">m+n－1个变量构成基变量组的充要条件是它们不包含闭回路<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046477_30675_0 name="lemonysoft_item_key_1046477_30675" ><TD>(A)<TD><label for=lemonysoft_item_key_1046477_30675_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046477_30675_1 name="lemonysoft_item_key_1046477_30675" ><TD>(B)<TD><label for=lemonysoft_item_key_1046477_30675_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    m+n－1个变量构成基变量组的充要条件是它们不包含闭回路
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046477_30675_0"
                                                            name="lemonysoft_item_key_1046477_30675"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046477_30675_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046477_30675_1"
                                                            name="lemonysoft_item_key_1046477_30675"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046477_30675_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046477_30675", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr
            id="tr_tblDataList_18"
            onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_18');}"
        >
            <td scoreid="437022263928160275" id="_DataListTD_tblDataList_18">
                <table width="100%" border="0" id="tblItem_419204695175725138" islabel="0" keylist="lemonysoft_item_key_1046476_12405" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">12.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">原问题具有无界解，则对偶问题不可行　<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046476_12405_0 name="lemonysoft_item_key_1046476_12405" ><TD>(A)<TD><label for=lemonysoft_item_key_1046476_12405_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046476_12405_1 name="lemonysoft_item_key_1046476_12405" ><TD>(B)<TD><label for=lemonysoft_item_key_1046476_12405_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    原问题具有无界解，则对偶问题不可行　
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046476_12405_0"
                                                            name="lemonysoft_item_key_1046476_12405"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046476_12405_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046476_12405_1"
                                                            name="lemonysoft_item_key_1046476_12405"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046476_12405_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046476_12405", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr
            id="tr_tblDataList_19"
            onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_19');}"
        >
            <td scoreid="437022263928160276" id="_DataListTD_tblDataList_19">
                <table width="100%" border="0" id="tblItem_419204695175725120" islabel="0" keylist="lemonysoft_item_key_1046491_38069" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">13.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">原问题无最优解，则对偶问题无可行解<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046491_38069_0 name="lemonysoft_item_key_1046491_38069" ><TD>(A)<TD><label for=lemonysoft_item_key_1046491_38069_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046491_38069_1 name="lemonysoft_item_key_1046491_38069" ><TD>(B)<TD><label for=lemonysoft_item_key_1046491_38069_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    原问题无最优解，则对偶问题无可行解
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046491_38069_0"
                                                            name="lemonysoft_item_key_1046491_38069"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046491_38069_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046491_38069_1"
                                                            name="lemonysoft_item_key_1046491_38069"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046491_38069_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046491_38069", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr
            id="tr_tblDataList_20"
            onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_20');}"
        >
            <td scoreid="437022263928160277" id="_DataListTD_tblDataList_20">
                <table width="100%" border="0" id="tblItem_419204695175725121" islabel="0" keylist="lemonysoft_item_key_1046490_56134" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">14.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">要求不低于目标值的目标函数是minZd<\/div><div style="line-height:20px;font-size:10pt"><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046490_56134_0 name="lemonysoft_item_key_1046490_56134" ><TD>(A)<TD><label for=lemonysoft_item_key_1046490_56134_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046490_56134_1 name="lemonysoft_item_key_1046490_56134" ><TD>(B)<TD><label for=lemonysoft_item_key_1046490_56134_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">要求不低于目标值的目标函数是minZd</div>
                                <div style="line-height: 20px; font-size: 10pt">
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046490_56134_0"
                                                            name="lemonysoft_item_key_1046490_56134"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046490_56134_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046490_56134_1"
                                                            name="lemonysoft_item_key_1046490_56134"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046490_56134_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046490_56134", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr
            id="tr_tblDataList_21"
            onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_21');}"
        >
            <td scoreid="437022263928160278" id="_DataListTD_tblDataList_21">
                <table width="100%" border="0" id="tblItem_419204695175725122" islabel="0" keylist="lemonysoft_item_key_1046489_48513" style="">
                    <tbody>
                        <tr>
                            <td width="30" valign="top">15.</td>
                            <td>
                                <script language="javascript">
                                    document.write(
                                        $wapper.api.getDisplayFormat(
                                            '<div style="line-height:20px;font-size:10pt">原问题求最大值，第i个约束是“≥”约束，则第i个对偶变量yi ≤0　<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046489_48513_0 name="lemonysoft_item_key_1046489_48513" ><TD>(A)<TD><label for=lemonysoft_item_key_1046489_48513_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046489_48513_1 name="lemonysoft_item_key_1046489_48513" ><TD>(B)<TD><label for=lemonysoft_item_key_1046489_48513_1>错<\/label><\/table><\/div><\/div>'
                                        )
                                    );
                                </script>
                                <div style="line-height: 20px; font-size: 10pt">
                                    原问题求最大值，第i个约束是“≥”约束，则第i个对偶变量yi ≤0　
                                    <div>
                                        <table isitemoption="1" optiontype="radio">
                                            <tbody>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="A"
                                                            id="lemonysoft_item_key_1046489_48513_0"
                                                            name="lemonysoft_item_key_1046489_48513"
                                                        />
                                                    </td>
                                                    <td>(A)</td>
                                                    <td><label for="lemonysoft_item_key_1046489_48513_0">对</label></td>
                                                </tr>
                                                <tr>
                                                    <td>
                                                        <input
                                                            type="radio"
                                                            value="B"
                                                            id="lemonysoft_item_key_1046489_48513_1"
                                                            name="lemonysoft_item_key_1046489_48513"
                                                        />
                                                    </td>
                                                    <td>(B)</td>
                                                    <td><label for="lemonysoft_item_key_1046489_48513_1">错</label></td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                                <script language="javascript">
                                    showAnswer("lemonysoft_item_key_1046489_48513", "", "3");
                                </script>
                            </td>
                        </tr>
                    </tbody>
                </table>

                <script language="javascript">
                    var oSpan = document.getElementsByTagName("SPAN");
                    for (var i = oSpan.length - 1; i >= 0; i--) {
                        if (oSpan[i].getAttribute("IsScoreRemark") == "1") {
                            oSpan[i].innerHTML = "&nbsp;&nbsp;<font color=gray>(第1-15题每题5分)</font>";
                            break;
                        }
                    }
                </script>
            </td>
        </tr>
    </tbody>
</table>
