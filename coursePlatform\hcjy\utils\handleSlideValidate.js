let api = require("./api");

async function handleSlideValidate(page, infoLogger) {
    //等待图片加载完毕，这里用的是无脑等待3秒
    await new Promise((r) => setTimeout(r, 3000));

    //获取图片base64
    let imageBase64=await page.evaluate(()=>{
        let imageEle=document.querySelector('#tianai-captcha-slider-bg-img')
        return imageEle.src.slice(23)
    })

    //获取图片缺口坐标
    let gapPositionRes=await api.slideValidate(imageBase64)
    if(gapPositionRes.code!==1){
        infoLogger('滑动验证码图片识别失败','red')
        throw Error('滑动验证码图片识别失败')
    }
    let gapPositionX=gapPositionRes.data['缺口']['X坐标值']

    // 获取滑块的定位和尺寸
    let sliderHandle = await page.$("#tianai-captcha-slider-move-btn");
    let boundingBox = await sliderHandle.boundingBox();

    // 计算滑块的起始位置和目标位置
    let startX = boundingBox.x + boundingBox.width / 2;
    let startY = boundingBox.y + boundingBox.height / 2;
    let endX = startX + gapPositionX/2-25; // 目标位置的X坐标，表示滑动的距离

    // 模拟鼠标拖动滑块
    await page.mouse.move(startX, startY);
    await page.mouse.down();
    await page.mouse.move(endX, startY, { steps: 20 }); // 增加拖动的平滑度
    await page.mouse.up();
}

module.exports = handleSlideValidate;