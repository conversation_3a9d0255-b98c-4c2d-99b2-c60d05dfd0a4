let fs = require('fs');
let path = require('path');
let parseHTML = require('../course/module/runAssignmentModule/parseHTML.js');
let collectQuestion = require('../course/module/runAssignmentModule/collectQuestion.js');

let str = fs.readFileSync(path.resolve('coursePlatform/nmwcxt/utils/收集试题.html'), 'utf-8');

let questionList = parseHTML(str);

console.log(`共解析${questionList.length}道题目`);

for(let questionObj of questionList){
    // console.log(questionObj)
    // console.log(questionObj.answersContent)
    if (!questionObj.content){
        console.log(questionObj)
        throw new Error('题目解析失败');
    }
}


// questionList.forEach((questionObj, index) => {
//     console.log(`${index + 1}. ${questionObj.id}`);
// });
// console.log(questionList);

let courseName = '流体力学';
collectQuestion(questionList, courseName).then(r => console.log(`共收集${r}道题目`));



