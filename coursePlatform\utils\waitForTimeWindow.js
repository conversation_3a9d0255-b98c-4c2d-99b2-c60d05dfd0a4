// 获取当前中国时间
function getChinaTime() {
    return new Date(new Date().toLocaleString('en-US', { timeZone: 'Asia/Shanghai' }));
}

// 判断当前时间是否在指定的工作时间内
function isWorkTime(chinaDate, startHour, startMinute, endHour, endMinute) {
    const hours = chinaDate.getHours();
    const minutes = chinaDate.getMinutes();
    const totalMinutes = hours * 60 + minutes;

    const workStart = startHour * 60 + startMinute;
    const workEnd = endHour * 60 + endMinute;

    return totalMinutes >= workStart && totalMinutes <= workEnd;
}

// 计算下一个工作时间的开始时间（中国时间）
function getNextWorkStart(chinaDate, startHour, startMinute) {
    const nextStart = new Date(chinaDate);
    nextStart.setHours(startHour, startMinute, 0, 0); // 设置为当天的开始时间

    if (chinaDate.getTime() >= nextStart.getTime()) {
        // 如果当前时间已经超过今天的工作开始时间，设置为明天的开始时间
        nextStart.setDate(nextStart.getDate() + 1);
    }
    return nextStart;
}

// 计算两个时间之间的差异，返回小时和分钟
function getTimeDifference(from, to) {
    const diffMs = to.getTime() - from.getTime(); // 差异毫秒数
    const diffMinutesTotal = Math.floor(diffMs / (1000 * 60));
    const hours = Math.floor(diffMinutesTotal / 60);
    const minutes = diffMinutesTotal % 60;
    return { hours, minutes };
}

// 等待直到下一个工作时间窗口，并定期记录日志
async function waitForTimeWindow(startHour, startMinute, endHour, endMinute, infoLogger, mainPage) {
    while (true) {
        const nowChina = getChinaTime();

        if (isWorkTime(nowChina, startHour, startMinute, endHour, endMinute)) {
            break; // 处于工作时间内，退出等待
        }

        const nextWorkStart = getNextWorkStart(nowChina, startHour, startMinute);
        const timeDiff = getTimeDifference(nowChina, nextWorkStart);

        // 记录当前时间和等待信息，并等待其完成
        await infoLogger(
            `当前时间：${nowChina.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}。` +
                `等待下一个工作时间，开始于 ${nextWorkStart.toLocaleString('zh-CN', { timeZone: 'Asia/Shanghai' })}。` +
                `还需要等待 ${timeDiff.hours} 小时 ${timeDiff.minutes} 分钟。`,
            'orange'
        );

        if (mainPage) {
            await mainPage.reload();
        }

        // 每 30 分钟记录一次日志
        const halfHourDelay = 30 * 60 * 1000;
        const remainingTime = nextWorkStart.getTime() - nowChina.getTime();
        const delay = Math.min(halfHourDelay, remainingTime);

        await new Promise(resolve => setTimeout(resolve, delay));
    }
}

module.exports = waitForTimeWindow;

if (false) {
    (async () => {
        console.log('hi');
        await waitForTimeWindow(08, 15, 20, 50,console.log,null);
        console.log('hi');
    })();
}
