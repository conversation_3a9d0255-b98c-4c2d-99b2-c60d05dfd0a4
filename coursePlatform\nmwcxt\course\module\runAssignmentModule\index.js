let api = require('../../../utils/api.js');

let parseHTML = require('./parseHTML.js');

let pageTools = require('../../../../utils/pageTools.js');
let getAnswerFromLocal = require('../../../../solveQuestion/getAnswerFromLocal.js');
let collectQuestion = require('./collectQuestion.js');

async function runAssignmentModule(mainPage, globalStore, infoLogger) {
    let { courseList } = globalStore;

    for (let i = 0; i < courseList.length; i++) {
        // 取出一个课程对象
        let courseObj = courseList[i];
        let courseLogStr = `课程[${i + 1}/${courseList.length}]`;
        await infoLogger(`${courseLogStr}：开始课程《${courseObj.courseName}》的作业`, 'blue');

        // 跳转到作业页面
        if (!courseObj.filePath) {
            await infoLogger(`${courseLogStr} ${courseObj.courseName}：课程链接为空，跳过`, 'red');
            continue;
        }
        await mainPage.goto(courseObj.filePath, { waitUntil: 'networkidle0' });
        let coursePageCookieStr = await pageTools.getPageCookies(mainPage);

        // 从服务器获取作业列表，通常有三个作业
        let assignmentInfoRes = await api.getAssignmentList(courseObj.courseId, coursePageCookieStr);
        let pattern = /ExamId=\\"(\d+)\\" ContentId=\\"(\d+)\\"/g;
        let assignmentList = [];
        let matches;
        while (true) {
            matches = pattern.exec(assignmentInfoRes);
            if (matches == null) {
                break;
            }
            assignmentList.push({ ExamId: matches[1], ContentId: matches[2] });
        }

        // 遍历作业列表
        for (let j = 0; j < assignmentList.length; j++) {
            let assignmentObj = assignmentList[j];
            let assignmentLogStr = `${courseLogStr}-作业[${j + 1}/${assignmentList.length}]`;

            let assignmentCount = 0; // 答题次数
            let hasSumbit = false; //是否提交

            await infoLogger(`${assignmentLogStr}，开始`, 'blue');

            async function getFrame() {
                //课程页面跳转到对应的作业url
                // await mainPage.goto(courseObj.filePath, { waitUntil: "networkidle0" });
                await pageTools.gotoWithRetry(mainPage, courseObj.filePath, { waitUntil: 'networkidle0' }, 3);

                //获取第一层iframe：mainFrame。 整个页面分为三段式，这里指的是中间部分。
                await mainPage.waitForSelector('body > table > tbody > tr:nth-child(2) > td > iframe');
                let mainFrameHandle = await mainPage.$('body > table > tbody > tr:nth-child(2) > td > iframe');
                let mainFrame = await mainFrameHandle.contentFrame();

                //点击frame中的作业和检测按钮
                await mainFrame.waitForSelector('a[href="javascript:learnHomework()"]');
                let kechengHandle = await mainFrame.$('a[href="javascript:learnHomework()"]');
                await kechengHandle.click();
                await mainFrame.waitForSelector('#_DataListTD_tblDataList_0 > div:nth-child(4) > a');

                // //点击“完成作业”按钮，一共又有三个，顺序是1，3，5
                await mainFrame.waitForSelector('a[href^="javascript:joinExam("]');
                let assignmentHandleArr = await mainFrame.$$('a[href^="javascript:joinExam("]');
                let assignmentHandle = assignmentHandleArr[j * 2 + 1];
                await assignmentHandle.click();
                // await mainPage.waitForTimeout(3000);

                //获取第二层iframe：cboxIframe
                await mainFrame.waitForSelector('#cboxIframe');
                let cboxFrameHandle = await mainFrame.$('#cboxIframe');
                let cboxIframe = await cboxFrameHandle.contentFrame();

                //获取第三层iframe：w_lmsFrame
                await cboxIframe.waitForSelector('#w_lms_content');
                let wlmsFrameHandle = await cboxIframe.$('#w_lms_content');
                let wlmsFrame = await wlmsFrameHandle.contentFrame();

                //获取第四层frame：wlmsscoFrame
                await wlmsFrame.waitForSelector('#w_lms_sco');
                let wlmsscoFrameHandle = await wlmsFrame.$('#w_lms_sco');
                let wlmsscoFrame = await wlmsscoFrameHandle.contentFrame();
                await wlmsscoFrame.waitForSelector('#_block_content_exam_info > table:nth-child(4) > tbody > tr:nth-child(5) > td:nth-child(2)');
                return wlmsscoFrame;
            }

            async function startAssignment() {
                if (assignmentCount == 4) {
                    await infoLogger(`${assignmentLogStr}已经提交${assignmentCount}次，强制退出`, 'red');
                    return;
                } else {
                    await infoLogger(`${assignmentLogStr} 已经提交${assignmentCount}次`);
                }

                //获取作业对应的frame
                let wlmsscoFrame = await getFrame();

                //获取当前状态
                let assignmentState = await wlmsscoFrame.$eval(
                    '#_block_content_exam_info > table:nth-child(4) > tbody > tr:nth-child(5) > td:nth-child(2)',
                    el => el.textContent
                );
                await new Promise(r => setTimeout(r, 1000));

                //特殊情况：没有考试次数，状态可以通过按钮文字获取
                let leftCountStr;
                try {
                    leftCountStr = await wlmsscoFrame.$eval('#btnExam', el => el.value);
                } catch (error) {}

                if (leftCountStr == '重考(剩余0次)') {
                    await infoLogger(`${assignmentLogStr} 没有答题机会了`, 'red');
                    return;
                }

                if (assignmentState.includes('批阅中')) {
                    await infoLogger(`${assignmentLogStr} 批阅中`, 'red');
                    return;
                }

                // 第一次运行不会走到这里，还是根据assignmentState的值进行判断，感觉有点多余
                if (hasSumbit) assignmentState = '已批改';

                //可以答题
                if (assignmentState !== '已批改') {
                    await infoLogger(`${assignmentLogStr} 开始答题`);
                    //1.点击开始考试
                    await wlmsscoFrame.evaluate(() => {
                        doExam();
                    });
                    //这时候会跳转到测试界面，这里选择强制等待，因为不好判断有没有加载完成
                    await new Promise(r => setTimeout(r, 5000));

                    //第五层frame
                    //获取左边frame
                    await wlmsscoFrame.waitForSelector('html > frameset > frame[name=w_left]'); // 出错
                    let wleftFrameHandle = await wlmsscoFrame.$('html > frameset > frame[name=w_left]');
                    let wleftFrame = await wleftFrameHandle.contentFrame();

                    //获取右边试卷内容，这里之前尝试用API获取，但是出错了，原因未知
                    await wlmsscoFrame.waitForSelector('html > frameset > frame[name=w_right]');
                    let wrighFrametHandle = await wlmsscoFrame.$('html > frameset > frame[name=w_right]');
                    let wrightFrame = await wrighFrametHandle.contentFrame();
                    await wrightFrame.waitForSelector('#tblDataList');
                    let tbodyHTML = await wrightFrame.$eval('#tblDataList', el => el.outerHTML);

                    //解析试题
                    let questionList = parseHTML(tbodyHTML);

                    //查找答案
                    let answersObj = {};
                    let submitCount = 0;
                    for (let j = 0; j < questionList.length; j++) {
                        let questionObj = questionList[j];
                        questionObj.courseName = courseObj.courseName;
                        let answers = await getAnswerFromLocal(questionObj);

                        if (answers) {
                            answersObj[questionObj.id] = answers;
                            submitCount++;
                        } else {
                            answersObj[questionObj.id] = '';
                        }
                    }
                    await infoLogger(`${assignmentLogStr}，共回答${submitCount}/${questionList.length}题`);

                    //提交试卷
                    let temArr = Object.keys(answersObj); // 只回答一题
                    // console.log(temArr);
                    let answerStr = temArr.map(key => `${key}=${encodeURIComponent(answersObj[key])}`).join('&');
                    // console.log("answerStr", answerStr);

                    let homeworkRes;
                    try {
                        homeworkRes = await api.submitHomework(coursePageCookieStr, assignmentObj, answerStr);
                        // console.log("homeworkRes", homeworkRes);
                        await infoLogger(`${assignmentLogStr}，交卷成功`);
                    } catch (error) {
                        await infoLogger(`${assignmentLogStr}，交卷失败，'${JSON.stringify(homeworkRes)}`, 'red');
                        return;
                    }

                    // 强制更改状态，一遍下一次走到下一步收集题目，因为提交之后有可能不显示分数，显示 批阅中
                    hasSumbit = true;
                    assignmentCount++;

                    //递归开始函数
                    await startAssignment();
                }

                //不可以答题，完成的提示词不止“已批改”，还有其他很多情况，所以这里用hasSubmit强制改为“已批改”
                if (assignmentState === '已批改') {
                    //点击“查看考卷”按钮
                    await wlmsscoFrame.evaluate(() => {
                        viewPaper();
                    });
                    await new Promise(r => setTimeout(r, 5000));

                    //获取分数
                    let score = await wlmsscoFrame.evaluate(() => {
                        //#_block_content_exam_1 > div:nth-child(1)
                        let scoreContent = document.querySelector('#_block_content_exam_1 > div:nth-child(1)').textContent;
                        let score = scoreContent.match(/当前得分：(\d+) 分/)[1];
                        return score * 1;
                    });
                    

                    if (score >= 80) {
                        await infoLogger(`${assignmentLogStr}，已批改，分数${score}，通过。`, 'green');
                        return;
                    } else {
                        await infoLogger(`${assignmentLogStr}，已批改，分数${score}，需要重做。`, 'gray');
                    }

                    //收集题目
                    let tbodyHTML = await wlmsscoFrame.$eval(
                        '#_block_content_exam_1 > form > table', //#_block_content_exam_1 > form > table
                        el => el.outerHTML
                    );
                    //解析试题
                    let questionList = parseHTML(tbodyHTML);
                    // try {
                    //     questionList= parseHTML(tbodyHTML);
                    // } catch (error) {
                    //     console.log(tbodyHTML)
                    //     await new Promise(r=>setTimeout(r,10000000))
                    // }
                    //收集题目
                    let saveCount = await collectQuestion(questionList, courseObj.courseName);

                    await infoLogger(`${assignmentLogStr}，共收集${saveCount}道题目`);

                    if (assignmentCount >= 3) {
                        await infoLogger(`${assignmentLogStr}：已经答题3次`, 'red');
                        return;
                    }

                    //这里本来想用API的方式重置作业状态，但是一直没成功
                    //改变状态
                    wlmsscoFrame = await getFrame();
                    await wlmsscoFrame.evaluate(() => {
                        doExam();
                    });
                    await new Promise(r => setTimeout(r, 1000));

                    await infoLogger(`${assignmentLogStr}，作业答题状态已经修改为可以答题`);
                    hasSumbit = false;
                    await startAssignment();
                }
            }

            await startAssignment();
        }
    }
}

module.exports = runAssignmentModule;
