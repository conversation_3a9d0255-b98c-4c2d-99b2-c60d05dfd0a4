// ========= 任务模块 =========
let videoTask = require('./courseTask/videoTask.js');
let documentTask = require('./courseTask/documentTask.js');
let testTask = require('./courseTask/testTask.js');
let signinModule = require('../signinModule.js');

let api = require('../../../utils/api.js');

async function runCourseModule(infoLogger, mainPage, courseList, globalStore) {
    //遍历课程
    courseLoop: for (let i = 0; i < courseList.length; i++) {
        let courseObj = courseList[i];
        let courseLogStr = `课程：[${i + 1}/${courseList.length}]`;

        await infoLogger(`${courseLogStr}：开始课程《${courseObj.courseName}》`, 'blue');

        if (courseObj.schedule == 100) {
            await infoLogger(`${courseLogStr}：课程已完成，跳过`, 'green');
            continue courseLoop;
        }

        // 更新token
        // await mainPage.evaluate(() => {
        //     localStorage.clear();
        // });
        await new Promise(r => setTimeout(r, 1000));
        await signinModule(infoLogger, mainPage, globalStore);
        let storageObj = globalStore.storageObj;

        // let cellList = courseObj.cellList;
        courseObj.cellList = [];
        let cellListRes = await api.getCellList(storageObj, courseObj.id);
        let cellList = cellListRes.list;

        if (!Array.isArray(cellList)) {
            await infoLogger(`${courseLogStr}：当前课程无课件`, 'red');
            continue;
        }

        cellList.forEach(chapter => {
            chapter.lessonList.forEach(lesson => {
                lesson.cellList.forEach(cell => {
                    courseObj.cellList.push(cell);
                });
            });
        });
        await infoLogger(`${courseLogStr}：获取课件列表成功，共${courseObj.cellList.length}门课件`);

        //遍历课件
        cellLoop: for (let j = 0; j < courseObj.cellList.length; j++) {
            let cellObj = courseObj.cellList[j];

            let cellLogStr = `课程${i + 1}/${courseList.length}]-章节[${j + 1}/${courseObj.cellList.length}]`;

            // 跳过已经完成的课件
            if (cellObj.studyCount * 1 >= 99) {
                await infoLogger(`${cellLogStr}：当前${cellObj.extension}课件已经完成，不需要重复 《${cellObj.title}》`);
                continue cellLoop;
            }

            // // 开发调试过滤用
            // if (cellObj.resType != 1) continue;

            switch (cellObj.resType) {
                // 视频课件
                case 1: {
                    await videoTask(infoLogger, mainPage, courseObj, cellObj, cellLogStr, globalStore);
                    break;
                }
                // 文档课件
                case 2: {
                    await documentTask(infoLogger, mainPage, courseObj, cellObj, cellLogStr, globalStore);
                    break;
                }
                // 作业课件
                case 7: {
                    await testTask(infoLogger, mainPage, courseObj, cellObj, cellLogStr, globalStore);
                    break;
                }
                default: {
                    await infoLogger(`${cellLogStr}：当前任务点为不支持的类型： ${cellObj.resType} 《${cellObj.title}》`, 'red');
                    break;
                }
            }

            await new Promise(r => setTimeout(r, 3000));
        }
    }
}

module.exports = runCourseModule;
