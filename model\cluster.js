const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('cluster', {
    id: {
      type: DataTypes.STRING(255),
      allowNull: false,
      primaryKey: true,
      comment: "唯一标识符id"
    },
    state: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "状态：1.idle 2.busy 3.exit 4.disconnec"
    },
    start_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "任务开始时间"
    },
    type: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "任务类型：1.course 2.exam"
    },
    schoolname: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "学校名称"
    },
    username: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "用户名"
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "密码"
    },
    platform: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "平台"
    },
    comment: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "备注"
    },
    user: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "进程所有者"
    },
    pc_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "主机ID"
    },
    worker_id: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    task_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "任务id"
    },
    memoryUsage: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "内存用量"
    }
  }, {
    sequelize,
    tableName: 'cluster',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
};
