let api = require("../../../../utils/api.js");
let { formatQuestion, handleAnswer, collectQuestion } = require("../../../../utils/handleQuestion.js");

let submitAnswer = ""; //防止死循环

async function testTask(infoLogger, globalStore, courseObj, cellObj, cellLogStr) {

    await infoLogger(`${cellLogStr}：开始${cellObj.icon}作业任务 《${cellObj.title}》`, "blue");

    //客观题目
    //从服务器获取题目信息
    let loadRes = await api.getPaper(globalStore, courseObj, cellObj);

    if (loadRes.code && loadRes.code != 1) {
        await infoLogger(`${cellLogStr}：${cellObj.icon}作业从服务器获取试题失败 《${cellObj.title}》`, "red");
        return;
    }

    //扁平化试题列表
    let questionList = [];
    loadRes.data.forEach((item) => {
        item.list.forEach((questionObj) => {
            let newObj = formatQuestion(questionObj, "安徽继续教育在线", courseObj);
            questionList.push(newObj);
        });
    });

    //可以答题，正常答题，然后提交
    if (loadRes.canModify) {
        await infoLogger(`${cellLogStr}：${cellObj.icon}作业准备答题`);

        //回答问题
        let submitCount = await handleAnswer(globalStore, courseObj, questionList);

        await infoLogger(`${cellLogStr}：${cellObj.icon}作业共回答${submitCount}道题 <${cellObj.title}>`);

        if (submitAnswer == "已经提交") {
            await infoLogger(`${cellLogStr}：${cellObj.icon}似乎作业提交失败，防止死循环 <${cellObj.title}>`, "red");
            submitAnswer =""
            return;
        }

        //提交试卷
        let submitOnlineAssignmentRes = await api.submitPaper(globalStore, courseObj, cellObj);
        await infoLogger(`${cellLogStr}：${cellObj.icon}作业试卷提交成功 <${cellObj.title}>`);
        submitAnswer = "已经提交";

        //更改课程状态为已完成
        let studiedRes = await api.finishChapter(globalStore, courseObj, cellObj);
        await infoLogger(`${cellLogStr}：${cellObj.icon}作业状态更改为：已提交 <${cellObj.title}>`);

        // 这里递归调用是为了收集题目
        await testTask(infoLogger, globalStore, courseObj, cellObj, cellLogStr);
    }

    //不可以答题，收集题目，
    if (!loadRes.canModify) {
        let collectCount = await collectQuestion(questionList);
        submitAnswer =""
        await infoLogger(`${cellLogStr}：题目收集完毕，共收集${collectCount}道题目 《${cellObj.title}》`);
    }

}

module.exports = testTask;