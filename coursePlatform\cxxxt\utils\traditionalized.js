function charPYStr() {
    return "⼀⼄⼆⼈⼉⼊⼋⼏⼑⼒⼔⼗⼘⼚⼜⼝⼞⼟⼠⼤⼥⼦⼨⼩⼫⼭⼯⼰⼲⼴⼸⼼⼽⼿⽀⽂⽃⽄⽅⽆⽇⽈⽉⽊⽋⽌⽍⽏⽐⽑⽒⽓⽔⽕⽖⽗⽚⽛⽜⽝⽞⽟⽠⽡⽢⽣⽤⽥⽩⽪⽫⽬⽭⽮⽯⽰⽲⽳⽴⽵⽶⽸⽹⽺⽻⽼⽽⽿⾁⾂⾃⾄⾆⾈⾉⾊⾍⾎⾏⾐⾒⾓⾔⾕⾖⾚⾛⾜⾝⾞⾟⾠⾢⾣⾤⾥⾦⾧⾨⾩⾪⾬⾭⾮⾯⾰⾲⾳⾴⾵⾶⾷⾸⾹⾺⾻⾼⿁⿂⿃⿄⿅⿇⿉⿊⿍⿎⿏⿐⿒⿓⼣⺁⺇⺌⺎⺏⺐⺒⺓⺛⺝⺟⺠⺱⺸⻁⻄⻅⻆⻉⻋⻒⻓⻔⻗⻘⻙⻚⻛⻜⻝⻢⻣⻤⻥⻦⻧⻨⻩⻬⻮⻯⻰⻳⾅⼝⼾⼉⼱";
}

function ftPYStr() {
    return "一乙二人儿入八几刀力匕十卜厂又口口土士大女子寸小尸山工己干广弓心戈手支文斗斤方无日曰月木欠止歹毋比毛氏气水火爪父片牙牛犬玄玉瓜瓦甘生用田白皮皿目矛矢石示禾穴立竹米缶网羊羽老而耳肉臣自至舌舟艮色虫血行衣見角言谷豆赤走足身車辛辰邑酉采里金長門阜隶雨青非面革韭音頁風飛食首香馬骨高鬼魚鳥鹵鹿麻黍黑鼎鼓鼠鼻齒龍夕厂几小兀尣尢巳幺旡月母民冈芈虎西见角贝车镸长门雨青韦页风飞食马骨鬼鱼鸟卤麦黄齐齿竜龙龟臼口户儿巾";
}

//康熙部首转为汉字
function traditionalized(cc) {
    var str = "";
    for (var i = 0; i < cc.length; i++) {
        if (charPYStr().indexOf(cc.charAt(i)) != -1) str += ftPYStr().charAt(charPYStr().indexOf(cc.charAt(i)));
        else str += cc.charAt(i);
    }
    return str;
}

//汉字转为康熙部首
function simplized(cc) {
    var str = "";
    for (var i = 0; i < cc.length; i++) {
        if (ftPYStr().indexOf(cc.charAt(i)) != -1) str += charPYStr().charAt(ftPYStr().indexOf(cc.charAt(i)));
        else str += cc.charAt(i);
    }
    return str;
}

module.exports={traditionalized,simplized}