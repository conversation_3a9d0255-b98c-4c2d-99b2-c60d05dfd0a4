const cluster = require('cluster');
const os = require('os');
let Model = require('../config/sequelize.config.js');
let pcId = require('../config/pcId.js');
let user = require('../config/user.js');
let { v4 } = require('uuid');
/**
 * 线程状态：1.空闲 2.工作 3.完成 4.失败
 */
class ClusterPool {
    constructor(size = os.cpus().length - 2) {
        this.size = size; //线程数量
        this.workerPool = []; //线程池 [{ worker:worker, state: 'idle' }]
        this.taskQueue = []; //任务列队 [taskObj,taskObj,taskObj]
        this.ws = undefined;
    }

    //初始化线程池
    async initWorkers() {
        //新建工作线程，并放到线程池中
        for (let i = 0; i < this.size; i++) {
            // 创建工作进程 创建一个新的进程，该进程会加载并执行当前文件（也就是 server.js 文件）的内容
            let worker = cluster.fork();
            // idle空闲 busy工作 done完成
            this.workerPool.push({ worker: worker, state: 'idle' });
            await Model.cluster.create({
                id: v4(),
                worker_id: worker.id,
                state: 'idle',
                pc_id: pcId,
                user,
            });
        }

        // 工作线程完成工作，通知主线程任务完成，初始化进程，通知ws服务器
        cluster.on('message', async (worker, message) => {
            // 工作进程之后，会发送'done'消息
            if (message === 'done') {
                const poolWorker = this.workerPool.find(w => w.worker.id === worker.id);
                if (poolWorker) {
                    poolWorker.state = 'idle';
                    await Model.cluster.update(
                        {
                            state: 'idle',
                            start_time: null,
                            type: '',
                            schoolname: '',
                            username: '',
                            password: '',
                            platform: '',
                            comment: '',
                            taskid: '',
                            memoryUsage: '',
                        },
                        {
                            where: { worker_id: worker.id, pc_id: pcId },
                        }
                    );
                }
                let queueLength = this.taskQueue.length - 1 < 0 ? 0 : this.taskQueue.length - 1;
                let dataObj = {
                    pcId,
                    wsType: 'taskFinish',
                    queueLength,
                };
                let data = JSON.stringify(dataObj);
                // 从任务列队中取出一个任务执行
                await this.checkTaskQueue();
                this.ws.send(data);
            }
        });

        // 在工作进程完全退出时触发，包括自然结束、异常退出或被杀死。
        cluster.on('exit', async (worker, code, signal) => {
            const poolWorker = this.workerPool.find(w => w.worker.id === worker.id);
            if (poolWorker) {
                poolWorker.state = 'exit';
                // console.log("exit");
                await Model.cluster.update(
                    {
                        state: 'exit',
                    },
                    {
                        where: { worker_id: worker.id, pc_id: pcId },
                    }
                );
            }
        });

        // 仅在工作进程与主进程断开连接时触发。这意味着工作进程仍然可能在运行，只是与主进程失去通信。
        cluster.on('disconnect', async worker => {
            const poolWorker = this.workerPool.find(w => w.worker.id === worker.id);
            if (poolWorker) {
                poolWorker.state = 'disconnect';
                // console.log("disconnect");
                await Model.cluster.update(
                    {
                        state: 'disconnect',
                    },
                    {
                        where: { worker_id: worker.id, pc_id: pcId },
                    }
                );
            }
        });
    }

    //添加任务
    async enqueueTask(task) {
        this.taskQueue.push(task);
        let ModelFn;
        switch (task.type) {
            case 'course':
                ModelFn = Model.course;
                break;
            case 'exam':
                ModelFn = Model.exam;
                break;
        }
        await ModelFn.update(
            {
                state: '列队中',
                pc_id: pcId,
            },
            {
                where: {
                    id: task.id,
                },
            }
        );
        await this.checkTaskQueue();
    }

    //给线程分配任务
    async checkTaskQueue() {
        const idleWorker = this.workerPool.find(w => w.state === 'idle');
        if (idleWorker && this.taskQueue.length > 0) {
            let pendingTask = this.taskQueue.shift();
            idleWorker.state = 'busy';
            idleWorker.worker.send(pendingTask); //给工作线程发送任务
            await Model.cluster.update(
                {
                    state: 'busy',
                    start_time: new Date(),
                    type: pendingTask.type,
                    schoolname: pendingTask.schoolname,
                    username: pendingTask.username,
                    password: pendingTask.password,
                    platform: pendingTask.platform,
                    comment: pendingTask.comment,
                    task_id: pendingTask.id,
                },
                { where: { worker_id: idleWorker.worker.id, pc_id: pcId } }
            );
        }
    }

    // //增加一个线程
    // async addWorker() {
    // 	const newWorker = cluster.fork();
    // 	this.workerPool.push({ worker: newWorker, state: 'idle' }); //idle空闲 busy工作 done完成
    // 	await Model.cluster.create({ id: newWorker.id, state: 'idle' });
    // 	await this.checkTaskQueue();
    // }

    // //删除一个线程
    // async delWorker(dataObj) {
    // 	let poolWorker = this.workerPool.find(w => w.worker.id === dataObj.worker_id);
    // 	if (poolWorker) {
    // 		poolWorker.worker.kill();
    // 		await Model.cluster.destroy({
    // 			where: {
    //                 id: dataObj.id,
    //             },
    // 		});
    // 	}
    // }

    // 重置一个进程
    async resetWorker(dataObj) {
        let oldWorker = this.workerPool.find(w => w.worker.id == dataObj.worker_id);
        if (oldWorker) {
            // 从workerPoll中删除旧进程
            this.workerPool = this.workerPool.filter(w => w.worker.id != dataObj.worker_id);
            // 删除旧进程
            oldWorker.worker.kill();
            await Model.cluster.destroy({
                where: {
                    id: dataObj.id,
                },
            });
            // 创建新进程
            let newWorker = cluster.fork();
            this.workerPool.push({ worker: newWorker, state: 'idle' }); //idle空闲 busy工作 done完成
            await Model.cluster.create({
                id: dataObj.id,
                state: 'idle',
                pc_id: dataObj.pc_id,
                user: dataObj.user,
                worker_id: newWorker.id,
            });
            await this.checkTaskQueue();
        }
    }
}
module.exports = ClusterPool;
