let Model = require('../../config/sequelize.config');
let matchAnswerWithLocal = require('./getAnswerFromAiDian/matchAnswerWithLocal.js');

//从本地题库找
async function getAnswerFromLocal(questionObj) {
    let whereObj = {
        type: questionObj.type,
        content: questionObj.content,
    };
    if (questionObj.platform) {
        whereObj.platform = questionObj.platform;
    }

    let qlist = await Model.bank.findAll({
        where: whereObj,
    });
    if(qlist.length==0){
        return 
    }
    qlist = qlist.map(item => {
        item.dataValues.options = JSON.parse(item.dataValues.options);
        return item.dataValues;
    });

    let answersRes = { qlist, source: 'local' };
    let answers;
    //根据题目类型，决定是否使用AI
    switch (questionObj.type) {
        case '判断题':
        case '单选题':
        case '多选题': {
            // 首先根据自己的逻辑进行答案匹配
            answers = matchAnswerWithLocal(answersRes, questionObj);
            break;
        }
        // 填空 简答 等其他题目
        default: {
            if (qlist.length > 0) {
                answers = qlist[0].answers.slice(0, 2500);
            }
            break;
        }
    }

    //本地题库没有，返回null
    if(answers&&answers!='false'){
        return answers;
    }
}

module.exports = getAnswerFromLocal;

// 单元测试
if (false) {
    let questionObj = {
        questionId: '092b4602985bd9e747a7e55e16da4761',
        type: '多选题',
        content: '要更加自觉地增强（ ），既不走封闭僵化的老路，也不走改旗易帜的邪路，保持政治定力，坚持实干兴邦，始终坚持和发展中国特色社会主义。',
        options: ['B:道路自信', 'C:制度自信', 'D:文化自信', 'E:理论自信'],
        platform: '弘成教育',
        courseName: '习近平新时代中国特色社会主义思想概论',
        parentId: undefined,
        isWrapQuestion: undefined,
    };
    getAnswerFromLocal(questionObj)
        .then(r => console.log(r))
        .catch(e => console.log(e));
}
