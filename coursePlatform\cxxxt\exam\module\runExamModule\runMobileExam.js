let getQuestionList = require('./getQuestionList');
let solveQuestion = require('../../../../solveQuestion/index');
let saveQuestion = require('./saveQuestion');
let collectQuestion = require('./collectQuestion');
let mobileFaceCompare = require('./mobileFaceCompare');
let handleSlideValidate = require('./handleSlideValidate');

let url = require('url');
// 把url中的查询字符串转为对象
function getQueryParams(urlString) {
    // 解析 URL
    const parsedUrl = new URL(urlString);

    // 获取查询参数
    const queryParams = {};
    for (const [key, value] of parsedUrl.searchParams) {
        queryParams[key] = value;
    }

    return queryParams;
}

// 把对象作为查询字符串添加到url后面
function addQueryParamsToUrl(baseUrl, params) {
    // 创建一个 URL 对象
    const url = new URL(baseUrl);

    // 遍历对象的属性
    for (const [key, value] of Object.entries(params)) {
        // 将每个键值对添加到 URL 的查询参数中
        url.searchParams.append(key, value);
    }

    // 返回完整的 URL 字符串
    return url.toString();
}

async function runMobileExam(mainPage, taskObj, courseObj, infoLogger, finalResult, courseLogStr) {
    // 设置设备表示 agent
    await mainPage.setUserAgent(
        'Mozilla/5.0 (Linux; Android 11; MI 9 Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.85 Mobile Safari/537.36 (schild:266452b0273a0b89734468f95662ac8c) (device:MI 9) Language/zh_CN com.chaoxing.mobile/ChaoXingStudy_3_6.4.8_android_phone_10834_264 (@Kalimdor)_693480ab70044c5e8137c1de8f594768'
    );

    // 跳转到考试界面 （确认界面）
    await mainPage.goto(courseObj.examUrl, { waitUntil: 'networkidle0' });
    await new Promise(r => setTimeout(r, 2000));
    await infoLogger(`${courseLogStr} 进入信息确认界面`);
    // 确认界面点击“进入考试”
    await mainPage.evaluate(() => enter());
    await new Promise(r => setTimeout(r, 2000));
    await infoLogger(`${courseLogStr} 进入考试`);

    // 获取courseId
    let courseId = await mainPage.evaluate(() => $('#courseId').val());

    // 验证码识别 (通过页面元素判断) #cx_image_margin
    await handleSlideValidate(mainPage, infoLogger);

    // 进行人脸识别 （手机端一般都是强制人脸识别）
    if (taskObj.others.includes('人脸识别')) {
        // 跳转到人脸识别页面 https://mooc1-api.chaoxing.com/exam-ans/exam/phone/to-face-compare
        await mainPage.goto(courseObj.examUrl, { waitUntil: 'networkidle0' });
        courseObj.examUrl = await mobileFaceCompare(mainPage, taskObj, courseObj, infoLogger);
        await mainPage.goto(courseObj.examUrl, { waitUntil: 'networkidle0' });
        await new Promise(r => setTimeout(r, 2000));
    } else {
        await infoLogger(`${courseLogStr} 无需人脸识别`);
    }

    // 切换到预览模式
    await mainPage.evaluate(() => {
        topreview();
    });
    await new Promise(r => setTimeout(r, 2000));

    // 获取题目列表
    let questionList = await getQuestionList(mainPage, courseId, taskObj);
    if (Array.isArray(questionList) && questionList.length > 0) {
        await infoLogger(`${courseLogStr} 获取题目成功，共有${questionList.length}道题目`, 'green');
    } else {
        await infoLogger(`${courseLogStr} 获取题目失败`, 'red');
        throw new Error(`${courseLogStr} 获取题目失败`); //结束进程池任务
    }

    // 查找答案
    let answerCount = 0;
    for (let i = 0; i < questionList.length; i++) {
        //1.取出一个问题对象
        let questionObj = questionList[i];

        let answers;
        try {
            answers = await solveQuestion(questionObj);
        } catch (error) {
            await infoLogger(`${courseLogStr} 查找答案出错${JSON.stringify(questionObj)}`, 'red');
            continue;
        }

        if (answers) {
            questionObj.answers = answers;
            answerCount++;
        }
    }
    if (answerCount == 0) {
        await infoLogger(`${courseLogStr} 一题都没找到答案`);
        return;
    }
    await infoLogger(`${courseLogStr} ，共找到${answerCount}/${questionList.length}题答案`);
    await new Promise(r => setTimeout(r, 2000));

    //回答题目
    await saveQuestion(mainPage, questionList, taskObj);
    await infoLogger(`${courseLogStr} 答题成功`);

    //等待30分钟后
    let waitMin = 30;
    for (let i = 0; i < waitMin; i++) {
        await infoLogger(`等待${waitMin - i}分钟后交卷`, 'green');
        await new Promise(resolve => setTimeout(resolve, 1 * 60 * 1000)); //等待加载完成
    }

    // 交卷
    await mainPage.evaluate(() => {
        var submitTest = $('#submitTest');
        var submitStyle = 0;
        let tempSave = false;

        finalSubmitTest(submitTest, submitStyle, tempSave);
    });
    await infoLogger(`${courseLogStr} 交卷成功`);

    // 考完之后会跳转到这个页面
    // https://mooc1-api.chaoxing.com/exam-ans/exam/phone/look?courseId=242337460&classId=101502575&examId=5434666&cpi=323008972&examAnswerId=131242229&code=null&protocol_v=1#INNER
    await new Promise(r => setTimeout(r, 2000));
    let temUrl = await mainPage.url();
    let paramsObj = getQueryParams(temUrl);

    //获取分数
    let score = await mainPage.evaluate(() => {
        let scoreTag = document.querySelector('body > div.examBox > div.exam_content > h2 > b');
        let score = scoreTag.innerText;
        return score;
    });
    await infoLogger(`${courseLogStr} 最终分数为：${score}`);
    finalResult.push({ courseName: courseObj.courseName, progress: score });

    // 手机端只能一题一题查看试卷，这里考虑通过url拼接方式跳转到电脑端查看
    try {
        let baseUrl = 'https://mooc1.chaoxing.com/exam-ans/exam/test/reVersionPaperMarkContentNew';
        let finalParams = {
            courseId: paramsObj.courseId,
            classId: paramsObj.classId,
            id: paramsObj.examAnswerId,
            newMooc: true,
        };
        let finalUrl = addQueryParamsToUrl(baseUrl, finalParams);
        await mainPage.goto(finalUrl, { waitUntil: 'networkidle0' });
        await new Promise(r => setTimeout(r, 2000));
        await infoLogger(`${courseLogStr}  查看答题试卷：${finalUrl}`);
        let collectCount = await collectQuestion(mainPage, courseId);
        await infoLogger(`${courseLogStr}  答错${collectCount}道题目，已经记录`);
    } catch (error) {
        await infoLogger(`${courseLogStr}  不支持查看试卷`);
    }
}

module.exports = runMobileExam;
