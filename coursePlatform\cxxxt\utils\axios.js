let axios = require('axios');
let { HttpsProxyAgent } = require('https-proxy-agent');


let axiosIns

if(global.isProxy){
    let agent = new HttpsProxyAgent(`http://${global.courseTaskOptions.proxy.ip}:${global.courseTaskOptions.proxy.port}`);  
    axiosIns=axios.create({
        httpsAgent:agent
    });
}else{
    axiosIns=axios.create();
}

// 添加响应拦截器
axiosIns.interceptors.response.use(
	// 对响应数据做点什么
	function (response) {
		if (response.status === 200) {
			return response.data;
		} else {
			return Promise.reject(new Error('error'));
		}
	},
	// 对响应错误做点什么
	function (error) {
		return Promise.reject(error);
	}
);

module.exports = axiosIns;