// ========= 私有模块 =========
let pageTools = require('../../utils/pageTools.js');
let getMainPage = require('../../utils/getMainPage.js');

// ========= 主体模块 =========
let singinModule = require('./module/signinModule.js');
let getCourseListModule = require('./module/getCourseListModule.js');
let runAssignmentModule = require('./module/runAssignmentModule.js');
let runCourseModule = require('./module/runCourseModule.js');
let endModule = require('./module/endModule.js');

async function hcjyCourse(taskObj, taskOptions) {
    //强制打开窗口
    // taskOptions.isHeadless = false;
    let infoLogger = pageTools.getCourseInfoLogger(taskObj.id, false);
    //最终要返回的值
    let globalStore = {
        cookieStr: '', //页面cookie
        needSlideValidate: false, //是否需要滑动验证
        mainUrl: '', //主页url
        warningMessage: '',
    };
    let { mainPage, browser } = await getMainPage(taskOptions);

    // 2.拦截弹窗，通过监听 dialog 事件 有可能会出现“此课程线下学习”的弹窗
    mainPage.on('dialog', async dialog => {
        await dialog.accept(); // 可以使用 dialog.accept() 来接受弹窗
    });

    try {
        if (!process.env.HAS_PERMISSION) {
            return;
        }

        // 一、登录
        try {
            await singinModule(mainPage, globalStore, infoLogger, taskObj);
        } catch (error) {
            await infoLogger('登录失败，过两分钟再试一次', 'red');
            await new Promise(r => setTimeout(r, 2 * 60 * 1000));
            await singinModule(mainPage, globalStore, infoLogger, taskObj);
        }

        // 二、获取课程列表
        let courseList = await getCourseListModule(taskObj, mainPage, globalStore, infoLogger);

        // 三、开始作业
        await runAssignmentModule(infoLogger, mainPage, courseList, globalStore);

        // 四、开始网课
        await runCourseModule(infoLogger, courseList, globalStore);

        // 五、收尾工作
        let finalResult = await endModule(mainPage, globalStore);
        await browser.close();
        return { finalResult, warningMessage: globalStore.warningMessage };
    } catch (error) {
        await browser.close();
        browser = null;
        throw error;
    } finally {
        if (browser) {
            await browser.close();
            browser = null;
        }
    }
}

module.exports = hcjyCourse;

// //测试用
// let taskOptions = {
//     isHeadless: "new", //浏览器模式 false ,'new'
// };

// let taskObj = {
//     username: "2023060023",
//     password: "czzy!095810",
//     runCourse: true, //网课
//     runAssignment: true, //作业
//     schoolurl: "http://czzycj.sccchina.net/",
//     schoolname: "滁州职业技术学院",
// };

// let taskObj = {
//     username: "24002486",
//     password: "055021*Syy",
//     runCourse: true, //网课
//     runAssignment: true, //作业
//     schoolurl: "http://acjjy.aufe.edu.cn/",
//     schoolname: "西安财经大学",
// };

// hcjyCourse(taskObj, taskOptions).then(
//     (val) => console.log(val),
//     (err) => {
//         console.log(err);
//     }
// );
