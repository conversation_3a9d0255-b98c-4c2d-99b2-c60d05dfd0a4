let pageTools=require('../../../../../utils/pageTools.js');
let api = require("../../../../utils/api.js");
async function bookTask(courseStore, infoLogger, attachment, mArgJson, attachLogStr, chapterCookieStr, chapterId) {
    await new Promise((r) => setTimeout(r, 5000));

    let attachmentName = attachment.property.name || attachment.property.title;

    //获取jobid
    jobid = attachment.jobid || attachment.property.jobid;

    let bookPath = "https://mooc1.chaoxing.com/ananas/job";
    let bookParams = {
        jobid: attachment.jobid,
        knowledgeid: chapterId,
        courseid: courseStore.courseId,
        clazzid: courseStore.classId,
        jtoken: attachment.jtoken,
    };
    let bookUrl = pageTools.buildUrl(bookPath, bookParams);
    let bookResult = await api.submitDocumentTask(bookUrl, chapterCookieStr);

    if(bookResult&&bookResult.status){
        await infoLogger(`${attachLogStr}阅读图书完成：${attachmentName}`, "green");
        return;
    }else{
        await infoLogger(`${attachLogStr}阅读图书失败，跳过任务：${attachmentName}`, "red");
        return;
    }

    // if (!bookResult) {
    //     await infoLogger(`[${j + 1}/${chapterLength}]阅读图书失败，跳过任务：${attachment.property.name}`, "red");
    //     return;
    // }
    // try {
    //     let doDocumentJson = JSON.parse(bookResult);
    //     if (doDocumentJson.status) {
    //         await infoLogger(`[${j + 1}/${chapterLength}]图书任务完成：${attachment.property.name}`, "green");
    //         return;
    //     } else {
    //         await infoLogger(`[${j + 1}/${chapterLength}]图书任务失败：${attachment.property.name}`, "red");
    //         return;
    //     }
    // } catch (e) {
    //     await infoLogger(`[${j + 1}/${chapterLength}]解析图书内容失败：${attachment.property.name}`, "red");
    //     return;
    // }
}

module.exports = bookTask;
