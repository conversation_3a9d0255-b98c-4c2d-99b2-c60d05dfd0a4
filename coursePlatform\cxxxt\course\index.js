// ========= 工具模块 =========
let processVerify = require('./processVerify');
let pageTools = require('../../utils/pageTools.js');
let getMainPage = require('../../utils/getMainPage.js');
let Model = require('../../../config/sequelize.config.js');

// ========= 组成模块 =========
let signinModule = require('./module/signinModule/index.js');
let getCourseListModule = require('./module/getCourseListModule.js');
let runCourseModule = require('./module/runCourseModule/index.js');
let runAssignmentModule = require('./module/runAssignmentModule/index.js');
let endModule = require('./module/endModule.js');

async function cxxxtCourse(taskObj, taskOptions) {
    // 打开浏览器观察
    // taskOptions.isHeadless = false;

    if (taskObj.others.includes('独立IP')) {
        taskOptions.isProxy = true;
    } else {
        taskOptions.isProxy = false;
    }

    let infoLogger = pageTools.getCourseInfoLogger(taskObj.id, false);
    let { mainPage, browser } = await getMainPage(taskOptions);
    let globalStore = { taskObj };

    if (taskOptions.isProxy) {
        await infoLogger(`使用独立IP`);
        global.courseTaskOptions = taskOptions;
        global.isProxy = true;
    }

    //放行所有request请求
    await mainPage.setRequestInterception(true);
    //添加中文请求头，应为用了puppeteer-extra-plugin-stealth之后，会导致英文请求头问题
    mainPage.on('request', request => {
        let headers = request.headers();
        headers['Accept-Language'] = 'zh-CN,zh;q=0.9';
        request.continue({ headers });
    });

    // 中间会出现操作异常界面，这里进行拦截
    //这里有问题，就是在 JavaScript 中，await 会等待异步操作完成，但事件监听器不会等待它们。
    // mainPage.on("response", async (response) => {
    //     const status = response.status();
    //     const headers = response.headers();

    //     // Check if the status code indicates a redirect
    //     if (status >= 300 && status < 400 && headers["location"]) {
    //         let redirectUrl = headers["location"];
    //         if (redirectUrl.includes("antispiderShowVerify.ac")) {
    //             await infoLogger(`拦截重定向：from ${response.url()} to ${headers["location"]}`, "red");
    //             await new Promise((resolve) => setTimeout(resolve, 3 * 24 * 60 * 1000)); //等待加载完成
    //             await processVerify(mainPage, infoLogger);
    //         }
    //     }
    // });

    try {

        if (!process.env.HAS_PERMISSION) {
            return;
        }

        // 一、登录
        await signinModule(infoLogger, globalStore, mainPage);

        // 二、获取课程列表 https://mooc2-ans.chaoxing.com/mooc2-ans/visit/interaction
        let courseList = await getCourseListModule(infoLogger, globalStore, mainPage);
        if (courseList.length == 0) {
            await infoLogger('课程数量为0', 'red');
            await browser.close();
            browser = null;
            return {
                finalResult: [{ courseName: '课程数量为0', progress: 0 }],
                warningMessage: '',
            };
        }

        // 三、开始课程
        if (!taskObj.others.includes('跳过网课')) {
            await runCourseModule(infoLogger, globalStore, mainPage, courseList);
        } else {
            await infoLogger('跳过网课学习');
        }

        // 四、开始作业
        if (!taskObj.others.includes('跳过作业')) {
            await runAssignmentModule(infoLogger, globalStore, mainPage, courseList);
        } else {
            await infoLogger('跳过作业学习');
        }

        // 五、收尾工作
        let finalResult = await endModule(infoLogger, globalStore, mainPage);
        await browser.close();
        browser = null;
        return { finalResult, warningMessage: '' };
    } catch (error) {
        await browser.close();
        browser = null;
        throw error;
    } finally {
        if (taskOptions.isProxy) {
            await Model.proxy.update(
                {
                    state: 'idle',
                },
                {
                    where: {
                        ip: taskOptions.proxy.ip + '',
                        port: taskOptions.proxy.port + '',
                    },
                }
            );
        }
        if (browser) {
            await browser.close();
            browser = null;
        }
        global.courseTaskOptions = null;
    }
}

module.exports = cxxxtCourse;
