<!--考生答题界面-->
<template>
  <div id="answer">
    <!--顶部信息栏-->
    <div class="top">
      <ul class="item">
        <li>
          <i
            class="iconfont icon-menufold icon20"
            ref="toggle"
            @click="slider_flag = !slider_flag"
          ></i>
        </li>
        <li>{{ examData.type }}-{{ examData.source }}</li>
        <li @click="flag = !flag">
          <i class="iconfont icon-user icon20"></i>
          <div class="msg" v-if="flag" @click="flag = !flag">
            <p>姓名：{{ userInfo.name }}</p>
            <p>学号: {{ userInfo.id }}</p>
          </div>
        </li>
        <li><i class="iconfont icon-arrLeft icon20"></i></li>
      </ul>
    </div>
    <div class="flexarea">
      <!--左边题目编号区-->
      <transition name="slider-fade">
        <div class="left" v-if="slider_flag">
          <ul class="l-top">
            <li>
              <a href="javascript:;"></a>
              <span>当前</span>
            </li>
            <li>
              <a href="javascript:;"></a>
              <span>未答</span>
            </li>
            <li>
              <a href="javascript:;"></a>
              <span>已答</span>
            </li>
            <li>
              <a href="javascript:;"></a>
              <span>标记</span>
            </li>
          </ul>
          <div class="l-bottom">
            <div class="item">
              <p>单选题部分</p>
              <ul>
                <li v-for="(list, index1) in topic[1]" :key="index1">
                  <a
                    href="javascript:;"
                    @click="change(index1)"
                    :class="{
                      border: index == index1 && currentType == 1,
                      bg:
                        (bg_flag && topic[1][index1].isClick == true) ||
                        radio[index1] == 1 || radio[index1]
                    }"
                  >
                    <span
                      :class="{ mark: topic[1][index1].isMark == true }"
                    ></span>
                    {{ index1 + 1 }}
                  </a>
                </li>
              </ul>
            </div>
            <div class="item">
              <p>多选题部分</p>
              <ul>
                <li v-for="(list, index2) in topic[2]" :key="index2">
                  <a
                    href="javascript:;"
                    @click="fill(index2)"
                    :class="{
                      border: index == index2 && currentType == 2,
                      bg:
                        (bg_flag && topic[2][index2].isClick == true) ||
                        fillAnswer[index2].length != 0
                    }"
                  >
                    <span
                      :class="{ mark: topic[2][index2].isMark == true }"
                    ></span>
                    {{ topicCount[0] + index2 + 1 }}
                  </a>
                </li>
              </ul>
            </div>
            <!-- <div class="item">
              <p>填空题部分</p>
              <ul>
                <li v-for="(list, index2) in topic[2]" :key="index2">
                  <a
                    href="javascript:;"
                    @click="fill(index2)"
                    :class="{
                      border: index == index2 && currentType == 2,
                      bg: fillAnswer[index2][3] == true
                    }"
                    ><span
                      :class="{ mark: topic[2][index2].isMark == true }"
                    ></span
                    >{{ topicCount[0] + index2 + 1 }}</a
                  >
                </li>
              </ul>
            </div> -->
            <div class="item">
              <p>判断题部分</p>
              <ul>
                <li v-for="(list, index3) in topic[3]" :key="index3">
                  <a
                    href="javascript:;"
                    @click="judge(index3)"
                    :class="{
                      border: index == index3 && currentType == 3,
                      bg:
                        (bg_flag && topic[3][index3].isClick == true) ||
                        judgeAnswer[index3]
                    }"
                    ><span
                      :class="{ mark: topic[3][index3].isMark == true }"
                    ></span
                    >{{ topicCount[0] + topicCount[1] + index3 + 1 }}</a
                  >
                </li>
              </ul>
            </div>
            <div>
              <el-button
                class="final"
                type="primary"
                @click="commit()"
                :disabled="isdisabled"
                >结束考试</el-button
              >
            </div>
          </div>
        </div>
      </transition>
      <!--右边选择答题区-->
      <transition name="slider-fade">
        <div class="right">
          <div class="title">
            <p>{{ title }}</p>
            <i class="iconfont icon-right auto-right"></i>
            <span
              >全卷共{{ topicCount[0] + topicCount[1] + topicCount[2] }}题
              <i class="iconfont icon-time"></i>倒计时：<b>{{ time }}</b
              >分钟</span
            >
          </div>
          <div class="content">
            <p class="topic">
              <span class="number">{{ number }}</span
              >{{ showQuestion }}
            </p>
            <!-- 单选题 -->
            <div v-if="currentType == 1">
              <el-radio-group v-model="radio[index]" @change="getChangeLabel">
                <el-radio :label="1">A. {{ showAnswer.answerA }}</el-radio>
                <el-radio :label="2">B. {{ showAnswer.answerB }}</el-radio>
                <el-radio :label="3">C. {{ showAnswer.answerC }}</el-radio>
                <el-radio :label="4">D. {{ showAnswer.answerD }}</el-radio>
                <el-radio v-if="showAnswer.answerE != null" :label="5"
                  >E. {{ showAnswer.answerE }}</el-radio
                >
                <el-radio v-if="showAnswer.answerF != null" :label="6"
                  >E. {{ showAnswer.answerF }}</el-radio
                >
              </el-radio-group>
              <div class="analysis" v-if="isPractice">
                <ul>
                  <li>
                    <el-tag type="success">正确姿势：</el-tag
                    ><span class="right">{{ reduceAnswer.rightAnswer }}</span>
                  </li>
                  <li><el-tag>题目解析：</el-tag></li>
                  <li>
                    {{
                      reduceAnswer.analysis == null
                        ? "暂无解析"
                        : reduceAnswer.analysis
                    }}
                  </li>
                </ul>
              </div>
            </div>
            <!-- 多选 -->
            <div v-if="currentType == 2">
              <!-- <div v-for="(item, currentIndex) in part" :key="currentIndex">
                <el-input
                  placeholder="请填在此处"
                  v-model="fillAnswer[index][currentIndex]"
                  clearable
                  @blur="fillBG"
                >
                </el-input>
              </div> -->
              <el-checkbox-group v-model="checkValue" @change="getMultiLabel">
                <el-checkbox :label="1" :key="1" style="display:block;"
                  >A. {{ showAnswer.answerA }}</el-checkbox
                >
                <el-checkbox :label="2" :key="2" style="display:block;"
                  >B. {{ showAnswer.answerB }}</el-checkbox
                >
                <el-checkbox :label="3" :key="3" style="display:block;"
                  >C. {{ showAnswer.answerC }}</el-checkbox
                >
                <el-checkbox :label="4" :key="4" style="display:block;"
                  >D. {{ showAnswer.answerD }}</el-checkbox
                >
                <el-checkbox
                  v-if="showAnswer.answerE != null"
                  :label="5"
                  :key="5"
                  style="display:block;"
                  >E. {{ showAnswer.answerE }}</el-checkbox
                >
                <el-checkbox
                  v-if="showAnswer.answerF != null"
                  :label="6"
                  :key="6"
                  style="display:block;"
                  >F. {{ showAnswer.answerF }}</el-checkbox
                >
              </el-checkbox-group>
              <div class="analysis" v-if="isPractice">
                <ul>
                  <li>
                    <el-tag type="success">正确姿势：</el-tag
                    ><span class="right">{{ topic[2][index].answer }}</span>
                  </li>
                  <li><el-tag>题目解析：</el-tag></li>
                  <li>
                    {{
                      topic[2][index].analysis == null
                        ? "暂无解析"
                        : topic[2][index].analysis
                    }}
                  </li>
                </ul>
              </div>
            </div>
            <div class="judge" v-if="currentType == 3">
              <el-radio-group
                v-model="judgeAnswer[index]"
                @change="getJudgeLabel"
                v-if="currentType == 3"
              >
                <el-radio :label="1">正确</el-radio>
                <el-radio :label="2">错误</el-radio>
              </el-radio-group>
              <div class="analysis" v-if="isPractice">
                <ul>
                  <li>
                    <el-tag type="success">正确姿势：</el-tag
                    ><span class="right">{{ topic[3][index].answer }}</span>
                  </li>
                  <li><el-tag>题目解析：</el-tag></li>
                  <li>
                    {{
                      topic[3][index].analysis == null
                        ? "暂无解析"
                        : topic[3][index].analysis
                    }}
                  </li>
                </ul>
              </div>
            </div>
          </div>
          <div class="operation">
            <ul class="end">
              <li @click="previous()">
                <i class="iconfont icon-previous"></i><span>上一题</span>
              </li>
              <li @click="mark()">
                <i class="iconfont icon-mark-o"></i><span>标记</span>
              </li>
              <li @click="next()">
                <span>下一题</span><i class="iconfont icon-next"></i>
              </li>
            </ul>
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script>
import { mapState } from "vuex";
import { throttle } from "lodash";
export default {
  data() {
    return {
      isdisabled: false,
      num: null,
      startTime: null, //考试开始时间
      endTime: null, //考试结束时间
      time: null, //考试持续时间
      reduceAnswer: [], //vue官方不支持3层以上数据嵌套,如嵌套则会数据渲染出现问题,此变量直接接收3层嵌套时的数据。
      answerScore: 0, //答题总分数
      allScore: 0,
      bg_flag: false, //已答标识符,已答改变背景色
      isFillClick: false, //选择题是否点击标识符
      slider_flag: true, //左侧显示隐藏标识符
      flag: false, //个人信息显示隐藏标识符
      currentType: 1, //当前题型类型  1--单选题  2--多选题  3--判断题
      radio: [], //保存考生所有单选题的选项
      mulit: [], //多选题选项
      title: "请选择正确的选项",
      index: 0, //全局index
      userInfo: {
        //用户信息
        name: null,
        id: null
      },
      topicCount: [], //每种类型题目的总数
      score: [], //每种类型分数的总数
      examData: {
        //考试信息
        // source: null,
        // totalScore: null,
      },
      topic: {
        //试卷信息
      },
      showQuestion: [], //当前显示题目信息
      showAnswer: {}, //当前题目对应的答案选项
      number: 1, //题号
      part: null, //填空题的空格数量
      fillAnswer: [[]], //二维数组保存所有填空题答案
      judgeAnswer: [], //保存所有判断题答案
      topic1Answer: [], //学生选择题作答编号,
      mulitAnswer: [],
      rightAnswer: "",
      checkValue: [],
      timer: ""
    };
  },
  beforeUpdate() {
    // 单选题
    // 多选题
    // 判断题
  },
  beforeDestroy() {
    // 在组件销毁前清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  created() {
    console.log("页面初始化", this.$store);
    // 在页面加载从本地读取状态数据并写入vuex
    // if (localStorage.getItem("topic1Answer")) {
    //   this.$store.replaceState(
    //     Object.assign(
    //       this.topic1Answer,
    //       JSON.parse(localStorage.getItem("topic1Answer"))
    //     )
    //   );
    // }

    this.getCookies();
    this.getExamData();
    this.showTime();

    //页面刷新前将vuex里的状态数据保存到sessionStorage
    // window.addEventListener("beforeunload", () => {
    //   sessionStorage.setItem("store", JSON.stringify(this.$store.state));
    // });
  },

  methods: {
    getTime(date) {
      //日期格式化
      let year = date.getFullYear();
      let month =
        date.getMonth() + 1 < 10
          ? "0" + (date.getMonth() + 1)
          : date.getMonth() + 1;
      let day = date.getDate() < 10 ? "0" + date.getDate() : date.getDate();
      let hours =
        date.getHours() < 10 ? "0" + date.getHours() : date.getHours();
      let minutes =
        date.getMinutes() < 10 ? "0" + date.getMinutes() : date.getMinutes();
      let seconds =
        date.getSeconds() < 10 ? "0" + date.getSeconds() : date.getSeconds();
      // 拼接
      return (
        year +
        "-" +
        month +
        "-" +
        day +
        " " +
        hours +
        ":" +
        minutes +
        ":" +
        seconds
      );
    },
    getCookies() {
      //获取cookie
      this.userInfo.name = this.$cookies.get("cname");
      this.userInfo.id = this.$cookies.get("cid");

      console.log("aaa", localStorage.getItem("topic1Answer"));
      if (localStorage.getItem("topic1Answer")) {
        this.radio = JSON.parse(localStorage.getItem("topic1Answer"));
      }
      // if(localStorage.getItem("fillAnswer")){
      //   this.fillAnswer= JSON.parse(localStorage.getItem("fillAnswer"))
      // }
      if (localStorage.getItem("judgeAnswer")) {
        this.judgeAnswer = JSON.parse(localStorage.getItem("judgeAnswer"));
      }
      // this.radio= JSON.parse(localStorage.getItem("topic1Answer"))
      // this.fillAnswer= JSON.parse(localStorage.getItem("fillAnswer"))
      // this.judgeAnswer= JSON.parse(localStorage.getItem("judgeAnswer"))
    },
    calcuScore() {
      //计算答题分数
    },
    getExamData() {
      //获取当前试卷所有信息
      let date = new Date();
      this.startTime = this.getTime(date);
      let examCode = this.$route.query.examCode; //获取路由传递过来的试卷编号
      console.log("路由穿过来的试卷id", examCode);
      let num = this.$route.query.num;
      console.log("路由穿过来的试卷份数", num);

      this.$axios(`/api/exam/${examCode}`).then(res => {
        //通过examCode请求试卷详细信息
        this.examData = { ...res.data.data }; //获取考试详情

        this.index = 0;
        this.allScore = this.examData.totalScore;
        this.time = this.examData.totalTime; //获取分钟数
        this.$axios(`/api/paper/${examCode}/${num}/${this.userInfo.name}`).then(
          res => {
            //通过paperId获取试题题目信息
            this.topic = { ...res.data };
            let reduceAnswer = this.topic[1][this.index];
            this.reduceAnswer = reduceAnswer;
            let keys = Object.keys(this.topic); //对象转数组
            keys.forEach(e => {
              let data = this.topic[e];
              this.topicCount.push(data.length);
              let currentScore = 0;
              for (let i = 0; i < data.length; i++) {
                //循环每种题型,计算出总分
                currentScore += data[i].score;
              }
              this.score.push(currentScore); //把每种题型总分存入score
            });
            let len = this.topicCount[1];
            if (localStorage.getItem("fillAnswer")) {
              this.fillAnswer = JSON.parse(localStorage.getItem("fillAnswer"));
            } else {
              let father = [];
              for (let i = 0; i < len; i++) {
                //根据填空题数量创建二维空数组存放每道题答案
                let children = [];
                father.push(children);
              }
              this.fillAnswer = father;
            }

            let dataInit = this.topic[1];
            this.number = 1;
            this.showQuestion = dataInit[0].question;
            this.showAnswer = dataInit[0];
          }
        );
      });
    },
    change(index) {
      //单选题
      this.index = index;
      let reduceAnswer = this.topic[1][this.index];
      this.reduceAnswer = reduceAnswer;
      this.isFillClick = true;
      // 当前是单选题
      this.currentType = 1;
      // 单选题的长度
      let len = this.topic[1].length;
      if (this.index < len) {
        if (this.index <= 0) {
          this.index = 0;
        }

        this.title = "请选择正确的选项";
        let Data = this.topic[1];
        // console.log(Data)
        this.showQuestion = Data[this.index].question; //获取题目信息
        this.showAnswer = Data[this.index];
        this.number = this.index + 1;
      } else if (this.index >= len) {
        this.index = 0;
        this.fill(this.index);
      }
    },
    fillBG() {
      //填空题已答题目 如果已答该题目,设置第四个元素为true为标识符
      if (this.fillAnswer[this.index][0] != null) {
        this.fillAnswer[this.index][3] = true;
      }
    },
    fill(index) {
      console.log("现在的是第几题", index);
      this.checkValue = [];

      // if(this.fillAnswer[index]){
      //  this.checkValue =this.fillAnswer[index];
      // }else{
      // }
      //多选题
      this.checkValue = this.mulit[index]
        ? this.mulit[index]
        : this.fillAnswer[index]
        ? this.fillAnswer[index]
        : [];
      this.index = index;
      console.log(
        "这是第几题",
        index,
        this.checkValue,
        this.fillAnswer,
        this.mulit[index]
      );
      let reduceAnswer = this.topic[2][this.index];
      console.log("reduceAnswer", this.topic[2][this.index]);
      this.reduceAnswer = reduceAnswer;
      this.isFillClick = true;
      // 当前是多选题
      this.currentType = 2;
      console.log("多选题部分", this.topic[2]);
      let len = this.topic[2].length;
      console.log("一共有多少题", len);
      if (this.index < len) {
        if (this.index < 0) {
          this.index = this.topic[1].length - 1;
          this.change(this.index);
        }
        this.title = "请选择正确的选项";
        let Data = this.topic[2];
        // console.log(Data)
        this.showQuestion = Data[this.index].question; //获取题目信息
        console.log("题目信息", this.showQuestion);
        this.showAnswer = Data[this.index];
        console.log("正确选项", this.showAnswer);
        this.number = this.index + 1;
        // debugger;
      } else if (this.index >= len) {
        this.index = 0;
        this.judge(this.index);
      }
      // //填空题
      // let len = this.topic[2].length;
      // this.currentType = 2;
      // this.index = index;
      // if (index < len) {
      //   if (index < 0) {
      //     index = this.topic[1].length - 1;
      //     this.change(index);
      //   } else {
      //     console.log(`总长度${len}`);
      //     console.log(`当前index:${index}`);
      //     this.title = "请在横线处填写答案";
      //     let Data = this.topic[2];
      //     console.log(Data);
      //     this.showQuestion = Data[index].question; //获取题目信息
      //     let part = this.showQuestion.split("()").length - 1; //根据题目中括号的数量确定填空横线数量
      //     this.part = part;
      //     this.number = this.topicCount[0] + index + 1;
      //   }
      // } else if (index >= len) {
      //   this.index = 0;
      //   this.judge(this.index);
      // }
    },
    judge(index) {
      //判断题
      let len = this.topic[3].length;
      this.currentType = 3;
      this.index = index;
      if (this.index < len) {
        if (this.index < 0) {
          this.index = this.topic[2].length - 1;
          this.fill(this.index);
        } else {
          this.title = "请作出正确判断";
          let Data = this.topic[3];

          this.showQuestion = Data[index].question; //获取题目信息
          this.number = this.topicCount[0] + this.topicCount[1] + index + 1;
        }
      } else if (this.index >= len) {
        this.index = 0;
        this.change(this.index);
      }
    },
    //单选序号
    getChangeLabel(val) {
      //获取选择题作答选项

      this.radio[this.index] = val; //当前选择的序号
      if (val) {
        let data = this.topic[1];
        this.bg_flag = true;
        data[this.index]["isClick"] = true;
      }
      /* 保存学生答题选项 */
      this.topic1Answer[this.index] = val;
      window.localStorage.setItem(
        "topic1Answer",
        JSON.stringify(this.topic1Answer)
      );
    },
    //多选序号
    getMultiLabel(val) {
      console.log(val, 213);
      this.mulit[[this.index]] = val;
      this.checkValue = val;
      console.log("选项参数", val);
      if (val) {
        let data = this.topic[2];
        console.log("选项参数", this.topic[2]);
        this.bg_flag = true;
        data[this.index]["isClick"] = true;
      }

      this.fillAnswer[[this.index]] = val; //最终选择序号
      console.log("最终选择序号", this.fillAnswer[[this.index]]);
      window.localStorage.setItem(
        "fillAnswer",
        JSON.stringify(this.fillAnswer)
      );
    },
    getJudgeLabel(val) {
      //获取判断题作答选项
      this.judgeAnswer[this.index] = val;
      window.localStorage.setItem(
        "judgeAnswer",
        JSON.stringify(this.judgeAnswer)
      );
      if (val) {
        let data = this.topic[3];
        this.bg_flag = true;
        data[this.index]["isClick"] = true;
      }
    },
    previous() {
      //上一题
      this.index--;
      switch (this.currentType) {
        case 1:
          this.change(this.index);
          break;
        case 2:
          this.fill(this.index);
          break;
        case 3:
          this.judge(this.index);
          break;
      }
    },
    next() {
      //下一题
      this.index++;
      switch (this.currentType) {
        case 1:
          this.change(this.index);
          break;
        case 2:
          this.fill(this.index);
          break;
        case 3:
          this.judge(this.index);
          break;
      }
    },
    mark() {
      //标记功能
      switch (this.currentType) {
        case 1:
          this.topic[1][this.index]["isMark"] = true; //单选题标记
          break;
        case 2:
          this.topic[2][this.index]["isMark"] = true; //多选题标记
          break;
        case 3:
          this.topic[3][this.index]["isMark"] = true; //判断题标记
      }
    },
    commit() {
      //答案提交计算分数
      /* 计算单选题总分 */
      let topic1Answer = this.topic1Answer;
      let finalScore = 0;
      topic1Answer.forEach((element, index) => {
        //循环每道选择题根据选项计算分数
        let right = null;
        if (element != null) {
          switch (
            element //选项1,2,3,4 转换为 "A","B","C","D"
          ) {
            case 1:
              right = "A";
              break;
            case 2:
              right = "B";
              break;
            case 3:
              right = "C";
              break;
            case 4:
              right = "D";
              break;
            case 5:
              right = "E";
              break;
          }
          if (right == this.topic[1][index].rightAnswer) {
            // 当前选项与正确答案对比
            finalScore += this.topic[1][index].score; // 计算总分数
          }
        }
        // console.log(topic1Answer)
      });
      /**计算多选题总分 */
      // console.log(`this.fillAnswer${this.fillAnswer}`)
      // console.log(this.topic[2][this.index])
      let fillAnswer = this.fillAnswer;

      console.log("多选题选择", fillAnswer);
      fillAnswer.forEach((element, index) => {
        let grade = 0; //多选题每题得分0不得分1得一半2全分
        //此处index和 this.index数据不一致，注意
        //element.map(String);
        element.forEach(ele => {
          if (ele != null) {
            switch (
              ele //选项1,2,3,4 转换为 "A","B","C","D"
            ) {
              case 1:
                this.mulitAnswer.push("A");
                break;
              case 2:
                this.mulitAnswer.push("B");
                break;
              case 3:
                this.mulitAnswer.push("C");
                break;
              case 4:
                this.mulitAnswer.push("D");
                break;
              case 5:
                this.mulitAnswer.push("E");
                break;
              case 6:
                this.mulitAnswer.push("F");
                break;
            }
          }
        });

        //正确答案数组
        let rightAnswer = this.topic[2][index].rightAnswer.split(",");

        console.log("正确答案数组", rightAnswer);
        // let res = this.mulitAnswer.reduce((res, it) => {
        //   if(!this.rightAnswer.includes(it)) {
        //     return 0
        //   }
        //   return res
        // }, 2)

        // let k = this.mulitAnswer.filter(it => {
        //   if (!this.rightAnswer.includes(it)) {
        //     return it
        //   }
        // })
        // if(k.length == 0 ) {
        //   if() {

        //   } else {

        //   }
        // } else {

        // }
        //判断得分
        if (rightAnswer.length < this.mulitAnswer.length) {
          // 选择的比答案多-0分
          grade = 0;
        } else if (rightAnswer.length > this.mulitAnswer.length) {
          grade = 0;
          // for (let i = 0; i < this.mulitAnswer.length; i++) {
          //   if (-1 == rightAnswer.indexOf(this.mulitAnswer[i])) {
          //     grade = 0;
          //     break; //答错任何一项，判错不得分
          //   } else {
          //     grade = 1; //少答得一半分
          //   }
          // }
        } else {
          for (let i = 0; i < this.mulitAnswer.length; i++) {
            if (-1 == rightAnswer.indexOf(this.mulitAnswer[i])) {
              grade = 0;
              break; //答错任何一项，判错不得分
            } else {
              grade = 2; //得满分
            }
          }
        }
        if (grade === 1) {
          finalScore += this.topic[2][this.index].score / 2;
        }
        if (grade === 2) {
          finalScore += this.topic[2][this.index].score;
        }
        //计算得分后，当前选项为空
        this.mulitAnswer = [];
      });
      /** 计算判断题总分 */
      let topic3Answer = this.judgeAnswer;
      topic3Answer.forEach((element, index) => {
        let right = null;
        switch (element) {
          case 1:
            right = "T";
            break;
          case 2:
            right = "F";
        }
        if (right == this.topic[3][index].answer) {
          // 当前选项与正确答案对比
          finalScore += this.topic[3][index].score; // 计算总分数
        }
      });

      if (this.time > 0) {
        this.$confirm("考试结束时间未到,是否提前交卷", "友情提示", {
          confirmButtonText: "立即交卷",
          cancelButtonText: "再检查一下",
          type: "warning"
        })
          .then(() => {
            let answerDate = this.$moment().format("YYYY-MM-DD HH:MM:SS");

            //提交成绩信息
            this.$axios({
              url: "/api/scoreUpload",
              method: "post",
              data: {
                examCode: this.examData.examCode, //考试编号
                studentId: this.userInfo.id, //学号
                subject: this.examData.source, //课程名称
                score: finalScore, //答题成绩
                answerDate: answerDate //答题日期
              }
            }).then(res => {
              if (res.data.code == 200) {
                localStorage.clear();
                this.isdisabled = true;
                this.$router.replace({
                  path: "/studentScore",
                  query: {
                    score: finalScore,
                    examCode: this.examData.examCode,
                    startTime: this.startTime,
                    endTime: this.endTime
                  }
                });
              }
              if (res.data.code == 400) {
                this.$message({
                  showClose: true,
                  type: "error",
                  message: res.data.message
                });
              }
            });
          })
          .catch(() => {});
      } else {
        let answerDate = this.$moment().format("YYYY-MM-DD HH:MM:SS");

        // 提交成绩信息
        this.$axios({
          url: "/api/scoreUpload",
          method: "post",
          data: {
            examCode: this.examData.examCode, //考试编号
            studentId: this.userInfo.id, //学号
            subject: this.examData.source, //课程名称
            score: finalScore, //答题成绩
            answerDate: answerDate //答题日期
          }
        }).then(res => {
          if (res.data.code == 200) {
            localStorage.clear();
            this.isdisabled = true;
            this.$router.replace({
              path: "/studentScore",
              query: {
                score: finalScore,
                examCode: this.examData.examCode,
                startTime: this.startTime,
                endTime: this.endTime
              }
            });
          }
          if (res.data.code == 400) {
            this.$message({
              showClose: true,
              type: "error",
              message: res.data.message
            });
          }
        });
      }
    },
    showTime() {
      // 倒计时
      this.timer = setInterval(() => {
        // 获取当前时间
        this.time -= 1;
        console.log(this.time);
        if (this.time == 10) {
          this.$message({
            showClose: true,
            type: "error",
            message: "考生注意,考试时间还剩10分钟！！！"
          });
        }
        if (this.time == 0) {
          this.$message({
            showClose: true,
            type: "error",
            message: "考试时间已到,强制交卷。！！！"
          });
          this.commit();
        }
      }, 1000 * 60);
    }
  },
  computed: mapState(["isPractice"])
};
</script>

<style lang="less">
.iconfont.icon-time {
  color: #2776df;
  margin: 0px 6px 0px 20px;
}

.analysis {
  margin-top: 20px;

  .right {
    color: #2776df;
    font-size: 18px;
    border: 1px solid #2776df;
    padding: 0px 6px;
    border-radius: 4px;
    margin-left: 20px;
  }

  ul li:nth-child(2) {
    margin: 20px 0px;
  }

  ul li:nth-child(3) {
    padding: 10px;
    background-color: #d3c6c9;
    border-radius: 4px;
  }
}

.analysis span:nth-child(1) {
  font-size: 18px;
}

.mark {
  position: absolute;
  width: 4px;
  height: 4px;
  content: "";
  background-color: red;
  border-radius: 50%;
  top: 0px;
  left: 22px;
}

.border {
  position: relative;
  border: 1px solid #ff90aa !important;
}

.bg {
  background-color: #5188b8 !important;
}

.fill .el-input {
  display: inline-flex;
  width: 150px;
  margin-left: 20px;

  .el-input__inner {
    border: 1px solid transparent;
    border-bottom: 1px solid #eee;
    padding-left: 20px;
  }
}

/* slider过渡效果 */
.slider-fade-enter-active {
  transition: all 0.3s ease;
}

.slider-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slider-fade-enter,
.slider-fade-leave-to {
  transform: translateX(-100px);
  opacity: 0;
}

.operation .end li:nth-child(2) {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  background-color: rgb(39, 118, 223);
  border-radius: 50%;
  width: 50px;
  height: 50px;
  color: #fff;
}

.operation .end li {
  cursor: pointer;
  margin: 0 100px;
}

.operation {
  background-color: #fff;
  border-radius: 4px;
  padding: 10px 0px;
  margin-right: 10px;
}

.operation .end {
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgb(39, 118, 223);
}

.content .number {
  display: inline-flex;
  justify-content: center;
  align-items: center;
  width: 20px;
  height: 20px;
  background-color: rgb(39, 118, 223);
  border-radius: 4px;
  margin-right: 4px;
}

.content {
  padding: 0px 20px;
}

.content .topic {
  padding: 20px 0px;
  padding-top: 30px;
}

.right .content {
  background-color: #fff;
  margin: 10px;
  margin-left: 0px;
  height: 470px;
}

.content .el-radio-group label {
  color: #000;
  margin: 5px 0px;
}

.content .el-radio-group {
  display: flex;
  flex-direction: column;
}

.right .title p {
  margin-left: 20px;
}

.flexarea {
  display: flex;
}

.flexarea .right {
  flex: 1;
}

.auto-right {
  margin-left: auto;
  color: #2776df;
  margin-right: 10px;
}

.right .title {
  margin-right: 10px;
  padding-right: 30px;
  display: flex;
  margin-top: 10px;
  background-color: #fff;
  height: 50px;
  line-height: 50px;
}

.clearfix {
  clear: both;
}

.l-bottom .final {
  cursor: pointer;
  display: inline-block;
  text-align: center;
  background-color: rgb(39, 118, 223);
  width: 240px;
  margin: 20px 0px 20px 10px;
  border-radius: 4px;
  height: 50px;
  line-height: 30px;
  color: #fff;
  margin-top: 22px;
}

#answer .left .item {
  padding: 0px;
  font-size: 16px;
}

.l-bottom {
  border-radius: 4px;
  background-color: #fff;
}

.l-bottom .item p {
  margin-bottom: 15px;
  margin-top: 10px;
  color: #000;
  margin-left: 10px;
  letter-spacing: 2px;
}

.l-bottom .item li {
  width: 15%;
  margin-left: 5px;
  margin-bottom: 10px;
}

.l-bottom .item {
  display: flex;
  flex-direction: column;
}

.l-bottom .item ul {
  width: 100%;
  margin-bottom: -8px;
  display: flex;
  justify-content: space-around;
  flex-wrap: wrap;
}

.l-bottom .item ul li a {
  position: relative;
  justify-content: center;
  display: inline-flex;
  align-items: center;
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #fff;
  border: 1px solid #eee;
  text-align: center;
  color: #000;
  font-size: 16px;
}

.left .l-top {
  display: flex;
  justify-content: space-around;
  padding: 16px 0px;
  border: 1px solid #eee;
  border-radius: 4px;
  margin-bottom: 10px;
  background-color: #fff;
}

.left {
  width: 260px;
  height: 100%;
  margin: 10px 10px 0px 10px;
}

.left .l-top li:nth-child(2) a {
  border: 1px solid #eee;
}

.left .l-top li:nth-child(3) a {
  background-color: #5188b8;
  border: none;
}

.left .l-top li:nth-child(4) a {
  position: relative;
  border: 1px solid #eee;
}

.left .l-top li:nth-child(4) a::before {
  width: 4px;
  height: 4px;
  content: " ";
  position: absolute;
  background-color: red;
  border-radius: 50%;
  top: 0px;
  left: 16px;
}

.left .l-top li {
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.left .l-top li a {
  display: inline-block;
  padding: 10px;
  border-radius: 50%;
  background-color: #fff;
  border: 1px solid #ff90aa;
}

#answer .top {
  background-color: rgb(39, 118, 223);
}

#answer .item {
  color: #fff;
  display: flex;
  padding: 20px;
  font-size: 20px;
}

#answer .top .item li:nth-child(1) {
  margin-right: 10px;
}

#answer .top .item li:nth-child(3) {
  position: relative;
  margin-left: auto;
}

#answer {
  padding-bottom: 30px;
}

.icon20 {
  font-size: 20px;
  font-weight: bold;
}

.item .msg {
  padding: 10px 15px;
  border-radius: 4px;
  top: 47px;
  right: -30px;
  color: #6c757d;
  position: absolute;
  border: 1px solid rgba(0, 0, 0, 0.15);
  background-color: #fff;
}

.item .msg p {
  font-size: 16px;
  width: 200px;
  text-align: left;
}
</style>



// WEBPACK FOOTER //
// src/components/student/answer.vue