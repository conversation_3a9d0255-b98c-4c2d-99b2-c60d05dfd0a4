let matchAnswerWithAI = require('./matchAnswerWithAI.js');
let matchAnswerWithLocal = require('./matchAnswerWithLocal.js');
let api = require('../api.js');
let { validateAnswer } = require('../format.js');
let collectQuestion = require('../collectQuestion.js');

async function getAnswerFromAiDian(questionObj) {
    // 从第三方题库获取答案
    let answersRes = await api.getAnswerFromAiDian(questionObj);
    if (answersRes.code != 1) {
        return;
    }
    answersRes.source = 'aidian';

    let answers;
    //根据题目类型，决定是否使用AI进行答案匹配
    switch (questionObj.type) {
        case '判断题':
        case '单选题':
        case '多选题': {
            // 首先根据自己的逻辑进行答案匹配
            answers = matchAnswerWithLocal(answersRes, questionObj);
            // 如果匹配不到，再使用AI进行匹配答案
            if (!answers) {
                answers = await matchAnswerWithAI(questionObj, answersRes);
            }
            break;
        }
        // 填空 简答 等其他题目
        default: {
            answers = await matchAnswerWithAI(questionObj, answersRes);
            break;
        }
    }

    // 校验题目类型
    let isFormat = validateAnswer(answers, questionObj.type);
    if (!isFormat) {
        return;
    }

    // 收集题目
    if (answers && answers != 'false') {
        await collectQuestion(questionObj, answers, 'getAnswerFromAiDian');
    }

    return answers;
}

module.exports = getAnswerFromAiDian;

// 单元测试
if (false) {
    // let questionObj = {
    //     id: 'id-maqmgd04-mitnaupijsigleQuestionDiv_884682952',
    //     content:
    //         'Plain Text下列程序的运行结果?publicclassTest{publicstaticvoidmain(Stringa[]){intx=3,y=4,z=5;if(x>3){if(y<2)System.out.println("showone");elseSystem.out.println("showtwo");}else{if(z>4)System.out.println("showthree");elseSystem.out.println("showfour");}}}',
    //     type: '单选题',
    //     options: ['A:show one', 'B:show two', 'C:show three', 'D:show four'],
    //     platform: '超星学习通',
    //     courseName: '2025第1学期Java语言程序设计期末考试',
    // };

    let questionObj = {
        id: 'id-mas8qemp-wilmmgml6sigleQuestionDiv_884682989',
        content: '4.MouseListener接口中共定义了__________个抽象方法\n。',
        type: '填空题',
        options: [],
        platform: '超星学习通',
        courseName: '2025第\n1学期Java语言程序设计期末考试',
        answers: '5|五',
    };
    getAnswerFromAiDian(questionObj).then(r => console.log('最终答案', r));
}
