let path = require('path');
let axiosIns = require('../utils/axiosIns.js');

// 我现在有两个源头，一个deepseek官网，一个是oai

async function aiChat(model, str) {
    let oaiproConfig = {
        method: 'post',
        // url: 'https://api.oaipro.com/v1/chat/completions',
        url: 'http://oaipro.mozhi0012.top/v1/chat/completions',
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer sk-JPTMbl93OqSRazlM50E9Ec94D75d48149dE46cF6Da908066`,
        },
        data: {
            model: model,
            messages: [{ role: 'user', content: str }],
            // temperature: 1,
        },
    };

    let deepSeekConfig = {
        method: 'post',
        maxBodyLength: Infinity,
        url: 'https://api.deepseek.com/chat/completions',
        headers: {
            'Content-Type': 'application/json',
            Accept: 'application/json',
            Authorization: 'Bearer sk-b27f3055fc9a41c6bb391f6640dba1d5',
        },
        data: {
            messages: [
                {
                    content: str,
                    role: 'user',
                },
            ],
            model: model,
            // model: "deepseek-coder",
            // frequency_penalty: 0,
            // max_tokens: 4096,
            // presence_penalty: 0,
            // stop: null,
            stream: false,
            temperature: 0,
            // top_p: 1,
            // logprobs: false,
            // top_logprobs: null,
        },
    };

    let res;
    let config;
    // deepseek-chat
    if (model.includes('deepseek')) {
        config = deepSeekConfig;
    } else {
        config = oaiproConfig;
    }
    res = await axiosIns(config);
    res = res.choices[0].message.content;
    return res;
}

module.exports = aiChat;

// 单元测试
if (false) {
    // aiChat('gpt-4-turbo', '你好啊', '').then(r => console.log('deepseek', r));
    aiChat('claude-3-5-sonnet-20240620', '你好啊').then(r => console.log(r));
    // aiChat('gpt-4o-mini', '你好啊').then(r =>console.log('cluade', r));
    // aiChat('deepseek-chat', '《2025年食品安全风险监测检验项目和检验方法》较《全国食品安全风险监测参考值 ( 2024年版）》多了哪些检测（监测）项目，并注明该项目所在大类和食品细类（四级）及检测方法').then(r =>console.log('cluade', r));
}

