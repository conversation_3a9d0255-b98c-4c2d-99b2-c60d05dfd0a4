let { v4 } = require('uuid');
let Model = require('../../../config/sequelize.config.js'); //用于搜题
let api = require('./api.js');
let getAnswerFromLocal = require('../../solveQuestion/getAnswerFromLocal.js');
let solveQuestion = require('../../solveQuestion/index.js');
let { handleImgs } = require('../../solveQuestion/format.js');

// '1,2,3,4,5'转为ABCDE
function numbersToLetters(str = '') {
    let letters = '';
    for (let i = 0; i < str.length; i++) {
        let num = parseInt(str[i]);
        if (!isNaN(num)) {
            letters += String.fromCharCode(num + 16 + 48); // 加上 17 和 48 来得到对应字母的编码
        }
    }
    return letters;
}

// ABCDE转为'1,2,3,4,5
function lettersToNumbers(str = '') {
    let codes = [];
    for (let i = 0; i < str.length; i++) {
        codes.push(str.charCodeAt(i) - 64);
    }
    return codes.join(',');
}

function trim(s) {
    return (
        s
            //删除多余字符串
            .replace(/(<([^>]+)>)/gi, '') //删除html标签 <span>
            .replaceAll('\n', '') //删除所有的换行符
            .replaceAll('&nbsp;', '')
            .replace(/\s{2,}/g, ' ') //删除两个及以上的空格，保留单个空格
            .trim()
    );
}

function handleContent(str) {
    return trim(handleImgs(unescape(str))).slice(0, 3000);
}

// 对原始API获取的题目进行格式化 其实应该更名为formatQuestion
function formatQuestion(questionListRes, courseName) {
    let questionList = [];
    let ruleId = questionListRes.ruleId;

    questionListRes.paperObj.forEach(typeItem => {
        let type = typeItem.QuestionTypeName;
        typeItem.GetPaperList.forEach(questionItem => {
            let questionObj = {
                id: v4(), //题目id，临时生成
                type: type,
                options: [], //经过原始选项处理后
                platform: '安徽继续教育在线',
                courseName: courseName, //外部传入
                questionId: questionItem.Id, // 原始题目id
                ruleId: ruleId, //
            };

            //处理题目内容 unescape可以去掉转义字符
            questionObj.content = handleContent(questionItem.Content);

            // 处理题目选项和答案
            switch (type) {
                case '单选题':
                case '多选题': {
                    // 处理选项
                    questionItem.CourseQuestionOptionList.forEach((optionItem, index) => {
                        let optionKey = String.fromCharCode(65 + index);
                        let optionValue = handleContent(optionItem.Content);
                        questionObj.options.push(`${optionKey}:${optionValue}`);
                    });
                    // 处理答案
                    if (questionItem.RightAnswer) {
                        questionObj.answers = numbersToLetters(questionItem.RightAnswer);
                    }
                    break;
                }
                case '判断题': {
                    // 处理选项
                    questionObj.options = ['A:对', 'B:错'];
                    // 处理答案
                    if (questionItem.RightAnswer) {
                        questionObj.answers = numbersToLetters(questionItem.RightAnswer);
                    }
                    break;
                }
                case '填空题': {
                    if (questionItem.RightAnswer) {
                        questionObj.answers = handleContent(questionItem.RightAnswer);
                    }
                    break;
                }
                case '问答题': {
                    if (questionItem.RightAnswer) {
                        questionObj.answers = handleContent(questionItem.RightAnswer);
                    }
                    break;
                }
                default: {
                    throw new Error('未知题型');
                }
            }

            questionList.push(questionObj);
        });
    });

    return questionList;
}

//回答 作业 问题(assignmentTask testTask) （不是一次性提交，而是一题一题的提交）
async function answerQusetion(questionList, storageObj) {
    let submitCount = 0;
    for (let i = 0; i < questionList.length; i++) {
        let questionObj = questionList[i];
        let answers;

        //查询答案
        if (!questionObj.answers) {
            questionObj.answers = await getAnswerFromLocal(questionObj);
        }

        if (!questionObj.answers) continue;

        switch (questionObj.type) {
            case '判断题':
            case '单选题':
            case '多选题': {
                answers = lettersToNumbers(questionObj.answers);
                break;
            }
            case '填空题': {
                answers = questionObj.answers;
                break;
            }
            case '问答题': {
                answers = questionObj.answers;
                break;
            }
        }

        let answersObj = {
            id: questionObj.questionId,
            studentAnswer: answers,
            ruleId: questionObj.ruleId,
            answerId: questionObj.questionId,
            optionId: '00000000-0000-0000-0000-000000000000',
        };

        //回答题目
        let submitPaperItem4UserRes = await api.answerQuestion(storageObj, answersObj);
        await new Promise(r => setTimeout(r, 10000));
        submitCount++;
    }
    return submitCount;
}

//收集题目
async function collectQuestion(questionList) {
    let collectCount = 0;
    //收集题目
    for (questionObj of questionList) {
        try {
            if (!questionObj.answers) continue;

            let answersContent = [];
            switch (questionObj.type) {
                // case '填空题': {
                //     // 填空题默认的填空题分隔符是","这里转为"|"
                //     questionObj.answers = questionObj.answers.replaceAll(',', '|');
                //     answersContent.push(questionObj.answers);
                //     break;
                // }
                // case '判断题': {
                //     answersContent.push(questionObj.answers);
                //     questionObj.answers = questionObj.answers == '对' ? 'A' : 'B';
                //     break;
                // }
                case '判断题':
                case '单选题':
                case '多选题': {
                    for (let str of questionObj.answers) {
                        let ansIndex = { A: 0, B: 1, C: 2, D: 3, E: 4, F: 5, G: 6, H: 7 }[str];
                        let queOption = questionObj.options[ansIndex];
                        let [queIndex, queValue] = queOption.split(/:(.+)/);
                        answersContent.push(queValue);
                        // console.log(queIndex, queValue)
                        // 这里不能直接用:，因为字符串可能有多个:
                        // let [queIndex, queValue] = queOption.split(':');
                        answersContent = [...answersContent].sort((x, y) => x.localeCompare(y, 'zh-Hans-CN'));
                    }
                    break;
                }
                default: {
                    // 处理简答题答案为“略”的情况
                    if ('略'.includes(questionObj.answers) || '言之成理即可得分。'.includes(questionObj.answers)) {
                        questionObj.answers = await solveQuestion(questionObj);
                    } else {
                        questionObj.answers = handleContent(questionObj.answers);
                    }
                    answersContent.push(questionObj.answers);
                    break;
                }
            }
            questionObj.answersContent = answersContent;

            let existAnswer = await Model.bank.findOne({
                where: {
                    content: questionObj.content,
                    type: questionObj.type,
                    platform: '安徽继续教育在线',
                },
            });
            // 如果题库中存在的题目，不是继续教育，柠檬文采，就重新收集
            if (!existAnswer) {
                await Model.bank.create({
                    id: questionObj.id,
                    content: questionObj.content,
                    options: JSON.stringify(questionObj.options),
                    type: questionObj.type,
                    answers: questionObj.answers,
                    answers_content: JSON.stringify(answersContent),
                    course_name: questionObj.courseName,
                    platform: questionObj.platform,
                    add_time: new Date(),
                });
                collectCount++;
            }
        } catch (error) {
            error.questionObj = questionObj;
            throw error;
        }

        // await new Promise((r) => setTimeout(r, 1000000));
    }
    return collectCount;
}

module.exports = { formatQuestion, answerQusetion, collectQuestion };

if (false) {
    let questionList = [
        // {
        //     id: 'ad4f54e6-6f1e-49d1-8bf2-401323fa7568',
        //     type: '单选题',
        //     options: ['A:中华优秀传统文化', 'B:社会主义先进文化', 'C:革命文化号', 'D:中国特色社会主义文化'],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-780f-56cd-0992d5748ca6',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '( )是中华文明的智慧结晶和精华所在，是中华民族的根和魂，是我们在世界文化激荡中站稳脚跟的根基。',
        //     answers: 'A',
        // },
        // {
        //     id: 'f4d2517a-bd25-4c39-b7e9-2a86a2404644',
        //     type: '单选题',
        //     options: ['A:社会主义', 'B:马克思主义', 'C:中国特色社会主义', 'D:中华优秀传统文化'],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-0f51-981e-4b036f6b0acd',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '( )是当代中国文化发展的灵魂。',
        //     answers: 'B',
        // },
        // {
        //     id: '45c78519-a55c-4ea7-beac-ca49e8c309b4',
        //     type: '单选题',
        //     options: ['A:社会主义经济', 'B:社会主义制度', 'C:中华文化', 'D:社会主义文化'],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-ad03-651e-01b3c52750b5',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '一个国家、一个民族的强盛，总是以文化兴盛为支撑的，中华民族伟大复兴需要以（ ）发展繁荣为条件。',
        //     answers: 'C',
        // },
        // {
        //     id: '4e483bd7-63fc-4c41-961a-51b03ac515c4',
        //     type: '单选题',
        //     options: ['A:文化自信', 'B:文化自觉', 'C:文化软实力', 'D:中国特色社会主义文化'],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-2003-3bd1-018fefd5f4df',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '（ ）是更基础、更广泛、更深厚的自信，是一个国家、一个民族发展中最基本、最深沉、最持久的力量。',
        //     answers: 'A',
        // },
        // {
        //     id: 'fd5ab2d7-9f80-4dea-ba6b-a4f3c1f22ddb',
        //     type: '单选题',
        //     options: ['A:社会主义核心价值体系', 'B:理想信念、价值理念、道德观念', 'C:中华优秀传统文化', 'D:社会主义核心价值观'],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-8c81-7516-43f2a40775d0',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '培育和践行社会主义核心价值观，要把( )融入社会生活各个方面。',
        //     answers: 'D',
        // },
        // {
        //     id: '064dd607-ebc8-4a89-a512-cfae11ded47a',
        //     type: '单选题',
        //     options: ['A:坚定中国特色社会主义', 'B:文化自信', 'C:高质量发展', 'D:提高综合国力', 'E:贯彻新发展理念'],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-0a00-8352-056990a36394',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '建设社会主义文化强国、推动社会主义文化繁荣兴盛，关键在于（）。',
        //     answers: 'A',
        // },
        // {
        //     id: 'f5b51db6-3108-4b6f-95fb-b2f9ac2b1767',
        //     type: '多选题',
        //     options: ['A:政治领导力', 'B:思想引领力', 'C:群众组织力', 'D:社会号召力'],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-4353-1e12-5c8281dee29b',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '坚持和加强党的全面领导，使党的（ ）显著增强',
        //     answers: 'ABCD',
        // },
        // {
        //     id: '279d19a2-95c9-4f4b-8333-3f96f2355c90',
        //     type: '多选题',
        //     options: ['A:国家性质', 'B:国体政体', 'C:经济基础', 'D:经济制度'],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-66a8-3618-5aad00957aae',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '党是最高政治领导力量，这是由我国（ ）和（ ）所决定的。',
        //     answers: 'AB',
        // },
        // {
        //     id: '421a4a7a-35ee-49f7-b9fd-e012eb8e185e',
        //     type: '多选题',
        //     options: [
        //         'A:团结带领全国各族人民全面建成社会主义现代化强国',
        //         'B:实现第二个百年奋斗目标',
        //         'C:以中国式现代化全面推进中华民族伟大复兴。',
        //         'D:贯彻新发展理念，促进高质量发展',
        //     ],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-d736-1448-304a15684c16',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '党的二十大指出，新时代新征程中国共产党的中心任务，就是（ ）。',
        //     answers: 'ABC',
        // },
        // {
        //     id: 'dc3ef82d-007e-4eb4-b0b5-ab7e1f68ace1',
        //     type: '多选题',
        //     options: [
        //         'A:能够有效保证人民享有更加广泛的权利和自由',
        //         'B:能够有效调节国家政治关系，形成安定团结的政治局面',
        //         'C:能够集中力量办大事，有效促进社会生产力解放和发展',
        //         'D:能够有效维护国家独立自主,有力维护国家主权、安全、发展利益',
        //     ],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-74ca-fe18-8b9707b9a487',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '中国特色社会主义政治制度的巨大优势体现在（ ）。',
        //     answers: 'ABCD',
        // },
        // {
        //     id: '7c33c4bf-608b-4747-9873-487e46f58330',
        //     type: '多选题',
        //     options: [
        //         'A:坚持中国共产党领导，有效保证国家沿着社会主义道路前进',
        //         'B:最大限度保障人民当家作主 。',
        //         'C:有效保证国家治理跳出治乱兴衰的历史周期率',
        //         'D:正确处理事关国家前途命运的一系列重大政治关系，维护国家统一和民族团结',
        //         'E:有效保证国家政治生活既充满活力又安定有序',
        //     ],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-a4bd-4890-9421c01a89cb',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '人民代表大会制度（ ）',
        //     answers: 'ABCDE',
        // },
        // {
        //     id: '3fd1122a-e311-4aed-8fa1-8fa7b008cbbd',
        //     type: '多选题',
        //     options: ['A:依法治国', 'B:协同发展', 'C:全面发展', 'D:以德治国'],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-5483-1fa5-13a9185a8433',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '要坚持（ ）相结合，实现法治和德治相辅相成、相得益彰',
        //     answers: 'AD',
        // },
        // {
        //     id: 'cd23cfe6-b470-443e-a43a-c2af9a90661f',
        //     type: '多选题',
        //     options: [
        //         'A:既把握了长期形成的历史传承，又把握了走过的发展道路',
        //         'B:把握了现实要求、着眼解决现实问题',
        //         'C:注重历史和现实、理论和实践、形式和内容有机统一',
        //         'D:具有鲜明的中国特色、民族特色、时代特色',
        //     ],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-4fb2-97e2-73ce1867f7aa',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '屮国特色社会主义政治制度（ ）。',
        //     answers: 'ABCD',
        // },
        // {
        //     id: '6788b666-94b3-474f-a5e6-0f1b9eb720aa',
        //     type: '多选题',
        //     options: ['A:全方面', 'B:全链条', 'C:全方位', 'D:全覆盖'],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-c412-bd3b-4c7108740740',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '全过程人民民主是（ ）的民主，是最广泛、最真实、最管用的社会主义民主。',
        //     answers: 'BCD',
        // },
        // {
        //     id: '2f516f16-eb99-40b1-86c5-e9ec2e7412e9',
        //     type: '多选题',
        //     options: [
        //         'A:国家治理的一场深刻革命，关系党执政兴国，关系人民幸福安康',
        //         'B:完善和发展中国特色社会主义制度、推进国家治理体系和治理能力现代化的重要方面',
        //         'C:坚持和发展中国特色社会主义的本质要求和重要保障',
        //         'D:“四个全面”战略布局之一',
        //     ],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-fa0c-6b76-7f6c959bc302',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '全面依法治国是（ ）',
        //     answers: 'ABCD',
        // },
        // {
        //     id: '2ccce5e8-2961-4ebe-8cf2-84bddd5b371d',
        //     type: '多选题',
        //     options: [
        //         'A:这是在近代以来中国历史发展中形成的',
        //         'B:这是由中国最广大人民根本利益决定的',
        //         'C:这是实现中华民族伟大复兴历史任务决定的',
        //         'D:这决定了中国特色社会主义其他特点和特征',
        //     ],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-1eb9-a8e6-2bf1d835d846',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '中国共产党领导是中国特色社会主义最本质的特征，（ ）。',
        //     answers: 'ABCD',
        // },
        // {
        //     id: '2a413cb4-eae6-481a-bb61-64d711fcddf1',
        //     type: '多选题',
        //     options: [
        //         'A:必须完善党在各种组织中发挥领导作用的制度',
        //         'B:必须完善党协调各方的机制',
        //         'C:必须完善党领导各项事业的具体制度',
        //         'D:必须完善人民代表大会制度',
        //     ],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-9e4d-93a1-8512c36672d8',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '健全党的全面领导制度，必须（ ）。',
        //     answers: 'ABC',
        // },
        // {
        //     id: 'b72e4296-c1a9-4318-8cf0-f6ae14e425cd',
        //     type: '多选题',
        //     options: [
        //         'A:人口规模巨大的现代化',
        //         'B:全体人民共同发展的现代化',
        //         'C:物质文明和精神文明相协调的现代化',
        //         'D:人与自然和谐共生的现代化',
        //         'E:走和平发展道路的现代化',
        //     ],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-540f-f2fa-08ea41f333ac',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '中国式现代化的特征是（ ）。',
        //     answers: 'ACDE',
        // },
        // {
        //     id: 'dee3d77f-f0b5-4302-9810-88f9f2ca9b32',
        //     type: '多选题',
        //     options: [
        //         'A:把党的主张、国家意志、人民意愿紧密融合在一起，彰显了人民民主的真实性',
        //         'B:真实反映人民的期盼、希望和诉求',
        //         'C:人民的意愿和呼声，经过民主决策程序成为党和国家的方针政策',
        //         'D:真真切切落实到国家政治生活和社会生活各方面',
        //     ],
        //     platform: '安徽继续教育在线',
        //     courseName: '习近平新时代中国特色社会主义思想概论',
        //     questionId: '3a18fd38-f2e8-b6ce-fe64-7b260cbc7892',
        //     ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
        //     content: '全过程人民民主是最真实的民主,具体体现在（ ）。',
        //     answers: 'ABCD',
        // },
        {
            id: '2f490e30-a46e-4d15-9158-b708476e40a2',
            type: '多选题',
            options: [
                'A:保证在党的领导下有效治理国家，切实防止出现一盘散沙的现象',
                'B:保证人民依法行使权利，切实防止出现选举时漫天许诺、选举后无人过问的现象',
                'C:加强社会各种力量的合作协调，切实防止出现党争纷沓、相互倾轧的现象',
                'D:发展基层民主，切实防止出现人民形式上有权、实际上无权的现象',
                'E:巩固平等团结互助和谐的社会主义民族关系，切实防止出现民族隔阂、民族冲突的现象',
            ],
            platform: '安徽继续教育在线',
            courseName: '习近平新时代中国特色社会主义思想概论',
            questionId: '3a18fd38-f2e8-a5a6-467b-43e9d00ef663',
            ruleId: '3a17a560-2bbe-807c-6a21-da7d1d3b52f6',
            content: '全过程人民民主是最管用的民主, 具体体现在（ ）。',
            answers: 'ABCDE',
        },
    ];
    collectQuestion(questionList);
}
