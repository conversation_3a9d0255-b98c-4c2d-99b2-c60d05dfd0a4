// 引入crypto-js模块
let CryptoJS = require('crypto-js');

/**
 * AES-CBC加密函数
 * @param {Object|String} data - 要加密的数据，如果是对象会自动转为JSON字符串
 * @param {String} key - 密钥 (默认使用从混淆代码中恢复的密钥)
 * @param {String} iv - 初始化向量 (默认使用从混淆代码中恢复的IV)
 * @returns {String} - 返回加密后的字符串
 */
function encryptAssignment(data, key = '3a1727db02b7d65ba9eac1897538ef35', iv = '3a1727db02b7d65b') {
    // 将数据转为JSON字符串(如果是对象)
    let dataStr = typeof data === 'object' ? JSON.stringify(data) : data;

    // 将密钥和IV转换为Utf8格式
    let keyUtf8 = CryptoJS.enc.Utf8.parse(key);
    let ivUtf8 = CryptoJS.enc.Utf8.parse(iv);

    // 执行AES加密
    let encrypted = CryptoJS.AES.encrypt(dataStr, keyUtf8, {
        mode: CryptoJS.mode.CBC, // 使用CBC模式
        padding: CryptoJS.pad.Pkcs7, // 使用Pkcs7填充
        iv: ivUtf8, // 初始化向量
    });

    // 返回加密后的字符串
    return encrypted.toString();
}

/**
 * AES-CBC解密函数
 * @param {String} encryptedStr - 加密后的字符串
 * @param {String} key - 密钥 (默认使用从混淆代码中恢复的密钥)
 * @param {String} iv - 初始化向量 (默认使用从混淆代码中恢复的IV)
 * @param {Boolean} parseJson - 是否将解密结果解析为JSON对象 (默认为true)
 * @returns {Object|String} - 返回解密后的数据，如果parseJson为true且解密结果是有效JSON则返回对象
 */
function decryptAssignment(encryptedStr, key = '3a1727db02b7d65ba9eac1897538ef35', iv = '3a1727db02b7d65b', parseJson = true) {
    // 将密钥和IV转换为Utf8格式
    let keyUtf8 = CryptoJS.enc.Utf8.parse(key);
    let ivUtf8 = CryptoJS.enc.Utf8.parse(iv);

    // 执行AES解密
    let decrypted = CryptoJS.AES.decrypt(encryptedStr, keyUtf8, {
        mode: CryptoJS.mode.CBC, // 使用CBC模式
        padding: CryptoJS.pad.Pkcs7, // 使用Pkcs7填充
        iv: ivUtf8, // 初始化向量
    });

    // 将解密结果转换为UTF-8字符串
    let decryptedStr = decrypted.toString(CryptoJS.enc.Utf8);

    // 如果要求解析JSON并且解密结果是有效的JSON
    if (parseJson) {
        try {
            return JSON.parse(decryptedStr);
        } catch (e) {
            // 如果解析失败，则返回原始字符串
            return decryptedStr;
        }
    }

    return decryptedStr;
}

/**
 * 使用AES-CBC加密数据
 * @param {Object|String} data - 要加密的数据（如果是对象会自动转为JSON字符串）
 * @param {String} key - 密钥 (默认使用示例中的密钥)
 * @param {String} iv - 初始化向量 (默认使用示例中的初始化向量)
 * @returns {String} - 返回加密后的字符串
 */
function encryptCourse(data, key = '3a1727da4f17b0d17394ea7a3da8162f', iv = '3a1727da4f17b0d1') {
    // 如果data是对象，则转换为JSON字符串
    let dataStr = typeof data === 'object' ? JSON.stringify(data) : data;

    // 将密钥和IV转换为Utf8格式
    let keyUtf8 = CryptoJS.enc.Utf8.parse(key);
    let ivUtf8 = CryptoJS.enc.Utf8.parse(iv);

    // 执行AES加密
    let encrypted = CryptoJS.AES.encrypt(dataStr, keyUtf8, {
        mode: CryptoJS.mode.CBC, // 使用CBC模式
        padding: CryptoJS.pad.Pkcs7, // 使用Pkcs7填充
        iv: ivUtf8, // 初始化向量
    });

    // 返回加密后的字符串
    return encrypted.toString();
}

/**
 * 使用AES-CBC解密数据
 * @param {String} encryptedStr - 加密后的字符串
 * @param {String} key - 密钥 (默认使用示例中的密钥)
 * @param {String} iv - 初始化向量 (默认使用示例中的初始化向量)
 * @param {Boolean} parseJson - 是否将解密结果解析为JSON对象 (默认为true)
 * @returns {Object|String} - 返回解密后的数据，如果parseJson为true且解密结果是有效JSON则返回对象
 */
function decryptCourse(encryptedStr, key = '3a1727da4f17b0d17394ea7a3da8162f', iv = '3a1727da4f17b0d1', parseJson = true) {
    // 将密钥和IV转换为Utf8格式
    let keyUtf8 = CryptoJS.enc.Utf8.parse(key);
    let ivUtf8 = CryptoJS.enc.Utf8.parse(iv);

    // 执行AES解密
    let decrypted = CryptoJS.AES.decrypt(encryptedStr, keyUtf8, {
        mode: CryptoJS.mode.CBC, // 使用CBC模式
        padding: CryptoJS.pad.Pkcs7, // 使用Pkcs7填充
        iv: ivUtf8, // 初始化向量
    });

    // 将解密结果转换为UTF-8字符串
    let decryptedStr = decrypted.toString(CryptoJS.enc.Utf8);

    // 如果要求解析JSON并且解密结果是有效的JSON
    if (parseJson) {
        try {
            return JSON.parse(decryptedStr);
        } catch (e) {
            // 如果解析失败，则返回原始字符串
            return decryptedStr;
        }
    }

    return decryptedStr;
}

module.exports = {
    encryptAssignment,
    decryptAssignment,
    encryptCourse,
    decryptCourse,
};
