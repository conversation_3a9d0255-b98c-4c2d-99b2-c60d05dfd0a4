let extractCoursesFromPageInBrowserContext = require('../../utils/extractCoursesFromPageInBrowserContext.js');
let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api.js');

async function endModule(infoLogger, globalStore, mainPage) {
    let taskObj = globalStore.taskObj;
    await mainPage.goto('https://mooc2-ans.chaoxing.com/mooc2-ans/visit/interaction', {
        waitUntil: 'networkidle0',
    });

    let courseList = await mainPage.evaluate(extractCoursesFromPageInBrowserContext);

    // 过滤掉没有截至时间的课程
    courseList = courseList.filter(course => course.rawTimeInfo);
    // 过滤掉已结束的课程
    courseList = courseList.filter(course => !(course.statusText == '课程已结束'));

    // 过滤掉指定课程名称
    if (taskObj.coursename) {
        courseList = courseList.filter(course => course.courseName.includes(taskObj.coursename));
    }

    let cookieStr = await pageTools.getPageCookies(mainPage);
    for (let courseObj of courseList) {
        if (courseObj.progress == '未知') {
            let info = courseObj.infoAttribute;
            let res;
            try {
                res = await api.getCourseInfo(info, cookieStr);
                courseObj.progress = res.jobArray[0].jobRate;
            } catch (error) {
                courseObj.progress = '未知';
            }
        }
    }

    courseList = courseList.map(course => {
        return {
            courseName: course.courseName,
            progress: course.progress,
        };
    });

    return courseList;
}

module.exports = endModule;
