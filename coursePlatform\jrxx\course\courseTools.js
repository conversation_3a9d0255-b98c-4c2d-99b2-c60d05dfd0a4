let dayjs = require("dayjs");
let Model = require("../../../config/sequelize.config");

//生成一个记录信息日志的函数
exports.getInfoLogger = function (taskId, showLog = true) {
    return async function (message, color = "black") {
        let date = new Date();
        let time = dayjs(date).format("MM-DD HH:mm:ss");
        if (showLog) console.log(`[${time}]： ${message}`);
        let msgTag = `<p style="color:${color}">[${time}]：${message}</p>`;
        await Model.course_info_log.create({
            course_id: taskId,
            log_message: msgTag,
            log_time: date,
        });
    };
};

//获取页面cookie
exports.getPageCookies = async function getPageCookies(page) {
    // 获取所有的 cookies
    const cookies = await page.cookies();
    // 将 cookies 格式化为一个字符串
    return cookies.map((cookie) => `${cookie.name}=${cookie.value}`).join("; ");
};

//转换时间 00:48:59=》秒
exports.timeToSeconds=function timeToSeconds(time) {
    const [hours, minutes, seconds] = time.split(":").map(Number);
    return hours * 3600 + minutes * 60 + seconds;
}
