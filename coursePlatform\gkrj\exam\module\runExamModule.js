let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api.js');

/**
 * 生成指定范围内的随机整数（包含 min 和 max）
 * @param {number} min 最小值（包含）
 * @param {number} max 最大值（包含）
 * @returns {number} 随机整数
 */
function getRandomInt(min, max) {
    min = Math.ceil(min); // 向上取整
    max = Math.floor(max); // 向下取整
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

async function runExamModule(infoLogger, mainPage, globalStore, taskObj, courseList) {
    let cookieStr = await pageTools.getPageCookies(mainPage);

    // 最终结果
    let courseListRes = await api.getScore(cookieStr, taskObj.username);
    let passCourseList = courseListRes.data.records;
    let passCourseIdArr = passCourseList.map(item => item.examCode);

    for (let i = 0; i < courseList.length; i++) {
        let courseObj = courseList[i];
        let examLogStr = `[课程${i + 1}/${courseList.length}]`;

        // if (passCourseIdArr.includes(courseObj.examCode)) {
        //     let finalObj = passCourseList.find(item => item.examCode == courseObj.examCode);
        //     await infoLogger(`${examLogStr}-${courseObj.description} 已经通过考试，${finalObj.score}`);
        //     continue;
     
        // }

        // 重新获取课程对象
        courseObjRes = await api.getCourseInfo(courseObj.examCode, cookieStr);
        courseObj = courseObjRes.data;

        await infoLogger(`${examLogStr} 开始考试：${courseObj.description}`);

        // 试卷编号
        // let num = Math.round(Math.random() * (courseObj.numPart - 1));

        // 获取试题
        // let questionList = await api.getQuestionList(taskObj, username, cookieStr);

        let score = getRandomInt(81, 95);

        
        // 产生一个30-60的随机值
        let randomValue = Math.floor(Math.random() * (60 - 31 + 1)) + 31;

        for(let i=0;i<randomValue;i++){
            await infoLogger(`等待1分钟，剩余${randomValue-i}分钟后交卷`, 'red');
            await new Promise(r => setTimeout(r, 1000));
        }

        // 提交答案
        let scoreRes = await api.submitScore(courseObj, taskObj.username, cookieStr, score);

        if (scoreRes.code == 200) {
            await infoLogger(`${examLogStr}-${courseObj.description}最终分数：${score}分`, 'green');
        } else {
            await infoLogger(`${examLogStr}-${courseObj.description} 试卷提交失败`, 'red');
        }

        
    }
}

module.exports = runExamModule;
