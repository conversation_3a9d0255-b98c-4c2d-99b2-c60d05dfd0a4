let pageTools = require('../../../../utils/pageTools.js');
let api = require('../../../utils/api.js');

// 组件
let doExam = require('./doExam.js');
let collectExam = require('./collectExam.js');

async function examTask(infoLogger, mainPage, globalStore, courseObj, examObj, taskLogStr) {
    // 调试用，只针对某一个课件
    // if (examId != 10000407553) return;
    let examLogStr = `${taskLogStr}-${examObj.title}-${examObj.type}`;

    if (!globalStore.others.includes('收集课程')) {
        // 是否合格
        if (examObj.activity_final_score && examObj.activity_final_score >= 85) {
            await infoLogger(`${examLogStr}  已经完成，分数： ${examObj.activity_final_score}，跳过`);
            return;
        }
        // 是否超过截至日期
        if (examObj.end_time) {
            // 定义 end_time 字符串
            let end_time = examObj.end_time;

            // 将 end_time 转换为 Date 对象 (UTC 时间)
            let endTimeDate = new Date(end_time);

            // 获取当前的 UTC 时间
            let nowUtc = new Date();

            // 将 UTC 时间转换为 UTC+8 (东八区) 时间
            let offsetMilliseconds = 8 * 60 * 60 * 1000; // UTC+8 的时差 (8小时的毫秒数)
            let nowLocal = new Date(nowUtc.getTime() + offsetMilliseconds);
            let endTimeLocal = new Date(endTimeDate.getTime() + offsetMilliseconds);

            if (nowLocal > endTimeLocal) {
                await infoLogger(`${examLogStr}  测试已截止，截至日期:${examObj.end_timel}`, 'red');
                return;
            }
        }
    }

    let cookieStr = await pageTools.getPageCookies(mainPage);
    let examId = examObj.id;

    async function start() {
        // 已经提交列表
        let submissionRes = await api.getSubmissions(examObj.id, cookieStr);
        let submissionList = submissionRes.submissions;

        let scoreId;
        if (submissionList.length > 0) {
            scoreId = submissionList[submissionList.length - 1].id;
        }

        if (globalStore.others.includes('收集课程')) {
            if (submissionList.length == 0) {
                await infoLogger(`${examLogStr}，当前作业没有完成，无法收集`, 'red');
                return;
            }
            if (submissionRes.exam_score && submissionRes.exam_score < 70) {
                await infoLogger(`${examLogStr}，当前作业分数过低，无法收集,分数：${submissionRes.exam_score}`, 'red');
                return;
            }
            await collectExam(infoLogger, mainPage, courseObj.name, examId, scoreId, examLogStr, '收集我的答案');
            return;
        }

        // 重新获取考试对象 多了has_temporary_submission属性submitted_times属性，少了activity_final_score属性
        examObj = await api.getExamObj(examObj.id, cookieStr);

        // 递归终止条件 作业完成，分数够
        if (submissionRes.exam_score > 80) {
            await infoLogger(`${examLogStr}，分数及格：${submissionRes.exam_score}`, 'green');
            return;
        }

        // 归终止条件 作业完成，未批阅
        if (examObj.submitted_times > 0 && submissionRes.exam_score == undefined) {
            await infoLogger(`${examLogStr}  已完成，未批阅跳过`);
            return;
        }

        // 归终止条件 剩余次数不足
        if (examObj.submit_times * 1 - examObj.submitted_times * 1 <= 1) {
            await infoLogger(`${examLogStr}  剩余次数不足，结束，最终分数：${submissionRes.exam_score}`, 'red');
            return;
        }

        // 第一次开始作业
        if (examObj.submitted_times == 0) {
            await infoLogger(`${examLogStr} 作业之前没做过，开始做题`);
            await doExam(infoLogger, mainPage, courseObj, examObj, examLogStr);
            return;
        }

        // // 作业完成一半
        if (examObj.has_temporary_submission) {
            await infoLogger(`${examLogStr} 当前状态为：正在答题，开始继续答题`);
            await doExam(infoLogger, mainPage, courseObj, examObj, examLogStr);
            return;
        }

        // 作业完成，分数不够
        if (examObj.submitted_times > 0 && submissionRes.exam_score < 80) {
            await infoLogger(`${examLogStr}，作业已提交，分数没有达到标准：${submissionRes.exam_score}，开始重新答题`);
            await collectExam(infoLogger, mainPage, courseObj.name, examId, scoreId, examLogStr, '收集正确答案');
            await doExam(infoLogger, mainPage, courseObj, examObj, examLogStr);
            return;
        }
    }

    await start();

    await infoLogger('测试已完成');
    // await new Promise(r => setTimeout(r, 60 * 1000));
}

module.exports = examTask;
