let api = require("../../../../utils/api.js");
async function virtualtestnewTask(globalStore, courseObj, cellObj, courseLength, cellLength, infoLogger, i, j) {
    await infoLogger(`课程${i + 1}/${courseLength}]-章节[${j + 1}/${cellLength}]：开始virtualtestnew任务 《${cellObj.title}》`);



    let finishRes=await api.finishChapter(globalStore, courseObj, cellObj);
    await infoLogger(`课程${i + 1}/${courseLength}]-章节[${j + 1}/${cellLength}]：virtualtestnew任务状态改为已完成 《${cellObj.title}》`);
}

module.exports = virtualtestnewTask;