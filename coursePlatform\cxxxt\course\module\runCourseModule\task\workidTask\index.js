// 可以用PC界面做完成作业，也可以用手机端界面完成作业，这里选择的PC界面

// let doPhoneWork = require("./doPhoneWork.js");
let upPcWorkId = require('./upPcWorkId.js');
let doPcWorkId = require('./doPcWorkId.js');
let pageTools = require('../../../../../../utils/pageTools.js');
async function workidTask(mainPage, courseStore, infoLogger, attachment, defaults, attachLogStr, chapterCookieStr,chapterId) {
    //获取woikid
    let workid;
    try {
        workid = attachment.jobid.replace('work-', '');
    } catch {}
    if (!workid) {
        try {
            workid = attachment.property.workid;
        } catch {}
    }
    if (!workid) {
        try {
            workid = attachment.property._jobid.replace('work-', '');
        } catch {}
    }

    //获取jobid
    let jobid;
    try {
        jobid = attachment.jobid;
    } catch {}
    if (!jobid) {
        try {
            jobid = attachment.property._jobid;
        } catch {}
    }
    if (!jobid) {
        try {
            jobid = 'work-' + attachment.property.workid;
        } catch {}
    }

    // 拼接workid页面url，会跳转到新页面
    let workidPath = 'https://mooc1.chaoxing.com/mooc-ans/api/work';
    let workidParams = {
        // mArg.defaults;
        cpi: defaults.cpi,
        clazzId: defaults.clazzId,
        courseid: defaults.courseid,
        knowledgeid: defaults.knowledgeid,
        ktoken: defaults.ktoken,

        // mArg.attachements[i]
        enc: attachment.enc,
        jobid: jobid,
        workId: workid,

        // others
        mooc2: '1',
        needRedirect: 'true',
        skipHeader: 'true',
        type: '',
        ut: 's',
        // utenc: "undefined",
        api: '1',
    };
    let workidUrl = pageTools.buildUrl(workidPath, workidParams);

    //进入work页面
    await mainPage.goto(workidUrl, {
        waitUntil: 'networkidle0',
    });
    await new Promise(r => setTimeout(r, 2000));

    //从work页面获取当前work的状态workStatus
    let workStatus;
    try {
        workStatus = await mainPage.evaluate(() => {
            return document.querySelector('#RightCon > div.newTestTitle > div.fr.testTit_status').innerText;
        });
    } catch (error) {
        await infoLogger(`${attachLogStr} 获取测试状态出错：${attachment.property.title},${workidUrl}`, 'red');
        return;
    }

    switch (workStatus) {
        case '已完成': {
            await infoLogger(`${attachLogStr}当前测试已完成，跳过：${attachment.property.title}`);
            break;
        }

        case '待做':
        case '待完成': {
            await infoLogger(`${attachLogStr}当前测试未完成（${workStatus}），开始答题：${attachment.property.title}`);
            //答题
            let submitRes = await doPcWorkId(mainPage, courseStore, infoLogger, attachment, attachLogStr,chapterId);
            // // 刷新页面
            // await mainPage.reload({
            //     waitUntil: 'networkidle0',
            // });

            // // 等待三秒
            // await new Promise(r => setTimeout(r, 3000));
            // //收集题目
            // await upLoadWorkId(mainPage, courseStore, infoLogger, attachment, attachLogStr, mArgJson);
            break;
        }
        default:
            await infoLogger(`[${attachLogStr}] 未知的workid状态：${workStatus}`, 'red');
            break;
    }
}

module.exports = workidTask;
