const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('bank_bak', {
    id: {
      type: DataTypes.STRING(255),
      allowNull: false,
      primaryKey: true
    },
    content: {
      type: DataTypes.STRING(3000),
      allowNull: true,
      comment: "问题内容 组合唯一键"
    },
    options: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: "答案选项"
    },
    type: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "问题类型 1单选"
    },
    answers: {
      type: DataTypes.STRING(5000),
      allowNull: true,
      comment: "答案"
    },
    platform: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "平台 组合唯一键"
    },
    comment: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "备注"
    },
    courseid: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "课程id 组合唯一键 "
    },
    coursename: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "课程名称"
    }
  }, {
    sequelize,
    tableName: 'bank_bak',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "content",
        type: "FULLTEXT",
        fields: [
          { name: "content" },
        ]
      },
    ]
  });
};
