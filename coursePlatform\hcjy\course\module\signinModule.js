let handleSlideValidate = require('../../utils/handleSlideValidate');
let api = require('../../utils/api');
let pageTools = require('../../../utils/pageTools.js');

async function signinModule(mainPage, globalStore, infoLogger, taskObj) {
    //1.跳转到登录页面
    await mainPage.goto(taskObj.schoolurl, { waitUntil: 'networkidle0' });
    await new Promise(r => setTimeout(r, 3000));

    //2.输入用户信息
    await infoLogger(`开始登录`); //记录日志
    await mainPage.type('#login-form > form > div.form-content > div.form-box.form-box-plusApp > div.logo-inputs > div:nth-child(1) > input', taskObj.username); //输入用户名
    await mainPage.type('#login-form > form > div.form-content > div.form-box.form-box-plusApp > div.logo-inputs > div:nth-child(2) > input', taskObj.password); //输入密码

    //处理验证码
    let imgSelect = '#login-form > form > div.form-content > div.form-box.form-box-plusApp > div.logo-inputs > div.clear-after.form-w > img';
    let validateDivHandle = await mainPage.$(imgSelect);
    let base64Image = await validateDivHandle.screenshot({ type: 'jpeg', encoding: 'base64' });

    let code;
    try {
        let validateCodeRes = await api.myValidateCode(base64Image);
        code = validateCodeRes.result;
    } catch (error) {
        await infoLogger(`验证码识别失败，进行第三方API识别`, 'red');
        let validateCodeRes = await api.slideValidate(base64Image);
        code = validateCodeRes.pic_str;
    }

    //输入验证码
    await mainPage.type('#login-form > form > div.form-content > div.form-box.form-box-plusApp > div.logo-inputs > div.clear-after.form-w > div > input', code);
    await new Promise(resolve => setTimeout(resolve, 1000)); //等待加载完成

    //4.点击登录
    let signinHandle = await mainPage.$('#login-form > form > div.form-content > div.form-box.form-box-plusApp > div.login-btn > input');
    await signinHandle.click(); //点击登录

    //5.处理滑动验证（可能）
    // 如果用的是外网的IP，点击登录之后，有可能会出现滑动验证  用的是tianai-captcha 这个验证模块
    let needSlideValidate = false;
    try {
        await mainPage.waitForSelector('#tianai-captcha-slider-move-track-font', {
            timeout: 3000,
        });
        await infoLogger(`需要滑动验证`);
        needSlideValidate = true;
    } catch (error) {}

    if (needSlideValidate) {
        try {
            await handleSlideValidate(mainPage, infoLogger);
            await infoLogger(`滑动验证成功`, 'green'); //记录日志
        } catch (error) {
            await infoLogger(`滑动验证失败`, 'red'); //记录日志
            throw new Error('滑动验证失败');
        }
    }

    await new Promise(r => setTimeout(r, 5000));

    // await mainPage.evaluate(() => {
    //     location.pathname = '/student/';
    // });

    //6.判断是否登录成功
    let currentUrl = mainPage.url();
    if (currentUrl.includes('/student/')) {
        await infoLogger(`登录成功`, 'green'); //记录日志
        globalStore.cookieStr = await pageTools.getPageCookies(mainPage);
        globalStore.mainUrl = await mainPage.url();
    } else {
        // console.log(currentUrl)
        await infoLogger('登录失败', 'red');
        throw Error('登录失败');
    }
}

module.exports = signinModule;
