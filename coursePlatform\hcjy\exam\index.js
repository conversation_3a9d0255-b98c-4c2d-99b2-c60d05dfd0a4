// ========= 公共模块 =========
let pageTools = require('../../utils/pageTools.js');
let getMainPage = require('../../utils/getMainPage.js');

// ========= 主体模块 =========
let singinModule = require('../course/module/signinModule.js');
let getCourseListModule = require('./module/getCourseListModule.js');
let runCourseModule = require('./module/runCourseModule.js');
let endModule = require('./module/endModule.js');

async function hcjyExam(taskObj, taskOptions) {
    //强制打开窗口
    // taskOptions.isHeadless = false;

    // 不使用代理
    taskOptions.isProxy = false;

    let infoLogger = pageTools.getExamInfoLogger(taskObj.id, false);
    //最终要返回的值
    let globalStore = {
        cookieStr: '', //页面cookie
        needSlideValidate: false, //是否需要滑动验证
        mainUrl: '', //主页url
        others:taskObj.others
    };

    let { mainPage, browser } = await getMainPage(taskOptions);

    try {
        if (!process.env.HAS_PERMISSION) {
            return;
        }

        // 一、登录
        await singinModule(mainPage, globalStore, infoLogger, taskObj);

        // 二、获取课程列表
        let courseList = await getCourseListModule(globalStore, infoLogger, taskObj);

        // 三、开始考试
        await runCourseModule(infoLogger, mainPage, courseList, globalStore, taskObj);

        // 四、收尾工作
        let finalResult = await endModule(mainPage, globalStore);
        await browser.close();
        return { finalResult, warningMessage: '' };
    } catch (error) {
        await browser.close();
        browser = null;
        throw error;
    } finally {
        if (browser) {
            await browser.close();
            browser = null;
        }
    }
}

module.exports = hcjyExam;

// //测试用
// let taskOptions = {
//     isHeadless: false, //浏览器模式 false ,'new'
// };

// let taskObj = {
//     username: "2024800047",
//     password: "100689",
//     runCourse: true, //网课
//     runAssignment: true, //作业
//     schoolurl: "https://nwujxjy.sccchina.net/",
//     schoolname: "西北大学",
//     platform:'hcjy'
// };

// hcjyExam(taskObj, taskOptions).then(
//     (val) => console.log(val),
//     (err) => {
//         console.log(err);
//     }
// );
