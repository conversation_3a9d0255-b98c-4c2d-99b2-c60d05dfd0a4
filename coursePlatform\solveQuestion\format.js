let aiChat = require('./aiChat.js');
//'0123'转为'ABCDE'
exports.numbersToLetters = function numbersToLetters(numberStr) {
    numberStr += '';
    let letters = '';
    for (let i = 0; i < numberStr.length; i++) {
        let num = parseInt(numberStr[i]);
        if (!isNaN(num)) {
            letters += String.fromCharCode(num + 17 + 48); // 加上 17 和 48 来得到对应字母的编码
        }
    }
    return letters;
};

//'ABCDE'转为'01234'
exports.lettersToNumbers = function lettersToNumbers(letters) {
    letters = letters.toUpperCase();
    letters = letters + '';
    let res = '';
    for (let letter of letters) {
        // 获取字符的 Unicode 编码
        let charCode = letter.charCodeAt(0);
        res += charCode - 65 + '';
    }
    return res;
};

// <img src="http://example.com/image1.png"> 和 <img src="https://example.com/image2.jpg">
// ->这是一段包含图片的文本：example.com/image1.png 和 example.com/image2.jpg
exports.handleImgs = function handleImgs(s) {
    // 匹配所有 img 标签，捕获 src 属性的 URL
    let imgEs = s.match(/<img\s+[^>]*src\s*=\s*['"]([^'"]+)['"][^>]*>/gi);

    if (imgEs) {
        for (let j = 0; j < imgEs.length; j++) {
            // 匹配 img 标签中的 src 属性
            let urlMatch = imgEs[j].match(/src\s*=\s*['"]([^'"]+)['"]/i);
            if (urlMatch && urlMatch[1]) {
                let url = urlMatch[1]; // 获取完整 URL
                // 用 [URL] 格式替换 img 标签为 URL
                s = s.replace(imgEs[j], `[URL]${url}[/URL]`);
            }
        }
    }

    return s;
};

// 验证答案格式
exports.validateAnswer = function validateAnswer(answer, questionType) {
    if (typeof answer !== 'string') {
        return false;
    }
    // 去除答案首尾的空格
    answer = answer.trim();

    // 对答案进行简单的格式验证
    switch (
        questionType // 更正：使用参数 questionType
    ) {
        case '判断题':
        case '单选题':
            // 验证答案是否是单个大写字母 (例如 'A', 'B')
            if (typeof answer !== 'string' || !/^[A-Z]$/.test(answer)) {
                // 更正：使用参数 answer
                return null; // 无效则返回 null
            }
            break;
        case '多选题':
            // 验证答案是否是指定数量的大写字母 (例如 'AB', 'ACD')
            // 注意: 这个正则表达式 ^[A-Z]{2,}$ 要求字母是连续的，并且至少两个。
            if (typeof answer !== 'string' || !/^[A-Z]{1,}$/.test(answer)) {
                // 更正：使用参数 answer
                return null; // 无效则返回 null
            }
            break;
        case '简答题':
            // 验证答案是否是非空的描述性文字
            // 对于简答题，通常格式要求较低，主要确保非空。
            if (typeof answer !== 'string' || answer.trim() === '') {
                // 更正：使用参数 answer
                return null; // 无效则返回 null
            }
            break;
        case '填空题':
            // 填空题的答案格式要求：
            // 1. 答案是一个字符串。
            // 2. 可能有多个空，每个空的答案用 "|" 字符连接。
            // 3. 每个空的答案内容（去除前后空格后）长度不超过20个字符。

            if (typeof answer !== 'string') {
                return null; // 如果答案不是字符串，则无效
            }

            // 按 "|" 分割答案字符串为多个部分
            let parts = answer.split('|'); // 使用 let 遵守"尽量不用const"的指示

            for (let i = 0; i < parts.length; i++) {
                let part = parts[i];
                let partContent = part.trim(); // 获取每个部分的内容并去除前后空格

                // 检查去除空格后的内容长度
                if (partContent === '' || partContent.length > 20) {
                    return null; // 如果任何一部分的长度超过20字符，则整个答案无效
                }
                // 注意：此逻辑允许空的填空答案，例如 "答案1||答案3" 中的第二个空。
                // "" (空字符串) .trim().length 是 0, 0 <= 20，所以是有效的。
                // 如果要求每个空必须填写内容（不允许空字符串），则需要额外检查 partContent === '' (例如 if (partContent === '' || partContent.length > 20))
            }
            // 如果所有部分都符合长度要求
            break;
        default:
            return null;
            break;
    }
    return true;
};

// AI格式化答案格式
exports.formatAnswer = async function formatAnswer(AIResult, questionType) {
    let AIModel = 'deepseek-chat';
    let answers;

    let promptStr = `
    以下是AI回复的一段内容，我需要你从中提取问题的答案，
        ${AIResult}

    回答格式：
        - 判断题：对就回答A，错就回答B。
        - 单选题：只回答一个大写字母，如"A"
        - 多选题：回答多个大写字母，如"BCD"
        - 填空题：多个答案用"|"分隔，如"答案1|答案2"
        - 简答题：给出简洁的描述性回答

    当前题目的类型是：${questionObj.type}，请直接给出答案不要包含任何解释、分析或多余文字，如果无法提取答案，请回答'false'
    `;

    answers = await aiChat(deepSeekModel, promptStr);
    if (answers != 'false') return answers;
};

// 获取选择题的答案内容
exports.getSelectAnswerContent = function (answers, options) {
    let answersContent = [];
    // 遍历答案，A,B,C
    for (let str of answers) {
        // 把A变成0
        let ansIndex = exports.lettersToNumbers(str);
        // 获取对应选项的具体内容 [A:恢复中华,B:驱除鞑虏]=》A:恢复中华
        let queOption = options[ansIndex];
        // 去掉编号 A:恢复中华=》恢复中华
        let [queIndex, queValue] = queOption.split(/:(.+)/);
        // 把内容放到最终数组
        answersContent.push(queValue);
    }
    // 对最终数组进行排序
    answersContent = [...answersContent].sort((x, y) => x.localeCompare(y, 'zh-Hans-CN'));
    // 转成JSON字符串
    let answersContentStr = JSON.stringify(answersContent);
    return answersContentStr;
};
