let api = require('../../utils/api');
let pageTools = require('../../../utils/pageTools.js');
let Model = require('../../../../config/sequelize.config.js');

// 从"【专升本-工商管理】"中提取层次和专业
function extractInTwoSteps(text) {
    // 步骤1：提取【】内的内容
    const bracketContent = text.match(/【(.+)】/)[1];

    // 步骤2：从提取的内容中分离层次和专业
    const parts = bracketContent.split('-');
    const educationLevel = parts[0];
    const major = parts[1];

    return { educationLevel, major };
}

async function getCourseListModule(infoLogger, mainPage, globalStore) {
    let taskObj = globalStore.taskObj;

    // 进入课程界面 https://menhu.pt.ouchn.cn/site/ouchnPc/index
    // let courseUrl = 'https://menhu.pt.ouchn.cn/site/ouchnPc/index';
    // await pageTools.gotoWithRetry(mainPage, taskObj.schoolurl, { waitUntil: 'networkidle0', timeout: 50 * 1000 }, 3, infoLogger);
    // let cookieStr = await pageTools.getPageCookies(mainPage);

    let cookieStr = await pageTools.getPageCookies(mainPage);
    // 未完成的tab是'termnow'，已完成的tab是'learned'
    let tab = 'termnow';
    if (globalStore.others.includes('收集课程')) tab = 'learned';
    let courseListRes = await api.getCourseList(cookieStr, tab);
    let courseList = courseListRes.d.list;

    // 课程名称过滤
    if (taskObj.coursename) {
        courseList = courseList.filter(courseObj => courseObj.name.includes(taskObj.coursename));
    }

    // 确保课程数量不为零
    if (courseList.length == 0) {
        return [];
    }

    await infoLogger(`获取课程列表成功，共${courseList.length}门课程`, 'green');

    globalStore.courseList = courseList;

    // 获取专业major 层次layer
    let multiInfoRes = await api.getMultiInfo(cookieStr);
    globalStore.major = multiInfoRes.d.identitys[0].sszy;
    globalStore.layer = multiInfoRes.d.identitys[0].sflx;

    // 获取urserId（大作业能用到）
    let upFileUrl = 'https://lms.ouchn.cn/user/index';
    // await mainPage.goto(upFileUrl, { waituntil: 'networkidle0' });
    await mainPage.goto(upFileUrl, { waitUntil: 'networkidle0', timeout: 90 * 1000 });
    await new Promise(r => setTimeout(r, 3000));

    let userId;
    try {
        userId = await mainPage.evaluate(() => userId.getAttribute('value'));
    } catch (error) {
        await infoLogger('ip被拦截，请稍后再试', 'red');
        throw error;
    }

    return courseList;
}

module.exports = getCourseListModule;
