let api = require('../../utils/api');
let pageTools = require('../../../utils/pageTools.js');

async function runCourseModule(mainPage, globalStore,infoLogger) {
    let { courseList,desktopInfoRes,schoolName } = globalStore;

    for (let i = 0; i < courseList.length; i++) {
        // 取出一个课程对象
        let courseObj = courseList[i];
        let courseLogStr = `课程[${i + 1}/${courseList.length}]`;
        await infoLogger(`${courseLogStr}：开始课程《${courseObj.courseName}》的网课`, 'blue');

        //跳转到课程页面，获取课程页面的cookie
        // await mainPage.goto(courseObj.filePath, { waitUntil: 'networkidle0' });
        if(!courseObj.filePath){
            await infoLogger(`${courseLogStr} ${courseObj.courseName}：课程链接为空，跳过`, 'red');
            continue
        }
        await pageTools.gotoWithRetry(mainPage, courseObj.filePath, { waitUntil: 'networkidle0' },3,infoLogger);
        let coursePageCookieStr = await pageTools.getPageCookies(mainPage);
        globalStore.coursePageCookieStr = coursePageCookieStr;


        //从服务器获取视频章节课件列表
        let CourseScormItemListRes = await api.getCourseScormItemList({ course_id: courseObj.courseId }, coursePageCookieStr);
        if (CourseScormItemListRes.code != 1000) {
            await infoLogger(`${courseLogStr} ${courseObj.courseName}：获取课程列表失败,${JSON.stringify(CourseScormItemListRes)}`, 'red');
            return;
        }
        let cellList = CourseScormItemListRes.debugData.listCourseLesson.filter(item => item.lessonType == 'scorm_content');
        await infoLogger(`${courseLogStr} ${courseObj.courseName}：获取课件列表成功，共有${cellList.length}个视频课件`);

        //2.遍历课件列表
        for (let j = 0; j < cellList.length; j++) {
            //取出一个课件对象
            let cellObj = cellList[j];
            let cellLogStr = `${courseLogStr}-课件[${j + 1}/${cellList.length}]`;

            //跳过已完成的课件
            if (cellObj.isFinish) {
                await infoLogger(`${cellLogStr}，已完成，无需重复。${cellObj.lessonName}`);
                continue;
            }

            //修改视频进度
            let submitScormAndHistorySaveData = {
                user_id: desktopInfoRes.debugData.learningUserId, //'_openlearning_1345617',
                learning_user_id: desktopInfoRes.debugData.studentId, //'69225',
                course_id: courseObj.courseId, //'350189655970283521',
                item_id: cellObj.lessonId, //'350189655970283524',
                last_view_time: cellObj.finishLen, //'1815',
                time: cellObj.timeLen, //'0',
                view_time: cellObj.timeLen, //'1815',
                video_length: cellObj.timeLen, //'1815',
            };
            let submitScormAndHistorySaveRes = await api.submitScormAndHistorySave(submitScormAndHistorySaveData, coursePageCookieStr);
            await new Promise(r => setTimeout(r, 1000));
            if (submitScormAndHistorySaveRes.code == 1000) {
                await infoLogger(`${cellLogStr}，视频已完成。${cellObj.lessonName}`, 'green');
            } else {
                await infoLogger(`${cellLogStr}，修改进度失败。${cellObj.lessonName}`, 'red');
            }
        }
    }
}

module.exports = runCourseModule;