let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api');

async function pageTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr) {
    // await infoLogger(`${taskLogStr}<${taskObj.title}> ${taskObj.type} 课件开始`);
    let cookieStr = await pageTools.getPageCookies(mainPage);
    let activitesRes = await api.updateDuration(taskObj.id, cookieStr, '{}');
    if (activitesRes.completeness == 'full') {
        await infoLogger(`${taskLogStr}<${taskObj.title}> ${taskObj.type} 已经完成，10秒后进行下一个课件`, 'green');
    }

    await new Promise(r => setTimeout(r, 10 * 1000));
}

module.exports = pageTask;