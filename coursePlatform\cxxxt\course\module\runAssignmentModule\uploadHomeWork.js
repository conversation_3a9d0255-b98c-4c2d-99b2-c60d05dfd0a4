let Model = require("../../../config/sequelize.config");
let axios = require("../utils/axios");
let { v4 } = require("uuid");
async function uploadHomeWork(mainPage, infoLogger, courseStore, homeWorkObj, k, i, totalCourse, homeWorkList) {
    //1.进入到答题页面
    await mainPage.goto(homeWorkObj.url, { waitUntil: "networkidle0" });

    //2.获取题目列表
    let questionList = await mainPage.evaluate((courseid) => {
        
        function handleImgs(s) {
            let imgEs = s.match(/(<img([^>]*)>)/gi);
            if (imgEs) {
                for (let j = 0, k = imgEs.length; j < k; j++) {
                    let urls = imgEs[j].match(/http[s]?:\/\/(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+/),
                        url;
                    if (urls) {
                        url = urls[0].replace(/http[s]?:\/\//, "");
                        s = s.replaceAll(imgEs[j], url);
                    }
                }
            }
            return s;
        }

        function trim(s) {
            return (
                s
                    //删除多余字符串
                    .replace(/(<([^>]+)>)/gi, "") //去掉所有的html标记 <a>xx<a>
                    .replace(/^\d+[\.、]/, "") //删除所有1.、1、的内容
                    .replace(/\(.{3}\)/, "") //删除所有（xx题）的内容
                    .replaceAll("&nbsp;", "")
                    .replace(/\s{2,}/g, " ") //删除两个及以上的空格，保留单个空格
                    .trim()
            );
        }

        function tidyQuestion(s) {
            if (s) {
                let str = s
                    .replace(/<(?!img).*?>/g, "")
                    .replace(/^【.*?】\s*/, "")
                    .replace(/\s*（\d+\.\d+分）$/, "")
                    .replace(/^\d+[\.、]/, "")
                    .trim()
                    .replace(/&nbsp;/g, "")
                    .replace("javascript:void(0);", "")
                    .replace(new RegExp("&nbsp;", "gm"), "")
                    .replace(/^\s+/, "")
                    .replace(/\s+$/, "");
                return str;
            } else {
                return null;
            }
        }

        //生成一个唯一id
        function generateUniqueId() {
            return "id-" + new Date().getTime().toString(36) + "-" + Math.random().toString(36).substr(2, 9);
        }

        let questionList = [];
        let TimuList = document.querySelectorAll(".mark_table .mark_item .questionLi");
        for (let i = 0; i < TimuList.length; i++) {
            let timuDom = TimuList[i];
            let questionFull = timuDom.querySelector("h3.mark_name").innerHTML;
            questionFull = tidyQuestion(questionFull);
            // questionFull='2. <span class="colorShallow">(单选题)</span>小李从今年开始每年年末存入银行一笔固定的存款以备将来养老使用，如果按复利计算第n年年末可以从银行取出的本利和，应该使用的时间价值系数是( &nbsp;)'

            //题目类型
            let type = questionFull.match(/[(](.*?)[)]|$/)[1].replace(/, .*?分/, "");

            //题目内容
            let content = trim(handleImgs(questionFull));

            //题目答案
            let answers;

            try {
                answers = timuDom.querySelectorAll("div.mark_key.clearfix span.colorGreen")[0].innerText;
                answers = answers.slice(5).trim();
            } catch (error) {
                return [];
            }

            //题目选项
            let options = [];
            switch (type) {
                case "单选题":
                case "多选题":
                    let optionList = timuDom.querySelectorAll("ul li");
                    for (let j = 0; j < optionList.length; j++) {
                        let optionContent = optionList[j].innerText;
                        optionContent = trim(optionContent); //'A.1100.5'
                        optionContent = optionContent.replace(/^([A-F])\./, "$1:");
                        options.push(optionContent);
                    }
                    break;
                case "判断题":
                    options = ["A:对", "B:错"];
                    break;
                default:
                    options = [];
                    break;
            }

            //收集题目对象
            questionList.push({
                id: generateUniqueId(), //id-ltf3k9bz-qnqw2bq3qanswer401584769
                content: content,
                type: type,
                options: options,
                platform: "超星学习通",
                courseid: courseId,
                answers: answers,
            });
        }
        return questionList;
    }, courseStore.courseId);

    let upCount = 0;
    //上传答案
    for (let i = 0; i < questionList.length; i++) {
        let questionObj = questionList[i];
        let defaultObj = {
            id: v4(),
            content: questionObj.content,
            options: questionObj.options,
            type: questionObj.type,
            platform: questionObj.platform,
            answers: questionObj.answers,
            courseid: questionObj.courseid,
        };
        let [instance, created] = await Model.bank.findOrCreate({
            where: {
                content: questionObj.content,
                type: questionObj.type,
                platform: questionObj.platform,
            },
            defaults: defaultObj,
        });
        if (created) {
            upCount++;
        }
    }
    await infoLogger(`课程[${i + 1}/${totalCourse}]-作业[${k + 1}/${homeWorkList.length}]：${homeWorkObj.name}，共收集${upCount}题`);
}

module.exports = uploadHomeWork;
