let Model = require('../../../../../config/sequelize.config.js');
let { v4 } = require('uuid');

/**
 * 从给定的 URL 中提取虚拟实验和用户相关的特定参数。
 *
 * @example
 * const url = "https://lms.ouchn.cn/external-api/v2/virtual-experiment/70007097540/user/70000783206/score/callback?app_key=u1sSAMNPR5G45uDT5Sm2eA==&ts=1730772071&token=39a047";
 * const params = extractParams(url);
 * console.log(params);
 * // 输出:
 * // {
 * //   experiment: '70007097540',
 * //   user: '70000783206',
 * //   app_key: 'u1sSAMNPR5G45uDT5Sm2eA==',
 * //   ts: '1730772071',
 * //   token: '39a0477537a5ba09f934'
 * // }
 */
function extractParams(url) {
    // 使用 URL 模块解析 URL
    let urlObj = new URL(url);

    // 初始化一个空对象来存储参数
    let params = {};

    // 提取路径中的 experiment 和 user 值
    let experimentMatch = urlObj.pathname.match(/virtual-experiment\/(\d+)\//);
    let userMatch = urlObj.pathname.match(/user\/(\d+)\//);

    if (experimentMatch) {
        params.experiment = experimentMatch[1];
    }
    if (userMatch) {
        params.user = userMatch[1];
    }

    // 遍历 searchParams，将键值对添加到 params 对象中
    urlObj.searchParams.forEach((value, key) => {
        params[key] = value;
    });

    return params;
}

let url = 'https://lms.ouchn.cn/external-api/v2/virtual-experiment/70007097985/user/70000783206/score/callback?app_key=u1sSAMNPR5G45uDT5Sm2eA==&ts=1730786415&token=79a2e7591d320f5762e0'

let body ={
    total_score: 0,
    start_time: 1730786368981,
    end_time: 1730786415967,
    steps: [
        { number: 1, title: '安装步骤1', score: 0, total_score: 2, recommended_time: 600, spent_time: 46.816, is_pass: false },
        { number: 2, title: '安装步骤2', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 3, title: '安装步骤3', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 4, title: '安装步骤4', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 5, title: '安装步骤5', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 6, title: '气动步骤1', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 7, title: '气动步骤2', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 8, title: '气动步骤3', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 9, title: '气动步骤4', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 10, title: '气动步骤5', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 11, title: '气动步骤6', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 12, title: '气动步骤7', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 13, title: '气动步骤8', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 14, title: '气动步骤9', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 15, title: '气动步骤10', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 16, title: '气动步骤11', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 17, title: '气动步骤12', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 18, title: '气动步骤13', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 19, title: '气动步骤14', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 20, title: '气动步骤15', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 21, title: '气动步骤16', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 22, title: '气动步骤17', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 23, title: '气动步骤18', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 24, title: '电气步骤1', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 25, title: '电气步骤2', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 26, title: '电气步骤3', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 27, title: '电气步骤4', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 28, title: '电气步骤5', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 29, title: '电气步骤6', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 30, title: '电气步骤7', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 31, title: '电气步骤8', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 32, title: '电气步骤9', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 33, title: '电气步骤10', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 34, title: '电气步骤11', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 35, title: '电气步骤12', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 36, title: '电气步骤13', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 37, title: '电气步骤14', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 38, title: '电气步骤15', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 39, title: '电气步骤16', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 40, title: '电气步骤17', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 41, title: '电气步骤18', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 42, title: '电气步骤19', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 43, title: '电气步骤20', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 44, title: '电气步骤21', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 45, title: '电气步骤22', score: 0, total_score: 2, recommended_time: 600, spent_time: 0.001, is_pass: false },
        { number: 46, title: '电气步骤23', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 47, title: '电气步骤24', score: 0, total_score: 2, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 48, title: '电气步骤25', score: 0, total_score: 1, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 49, title: '电气步骤26', score: 0, total_score: 1, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 50, title: '电气步骤27', score: 0, total_score: 1, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 51, title: '电气步骤28', score: 0, total_score: 1, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 52, title: '电气步骤29', score: 0, total_score: 1, recommended_time: 600, spent_time: 0, is_pass: false },
        { number: 53, title: '电气步骤30', score: 0, total_score: 1, recommended_time: 600, spent_time: 0, is_pass: false },
    ],
    data_token: '2bc7905a5a1542b028ca12d0d9069485',
}

body.steps.forEach(item => {
    item.score = item.total_score;
    // 随机产生一个1-100的数字
    let timeNum = Math.floor(Math.random() * 1000) + 1;
    timeNum = timeNum / 100;
    item.spent_time = timeNum;
    item.is_pass = true;
});

let urlObj = extractParams(url);

Model.gjkfdx_experiment
    .findOrCreate({
        where: {
            task_id: urlObj.experiment,
            app_key: urlObj.app_key,
        },
        defaults: {
            id: v4(),
            task_id: urlObj.experiment,
            course_name: '综合实训(机电)', // 自己写
            task_name: '实训项目十:机电一体化技术综合控制', // 自己写
            experiment_name: '设备的安装', // 自己写
            app_key: urlObj.app_key,
            task_steps: body.steps,
        },
    })
    .then(([requestBody, created]) => {
        if (!created) {
            console.log('该实验已经存在');
        } else {
            console.log('实验创建成功');
        }
    });
