let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api.js');
let path = require('path');
let fs = require('fs');
let Model = require('../../../../config/sequelize.config.js');
let examTask = require('./examTask/index.js');
let homeWorkTask = require('./homeWorkTask/index.js');

async function mixTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr) {
    // await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 任务开始`);

    // 打开课件页面
    // let taskUrl = `https://lms.ouchn.cn/course/${courseObj.id}/learning-activity/full-screen#/${taskObj.id}`;
    // await pageTools.gotoWithRetry(mainPage, taskUrl, { waitUntil: 'networkidle0' }, 3);
    // await new Promise(r => setTimeout(r, 3 * 1000));
    let cookieStr = await pageTools.getPageCookies(mainPage);

    // 获取复合大作业中的任务列表
    let mixTaskListRes = await api.getMixTaskList(globalStore.userId, taskObj.id, cookieStr);

    // 默认只需要完成第一个就行
    for (let i = 0; i < mixTaskListRes.subtasks.length; i++) {
        let subObj = mixTaskListRes.subtasks[i];
        // 做一个标记
        subObj.isMix = true;

        let subtaskLogStr = `${taskLogStr}-子作业[${i + 1}/${mixTaskListRes.subtasks.length}]`;

        await infoLogger(`${taskLogStr} <${subObj.title}> 子课件类型为： ${subObj.type}`);

        switch (subObj.type) {
            // 测试 作为module处理，这里跳过
            case 'exam': {
                await examTask(infoLogger, mainPage, globalStore, courseObj, subObj, subtaskLogStr);
                break;
            }
            // 测试 作为module处理，这里跳过
            case 'homework': {
                await homeWorkTask(infoLogger, mainPage, globalStore, courseObj, subObj, subtaskLogStr);
                break;
            }

            default: {
                await infoLogger(`${subtaskLogStr} <${subObj.title}> ${subObj.type} 未知任务类型，跳过`, 'red');
                break;
            }
        }

        await new Promise(r => setTimeout(r, 5 * 1000));
    }
}

module.exports = mixTask;
