let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api.js');

async function signinModule(infoLogger, mainPage, globalStore, taskObj){

    //1.跳转到登录页面
    await mainPage.goto(taskObj.schoolurl, { waitUntil: 'networkidle0' });
    await new Promise(r => setTimeout(r, 3000));

    //2.输入用户信息
    await infoLogger(`开始登录`); //记录日志
    await mainPage.type('#login > div.main-container.el-row > div > div > div > form > div:nth-child(1) > div > div > input', taskObj.username); //输入用户名
    await mainPage.type('#login > div.main-container.el-row > div > div > div > form > div:nth-child(2) > div > div > input', taskObj.password); //输入密码

    //处理验证码
    let base64Image=await mainPage.evaluate(()=>{
        let imgTag=document.querySelector('div.login-code > img')
        let base64Image=imgTag.getAttribute('src')
        base64Image=base64Image.slice(22) // 去掉开头的“data:image/png;base64,”
        return base64Image
    })
    try {
        validateCodeRes = await api.validateCode(base64Image);
        if (validateCodeRes.err_no !== 0) {
            await infoLogger(`验证码识别失败：${validateCodeRes.err_str}`, 'red'); //记录日志
            throw new Error(`验证码识别失败：${validateCodeRes.err_str}`);
        }
    } catch (error) {
        await infoLogger(`验证码识别服务器出错`, 'red'); //记录日志
        throw new Error('验证码识别服务器出错');
    }

    //输入验证码
    await mainPage.type(
        '#login > div.main-container.el-row > div > div > div > form > div.el-form-item.code > div > div > input',
        validateCodeRes.pic_str
    );
    // await new Promise(resolve => setTimeout(resolve, 1000)); //等待加载完成

    //4.点击登录
    let signinHandle = await mainPage.$('#login > div.main-container.el-row > div > div > div > form > div.submit > button');
    await signinHandle.click();

    await new Promise(resolve => setTimeout(resolve, 3000)); //等待加载完成


    //6.判断是否登录成功 http://test2024b.souchn.cn/#/student
    currentUrl = mainPage.url();
    // console.log('currentUrl',currentUrl)
    if (currentUrl == 'http://test2024b.souchn.cn/#/student') {
        await infoLogger('登录成功', 'green');
    } else {
        await infoLogger('登录失败', 'red');
        throw Error('登录失败');
    }
}

module.exports = signinModule;