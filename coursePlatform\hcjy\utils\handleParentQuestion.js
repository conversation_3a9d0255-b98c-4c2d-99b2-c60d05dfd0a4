let { v4 } = require('uuid');
let path = require('path');

let axiosIns = require('./axios');
let Model = require('../../../config/sequelize.config.js');
let aiChat = require('../../solveQuestion/aiChat');
let { numbersToLetters, lettersToNumbers, handleImgs } = require('../../solveQuestion/format.js');
let getAnswerFromLocal=require('../../solveQuestion/getAnswerFromLocal');

function trim(s) {
    return (
        s
            //删除html标签 <span>
            .replace(/(<([^>]+)>)/gi, '')
            //删除所有的换行符
            .replaceAll('\n', '')
            // 把Unicode 转义序列转为正常字符，例如\u0026->&
            .replace(/\\u([0-9a-fA-F]{4})/g, function (match, p1) {
                return String.fromCharCode(parseInt(p1, 16));
            })
            //把&nbsp转为一个空格
            .replaceAll('&nbsp;', ' ')
            //删除两个及以上的空格，保留单个空格
            .replace(/\s{2,}/g, ' ')
            // 删除前后字符串
            .trim()
    );
}

// 结合 handleImgs 和 trim，处理题目内容
function handleContent(str) {
    return trim(handleImgs(str)).slice(0, 4500);
}

async function handleParentQuestion(parentQuestion, courseName) {
    let answers;

    
    // 格式化题目
    let temObj = {
        id: v4(),
        type: parentQuestion.questiontypename + '题',
        content: handleContent(parentQuestion.stem), //题目内容
        options: '[]', // 题目选项
        platform: '弘成教育',
        course_name: courseName,
        // answers: answers,
        // answers_content: JSON.stringify([answers]),
        comment: 'hc_handleParentQuestion',
        add_time: new Date(),
    };

    // 先从本地查找
    answers=await getAnswerFromLocal(temObj)

    if(answers){
        // console.log('本地有')
        return answers
    }

    // console.log('temObj', temObj);

    let str = `
        你是一个专业的题目分析助手。请根据以下信息分析题目并生成答案：

        题目类型：${parentQuestion.questiontypename}

        文章内容：
        ${parentQuestion.stem}

        题目列表：
        ${JSON.stringify(parentQuestion.subqustionList)}

        注意事项：
        1. 题目列表是一个JSON字符串。
        2. 每个题目对象(questionObj)中，stem属性表示题目内容。
        3. questionObj.answerArea.optionList表示题目选项。

        请按以下格式提供答案：
        [
                    { questionId: 'b2e88303-14a1-4849-b201-a9d5b487e602', content: { id: '0' } },
                    ...
        ]

        你的回答必须是一个有效的JSON字符串，包含一个数组，不能有多余文字，确保可以被JS中的JSON.parse()方法解析。
    `;

    // str='你好'

    let aiModel = 'claude-3-5-sonnet-********';

    try {
        answers = await aiChat(aiModel, str);
        // console.log('answers',answers)
    } catch (error) {
        // console.log('查找出错',error)
        // return false;
    }

    if (answers) {
        temObj.answers = answers;
        temObj.answers_content = JSON.stringify([answers]);
        await Model.bank.create(temObj);
        return answers;
    }
}

module.exports = handleParentQuestion;

if (false) {
    let obj = {
        solvingProcess: '',
        sequenceNumber: '31',
        uploadFile: 0,
        studentSoundRecordingQuestionAnswerTimes: -1,
        questionId: '47e3f9f996bf58095977978fa836b269',
        answerRecord: {},
        questiontypename: '阅读理解',
        difficultyDegree: 2,
        usedsequence: '1',
        answerArea: null,
        answerMode: 'Composite',
        sequence: 1,
        score: 20,
        judgmentMode: 2,
        studentVideoAndAudioPlaybackTimes: -1,
        answer: null,
        operationQuestion: 0,
        uploadImg: 1,
        subqustionList: [
            {
                solvingProcess: null,
                sequenceNumber: null,
                uploadFile: 0,
                studentSoundRecordingQuestionAnswerTimes: -1,
                questionId: '15934a1f77cbab7777112607f88193df',
                answerRecord: {},
                questiontypename: '单选题',
                answerArea: {
                    optionList: [
                        {
                            id: '1',
                            content: 'natural',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '2',
                            content: 'great',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '3',
                            content: 'obvious',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '4',
                            content: 'absolute',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                    ],
                    reorder: false,
                },
                answerMode: 'SingleSelection',
                sequence: 1,
                score: 4,
                studentVideoAndAudioPlaybackTimes: -1,
                answer: null,
                operationQuestion: 0,
                uploadImg: 1,
                answerAreaStyle: '1,2,3,4',
                stem: 'The underlined word in the second paragraph means_________.',
            },
            {
                solvingProcess: null,
                sequenceNumber: null,
                uploadFile: 0,
                studentSoundRecordingQuestionAnswerTimes: -1,
                questionId: '6b06589afe342dcde553708506bbf524',
                answerRecord: {},
                questiontypename: '单选题',
                answerArea: {
                    optionList: [
                        {
                            id: '1',
                            content: 'cloud',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '2',
                            content: 'sheep',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '3',
                            content: 'trees',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '4',
                            content: 'goose',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                    ],
                    reorder: false,
                },
                answerMode: 'SingleSelection',
                sequence: 2,
                score: 4,
                studentVideoAndAudioPlaybackTimes: -1,
                answer: null,
                operationQuestion: 0,
                uploadImg: 1,
                answerAreaStyle: '1,2,3,4',
                stem: 'According to the passage, which of the following can Not be typed into the same category?',
            },
            {
                solvingProcess: null,
                sequenceNumber: null,
                uploadFile: 0,
                studentSoundRecordingQuestionAnswerTimes: -1,
                questionId: '1609f9a986e54e3b3bf4edfe1ce52a52',
                answerRecord: {},
                questiontypename: '单选题',
                answerArea: {
                    optionList: [
                        {
                            id: '1',
                            content: 'Men do better than women when it comes to learning English',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '2',
                            content: 'Women stand out at remembering people’s names',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '3',
                            content: 'Men excel at typing as many words in a particular category as possible in the given time',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '4',
                            content: 'Women excel at dealing mathematic problems',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                    ],
                    reorder: false,
                },
                answerMode: 'SingleSelection',
                sequence: 3,
                score: 4,
                studentVideoAndAudioPlaybackTimes: -1,
                answer: null,
                operationQuestion: 0,
                uploadImg: 1,
                answerAreaStyle: '1,2,3,4',
                stem: 'Which of the following statements is true according the article?',
            },
            {
                solvingProcess: null,
                sequenceNumber: null,
                uploadFile: 0,
                studentSoundRecordingQuestionAnswerTimes: -1,
                questionId: '43ee2b1ef5b94290a3af821d904e8518',
                answerRecord: {},
                questiontypename: '单选题',
                answerArea: {
                    optionList: [
                        {
                            id: '1',
                            content: 'the old men tested may not have shown such cognitive decline',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '2',
                            content: 'people surveyed are all old',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '3',
                            content: 'people taking part in this test came from all over the world',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '4',
                            content: 'women live longer than men&nbsp;',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                    ],
                    reorder: false,
                },
                answerMode: 'SingleSelection',
                sequence: 4,
                score: 4,
                studentVideoAndAudioPlaybackTimes: -1,
                answer: null,
                operationQuestion: 0,
                uploadImg: 1,
                answerAreaStyle: '1,2,3,4',
                stem: 'One important factor that affects the correctness of the results is that _________.',
            },
            {
                solvingProcess: null,
                sequenceNumber: null,
                uploadFile: 0,
                studentSoundRecordingQuestionAnswerTimes: -1,
                questionId: 'b0134db7063b346c926a51137445e46f',
                answerRecord: {},
                questiontypename: '单选题',
                answerArea: {
                    optionList: [
                        {
                            id: '1',
                            content: 'women’s minds perform better than men’s',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '2',
                            content: 'men’s minds decline more with age',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '3',
                            content: 'everyone becomes a little more forgetful as they get older',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                        {
                            id: '4',
                            content: 'a survey on human’s mind decline was done recently',
                            sequence: 0,
                            score: 0,
                            isInput: false,
                        },
                    ],
                    reorder: false,
                },
                answerMode: 'SingleSelection',
                sequence: 5,
                score: 4,
                studentVideoAndAudioPlaybackTimes: -1,
                answer: null,
                operationQuestion: 0,
                uploadImg: 1,
                answerAreaStyle: '1,2,3,4',
                stem: 'The author aims to tell us that __________.',
            },
        ],
        category: [
            {
                courseName: '大学英语（下）（2020）',
                code: '00858001',
                courseCode: '00858',
                name: '大学英语（下）（2020）',
            },
        ],
        answerAreaStyle: null,
        stem: '<p style="text-indent:28px">Everyone becomes a little more forgetful as they get older, but men&#39;s minds decline more than women&#39;s, according to the results of a worldwide survey.<span id="JHABV955"></span></p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Certain differences seem to be inherent in male and female brains: Men are better at maintaining and dealing with mental images (useful in mathematical reasoning and spatial skills), while women tend to excel at recalling information from their brain&#39;s files (helpful with language skills and remembering the locations of objects).<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Many studies have looked for a connection between sex and the amount of mental decline people experience as they age, but the results have been mixed.</p>&nbsp; &nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Some studies found more age-related decline in men than in women, while others saw the opposite or even no relationship at all between sex and mental decline. Those results could be improper because the studies involved older people, and women live longer than men: The men tested are the survivors, &quot;so they&#39;re the ones that may not have shown such cognitive decline,&quot; said study team leader Elizabeth of the University of Warwick in England.<p>&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; People surveyed completed four tasks that tested sex-related cognitive skills: matching an object to its rotated form, matching lines shown from the same angle, typing as many words in a particular category as possible in the given time, e.g. &quot;object usually colored gray&quot;, and recalling the location of objects in a line drawing. The first two were tasks at which men usually excel; the latter are typically dominated by women.</p><p><span style="font-size:14px;font-family: &#39;Calibri&#39;,&#39;sans-serif&#39;">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp; Within each age group studied, men and women performed better in their separate categories on average. And though performance declined with age for both genders, women showed obviously less decline than men overall.</span></p>',
        batchNumber: '47e3f9f996bf58095977978fa836b269',
    };
    (async () => {
        let res = await handleParentQuestion(obj);
        console.log(res);
    })();
}
