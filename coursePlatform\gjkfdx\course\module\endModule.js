let api = require('../../utils/api');
let pageTools = require('../../../utils/pageTools.js');
let signinModule = require('./singinModule/index.js');

async function endModule(infoLogger, mainPage, courseList, globalStore) {
    if (globalStore.others.includes('收集课程')) {
        let infoUrl='https://menhu.pt.ouchn.cn/site/ouchnPc/personalInformation'
        await mainPage.goto(infoUrl,{ waitUntil: 'networkidle2'})
        let cookieStr = await pageTools.getPageCookies(mainPage);
        let pcMultiInfoRes = await api.getPcMultiInfo(cookieStr);
       
        let moduleItem = pcMultiInfoRes.d.info.other[0].moduleItem;
        await infoLogger('收集课程，不进行刷课', 'green');
        return {
            finalResult: [
                {
                    courseName: `${moduleItem['学年学期']}-${moduleItem['专业层次']}-${moduleItem['学籍状态']}`,
                    progress: globalStore.courseList.length,
                },
            ],
            warningMessage: globalStore.warningMessage,
        };
    }

    await signinModule(infoLogger, mainPage, globalStore);
    let finalResult = [];

    // 进入学习分析界面，可以更新进度，否则总体进度有可能不对
    for (let courseObj of courseList) {
        let courseUrl = `https://lms.ouchn.cn/course/${courseObj.id}/ng#/my-stat`;
        // await mainPage.goto(courseUrl, {waitUntil: 'networkidle0', timeout: 9000 });
        await pageTools.gotoWithRetry(mainPage, courseUrl, { waitUntil: 'networkidle0', timeout: 9000 }, 3, infoLogger);
        await new Promise(r => setTimeout(r, 2000));
    }

    await mainPage.goto('https://menhu.pt.ouchn.cn/site/ouchnPc/index', { waitUntil: 'networkidle0' });
    await new Promise(r => setTimeout(r, 3000));

    let cookieStr = await pageTools.getPageCookies(mainPage);

    let courseListRes = await api.getCourseList(cookieStr);

    // console.log(courseListRes)
    if (!courseListRes || courseListRes.e !== 0) {
        await infoLogger(`最后阶段获取课程列表服务器数据不对，5分钟后重试,数据为空,${JSON.stringify(courseListRes)}`, 'red');
        await new Promise(r => setTimeout(r, 5 * 60 * 1000));
        courseListRes = await api.getCourseList(cookieStr);
    }

    courseList = courseListRes.d.list;
    for (let courseObj of courseList) {
        finalResult.push({
            courseName: courseObj.name,
            progress: courseObj.completeness,
        });
    }
    await infoLogger('全部完成', 'green');
    return {
        finalResult,
        warningMessage: globalStore.warningMessage,
    };
}

module.exports = endModule;
