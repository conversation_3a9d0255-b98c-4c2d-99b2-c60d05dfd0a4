const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('gjkfdx', {
    id: {
      type: DataTypes.STRING(255),
      allowNull: false,
      primaryKey: true
    },
    layer: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "层次"
    },
    major: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "专业"
    },
    course_name: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "课程名称"
    },
    course_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "课程代码"
    },
    status: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "状态"
    },
    comment: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "备注"
    },
    add_time: {
      type: DataTypes.DATE,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'gjkfdx',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
};
