# 使用Puppeteer官方镜像作为基础镜像
FROM ghcr.io/puppeteer/puppeteer:latest

# 切换到root用户以获得足够的权限
USER root

# 安装 tzdata 包
RUN apt-get update && apt-get install -y tzdata

# 设置时区为上海
RUN ln -snf /usr/share/zoneinfo/Asia/Shanghai /etc/localtime && echo "Asia/Shanghai" > /etc/timezone

# 设置工作目录
WORKDIR /app

# 复制package.json和package-lock.json(如果存在)
COPY package*.json ./

# 复制项目文件到工作目录
COPY . .

# 安装项目依赖
RUN npm install

# 代码混淆
RUN npm run obfuscate:all
# RUN npx javascript-obfuscator ./config/ --output ./config/ --config obfuscator-config.json
# RUN npx javascript-obfuscator ./cluster/ --output ./ --config obfuscator-config.json
# RUN npx javascript-obfuscator ./coursePlatform/ --output ./ --config obfuscator-config.json
# RUN npx javascript-obfuscator ./main/ --output ./ --config obfuscator-config.json
# RUN npx javascript-obfuscator ./model/ --output ./ --config obfuscator-config.json

# Puppeteer 在 root 用户下默认会拒绝启动浏览器进程，除非明确指定了 --no-sandbox 参数
# 将所有文件的所有权更改为pptruser(Puppeteer镜像的默认用户)
RUN chown -R pptruser:pptruser /app
# 切换回pptruser
USER pptruser

# 定义启动容器时执行的命令
CMD [ "node", "server.js" ]