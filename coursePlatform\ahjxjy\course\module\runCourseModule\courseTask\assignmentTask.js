// let count = 20; //只能答题20次，超过这个次数，直接下一个
let api = require('../../../../utils/api.js');
let { formatQuestion, handleAnswer, collectQuestion } = require('../../../../utils/handleQuestion.js');
let { v4 } = require('uuid');
let aiChat = require('../../../../../solveQuestion/aiChat.js');
let Model = require('../../../../../../config/sequelize.config.js');
let getAnswerFromLocal = require('../../../../../solveQuestion/getAnswerFromLocal.js');

function trim(s) {
    return (
        s
            //删除html标签 <span>
            .replace(/(<([^>]+)>)/gi, '')
            //删除所有的换行符
            .replaceAll('\n', '')
            // 把Unicode 转义序列转为正常字符，例如\u0026->&
            .replace(/\\u([0-9a-fA-F]{4})/g, function (match, p1) {
                return String.fromCharCode(parseInt(p1, 16));
            })
            //把&nbsp转为一个空格
            .replaceAll('&nbsp;', '')
            //删除两个及以上的空格，保留单个空格
            .replace(/\s{2,}/g, ' ')
            // 删除前后字符串
            .trim()
    );
}

// 从api获取题目 -> 对题目进行格式化(formatQuestion) -> 回答题目(handleAnswer) 或者 收集题目(collectQuestion) -> 提交试卷
async function assignmentTask(count, infoLogger, globalStore, courseObj, cellObj, cellLogStr) {
    //从服务器获取题目信息
    let loadRes = await api.loadOnlineAssignment(globalStore, courseObj, cellObj);

    //判断是否是客观题
    let isSubjective = loadRes.code && loadRes.code == 1;
    let type = isSubjective ? '客观题' : '主观题';

    await infoLogger(`${cellLogStr}：当前${cellObj.icon}作业任务为${type} 《${cellObj.title}》`, 'blue');

    //loadOnlineAssignment获取成功说明是客观题目
    if (isSubjective) {
        await infoLogger(`开始第${count}次答题，当前试卷是否可以答题：${loadRes.canModify} 当前试卷分数：${loadRes.totalScore}`, 'orange');

        //扁平化试题列表
        let questionList = [];
        loadRes.data.forEach(item => {
            item.list.forEach(questionObj => {
                let newObj = formatQuestion(questionObj, '安徽继续教育在线', courseObj);
                questionList.push(newObj);
            });
        });

        //可以答题，正常答题，然后提交
        if (loadRes.canModify) {
            count++;
            await infoLogger(`${cellLogStr}：开始准备答题 《${cellObj.title}》`);

            //回答问题
            let submitCount = await handleAnswer(globalStore, courseObj, questionList);
            await infoLogger(`${cellLogStr}：共回答${submitCount}道题 《${cellObj.title}》`);

            //提交试卷
            let submitOnlineAssignmentRes = await api.submitOnlineAssignment(globalStore, courseObj, cellObj);
            if (submitOnlineAssignmentRes.code == 1) {
                await infoLogger(`${cellLogStr}：试卷提交成功，第${count}次 《${cellObj.title}》`);
            } else {
                await infoLogger(`${cellLogStr}：试卷提交失败，第${count}次，${submitOnlineAssignmentRes.msg} 《${cellObj.title}》`, 'red');
                return;
            }

            if (count == 20) {
                await infoLogger(`${cellLogStr}：已经答题20次，仍没有达到要求 《${cellObj.title}》`, 'red');
                return;
            }

            //更改课程状态为已完成
            let studiedRes = await api.finishChapter(globalStore, courseObj, cellObj);
            await infoLogger(`${cellLogStr}：将课件状态更改为：已提交，第${count}次 《${cellObj.title}》`);
            await assignmentTask(count, infoLogger, globalStore, courseObj, cellObj, cellLogStr);
        }

        //不可以答题，收集题目，然后更改题目状态为可以答题
        if (!loadRes.canModify) {
            //收集题目，更改状态
            if (loadRes.totalScore <= 90) {
                await infoLogger(`${cellLogStr}：当前分数${loadRes.totalScore},小于90，进行重做`);

                //收集题目
                let collectCount = await collectQuestion(questionList);
                await infoLogger(`${cellLogStr}：${cellObj.icon}题目收集成功，共收集${collectCount}道题目`);

                //更改状态为未开始
                let res = await api.resetOnlineAssignment(globalStore, courseObj, cellObj);
                await infoLogger(`${cellLogStr}：将课件状态更改为：未开始，第${count}次`);
                if (res.code != 1) {
                    await infoLogger(`${cellLogStr}：将课件状态更改为：未开始，第${count}次,,${JSON.stringify(res)} 失败`, 'red');
                    return;
                }
                await assignmentTask(count, infoLogger, globalStore, courseObj, cellObj, cellLogStr); //重新开始答题
            }

            //作业结束
            if (loadRes.totalScore > 90) {
                await infoLogger(`${cellLogStr}：作业已经完成，分数为${loadRes.totalScore}`, 'green');
                return;
            }
        }
    }

    //否则有可能是主观题目
    if (!isSubjective) {
        await infoLogger(`${cellLogStr}：当前${cellObj.icon}作业任务为${type}，《${cellObj.title}》`);
        //从服务器获取 assignment作业 主观题题目数据
        let detailRes = await api.getDetail(globalStore, courseObj, cellObj);

        // let questionObj = {
        //     type: '实训题',
        //     content: trim(detailRes.description),
        //     courseName: courseObj.courseName,
        //     platform: '安徽继续教育在线',
        //     options: [],
        // };
        // let answer;
        // answer = await getAnswerFromLocal(questionObj);

        // if (!answer) {
        //     let prompt = `帮我写一个800字左右的简要的<${detailRes.title}?实训报告，不要封面，只要把内容发给我就行，我只要结果，不要包含任何多余文字。如果无法回答，就回答false。要求如下：${questionObj.content}`;

        //     answer = await aiChat('deepseek-coder', prompt);

        //     // 收集答案
        //     await Model.bank.create({
        //         id: v4(),
        //         content: questionObj.content,
        //         options: JSON.stringify(questionObj.options),
        //         type: questionObj.type,
        //         platform: questionObj.platform,
        //         answers: answer,
        //         answers_content: JSON.stringify([answer]),
        //         course_name: questionObj.courseName,
        //         comment: '',
        //         add_time: new Date(),
        //     });
        // }
        if (globalStore.taskObj.others.includes('跳过离线作业')) {
            await infoLogger(`${cellLogStr}：当前为离线作业，根据设置，跳过`);
        } else {
            await infoLogger(`${cellLogStr}：开始答题`);
            //提交答案
            await api.submitAssignment(globalStore, courseObj, cellObj, 'done');
            await api.finishChapter(globalStore, courseObj, cellObj);
            await new Promise(r => setTimeout(r, 2000));
        }
    }
}

module.exports = assignmentTask;
