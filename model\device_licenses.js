const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('device_licenses', {
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    pc_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "机器码"
    },
    username: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "用户名"
    },
    ip_address: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "设备IP"
    },
    status: {
      type: DataTypes.ENUM('Active','Inactive','Expired','Revoked'),
      allowNull: true,
      comment: "ENUM('Active', 'Inactive', 'Expired', 'Revoked')"
    },
    created_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "记录创建时间。"
    },
    activation_date: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "授权激活日期，记录授权何时被激活。"
    },
    expiry_date: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "授权到期日期，适用于有时间限制的授权类型。"
    },
    location: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "机器地址"
    },
    last_heartbeat: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "最后一次心跳时间"
    },
    last_ip_adress: {
      type: DataTypes.STRING(255),
      allowNull: true
    },
    last_location: {
      type: DataTypes.STRING(255),
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'device_licenses',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
};
