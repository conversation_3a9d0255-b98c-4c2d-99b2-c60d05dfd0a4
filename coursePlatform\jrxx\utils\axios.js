let axios = require('axios');

let axiosIns = axios.create();
// 添加响应拦截器
axiosIns.interceptors.response.use(
	// 对响应数据做点什么
	function (response) {
		if (response.status === 200) {
			return response.data;
		} else {
			return Promise.reject(new Error('error'));
		}
	},
	// 对响应错误做点什么
	function (error) {
		return Promise.reject(error);
	}
);

// // 添加请求拦截器
// axiosIns.interceptors.request.use(
//     // 在发送请求之前做些什么
//     function (config) {
//         console.log(config.url)
//         return config;
//     },
//     // 对请求错误做些什么
//     function (error) {
//         return Promise.reject(error);
//     },
// );

module.exports = axiosIns;
