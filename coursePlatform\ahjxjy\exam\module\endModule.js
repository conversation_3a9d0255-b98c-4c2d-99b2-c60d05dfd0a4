let api = require('../../utils/api.js');
async function endModule(infoLogger, globalStore, taskObj) {
    let result = [];

    // 正考
    if (!globalStore.taskObj.others.includes('补考')) {
        //获取课程列表
        let courseListRes;
        try {
            courseListRes = await api.getExamList(globalStore, taskObj);
            courseListRes.list.forEach(item => (item.type = 'sxsk'));
        } catch (error) {
            await infoLogger(`获取随学随考课程列表失败`, 'red'); //记录日志
            throw error; //抛出错误，结束进程池
        }
        for (let i = 0; i < courseListRes.list.length; i++) {
            let courseObj = courseListRes.list[i];
            let loadSXSKRes = await api.loadSXSK(globalStore, courseObj);
            result.push({
                courseName: courseObj.courseName,
                progress: loadSXSKRes.totalScore,
            });
        }

        //获取课程列表
        let onlineListRes;
        try {
            onlineListRes = await api.getOnlineExamList(globalStore, taskObj);
            onlineListRes.list.forEach(item => (item.type = 'tk'));
        } catch (error) {
            await infoLogger(`获取学校统考课程列表失败`, 'red'); //记录日志
            throw error; //抛出错误，结束进程池
        }
        for (let i = 0; i < onlineListRes.list.length; i++) {
            let courseObj = onlineListRes.list[i];
            let loadSXSKRes = await api.loadTK(globalStore, courseObj);
            result.push({
                courseName: courseObj.courseName,
                progress: loadSXSKRes.totalScore,
            });
        }
    }

    // 补考
    if (globalStore.taskObj.others.includes('补考')) {
        let bkSxskListRes;
        try {
            // 'https://main.ahjxjy.cn/studentstudio/ajax-bkExam-bkSxskTable'
            bkSxskListRes = await api.getBkSxskExamList(globalStore, taskObj);
            bkSxskListRes.list.forEach(item => (item.type = 'bkSxsk'));
        } catch (error) {
            await infoLogger(`获取补考课程列表失败`, 'red'); //记录日志
            throw error; //抛出错误，结束进程池
        }
        for (let i = 0; i < bkSxskListRes.list.length; i++) {
            let courseObj = bkSxskListRes.list[i];
            let loadSXSKRes = await api.getQuestion(globalStore, courseObj);
            result.push({
                courseName: courseObj.courseName,
                progress: loadSXSKRes.totalScore,
            });
        }
    }

    return result;
}

module.exports = endModule;
