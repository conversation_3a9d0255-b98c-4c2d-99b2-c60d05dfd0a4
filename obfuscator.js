let fs = require('fs').promises; // 使用 Promise 版的 fs
let path = require('path'); // 用于处理路径
let axios = require('axios'); // 用于发起 HTTP 请求
let getMainPage = require('./coursePlatform/utils/getMainPage.js');

let querystring = require('querystring');

function objectToQueryString(obj) {
    const params = new URLSearchParams();

    // 遍历对象的每个键值对
    for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'object' && !Array.isArray(value)) {
            // 如果值是一个对象，则处理嵌套键值对
            for (const [subKey, subValue] of Object.entries(value)) {
                params.append(`${key}[${subKey}]`, subValue);
            }
        } else {
            // 普通键值对
            params.append(key, value);
        }
    }

    // 返回编码后的查询字符串
    return params.toString();
}
let count = 0;
let mainPage;
async function obfuscator(filePath) {
    try {
        // 获取文件或目录的状态
        let stats = await fs.lstat(filePath);

        if (stats.isDirectory()) {
            // 如果是目录，读取目录中的所有项
            let items = await fs.readdir(filePath);
            // 递归处理每一个子项
            for (let item of items) {
                let fullPath = path.join(filePath, item);
                await obfuscator(fullPath);
            }
        } else if (stats.isFile()) {
            // 如果是文件，进行混淆处理
            // 这里可以根据需要过滤特定类型的文件，例如只处理 .js 文件
            if (path.extname(filePath) === '.js') {
                // if (filePath.includes('coursePlatform\\utils\\pageTools.js')) {
                //     console.log('跳过特殊文件', filePath);
                //     return;
                // }
                // if (filePath.includes('coursePlatform\\gjkfdx\\course\\courseTask\\homeWorkTask.js')) {
                //     console.log('跳过特殊文件', filePath);
                //     return;
                // }
                if (filePath.includes('coursePlatform\\nmwcxt\\utils\\aesF.js')) {
                    console.log('跳过特殊文件', filePath);
                    return;
                }
                if (filePath.includes('coursePlatform\\cxxxt\\utils\\decrypteFont\\index.js')) {
                    console.log('跳过特殊文件', filePath);
                    return;
                }
                if (filePath.includes('coursePlatform\\cxxxt\\utils\\decrypteFont\\originalFontHashes.js')) {
                    console.log('跳过特殊文件', filePath);
                    return;
                }
                if (filePath.includes('coursePlatform\\cxxxt\\utils\\traditionalized.js')) {
                    console.log('跳过特殊文件', filePath);
                    return;
                }
                if (filePath.includes('coursePlatform\\nmwcxt\\utils\\aesF.js')) {
                    console.log('跳过特殊文件', filePath);
                    return;
                }
                if (filePath.includes('coursePlatform\\nmwcxt\\utils\\CryptoJS.js')) {
                    console.log('跳过特殊文件', filePath);
                    return;
                }
                console.log(`开始处理第 ${++count} 个文件: ${filePath}`);

                // 1. 读取文件内容
                let fileContent = await fs.readFile(filePath, 'utf8');

                let requestData = {
                    js_code: fileContent,
                    config: {
                        // 启用紧凑模式，会移除代码中的注释和多余的空白字符，以缩减代码的体积。
                        compact: true,
                        // 控制是否重命名全局函数或变量。如果设置为 true，混淆器将会对全局函数和变量进行重命名
                        renameGlobalFunctionVariable: false,
                        // 控制流程平坦化。流程平坦化是一种将原始的程序控制流转换为更复杂的流程，以使代码逻辑更加难以理解。
                        controlFlowFlattening: false,
                        // 控制字符串数组的编码。如果启用（true），则字符串数组中的字符串将被编码
                        stringArray: true,
                        // 控制字符串数组的编码。如果启用（true），则字符串数组中的字符串将被编码（如Base64），
                        stringArrayEncoding: false,
                        // 控制是否禁用 console 输出。启用时（true）
                        disableConsoleOutput: false,
                        // 控制是否启用调试保护功能。
                        debugProtection: false,
                        // 启用死代码注入。混淆器会插入一些无效的、不会被执行的代码
                        deadCodeInjection: false,
                        // 制时间范围保护。如果启用（true），代码只能在特定的时间范围内运行
                        time_range: true,
                        time_start: 20241021,
                        time_end: 20241221,
                        reservedNames: [
                            '@ffprobe-installer/ffprobe',
                            'axios',
                            'chalk',
                            'crypto-js',
                            'dayjs',
                            'execa',
                            'ffprobe-static',
                            'fontkit',
                            'form-data',
                            'get-video-duration',
                            'https-proxy-agent',
                            'is-stream',
                            'jsdom',
                            'music-metadata',
                            'mysql2',
                            'online_mp4_duration',
                            'puppeteer',
                            'puppeteer-extra-plugin-anonymize-ua',
                            'puppeteer-extra-plugin-stealth',
                            'puppeteer-extra-plugin-user-data-dir',
                            'puppeteer-extra-plugin-user-preferences',
                            'qs',
                            'readline',
                            'sequelize',
                            'sequelize-auto',
                            'sharp',
                            'uuid',
                            'webpack',
                            'webpack-cli',
                            'ws',
                            './',
                            '../',
                            'crypto',
                            'querystring',
                            'url',
                            'fs',
                            'cluster',
                            'os',
                            'path',
                            'child_process',
                            'user-agents',
                            'v8',
                        ],
                    },
                    vip_code: '1729-**************',
                };
                let dataStr = objectToQueryString(requestData);

                // 2. 通过 API 混淆代码（假设 API URL 为 'https://www.jshaman.com:4430/submit_js_code'）
                let apiUrl = 'https://www.jshaman.com:4430/submit_js_code';
                let response = await axios({
                    url: apiUrl,
                    data: requestData,
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json', // 根据 API 要求设置合适的 Content-Type
                    },
                });

                await new Promise(r => setTimeout(r, 2000));

                if (response.data && response.data.message.includes('提交次数太多，您已受限')) {
                    console.log(response.data.message);
                    await new Promise(r => setTimeout(r, 310 * 1000));
                }

                // 检查 API 返回结果
                if (response.status == 200 && response.data && response.data.status == 0) {
                    let obfuscatedCode = response.data.content; // 获取混淆后的代码

                    // let deobfuscatedCode = await mainPage.evaluate(obfuscatedCode => {
                    //     window.input.value = obfuscatedCode;
                    //     start();
                    //     return window.output.value;
                    // }, obfuscatedCode);

                    // 3. 将混淆后的代码写入原文件，替换原来的内容
                    await fs.writeFile(filePath, obfuscatedCode, 'utf8');
                    console.log(`文件已混淆: ${filePath}`);
                } else {
                    throw new Error(`API 返回无效的混淆代码。文件: ${filePath}，代码:${JSON.stringify(response.data)}`);
                }
            } else {
                console.log(`跳过非 JavaScript 文件: ${filePath}`);
            }
        } else {
            console.log(`跳过未知类型的路径: ${filePath}`);
        }
    } catch (error) {
        console.error(`处理路径 "${filePath}" 时发生错误:`, error.message);
        console.log(error);
    }
}

if (true) {
    (async () => {
        // let taskOptions = { isHeadless: false };
        // let result = await getMainPage(taskOptions);
        // mainPage = result.mainPage;
        // let deobfuscatorUrl = 'https://dev-coco.github.io/Online-Tools/JavaScript-Deobfuscator.html';
        // try {
        //     await mainPage.goto(deobfuscatorUrl, { waitUntil: 'networkidle0' });
        // } catch (error) {}
        // await new Promise(r => setTimeout(r, 3000));
        // console.log('网站打开完毕');

        await obfuscator(path.resolve('config'));
        await obfuscator(path.resolve('coursePlatform'));
        await obfuscator(path.resolve('main'));
        await obfuscator(path.resolve('model'));
        await obfuscator(path.resolve('server.js'));
        // await obfuscator(path.resolve('1.js'));
    })();
}
