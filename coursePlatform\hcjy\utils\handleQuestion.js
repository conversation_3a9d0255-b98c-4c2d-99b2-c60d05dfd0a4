/**
 *  选项列表，有时候从0开始，有时候从1开始
 */

let { v4 } = require('uuid');
let handleParentQuestion = require('./handleParentQuestion.js');

let path = require('path');
let Model = require('../../../config/sequelize.config.js');
let { numbersToLetters, lettersToNumbers, handleImgs } = require('../../solveQuestion/format.js');
let solveQuestion = require('../../solveQuestion/index.js');
let getAnswerFromLocal = require('../../solveQuestion/getAnswerFromLocal.js');

function trim(s) {
    return (
        s
            //删除html标签 <span>
            .replace(/(<([^>]+)>)/gi, '')
            //删除所有的换行符
            .replaceAll('\n', '')
            // 把Unicode 转义序列转为正常字符，例如\u0026->&
            .replace(/\\u([0-9a-fA-F]{4})/g, function (match, p1) {
                return String.fromCharCode(parseInt(p1, 16));
            })
            //把&nbsp转为一个空格
            .replaceAll('&nbsp;', ' ')
            //删除两个及以上的空格，保留单个空格
            .replace(/\s{2,}/g, ' ')
            // 删除前后字符串
            .trim()
    );
}

// 结合 handleImgs 和 trim，处理题目内容
function handleContent(str) {
    return trim(handleImgs(str)).slice(0, 4500);
}

// 查找答案
async function handleQuestion(data, courseName, infoLogger) {
    // 准备最终提交的数据
    let submitData = {
        arrangementId: data.arrangementId,
        studentTestActivityScoreId: data.studentTestActivityScoreId,
        answerPaperRecordId: data.answerPaperRecordId,
        tempSaveAnswerExpire: 72,
        temp: 0,
        resourcePackageId: data.resourcePackageId,
        paperAnswerResult: {
            answerPaperRecordId: data.answerPaperRecordId,
            questionAnswerList: [
                // { questionId: "03f9412d-f41a-49a8-97d9-b24cd0a1678a", content: { id: "0" } },
                // { questionId: "d03c12e0-9819-4eb1-9429-a0179322c09c", content: { idList: ["0", "1", "2", "3"] } },
                // { questionId: "fea5e5bd-0a9f-4f26-acfe-0e47e383ea30", content: { id: "1" } },
                // { questionId: "7de32b2d-cccd-4031-87d3-dc4294636915", content: { id: "2" } },
                // {
                //     questionId: "0c0510b2-e19c-4e15-82c0-1c5a027992a0",
                //     content: "",
                //     subQuestionAnswerList: [
                //         {
                //             questionId: "ed7ad69e-6cb5-4d4e-83ce-bad2e39b188b",
                //             content: '{"id":"4"}',
                //         },
                //         {
                //             questionId: "39447adc-cbfd-4397-b871-7ef0e4a39cfc",
                //             content: '{"id":"1"}',
                //         },
                //         {
                //             questionId: "7029b041-c3e0-4ef9-b739-65983067930e",
                //             content: '{"id":"4"}',
                //         },
                //         {
                //             questionId: "dc128b08-b150-4e08-8f03-c85942e8128e",
                //             content: '{"id":"3"}',
                //         },
                //         {
                //             questionId: "4cd23b5c-e230-4503-aa72-cc34e707c6f8",
                //             content: '{"id":"3"}',
                //         },
                //     ],
                // },
            ],
        },
    };

    // 从API数据中提取题目
    let psOutputDto = data.paper.psOutputDto;
    // 过滤掉题干 level值为1 无意义
    psOutputDto = psOutputDto.filter(item => item.level == 2);
    // 普通题目
    let questionList = [];
    let parentQuestionList = []; // 嵌套题目
    psOutputDto.forEach(item => {
        // 每一个itme都是一个题目类型，paperQuestionList是具体题目
        let paperQuestionList = item.paperQuestionList;
        paperQuestionList.forEach(paperQuestionListObj => {
            // // 对于阅读理解，完形填空等题型，会有嵌套
            if (paperQuestionListObj.subqustionList) {
                // 添加一个子题目标记
                paperQuestionListObj.isWrapQuestion = true;
            }
            // 没有嵌套的题目，直接收集即可
            questionList.push(paperQuestionListObj);
        });
    });

    // 处理题目
    for (let questionObj of questionList) {
        // 格式化题目
        let temObj = {
            questionId: questionObj.questionId, // 原始题目id
            type: questionObj.questiontypename, // 原始题目类型
            content: handleContent(questionObj.stem), //题目内容
            options: [], // 题目选项
            platform: '弘成教育',
            courseName: courseName,
            parentId: questionObj.parentId, // 如果是嵌套子题，这个是父题的id
            isWrapQuestion: questionObj.isWrapQuestion, // 嵌套提标记
        };
        if ('判断填空翻译阅读理解'.includes(temObj.type)) {
            temObj.type += '题';
        }

        // 作文题当简答题处理
        if (temObj.type == '作文题') {
            temObj.type = '简答题';
        }

        // 综合体当作简答题处理
        if (temObj.type == '综合题') {
            temObj.type = '简答题';
        }

        // 论述题当作简答题处理
        if (temObj.type == '论述题') {
            temObj.type = '简答题';
        }

        // 目前发现完形填空和阅读理解是 嵌套类型的题目
        if ('完形填空题阅读理解题'.includes(temObj.type)) {
            temObj.isWrapQuestion = true;
        }

        switch (temObj.type) {
            case '判断题': {
                temObj.options = ['A:对', 'B:错'];
                break;
            }
            case '单选题':
            case '多选题': {
                questionObj.answerArea.optionList.forEach((item, index) => {
                    let letter = numbersToLetters(item.id);
                    let value = handleContent(item.content);
                    temObj.options.push(`${letter}:${value}`);
                });
                break;
            }
        }

        // 查找答案
        let answers;
        // 普通题目
        try {
            answers = await solveQuestion(temObj);
        } catch (error) {
            answers = undefined;
            // await infoLogger(`solveQuestion查找答案出错，${error.message}，题目内容${JSON.stringify(temObj)}`)
            // console.log(`solveQuestion查找答案出错，${error.message}，题目内容${JSON.stringify(temObj)}`)
        }
        // 嵌套题目
        if (temObj.isWrapQuestion) {
            try {
                answers = await handleParentQuestion(questionObj, courseName);
            } catch (error) {
                answers = undefined;
                // await infoLogger(`handleParentQuestion查找答案出错，${error.message}，题目内容${JSON.stringify(questionObj)}，课程名称：${courseName}`)
                // console.log(`handleParentQuestion查找答案出错，${error.message}，题目内容${JSON.stringify(questionObj)}，课程名称：${courseName}`)
            }
        }

        if (!answers) continue;
        temObj.answers = answers;

        // 生成最终数据
        switch (temObj.type) {
            case '判断题': {
                let numbers = lettersToNumbers(answers);
                submitData.paperAnswerResult.questionAnswerList.push({
                    questionId: temObj.questionId,
                    // content: JSON.stringify({ id: numbers }),
                    content: { id: numbers * 1 + 1 + '' },
                });
                break;
            }
            case '单选题': {
                // console.log('答题',temObj)
                let numbers = lettersToNumbers(answers);
                submitData.paperAnswerResult.questionAnswerList.push({
                    questionId: temObj.questionId,
                    // content: JSON.stringify({ id: numbers }),
                    content: { id: numbers },
                });
                break;
            }
            case '多选题': {
                let numbers = lettersToNumbers(answers);
                let numArr = numbers.split('');
                numArr = numArr.map(item => item);
                submitData.paperAnswerResult.questionAnswerList.push({
                    questionId: temObj.questionId,
                    // content: JSON.stringify({ idList: numArr }),
                    content: { idList: numArr },
                });
                break;
            }

            case '填空题': {
                // { questionId: "1d88436f0aa8fc7c4c2d1197bb7482a2", content: '{"pairList":[{"id":"1","content":"<p>oral</p>"}]}' },
                let ansArr = answers.split('|');
                let pairList = [];
                ansArr.forEach((item, index) => {
                    pairList.push({
                        id: index + 1,
                        content: `<p>${item}</p>`,
                    });
                });
                submitData.paperAnswerResult.questionAnswerList.push({
                    questionId: temObj.questionId,
                    // content: JSON.stringify({ pairList: pairList }),
                    content: { pairList: pairList },
                });
                break;
            }

            case '翻译题':
            case '论述题':
            case '作文题':
            case '简答题': {
                submitData.paperAnswerResult.questionAnswerList.push({
                    questionId: temObj.questionId,
                    // content: JSON.stringify({ content: `<p>${answers}</p>` }),
                    content: { content: `<p>${answers}</p>` },
                });
                break;
            }

            // 以下是嵌套题目
            case '完形填空题':
            case '阅读理解题': {
                let subQuestionAnswerList = JSON.parse(answers);
                // subQuestionAnswerList = [
                //     { questionId: 'ed7ad69e-6cb5-4d4e-83ce-bad2e39b188b', content: { id: '4' } },
                //     { questionId: '39447adc-cbfd-4397-b871-7ef0e4a39cfc', content: { id: '1' } },
                //     { questionId: '7029b041-c3e0-4ef9-b739-65983067930e', content: { id: '4' } },
                //     { questionId: 'dc128b08-b150-4e08-8f03-c85942e8128e', content: { id: '3' } },
                //     { questionId: '4cd23b5c-e230-4503-aa72-cc34e707c6f8', content: { id: '3' } },
                // ];

                if (!Array.isArray(subQuestionAnswerList)) {
                    console.log('嵌套题目出错', temObj);
                    break;
                }

                subQuestionAnswerList.forEach((item, index) => {
                    item.questionId = questionObj.subqustionList[index].questionId;
                });
                submitData.paperAnswerResult.questionAnswerList.push({
                    questionId: temObj.questionId,
                    content: '',
                    subQuestionAnswerList: subQuestionAnswerList,
                });
                break;
            }
        }
    }

    // submitData.paperAnswerResult = JSON.stringify(submitData.paperAnswerResult);
    return submitData;
}

// 收集答案
async function collectQuestion(paperRes, courseName) {
    // 扁平化题目列表
    let paperStructures = paperRes.data.paperStructures;
    let questionList = [];
    paperStructures.forEach(item => {
        questionList = questionList.concat(item.questions);
    });

    // 开始收集题目
    let collectCount = 0; // 收集题目数量
    let updateCont = 0; // 更新题目数量
    for (let questionObj of questionList) {
        // 格式化题目
        let temObj = {
            id: v4(),
            questionId: questionObj.id,
            type: questionObj.questionTypeName,
            content: handleContent(questionObj.stem),
            options: '[]',
            platform: '弘成教育',
            courseName: courseName,
            isWrapQuestion: questionObj.isWrapQuestion,
            add_time: new Date(),
        };

        if ('判断填空翻译阅读理解'.includes(temObj.type)) {
            temObj.type += '题';
        }
        // 作文题不收集，因为答案往往是“略”
        if (temObj.type == '作文题') {
            break;
        }

        // 综合体当作简答题处理
        if (temObj.type == '综合题') {
            temObj.type = '简答题';
        }

        // 论述题当作简答题处理
        if (temObj.type == '论述题') {
            temObj.type = '简答题';
        }

        //处理答案
        let answerObj = questionObj.answer;
        switch (temObj.type) {
            // 已经核对
            case '判断题': {
                temObj.options = JSON.stringify(['A:对', 'B:错']);
                let correctOption = questionObj.answerArea.optionList.find(option => option.id === questionObj.answer.id);
                correctOption = correctOption ? correctOption.content : null;
                if ('正确对TA'.includes(correctOption)) {
                    temObj.answers = 'A';
                    temObj.answers_content = '["对"]';
                }
                if ('错误FB'.includes(correctOption)) {
                    temObj.answers = 'B';
                    temObj.answers_content = '["错"]';
                }
                break;
            }

            // 已经核对
            case '单选题': {
                // 处理选项
                let options = [];
                questionObj.answerArea.optionList.forEach((item, index) => {
                    let letter = numbersToLetters(index);
                    let value = handleContent(item.content);
                    options.push(`${letter}:${value}`);
                });
                temObj.options = JSON.stringify(options);

                // 通过id，获取正确答案的内容
                let answersContent;
                questionObj.answerArea.optionList.forEach((item, index) => {
                    if (item.id == questionObj.answer.id) {
                        answersContent = handleContent(item.content);
                    }
                });

                // 根据答案内容，获取答案选项
                options.forEach((item, index) => {
                    let content = item.slice(2);
                    if (content == answersContent) {
                        temObj.answers = numbersToLetters(index + '');
                        temObj.answers_content = JSON.stringify([content]);
                    }
                });
                break;
            }

            // 已经核对
            case '多选题': {
                // 处理选项
                let options = [];
                questionObj.answerArea.optionList.forEach((item, index) => {
                    let letter = numbersToLetters(index);
                    let value = handleContent(item.content);
                    options.push(`${letter}:${value}`);
                });
                temObj.options = JSON.stringify(options);

                // 通过id，获取正确答案的内容
                let answersContent = [];
                questionObj.answerArea.optionList.forEach(optionItem => {
                    questionObj.answer.idList.forEach(answerItem => {
                        if (optionItem.id == answerItem) {
                            answersContent.push(handleContent(optionItem.content));
                        }
                    });
                });

                // 根据答案内容，获取答案选项
                let answers = [];
                options.forEach((optionItem, index) => {
                    let optionContent = optionItem.slice(2);
                    answersContent.forEach(contentItem => {
                        if (optionContent == contentItem) {
                            answers.push(numbersToLetters(index));
                        }
                    });
                });

                temObj.answers = answers.join('');

                answersContent = [...answersContent].sort((x, y) => x.localeCompare(y, 'zh-Hans-CN'));
                temObj.answers_content = JSON.stringify(answersContent);
                break;
            }

            // 已经核对
            case '填空题': {
                let pairList = answerObj.pairList;
                pairList = pairList.map(item => trim(handleImgs(item.contentList[0])));
                let str = pairList.join('|');
                temObj.answers = str;
                temObj.answers_content = JSON.stringify([str]);
                break;
            }

            // 已经核对
            case '翻译题':
            case '简答题': {
                temObj.answers = handleContent(answerObj.content);
                temObj.answers_content = JSON.stringify([temObj.answers]);
                break;
            }

            // 嵌套题目，查看试卷界面的题目ID，和答题界面的题目ID，不一样，所以不能收集
            // 答题界面ID格式：b318dcb153b41ca79ac4f4e5d7e23117
            // 查看界面ID格式：70922b5a-b9f3-439a-b193-ab0654226a88
            // case '完形填空题':
            // case '阅读理解题': {
            //     // 获取所有的子问题
            //     let subQuestions = questionObj.subQuestions;

            //     // 转换每个子问题，提取需要的数据
            //     let result = subQuestions.map(subQuestion => {
            //         return {
            //             questionId: subQuestion.id,
            //             content: { id: subQuestion.answer.id },
            //         };
            //     });
            //     temObj.answers = JSON.stringify(result);
            //     temObj.answers_content = JSON.stringify(result);
            //     break;
            // }
        }

        if (!temObj.answers) {
            continue;
        }

        // console.log(temObj);

        // 收集到服务器
        let findObj = {
            where: {
                type: temObj.type,
                content: temObj.content,
                platform: '弘成教育',
                course_name: courseName,
            },
        };
        // console.log('findObj',findObj)
        let existingRecord = await Model.bank.findOne(findObj);
        if (existingRecord) {
            await Model.bank.update(
                {
                    answers: temObj.answers,
                    answers_content: temObj.answers_content,
                    comment: '收集',
                },
                {
                    where: {
                        id: existingRecord.id,
                    },
                }
            );
            updateCont++;
        } else {
            // 如果未找到记录，则创建新记录
            temObj.course_name = courseName;
            temObj.comment = '收集';
            await Model.bank.create(temObj);
            collectCount++;
        }
    }

    // 返回结果
    return { collectCount, updateCont };
}

module.exports = { handleQuestion, collectQuestion };

if (false) {
    (async () => {
        // collectQuestion(res, '大学英语（下）（2020）统考');
    })();
}
