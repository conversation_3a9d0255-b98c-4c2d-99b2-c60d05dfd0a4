let pageTools = require("../../../utils/pageTools.js");

async function signinModule(infoLogger, mainPage, taskObj, globalStore) {
    //1.跳转到登录页面
    await mainPage.goto(taskObj.schoolurl, { waitUntil: "networkidle0" });
    // console.log('here')

    //获取登录窗口 #firstPart > div.topDiv > div.loginBtn
    let signinHandle = await mainPage.$("#firstPart > div.topDiv > div.loginBtn");
    await signinHandle.click();
    await mainPage.waitForSelector("#submitBtn");

    //2.输入用户名，密码
    await mainPage.type("#userName", taskObj.username); //输入用户名
    await mainPage.type("#userPwd", taskObj.password); //输入密码

    //3.登录
    await mainPage.click("#submitBtn");

    //等待登录完成
    try {
        await mainPage.waitForSelector("#iul > li:nth-child(2) > a", {
            timeout: 10000,
        });
    } catch (error) {
        await infoLogger("登录失败", "red");
        throw new Error("密码错误"); //抛出错误，关闭线程
    }

    globalStore.mainPageCookieStr = await pageTools.getPageCookies(mainPage);
    globalStore.mainUrl = await mainPage.url();
    await infoLogger("登录成功", "green");
}

module.exports = signinModule