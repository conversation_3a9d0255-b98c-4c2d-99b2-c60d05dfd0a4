let extractCoursesFromPageInBrowserContext = require('../../utils/extractCoursesFromPageInBrowserContext.js');
let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api.js');

async function getCourseListModule(infoLogger, globalStore, mainPage) {
    let taskObj = globalStore.taskObj;
    await mainPage.goto('https://mooc2-ans.chaoxing.com/mooc2-ans/visit/interaction', {
        waitUntil: 'networkidle0',
    });
    await mainPage.reload();

    let courseList = await mainPage.evaluate(extractCoursesFromPageInBrowserContext);

    // 过滤掉没有截至时间的课程
    courseList = courseList.filter(course => course.rawTimeInfo);
    // 过滤掉已结束的课程
    courseList = courseList.filter(course => !(course.statusText == '课程已结束'));

    // 过滤掉指定课程名称
    if (taskObj.coursename) {
        courseList = courseList.filter(course => course.courseName.includes(taskObj.coursename));
    }

    if (courseList.length == 0) {
        // await infoLogger('课程数量为0', 'red');
        return [];
    }

    let cookieStr = await pageTools.getPageCookies(mainPage);
    for (let courseObj of courseList) {
        if (courseObj.progress == '未知') {
            let info = courseObj.infoAttribute;
            let res;
            try {
                res = await api.getCourseInfo(info, cookieStr);
                courseObj.progress = res.jobArray[0].jobRate;
            } catch (error) {
                courseObj.progress = '未知';
            }
        }
    }

    await infoLogger(`获取课程列表成功，共${courseList.length}门课程`);

    if (false) {
        courseList = [
            {
                clazzId: '96519784',
                courseId: '234508932',
                curPersonId: '381683567',
                infoAttribute: '96519784_381683567',
                roleIdAttribute: 'stu_96519784',
                elementId: 'c_234508932',
                coverImageUrl: 'http://p.ananas.chaoxing.com/star3/240_130c/62bda628a4db2f6c7b8fecac6cd276df.png',
                link: 'https://mooc1.chaoxing.com/visit/stucoursemiddle?courseid=234508932&clazzid=96519784&cpi=381683567&ismooc2=1',
                name: '计算机语言程序设计',
                schoolOrDescription: null,
                teacher: '刘燕',
                rawTimeInfo: '开课时间：2024-12-04～2024-12-15',
                startTime: '2024-12-04',
                endTime: '2024-12-15',
                isFinished: true,
                statusText: '课程已结束',
            },
        ];
    }

    return courseList;
}

module.exports = getCourseListModule;
