let pageTools = require('../../../../utils/pageTools.js');
let api = require('../../../utils/api.js');
let collectQuestionList = require('./collectQuestionList.js');
async function collectExam(infoLogger, mainPage, courseName, examId, scoreId, examLogStr, collectType) {
    await infoLogger(`${examLogStr}，${collectType}`);

    // 进入到考试界面
    // let lastUrl = `https://lms.ouchn.cn/exam/${examId}/subjects#/submission/${scoreId}`;
    // await mainPage.goto(lastUrl, { waitUntil: 'networkidle2' });
    let cookieStr = await pageTools.getPageCookies(mainPage);

    //查看已答试卷 https://lms.ouchn.cn/api/exams/${examId}/submissions/${scoreId}
    let viewPageRes = await api.viewPage(examId, scoreId, cookieStr);

    if (collectType == '收集正确答案') {
        if (!viewPageRes.correct_answers_data.correct_answers) {
            await infoLogger(`${examLogStr}，试卷不提供答案，无法收集题目`);
            return false;
        }
        if (viewPageRes.correct_answers_data.correct_answers.length == 0) {
            await infoLogger(`${examLogStr}，试卷不提供答案，无法收集题目`);
            return false;
        }
    }

    if (collectType == '收集我的答案') {
        if (viewPageRes.score && viewPageRes.score < 70) {
            await infoLogger(`${examLogStr}，当前试卷分数过低${viewPageRes.score}，不予收集`, 'red');
            return;
        }
    }

    //收集题目
    let collectRes = await collectQuestionList(viewPageRes, courseName,collectType);
    let { collectNum, updateNum } = collectRes;
    await infoLogger(`${examLogStr}，题目收集完成，共收集${collectNum}道题目，共更新${updateNum}道题目`);
}

module.exports = collectExam;
