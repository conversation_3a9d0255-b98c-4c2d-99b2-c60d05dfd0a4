// 题库
let getAnswerFromLocal = require('./getAnswerFromLocal');
let getAnswerFromAiDian = require('./getAnswerFromAiDian/index.js');
let getPicAnswerFromAI = require('./getPicAnswerFromAI.js');
let getAnswerFromAI = require('./getAnswerFromAI.js');

async function solveQuestion(questionObj) {
    // 处理特殊情况：弘成平台这两个题型属于嵌套题，特殊处理
    if (questionObj.platform == '弘成教育') {
        if ('完形填空题阅读理解题'.includes(questionObj.type)) {
            return;
        }
    }

    if (questionObj.platform == '弘成教育' && questionObj.isSubQuestion) {
        return;
    }

    let answers;

    // 从本地词库查找答案
    answers = await getAnswerFromLocal(questionObj);

    try {
        // 本地题库搜不到，题目中带有图片
        if (!answers) {
            let optionStr = questionObj.options.join('');
            if (questionObj.content.includes('[URL]') || optionStr.includes('[URL]')) {
                // 如果题目带有图片，直接用AI回答 选择，判断，简答
                answers = await getPicAnswerFromAI(questionObj);
            }
        }
    } catch (error) {
        return;
    }

    // 本地题库搜不到，用爱点题库
    try {
        if (!answers) {
            answers = await getAnswerFromAiDian(questionObj);
        }
    } catch (error) {
        answers = '';
    }

    // 本地题库搜不到，用AI答题
    if (!answers) {
        answers = await getAnswerFromAI(questionObj);
    }

    return answers;
}

module.exports = solveQuestion;

if (false) {
    let questionObj = {
        id: 'id-mauhzh41-oxmvmi6a3sigleQuestionDiv_884682970',
        content: 'Plain Text下列叙述中，正确的是？',
        type: '多选题',
        options: ['A:声明变量时必须指定一个类型', 'B:java认为变量number与Number相同', 'C:Java中唯一的注释方式是"//"', 'D:源文件中public类可以有0或多个'],
        platform: '超星学习通',
        courseName: '2025第1学期Java语言程序设计期末考试',
    };

    solveQuestion(questionObj).then(r => console.log('最终答案', r));
}
