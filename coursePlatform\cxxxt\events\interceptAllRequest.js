module.exports = async mainPage => {
	mainPage.on('requestfinished', async request => {
		let url = request.url();
		const response = await request.response();

		//处理重定向
		if (response && response.status() >= 300 && response.status() <= 399) {
			return;
		}

		let resourceType = request.resourceType();
		if (resourceType == 'xhr') {
			let responseData = '';
			try {
				responseData = await response.json();
			} catch (e) {
				responseData = '解析失败';
			}
		}
		if (resourceType == 'document') {
			let responseData = '';
			try {
				responseData = await response.text();
			} catch (e) {
				responseData = '解析失败';
			}
		}

		// // 检查是否存在响应和响应的方法
		// if (response && response.request().method() == 'OPTIONS') {
		// 	return;
		// }

		// const contentType = response.headers()['content-type'];
		// //响应体默认值
		// let responseData = await response.text();
		// //如果响应体是json
		// if (contentType && contentType.includes('application/json')) {
		// 	responseData = await response.json();
		// }
		// 如果响应体是图片
		// if (contentType && contentType.includes('image')) {
		// 	responseData = await response.buffer();
		// }

	});
};
