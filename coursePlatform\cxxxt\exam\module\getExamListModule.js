let pageTools = require('../../../utils/pageTools.js');
async function getExamList(taskObj, mainPage, infoLogger) {
    let courseList;

    // 电脑端考试
    if (taskObj.others.includes('电脑端')) {
        //跳转到考试界面
        await mainPage.goto('https://mooc1-api.chaoxing.com/exam/test/examcode', {
            waitUntil: 'networkidle0',
        });

        // 跳转到 全部
        await mainPage.select('select[name="status"]', '-1');
        await new Promise(r => setTimeout(r, 2000));

        //获取考试列表
        courseList = await mainPage.evaluate(() => {
            function updateCourseStatus(courseObj) {
                // 确保格式正确
                if (!courseObj.startTime || !courseObj.endTime) {
                    courseObj.startTime = courseObj.examDate.split('至')[0].trim();
                    courseObj.endTime = courseObj.examDate.split('至')[1].trim();
                }

                // 获取当前时间
                let now = new Date();

                // 使用更安全的方式解析日期
                try {
                    // 解析开始时间
                    let startDate = new Date(courseObj.startTime);

                    // 解析结束时间
                    let endDate = new Date(courseObj.endTime);

                    // 检查日期是否有效
                    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                        // 如果日期解析失败，尝试更严格的格式解析
                        let parseCustomDate = function (dateStr) {
                            // 格式: '2025-02-01 00:00'
                            let parts = dateStr.split(' ');
                            if (parts.length !== 2) return null;

                            let dateParts = parts[0].split('-');
                            let timeParts = parts[1].split(':');

                            if (dateParts.length !== 3 || timeParts.length < 2) return null;

                            // 注意：月份从0开始，所以要减1
                            return new Date(Number(dateParts[0]), Number(dateParts[1]) - 1, Number(dateParts[2]), Number(timeParts[0]), Number(timeParts[1]));
                        };

                        startDate = parseCustomDate(courseObj.startTime);
                        endDate = parseCustomDate(courseObj.endTime);

                        if (!startDate || !endDate) {
                            // 如果还是解析失败，设置默认值
                            courseObj.isStart = null;
                            courseObj.isExpired = null;
                            return courseObj;
                        }
                    }
                    // 判断课程是否有效
                    courseObj.isValid = now >= startDate && now <= endDate;
                } catch (error) {
                    console.error('更新课程状态时出错:', error);
                    // 出错时设置默认值
                    courseObj.isStart = false;
                    courseObj.isExpired = false;
                }

                return courseObj;
            }

            let courseList = [];
            let courseTrList = document.querySelectorAll('body > div.dr_box > div > div.dr_table > table > tbody > tr[tabindex]');
            let keyArr = ['courseIndex', 'courseName', 'examDate', 'examDuration', 'examState', 'examScore', 'examType'];
            courseTrList.forEach(courseTr => {
                let courseObj = {};
                let tdList = courseTr.querySelectorAll('td');
                //遍历tdList
                for (let i = 0; i < tdList.length - 1; i++) {
                    let courseTd = tdList[i];
                    let key = keyArr[i];
                    courseObj[key] = courseTd.textContent.trim();
                }

                // 处理时间  "2025-02-01 00:00 至 2025-03-31 23:59"
                courseObj.startTime = courseObj.examDate.split('至')[0];
                courseObj.endTime = courseObj.examDate.split('至')[1];
                courseObj = updateCourseStatus(courseObj);

                //最后一个td特殊处理，分理处url和operation
                let lastTd = tdList[tdList.length - 1];
                let aTag = lastTd.querySelector('a'); //<a class="col_blue" href=>
                let onclickAttr = aTag.getAttribute('onclick'); //go('/exam=ans/mycourse/tran....')
                let examUrl = onclickAttr.match(/go\('(.*)'\)/)[1];
                courseObj.examUrl = location.origin + examUrl;
                courseObj.opration = aTag.textContent.trim();

                if (courseObj.isValid) {
                    courseList.push(courseObj);
                }
            });

            return courseList;
        });

        if (false) {
            courseList = [
                {
                    courseIndex: '1',
                    courseName: '安庆师范大学继续教育学院2025《写作》期末考试',
                    examDate: '2025-03-17 19:07 至 2025-06-10 23:59',
                    examDuration: '60',
                    examState: '已完成',
                    examScore: '92.5',
                    examType: '未设置终端',
                    startTime: '2025-03-17 19:07 ',
                    endTime: ' 2025-06-10 23:59',
                    isValid: true,
                    examUrl:
                        'https://mooc1-api.chaoxing.com/exam-ans/mycourse/transfer?moocId=*********&clazzid=*********&ut=s&refer=%2Fexam-ans%2Fexam%2Ftest%2Fexamcode%2Fexamnotes%3FcourseId%3D*********%26classId%3D*********%26examId%3D6742066%26nohead%3D0%26qbanksystem%3D1%26qbankbackurl%3D%252Fexam-ans%252Fexam%252Ftest%252Fexamcode%252Fexamlist%253Fedition%253D1%2526nohead%253D0%2526fid%253D',
                    opration: '查看',
                },
            ];
        }
    }

    // 手机端考试
    if (taskObj.others.includes('手机端')) {
        // 这个是通用地址，但是在考试的url里面没有user_id
        let listUrl = 'https://mooc1-api.chaoxing.com/exam-ans/exam/phone/examcode';
        // 根据学校名称选择不同的地址，这样会有完成地址
        switch (taskObj.schoolname) {
            case '湖南师范大学':
                listUrl = 'http://hunnucj.jxjy.chaoxing.com/ks/mobile/examSysList';
                break;
            case '长江大学':
                listUrl = 'https://yangtzeucj.jxjy.chaoxing.com/ks/mobile/examSysList';
                break;
            default:
                await infoLogger('获取课程列表url失败', 'red');
                throw new Error('获取课程列表url失败');
                break;
        }
        //跳转到考试界面
        await mainPage.goto(listUrl, { waitUntil: 'networkidle0' });

        //获取页面cookie
        let cookieStr = await pageTools.getPageCookies(mainPage);

        //获取考试列表
        courseList = await mainPage.evaluate(() => {
            let courseList = [];
            let cellList = document.querySelectorAll('.toexam_cell');
            for (let examTag of cellList) {
                let courseObj = {};

                // 获取课程url
                courseObj.examUrl = examTag.getAttribute('onclick').match(/toExam\('(.*)'\)/)[1];

                // 获取课程名称
                // <div class="zxks_text">仪器分析</div>
                courseObj.courseName = examTag.querySelector('.zxks_text').innerText;

                // 获取考试状态
                // <span class="colorBlue">开始考试</span>
                courseObj.courseState = examTag.querySelector('.colorBlue').innerText;
                courseList.push(courseObj);
            }
            return courseList;
        });

        if (false) {
            courseList = [
                {
                    examUrl:
                        'https://mooc1-api.chaoxing.com/exam/phone/task-exam?taskrefId=6547469&courseId=226495783&classId=114276401&userId=284968523&role=3&source=0&enc_task=&cpi=327328741&code=null&v=1#INNER',
                    courseName: '仪器分析',
                    courseState: '开始考试',
                },
            ];
        }
    }

    // 根据课程名称过滤课程
    courseList = courseList.filter(courseObj => courseObj.courseName.includes(taskObj.coursename));

    if (courseList.length == 0) {
        await infoLogger('考试科目为0', 'red');
        // throw new Error('考试科目为0');
        return [];
    } else {
        await infoLogger(`找到${courseList.length}个考试科目`);
    }

    return courseList;
}

module.exports = getExamList;
