let Model = require('../../../../../config/sequelize.config.js');
let { v4 } = require('uuid');
let api = require('../../../utils/api.js');
let path = require('path');
let fs = require('fs');

async function collectHomework(infoLogger, courseObj, cookieStr, examId, homeWorkLogStr, formatName, globalStore, baseFilePath) {
    await infoLogger('开始收集大作业');
    let userId = globalStore.userId;

    // 获取提交的数据对象
    let submissionsListRes = await api.submissionList(examId, userId, cookieStr);
    if (submissionsListRes.list.length == 0) {
        await infoLogger('作业还没开始做，无法收集', 'red');
        return;
    }
    let lastSubjectObj = submissionsListRes.list[0];

    // 判断文本类型
    if (lastSubjectObj.uploads.length == 0) {
        await infoLogger(`${homeWorkLogStr} 当前大作业类型为：文本类`);
        let answers = lastSubjectObj.comment;
        answers = answers
            .replace(/\s{2,}/g, ' ')
            .trim()
            .slice(0, 4900);
        // 尝试更新
        let [affectedRows] = await Model.bank.update(
            {
                answers: answers,
                answers_content: JSON.stringify([answers]),
                comment: '收集我的答案',
                add_time: new Date(),
            },
            {
                where: {
                    platform: '国家开放大学',
                    type: '国开大作业',
                    content: formatName,
                },
            }
        );
        if (affectedRows == 0) {
            await Model.bank.create({
                id: v4(),
                content: formatName,
                options: '[]',
                platform: '国家开放大学',
                type: '国开大作业',
                answers: answers,
                answers_content: JSON.stringify([answers]),
                course_name: courseObj.name,
                add_time: new Date(),
                comment: '收集我的答案',
            });
        }
        await infoLogger(`${homeWorkLogStr} 收集正确答案成功`, 'green');
        return;
    }

    // 上传类型
    if (lastSubjectObj.uploads.length > 0) {
        await infoLogger(`${homeWorkLogStr} 当前大作业类型为：上传类`);

        // // 获取本地存放文件目录
        // let basePath = `static/gkHomework/${globalStore.major}/${courseObj.name}-${courseObj.id}`;
        // let filePath = process.pkg ? path.join(path.dirname(process.execPath), basePath) : path.resolve(basePath);

        // 确保目标目录存在
        if (!fs.existsSync(baseFilePath)) {
            fs.mkdirSync(baseFilePath, { recursive: true });
        }

        let count = 0;
        for (let i = 0; i < lastSubjectObj.uploads.length; i++) {
            await infoLogger(`共有${lastSubjectObj.uploads.length}个文件，开始下载第${i + 1}个文件`);
            // 获取下载对象
            let uploadObj = lastSubjectObj.uploads[i];

            // 获取原始文件名
            // let rawFileName = uploadObj.name;
            // let ext = rawFileName.split('.').pop();
            let newFilePath = `${baseFilePath}/${uploadObj.name}`;

            if (fs.existsSync(newFilePath)) {
                await infoLogger(`${i + 1}/${lastSubjectObj.uploads.length} 文件已经存在，跳过`);
                continue;
            }

            // 拼接下载url
            let fileUrl = `https://lms.ouchn.cn/api/uploads/reference/${uploadObj.reference_id}/blob`;
            await infoLogger(`${homeWorkLogStr} 开始下载文件：${newFilePath}`);
            // 通过axios方式下载文件
            let downloadRes = await api.downloadFile(fileUrl, cookieStr);
            fs.writeFileSync(newFilePath, downloadRes);
            // // 等待3秒，等待下载完成
            await new Promise(r => setTimeout(r, 3 * 1000));

            // 判断是否下载成功
            if (!fs.existsSync(newFilePath)) {
                await infoLogger(`${homeWorkLogStr}-${i + 1}/${lastSubjectObj.uploads.length} 文件下载失败`, 'red');
                continue;
            }

            await infoLogger(`${homeWorkLogStr}-${i + 1}/${lastSubjectObj.uploads.length} 文件下载成功`, 'green');
            count++;
        }

        let [affectedRows] = await Model.bank.update(
            {
                answers: '上传类',
                answers_content: JSON.stringify(['上传类']),
                comment: '收集我的答案',
                add_time: new Date(),
            },
            {
                where: {
                    platform: '国家开放大学',
                    type: '国开大作业',
                    content: formatName,
                },
            }
        );
        if (affectedRows == 0) {
            await Model.bank.create({
                id: v4(),
                content: formatName,
                options: '[]',
                platform: '国家开放大学',
                type: '国开大作业',
                answers: '上传类',
                answers_content: JSON.stringify(['上传类']),
                course_name: courseObj.name,
                add_time: new Date(),
                comment: '收集我的答案',
            });
        }

        if (count != lastSubjectObj.uploads.length) {
            await infoLogger(`共有${lastSubjectObj.uploads.length}个文件，收集成功${count}个文件`, 'red');
        } else {
            await infoLogger(`共有${lastSubjectObj.uploads.length}个文件，收集成功${count}个文件`, 'green');
        }
    }
}
module.exports = collectHomework;
