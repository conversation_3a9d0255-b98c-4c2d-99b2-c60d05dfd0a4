let axiosIns = require('../../utils/axiosIns.js');
let { encryptData, decryptData, decodeRequestData, encodeRequestData } = require('./aesF.js');

let {Jimp} = require('jimp');

/**
 * ======================= 一、公共API =======================
 *
 * 1.获取页面数据，为API请求做准备
 */

//获取首页信息
exports.getDesktopInfo = async (schoolName, cookieStr) => {
    let config = {
        method: 'POST',
        url: `https://jw.wencaischool.net/${schoolName}_student/desktop.action?req=getDesktopInfo`,
        headers: {
            cookie: cookieStr,
        },
    };
    let desktopInfoRes = await axiosIns(config);
    let debugData = decryptData(desktopInfoRes.data);
    desktopInfoRes.debugData = JSON.parse(debugData);
    return desktopInfoRes;
};
if (false) {
    res = {
        success: true,
        code: 1001,
        message: '帐号不能为空！',
        totalPage: 0,
        totalCount: 0,
    };
    desktopInfoRes = {
        data: '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',
        debugData: {
            studentId: '69340',
            cid: '341122199711270423',
            studentNo: '2023313355',
            studentName: '孙悦',
            enrollCode: '20230110069340',
            courseTotalCount: 0,
            coursePassCount: 0,
            userExper: 0,
            shareCode: '',
            customerService:
                '<div class="lineA">\n <span style="margin-left:20px;">华东地区客服群:605597439</span><a target="_blank" href="https://qm.qq.com/cgi-bin/qm/qr?k=k8eQN7PxBJmVAdD9jR2Q9d9dCvH6eKNn&jump_from=webapi&authKey=NLrhwt2lPRUXuwvoDOqd2/h1SZxoZ4EHIR0SKMoA2Zz5jZ3GYMovcOL0FY9t+NWp"><img border="0" src="//pub.idqqimg.com/wpa/images/group.png" alt="华东地区客服群" title="华东地区客服群"></a></div>\n<div class="lineA">\n <span style="margin-left:20px;">客服电话：</span><a href="tel:4009025686" class="telNum">4009025686</a></div>',
            userHeadUrl: 'http://jw.wencaischool.net/hfgy_student/console/images/face_default.png',
            schoolCode: '10359',
            gradeCode: '20230',
            learningUserId: '_openlearning_1347465',
            siteInfo: '',
            isVip: false,
            energyNum: 100,
            isShowEnergyInfo: true,
            isShowPayForEnergy: false,
            isShowPayMeg: false,
            isShowAnnualPayment: false,
            isShowTermReport: false,
            hibernateStatus: '0',
            vipType: 0,
            documentTips: '',
            url: '/files/student_photo/nophoto_l.jpg',
            isJF: 0,
            isShowApplyEnergy: true,
            isShowInfoAcquisition: false,
            collectionStatus: '-1',
            siteName: '滁州函授站',
            collectionStatusHFGY: '-1',
            isShowInfoAcquisitionHFGY: false,
        },
        success: true,
        code: 1000,
        message: '获取成功！',
        totalPage: 0,
        totalCount: 0,
    };
}

// fetch("https://jw.wencaischool.net/hfgy_student/desktop.action?req=getDesktopInfo", {
//     headers: {
//         accept: "application/json, text/javascript, */*; q=0.01",
//         "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
//         "sec-ch-ua": '"Not/A)Brand";v="8", "Chromium";v="126", "Google Chrome";v="126"',
//         "sec-ch-ua-mobile": "?0",
//         "sec-ch-ua-platform": '"Windows"',
//         "sec-fetch-dest": "empty",
//         "sec-fetch-mode": "cors",
//         "sec-fetch-site": "same-origin",
//         "x-requested-with": "XMLHttpRequest",
//         cookie: 'login_url="http://jw.wencaischool.net/hfgy/console"; hfgy_skin=; hfgy_student_skin=; bbjj_student_skin=; Hm_lvt_0972607132d5893bc21b477c27fd3708=1717208834; logo_src=http%3A//jw.wencaischool.net/hfgy/console/images/roll_form_header.png; hfgy_COOKIE=%26user%5fid%3d688%26login%5fname%3dtc001%26user%5fname%3d%e5%a4%a9%e9%95%bf%26role%5fid%3d82%26site%5fcode%3d13%26hand%5fcode%3d00%26agent%5fcode%3d%26class%5fteacher%5fid%3d%26check%5fmsg%3dzQdR5D00fBz%2bdSvpuKCQKw%253D%253D; acw_tc=76b20fee17192183210604308e7052f5e14de338e54281b8bc6e75551013d5; login_name=4UPQH/RHuVSAJPPCTMbzKA%3D%3D; login_pwd=G2SohQkuIfhunaT2dkwjrw%3D%3D; hfgy_student_COOKIE=%26learning%5fuser%5fid%3d%5fopenlearning%5f431452773673861122%26third%5fparty%5fid%3d10359%5f20240%5f640322198610102726%26organization%5fid%3d431452524568903682%26school%5fclass%5fcode%3d2024%e5%b7%a5%e5%95%86%e5%8d%87%e5%87%bd%e6%8e%88%e5%a4%a9%e9%95%bf%26school%5fcode%3d10359%26grade%5fcode%3d20240%26learning%5flogin%5fname%3dhfgy%5f2024313082%26user%5fid%3d417265358531985443%26login%5furl%3dhttp%3a%2f%2fjw%2ewencaischool%2enet%2fhfgy%2fconsole%26login%5fname%3d2024313082%26user%5fname%3d%e5%90%b4%e5%a8%9f%26enroll%5fcode%3d417265358531985444%26student%5fno%3d2024313082%26student%5fcode%3d417265358531985443%26user%5ftype%3dstudent%26password%3dNMWCXT%4010102726; un=rcoU1PDHtXLesaKaXMNEqQ%3D%3D; ln=4UPQH/RHuVSAJPPCTMbzKA%3D%3D',
//         Referer: "https://jw.wencaischool.net/hfgy_student/console/templates/normal/",
//         "Referrer-Policy": "strict-origin-when-cross-origin",
//     },
//     body: null,
//     method: "POST",
// });

/**
 * ======================= 一、课程API =======================
 *
 * 1.获取页面数据，为API请求做准备
 */

//获取学期
exports.getTerm = async (schoolName, cookieStr) => {
    let getTermRes = await axiosIns({
        method: 'post',
        url: `https://jw.wencaischool.net/${schoolName}_student/student_learn.action?req=getTerm`,
        headers: {
            cookie: cookieStr,
        },
    });
    return getTermRes;
};
if (false) {
    getTermRes = {
        data: 'B/fEC78w/7QSI/JW9LvbB1/4tRm7JTX9ZyJtqkNlR8DKXZkWvf3SZzzHaASNTAXY+OTARAWDX2ppoxu0B3eN4Uoe/2UplCtVIK13gZmmBo+hlVtkydbX31KcALuvQ0MQNCaswYiPMpZ6whGgK+LW5lPio/+efKhRcLp+rdQrV0P/HvYJShcfeqyq1UQQwyIv5MwGK/iYnapsa6HHLtAhkQ==',
        debugData: [
            {
                term: 1,
                termCode: '20230',
                isCurrentTerm: false,
            },
            {
                term: 2,
                termCode: '20231',
                isCurrentTerm: false,
            },
            {
                term: 3,
                termCode: '20240',
                isCurrentTerm: true,
            },
        ],
        success: true,
        code: 1000,
        message: '获取成功！',
        totalPage: 0,
        totalCount: 0,
    };
}

//获取课程列表
exports.getStudentLearnInfo = async (schoolName, cookieStr, termCode) => {
    // termCode = encryptData(termCode);
    let dataObj = {
        term_code: termCode,
    };
    let dataStr = encodeRequestData(dataObj);
    let config = {
        method: 'post',
        url: `http://jw.wencaischool.net/${schoolName}_student/student_learn.action?req=getStudentLearnInfo`,
        headers: {
            cookie: cookieStr,
        },
        data: dataStr,
    };
    let getStudentLearnInforRes = await axiosIns(config);
    let debugData = decryptData(getStudentLearnInforRes.data);
    getStudentLearnInforRes.debugData = JSON.parse(debugData);

    return getStudentLearnInforRes;
};
if (false) {
    getStudentLearnInforRes.debugData = {
        courseTotalCount: 8,
        XSKSCJ: true,
        passCourseCount: 0,
        noPassCourseCount: 8,
        CJWRXZPFKXX: false,
        XXXSCJXS: true,
        courseInfoList: [
            {
                term: 3,
                courseName: '财务管理学',
                courseCode: '0148',
                isPossibleLearn: true,
                message: '开始学习',
                isPass: false,
                filePath:
                    'https://learning.wencaischool.net/openlearning/netedu_login.jsp?user_name=陈冬&login_name=2023313168&password=NMWCXT%4011010449&school_code=10359&grade_code=20230&course_id=42062&course_code=0148',
                onlineScore: '0',
                onlineTotalScore: 100,
                progressScore: '0',
                progressTotalScore: 25,
                testScore: '0',
                testTotalScore: 20,
                bbsScore: '0',
                bbsTotalScore: 0,
                ispaceScore: '0',
                ispaceTotalScore: 5,
                meetingScore: '0',
                meetingTotalScore: 0,
                examScore: '0',
                examTotalScore: 50,
                videoFinishLearningCount: 0,
                videTotalCount: 31,
                workFinishCount: 0,
                workTotalCount: 3,
                progress: 0,
                isCore: 0,
                isDetailCore: 0,
                courseId: '350194754100658177',
                schoolId: '3663',
                studentNo: '2023313168',
                studentPwd: 'NMWCXT%4011010449',
                learningUrl: 'http://learning.wencaischool.net/openlearning',
                isRequire: '1',
                userInfoVo: {
                    userType: 'student',
                    studentNo: '2023313168',
                    studentName: '陈冬',
                    enrollCode: '20230110069153',
                    password: 'NMWCXT%4011010449',
                    majorCode: 'C1001',
                    enterBatch: '0',
                    schoolCode: '10359',
                    gradeCode: '20230',
                    classCode: '230400C10011',
                    courseId: '350194754100658177',
                    courseCode: '0148',
                    courseName: '财务管理学',
                    courseTerm: '3',
                    typeCode: 'progress',
                    accessType: 'web',
                    learningUrl: 'http://learning.wencaischool.net/openlearning',
                    isBookingExam: 0,
                },
            },
        ],
        XSXSKCSZ: false,
    };
}

//获取课程视频章节列表
exports.getCourseScormItemList = async (dataObj, cookieStr) => {
    let dataStr = encodeRequestData(dataObj);
    let CourseScormItemListRes = await axiosIns({
        method: 'post',
        url: 'https://learning.wencaischool.net/openlearning/newApp_learn_course.action?req=getCourseScormItemList',
        headers: {
            cookie: cookieStr,
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        },
        data: dataStr,
    });
    return CourseScormItemListRes;
};
if (false) {
    CourseScormItemListRes = {
        debugData: {
            sLastPlayLessonId: -1,
            courseName: '材料力学',
            needUpdate: false,
            courseQuestionnaireInfo: { code: 2000, message: '开关未开启', isOpen: false, canDiss: false },
            listCourseLesson: [
                {
                    hasDetail: false,
                    isFinish: false,
                    videoPosition: 0,
                    lastPotition: 0,
                    finishLen: 0,
                    lessonFormat: 1,
                    lessonId: '350189604430675974',
                    lessonName: '第一章 绪论',
                    timeLen: 2055,
                    lessonType: 'scorm_content_folder', //说明是目录，不是具体课件
                    isChapter: true,
                    playUrl: '',
                    minTime: 60,
                    chapterName: '第一章 绪论',
                    useEnergyNum: 0,
                    allFinish: false,
                    isFree: true,
                },
                {
                    hasDetail: false,
                    isFinish: false,
                    videoPosition: 0,
                    lastPotition: 0,
                    finishLen: 0,
                    lessonFormat: 1,
                    lessonId: '435502137560006663',
                    lessonName: '1.1.1前言',
                    timeLen: 334,
                    lessonType: 'scorm_content', //说明是课件
                    courseId: '305616996477698051',
                    courseName: '形势与政策',
                    isChapter: false,
                    playUrl: 'http://cnet-s7.wencaischool.net/ispace2_upload7/131/2023-07-04/29693/content/ch010101/HD1-1-1.mp4',
                    minTime: 334,
                    chapterName: '第1章:当前世界经济形势与前景分析（上）',
                    useEnergyNum: 0,
                    allFinish: false,
                    isFree: true,
                },
            ],
        },
        success: true,
        code: 1000,
        message: '请求成功',
        totalPage: 0,
        totalCount: 0,
    };
}

//修改视频进度
exports.submitScormAndHistorySave = async (submitScormAndHistorySaveData, cookieStr) => {
    let dataStr = encodeRequestData(submitScormAndHistorySaveData);
    let submitScormAndHistorySaveRes = await axiosIns({
        method: 'post',
        url: 'https://learning.wencaischool.net/openlearning/learning.action?req=submitScormAndHistorySave',
        headers: {
            cookie: cookieStr,
        },
        data: dataStr,
    });
    return submitScormAndHistorySaveRes;
};
if (false) {
    res = {
        data: '3XTVUcLamHLJDNB8lgIDVtbzjaI0pDN4A2PtUAsEqcnJAbdQYbEN3H9Ww17OyqPVtUapk3Jk+GBLwNXcChFX6UyqPKM1YuZwkT3UGVX9Jrn2HwqyNlDgV+/wFbbmxnk0iJRSwGNzOq1byxRU5SJ/g8BMmp+1vxDeCT0qiTqVwgzAcK9wGh1ckR/MNmQsiej73w7Q0Tsu/C93Ak05mYlDsjeRHwPMBjqrf/dOW40L6j5+VypEVLOFwKXJpPmVYotbCxkD1UjjRleX4MX4fKWBxA==',
        debugData: {
            hasDetail: false,
            isFinish: false,
            videoPosition: 142,
            lastPotition: 0,
            finishLen: 143,
            lessonFormat: 1,
            timeLen: 759,
            isChapter: false,
            minTime: 1200,
            useEnergyNum: 0,
            allFinish: false,
            isFree: true,
        },
        success: true,
        code: 1000,
        message: '操作成功，保存记录成功',
        totalPage: 0,
        totalCount: 0,
    };
}

exports.getAssignmentList = async (courseId, cookie) => {
    let assignmentInfoRes = await axiosIns({
        url: `https://learning.wencaischool.net/openlearning/course/learning/learn_homework.jsp?is_site=0&course_id=${courseId}`,
        headers: {
            cookie,
        },
        method: 'GET',
    });
    return assignmentInfoRes;
};
if (false) {
    assignmentList = [
        { ExamId: '1219154', ContentId: '20925129' },
        { ExamId: '1219155', ContentId: '20926228' },
        { ExamId: '1219156', ContentId: '20926229' },
    ];
}

//提交作业
exports.submitHomework = async (cookieStr, assignmentObj, answerStr) => {
    let config = {
        url: `https://learning.wencaischool.net/openlearning/servlet/com.lemon.web.ActionServlet?handler=com%2elemon%2elearning%2eexam%2eStudentExamAction&op=submit_exam&exam_id=${assignmentObj.ExamId}&b_out=1&item_id=&type=work&_no_html=1&r=0%2e9935575858240004&57546`,
        method: 'POST',
        headers: {
            'content-type': 'application/x-www-form-urlencoded;',
            cookie: cookieStr,
        },
        data: answerStr,
    };
    let homeworkRes = await axiosIns(config);
    return homeworkRes;
};

// fetch(
//     "https://learning.wencaischool.net/openlearning/servlet/com.lemon.web.ActionServlet?handler=com%2elemon%2elearning%2eexam%2eStudentExamAction&op=submit_exam&exam_id=451125703872610309&b_out=1&item_id=&type=work&_no_html=1&r=0%2e09559537216593483&77465",
//     {
//         headers: {
//             accept: "*/*",
//             "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
//             "content-type": "application/x-www-form-urlencoded;",
//             "sec-ch-ua": '"Google Chrome";v="125", "Chromium";v="125", "Not.A/Brand";v="24"',
//             "sec-ch-ua-mobile": "?0",
//             "sec-ch-ua-platform": '"Windows"',
//             "sec-fetch-dest": "empty",
//             "sec-fetch-mode": "cors",
//             "sec-fetch-site": "same-origin",
//             cookie: "JSESSIONID=86CB0CD2BD5ACF44B0D4BAE45F1ACA35; ispace_web_id=ispace2; acw_tc=0a47314717174117683423198e005aaa61ef21427c1f4a8385d0e79a008b78; openlearning_COOKIE=%26user%5fid%3d%5fopenlearning%5f451125708167577628%26login%5fname%3dbbjj%5f%e6%88%9024503014%26user%5fname%3d%e5%bd%ad%e8%89%af%e7%8e%89%26user%5ftype%3dstudent%26school%5fmajor%5fid%3d451125648032792576%26school%5fclass%5fcode%3d240000C20314%26manage%3d%26default%5findex%3d%26third%5fpart%5fid%3d200121%5f20240%5f341103199110182621%26user%5fschool%5fcode%3d200121%26portal%5furl%3d%26cur%5fschool%5fcode%3d200121%26cur%5fgrade%5fcode%3d20240%26login%5fflag%3dYCRNIWobb3%26client%5fip%3d100%2e121%2e137%2e226%26is%5fsite%3d0%26cur%5fcourse%5fcode%3d0303%26course%5f451125699577643010%5frole%3d2%26course%5f451125699577643010%5fgrade%3d0%26exam%5fcorrect%3d",
//             Referer:
//                 "https://learning.wencaischool.net/openlearning/console/?urlto=https%3a%2f%2flearning%2ewencaischool%2enet%2fopenlearning%2fcourse%2flearning%2flearn%5fnotice%2ejsp%3fcourse%5fid%3d451125699577643010&0.014800744601543903",
//             "Referrer-Policy": "strict-origin-when-cross-origin",
//         },
//         body: "lemonysoft_item_q4a1myjr4x_34=A",
//         method: "POST",
//     }
// );

/**
 * ======================= 三、考试API =======================
 *
 * 1.获取页面数据，为API请求做准备
 */

exports.courseExamAction = async (courseExamActionData, cookieStr) => {
    let dataStr = encodeRequestData(courseExamActionData);
    let courseExamActionRes = await axiosIns({
        method: 'POST',
        url: 'http://jw.wencaischool.net/hfgy_student/course_exam.action',
        headers: {
            cookie: cookieStr,
        },
        data: dataStr,
    });
    return courseExamActionRes;
};
if (false) {
    courseExamActionRes = {
        data: '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',
        debugData: [
            {
                courseCode: '0237',
                courseName: '管理统计学',
                timeInfo: '2024-03-23 12:30 至 2024-03-23 20:30',
                timeRemark: '考试起止时间：',
                makeUpRemark: '',
                isFormal: false,
                examName: '补考',
                examNameColor: '#D84444',
                remarkTitle: '',
                remarkContent: '',
                courseExamStatus: 5,
                courseExamStatusName: '未参加',
                statusColor: '#AAAAAA',
                isJoinExam: 1,
                userInfoVo: {
                    userType: 'student',
                    studentNo: '2023313170',
                    studentName: '陈伟',
                    enrollCode: '20230110069155',
                    password: 'NMWCXT@04026814',
                    majorCode: 'C1001',
                    enterBatch: '0',
                    schoolCode: '10359',
                    gradeCode: '20230',
                    classCode: '230400C10011',
                    courseId: '350190849970143239',
                    courseCode: '0237',
                    courseName: '管理统计学',
                    courseTerm: '2',
                    typeCode: 'exam',
                    accessType: 'web',
                    learningUrl: 'http://learning.wencaischool.net/openlearning',
                    isBookingExam: 0,
                    gender: '男',
                    cid: '341124200104026814',
                    photoUrl: '/files2015/upload/hfgy/student/data/current/1/341124200104026814.jpg',
                },
                isNeedPay: 0,
                tipsInfo: '',
                maintainInfo: '每日20:30 ~ 次日12:30考试系统维护，无法进入考试',
                makeUpExamPayment: 0,
            },
            {
                courseCode: '0379',
                courseName: '大学英语4',
                timeInfo: '2024-03-24 12:30 至 2024-03-24 20:30',
                timeRemark: '考试起止时间：',
                makeUpRemark: '',
                isFormal: false,
                examName: '',
                examNameColor: '#AAAAAA',
                remarkTitle: '',
                remarkContent: '',
                courseExamStatus: 4,
                courseExamStatusName: '不在考试时间',
                statusColor: '#AAAAAA',
                isJoinExam: 0,
                userInfoVo: {
                    userType: 'student',
                    studentNo: '2023313170',
                    studentName: '陈伟',
                    enrollCode: '20230110069155',
                    password: 'NMWCXT@04026814',
                    majorCode: 'C1001',
                    enterBatch: '0',
                    schoolCode: '10359',
                    gradeCode: '20230',
                    classCode: '230400C10011',
                    courseId: '350189621610545189',
                    courseCode: '0379',
                    courseName: '大学英语4',
                    courseTerm: '2',
                    typeCode: 'exam',
                    accessType: 'web',
                    learningUrl: 'http://learning.wencaischool.net/openlearning',
                    isBookingExam: 0,
                    gender: '男',
                    cid: '341124200104026814',
                    photoUrl: '/files2015/upload/hfgy/student/data/current/1/341124200104026814.jpg',
                },
                isNeedPay: 0,
                tipsInfo: '',
                maintainInfo: '每日20:30 ~ 次日12:30考试系统维护，无法进入考试',
                makeUpExamPayment: 0,
            },
            {
                courseCode: '0236',
                courseName: '管理学原理',
                timeInfo: '2024-03-22 12:30 至 2024-03-22 20:30',
                timeRemark: '考试起止时间：',
                makeUpRemark: '',
                isFormal: false,
                examName: '',
                examNameColor: '#AAAAAA',
                remarkTitle: '',
                remarkContent: '',
                courseExamStatus: 11,
                courseExamStatusName: '缺考',
                statusColor: '#D84444',
                isJoinExam: 1,
                userInfoVo: {
                    userType: 'student',
                    studentNo: '2023313170',
                    studentName: '陈伟',
                    enrollCode: '20230110069155',
                    password: 'NMWCXT@04026814',
                    majorCode: 'C1001',
                    enterBatch: '0',
                    schoolCode: '10359',
                    gradeCode: '20230',
                    classCode: '230400C10011',
                    courseId: '350190862855045121',
                    courseCode: '0236',
                    courseName: '管理学原理',
                    courseTerm: '2',
                    typeCode: 'exam',
                    accessType: 'web',
                    learningUrl: 'http://learning.wencaischool.net/openlearning',
                    isBookingExam: 0,
                    gender: '男',
                    cid: '341124200104026814',
                    photoUrl: '/files2015/upload/hfgy/student/data/current/1/341124200104026814.jpg',
                },
                isNeedPay: 0,
                tipsInfo: '',
                maintainInfo: '每日20:30 ~ 次日12:30考试系统维护，无法进入考试',
                makeUpExamPayment: 0,
            },
        ],
        success: true,
        code: 1000,
        message: '执行成功！',
        totalPage: 1,
        totalCount: 4,
    };
}

exports.examInfo = async (examModuleDate, examUrl) => {
    let examModuleRes = await axiosIns({
        method: 'POST',
        url: 'https://learning.wencaischool.net/openlearning/examModular_exam_info.action',
        headers: {
            Referer: examUrl,
        },
        data: encodeRequestData(examModuleDate),
    });
    return examModuleRes;
};
if (false) {
    examModuleRes = {
        data: '0NnUGGKKHGkIOD8LGPeKE/G59u8Pmlp7JZD70LRD/tqRlUQcLMMl5FTw/8Le6WW98cQsR2hV3x7zPp7HpG4+7bjY48YMdcMGjEvL2wm7GXXuc4GgZFb8F+CQkJp48IPHTeCNo8I8rfjlPRitgeh69ftd/3TWrH+KjSWtlWtgJC6Wbb1dSgupjkGTtBDgx+fd3Ah7GtYPK0rkUaOBOaYH99710P9SdaKQbIRkEm9xlPkjKeF4+Z6EGkRdIX3ADS3GSwyZGB1jd1I5f4FfAS1lUAWW5VLpGGGHBbBmi0g+4nYg3wDAtMWc4WEW3agF6Eou6hmPn4u/F5W1klVVVZRBEpE7iIuqBxhrWHqRL/0fHNF79PebAPL2lbn3slMkQP9rFYnmzAbyvt+ZYdLyXYNjujX9hYJ7l+1SB/I3C+OsLs+2i9VbmO/IxCNCEACZMPjpHgHJq2tPb9wXSz0xqBpIT8f+NmCcQhb6Ej5Rz7d6+hAZbTaBiysFPm+r65ncz3cjFzh5+/XQnfBFYZ6Jncmw/hZRpX78/e+tWuvbD0AswLvfO68khcLIDla6Ck0IOVuYK+BhKCGfLcVo0wrWEtFudi44sDVI3MmXrPkQ4Fy5qhzdUdEtsUnlKOcoouV4XhyeXkvWgVHWR9q+KPYGKKFwjy4tqQfarDG1H9irrJthqWWRDS73HtI7C0Vq0lWr5fsOjRaMK7TlYtq4iS09TnTPYsqhKUBrQrjpdhS6Bq5WLUNK2Di4NGo4nMY8mgh85t0bUVNAuZF3GDl9ZIVgFbjRUCUgXDytXYRKLedmhuNdJMXicWaXDIlJQnJZrBP+lW8xNGtotj/xcIgTaYzO2IaFCJk94BlGbkY2aC2w3jwGjjbrAKJF4CO/dZBSfnVyayvv/+w0aFvdxbWem5geDMmES60iV73OlpacogAfxEEoEPalE9QZfQqQ/L7vDPV5488Y0ST/jD9ffYpbkumIWdS92AFNuj5L3WIEfcUwqcMceRyWvJ4kl9mj1y3q8vXih+Y5NqU+PPPHDO0YFIavXLiWRvffHnJwVm9i4B6Nd9odOvXh4WJ3YkOicEqe5mED+rQ/9a8DmmWXSn/RZz3EpCh582MGveSoXuqDnv4w2XSedAnJ5UlJn5333WfrdbWh6wCTs2xqIdutW9YTiRLzkFZnd0i2Pc/26dTGFZ9KcMiPjmL7/x6ssBaO0tWhxubbTyhaIhe9JODevwRQWfBM4ei2Lnp5GWQhy8GYlNyeeeJp4WSazFoa4Lc+A9DHel4tC0tSuRNzll5lCqamOL0ZdsjkwnIxJyAFzjutyinQE1Z9E0sMvF8VNxfJSCVU27DV34xNnVKUtY/jSO6FaiPBNukMYzCChLSNEGji6X8VxHIK5psCyynPevFY6UU3GmISbbs8FJHCbC8VcV+K1kpUbS7iWMzNRvU+A/BbRad8AmzE1QzdZej9TO8afNiuf3WYDQNQwRBkROLkEUXq0BHKgBKc92YUcpISiQLzepFpAx0iFZj1V0SnxzrQAHOC6JLOQBPG',
        success: true,
        code: 1000,
        message: '获取成功',
        totalPage: 0,
        totalCount: 0,
    };
    let debugData = decodeRequestData(examModuleRes.data);
    debugData = [
        {
            scoreId: '',
            examId: '406929794606825472',
            examName: '线上期末考试试卷',
            courseId: '350190849970143239',
            courseName: '管理统计学',
            courseCode: '0237',
            timeRemark: '考试起止时间：',
            timeInfo:
                '2024-03-23 12:30 ~ 2024-03-23 20:30\r\n\r\n*每日20:30 ~ 次日12:30考试系统维护,无法进入考试,请合理安排考试时间,考试成绩将在本次考试全部结束后更新到总评。',
            minutes: '90',
            totalScore: 100.0,
            reexaminationTime: 3,
            restExamTime: 3,
            isAvailable: true,
            beginTime: '暂无',
            submitTime: '暂无',
            finalScore: '0',
            topScore: '0',
            isShowExamScore: false,
            remark: '未参加',
            useEnergyNum: 0,
            makeUpTitle: '补考说明',
            makeUpRemark:
                '补考标准：该课程教务总评成绩为0.0分，按照学校要求，教务总评成绩不满60分需要参加补考。\r\n成绩说明：本次补考成绩将直接计入总评，高于等于60分合格，低于60分不合格。',
            isPass: 5,
            buttonType: 0,
            buttonName: '补考',
            buttonColor: '#9D0D0D',
            buttonTips: '',
            isCloseViewExam: true,
            isSupportViewAnswer: false,
            isShowTopScore: false,
            topRemark: '未参加',
            nStatus: -1,
            isMaintain: false,
        },
    ];
}

// 获取考试人脸识别二维码
exports.getFaceCheckPic = async faceCheckPostObj => {
    let faceCheckRes = await axiosIns({
        method: 'POST',
        url: 'https://learning.wencaischool.net/openlearning/examModular_face_recognition.action',
        data: encodeRequestData(faceCheckPostObj),
    });
    return faceCheckRes;
};

// 获取考试人脸识别结果
exports.getFaceCheckResult = async faceCheckPostObj => {
    let faceCheckRes = await axiosIns({
        method: 'POST',
        url: 'https://learning.wencaischool.net/openlearning/examModular_face_recognition.action?req=checkFaceRecognitionExam',
        data: encodeRequestData(faceCheckPostObj),
    });
    return faceCheckRes;
};
if (false) {
    getFaceCheckResult = {
        success: true,
        code: 2000,
        message: '缺少考试id',
        totalPage: 0,
        totalCount: 0,
    };
    getFaceCheckResult = {
        success: true,
        code: 1001,
        message: '暂未人脸识别或识别认证失败',
        totalPage: 0,
        totalCount: 0,
    };
}

// 获取官方人脸识别照片
exports.getOfficialFacePic = async (url) => {
    try {
        // 获取图片的二进制数据
        let response = await axiosIns.get(url, { responseType: 'arraybuffer' });
        let imageBuffer = Buffer.from(response, 'binary');

        // 使用 Jimp 处理图片：先放大，再裁剪
        let image = await Jimp.read(imageBuffer);

        // 使用裁剪功能
        image.crop({ x: 10, y: 0, w: 295, h: 640 });


        // 获取处理后的图片 Buffer
        let resizedBuffer = await image.getBuffer("image/jpeg", { quality: 50 });

        // 将处理后的图片转换为 Base64
        let base64Image = resizedBuffer.toString('base64');

        // 返回完整的 Base64 图片数据 (JPEG 格式)
        return base64Image;
    } catch (error) {
        throw error; // 抛出错误，以便调用者可以处理
    }
};

// 进行考试人脸识别
exports.faceRecognitionExam = async postdataStr => {
    let faceCheckRes = await axiosIns({
        method: 'POST',
        url: 'https://learning.wencaischool.net/openlearning/newApp_face_recognition.action?req=faceRecognitionExam',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
            'Content-Length': postdataStr.length,
            Host: 'learning.wencaischool.net',
            Connection: 'Keep-Alive',
            'Accept-Encoding': 'gzip',
            'User-Agent': 'okhttp/4.9.0',
        },
        data: postdataStr,
    });
    return faceCheckRes;
};
if (false) {
    faceRecognitionExam = {
        success: true,
        code: 1000,
        message: '人脸识别成功',
        totalPage: 0,
        totalCount: 0,
    };
}

//获取考试题目列表
exports.getItemList = async getExamListDataObj => {
    let getExamListDataStr = encodeRequestData(getExamListDataObj);
    let getExamListRes = await axiosIns({
        method: 'POST',
        url: 'https://learning.wencaischool.net/openlearning/examModular_exam_item.action?req=getItemList',
        data: getExamListDataStr,
    });
    return getExamListRes;
};
if (false) {
    // 没有进行考前承诺就会提示“无题库数据”
    let getExamListRe1s = {
        success: true,
        code: 1002,
        message: '无题库数据',
        totalPage: 0,
        totalCount: 0,
    };
    let getExamListRes2 = {
        success: true,
        code: 1003,
        message: '异常！',
        totalPage: 0,
        debugMsg: 'Exception:class java.lang.IllegalStateException,Expected BEGIN_OBJECT but was STRING at line 1 column 1 path $',
        totalCount: 0,
    };
    let getExamListRes3 = {
        data: '加密数据',
        success: true,
        code: 1000,
        message: '操作成功',
        totalPage: 0,
        totalCount: 0,
    };
    let debugData = {
        examScoreId: '437075109207867392',
        LastItemNo: 1,
        itemInfoList: [
            {
                itemNo: 47,
                itemId: '539326864497311745',
                examScoreDetailId: '544268241555423270',
                itemName: '公司创业与个体创业的差异：___(1)___、___(2)___、___(3)___',
                optionNodes: [],
                itemAnswer: [
                    {
                        option: '(1)',
                        optionContent: '初始条件',
                        myOption: '',
                        smallStatus: 0,
                        itemStatus: 0,
                        score: '0.0',
                        myOptionKey: 'lemonysoft_item_key_14341289_29241',
                    },
                    {
                        option: '(2)',
                        optionContent: '报酬与风险',
                        myOption: '',
                        smallStatus: 0,
                        itemStatus: 0,
                        score: '0.0',
                        myOptionKey: 'lemonysoft_item_key_14341289_9689',
                    },
                    {
                        option: '(3)',
                        optionContent: '独立性',
                        myOption: '',
                        smallStatus: 0,
                        itemStatus: 0,
                        score: '0.0',
                        myOptionKey: 'lemonysoft_item_key_14341289_3060',
                    },
                ],
                itemType: '1',
                itemRemark: '',
            },
            {
                itemNo: 17,
                itemId: '539326770005934081',
                examScoreDetailId: '544268241555423264',
                itemName: '特“色”吧的服务时间是',
                optionNodes: [
                    { option: 'A', optionNo: '0', optionContent: '周一至周五' },
                    { option: 'B', optionNo: '1', optionContent: '周一至周六' },
                    { option: 'C', optionNo: '2', optionContent: '周一至周日' },
                    { option: 'D', optionNo: '3', optionContent: '周六至周日' },
                ],
                itemAnswer: [
                    {
                        option: '1',
                        optionContent: 'C',
                        myOption: '',
                        smallStatus: 0,
                        itemStatus: 0,
                        score: '0.0',
                        myOptionKey: 'lemonysoft_item_qw0pd7w0oa_41',
                    },
                ],
                itemType: '3',
                itemRemark: '',
            },
        ],
    };
}

//进行考前承诺
exports.examModularExamItemAction = async examModularExamItemObj => {
    let data = encodeRequestData(examModularExamItemObj);
    let examModularExamItemRes = await axiosIns({
        method: 'POST',
        url: 'https://learning.wencaischool.net/openlearning/examModular_exam_item.action',
        data,
    });
    return examModularExamItemRes;
};
if (false) {
    examModularExamItemRes = {
        data: 'mtf09DD/RqJaDeIsoaCnJ0HesvIRnnemSRitQWlK3fjOiOqYCyaDBHcjtp7Q9/sd2KrDZjy3Sh/F07AAcPXpd/HX95KWevW73rRtD2Cf67XyiAxP8uGWnR7Y/L0eUxbARayFqTZKjcL93Wfc6Vznpyu6cBZe2/ORi6fIpgA1qBAV1FqdLR8una4z24id/Gxwth1MZtua7B9hJkJYReiDhlLhUUgbMl6h0FDQGDl+Sl68aIce3AVdXsLGIAQpZsch+jwB9ELAAryk2m5+oUrPFESUfDS/xakqvRCNdPA4JBqw0KRR3d+zT3pMZ9SNbHoGa3mf6xV30n2uw0+tHkP0489KwZQ/RDfb3aavBP1dEofFnHhuk6n/4KcbYA65bI3rYNXCoQlkCZ6Kgg30cAvdE1ui66sFedF3t+DFiKr4rtDn1M7zlCvgt8OA1xH1neaoPFnRbcTFIdiYQegC6CFUIEjW0ILFz8nMnlNSz8HKm2WVYyso8/NlBXtxw+cwSH3gBBxcr0dhYHRSflJF/I1fVEx2itKLf5JAQIKcP+3u+fY=',
        success: true,
        code: 1000,
        message: '获取成功',
        totalPage: 0,
        totalCount: 0,
    };
    let debugData = decryptData(examModularExamItemRes.data);
    debugData = {
        itemTypeTotalCount: 2,
        currentTime: '2024-03-23 19:54:39',
        examScoreId: '437075109207867392',
        itemTotalCount: 50,
        timeRemaining: 2121,
        voList: [
            { itemType: '3', typeCount: 45, remark: '备选项中只有一个选项是正确的', typeName: '单选' },
            { itemType: '2', typeCount: 5, remark: '根据题目，回答问题', typeName: '简答' },
        ],
        beginTime: '2024-03-23 19:54:39',
        endTime: '2024-03-23 20:30:00',
    };
    res = {
        success: true,
        code: 2000,
        message: '考试未在规定时间内！',
        totalPage: 0,
        totalCount: 0,
    };
}

//保存考试试卷
exports.examSaveAction = async examSaveObj => {
    let examSaveObjStr = encodeRequestData(examSaveObj);
    let examSaveRes = await axiosIns({
        method: 'POST',
        url: 'https://learning.wencaischool.net/openlearning/examModular_exam_save.action',
        data: examSaveObjStr,
    });
    return examSaveRes;
};
if (false) {
    examSaveRes = {
        success: true,
        code: 1000,
        message: '保存成功',
        totalPage: 0,
        totalCount: 0,
    };
}

//提交考试试卷
exports.examSubmitAction = async submitExamObj => {
    let submitExamStr = encodeRequestData(submitExamObj);
    let submitExamRes = await axiosIns({
        method: 'POST',
        url: 'https://learning.wencaischool.net/openlearning/examModular_exam_submit.action',
        data: submitExamStr,
        headers: {
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        },
    });
    return submitExamRes;
};
if (false) {
    submitExamRes = {
        success: true,
        code: 1000,
        message: '交卷成功',
        totalPage: 0,
        totalCount: 0,
    };
}
