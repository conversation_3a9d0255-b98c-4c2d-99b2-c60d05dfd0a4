// 通过html页面解析课程列表
async function extractCoursesFromPageInBrowserContext() {
    await new Promise(resolve => setTimeout(resolve, 5*1000));
    // 直接使用浏览器环境的 document 对象
    var courseElements = document.querySelectorAll('.course.clearfix.learnCourse'); // 确保选择器与1.html中的结构匹配
    var coursesData = []; // 初始化一个空数组，用于存放提取到的所有课程对象

    // 遍历每个课程元素
    for (let courseElement of courseElements) {
        var course = {}; // 为当前课程创建一个空对象

        // 提取隐藏的ID信息
        var clazzIdInput = courseElement.querySelector('input.clazzId');
        var courseIdInput = courseElement.querySelector('input.courseId');
        var curPersonIdInput = courseElement.querySelector('input.curPersonId');


        course.clazzId = clazzIdInput ? clazzIdInput.value : null; // 班级ID
        course.courseId = courseIdInput ? courseIdInput.value : null; // 课程ID
        course.curPersonId = curPersonIdInput ? curPersonIdInput.value : null; // 当前用户ID
        course.infoAttribute = courseElement.getAttribute('info'); // 元素的info属性
        course.roleIdAttribute = courseElement.getAttribute('roleId'); // 元素的roleId属性
        course.elementId = courseElement.id; // 课程元素的ID

        // 提取课程封面图片URL
        var coverImage = courseElement.querySelector('.course-cover img');
        course.coverImageUrl = coverImage ? coverImage.src : null;

        // 提取课程名称和课程链接
        var courseLinkElement = courseElement.querySelector('.course-info h3 a');
        course.courseUrl = courseLinkElement ? courseLinkElement.href : null; // 课程详情页链接
        // 尝试获取课程名称，优先用title，其次用innerText
        var courseNameElement = courseLinkElement ? courseLinkElement.querySelector('.course-name') : null;
        var courseNameText = courseNameElement ? courseNameElement.innerText.trim() : null;
        var courseNameTitle = courseNameElement ? courseNameElement.getAttribute('title') : null;
        course.courseName = courseNameTitle || courseNameText; // 课程名称

        // 提取学校或课程描述信息
        var schoolElement = courseElement.querySelector('.course-info p.margint10.line2.color2');
        course.schoolOrDescription = schoolElement ? schoolElement.getAttribute('title') || schoolElement.innerText.trim() : null;

        // 提取教师信息
        var teacherElement = courseElement.querySelector('.course-info p.line2.color3');
        course.teacher = teacherElement ? teacherElement.getAttribute('title') || teacherElement.innerText.trim() : null;

        // 提取开课时间信息
        var pElements = courseElement.querySelectorAll('.course-info p');
        var rawTimeP = null;
        for (var i = pElements.length - 1; i >= 0; i--) {
            if (pElements[i].textContent.includes('开课时间：')) {
                rawTimeP = pElements[i];
                break;
            }
        }
        course.rawTimeInfo = rawTimeP ? rawTimeP.textContent.trim() : null; // 原始时间字符串
        if (course.rawTimeInfo && course.rawTimeInfo.includes('开课时间：')) {
            var timeParts = course.rawTimeInfo.replace('开课时间：', '').split('～');
            course.startTime = timeParts[0] ? timeParts[0].trim() : null; // 开始时间
            course.endTime = timeParts[1] ? timeParts[1].trim() : null; // 结束时间
        } else {
            course.startTime = null;
            course.endTime = null;
        }

        // 判断课程是否已结束
        var isFinishedElement = courseElement.querySelector('.course-cover a.not-open-tip');
        course.isFinished = isFinishedElement ? true : false; // 是否结束
        course.statusText = isFinishedElement ? isFinishedElement.textContent.trim() : '进行中'; // 状态文本

        // 提取课程进度
        let progressTag = document.querySelector(`#${courseElement.id} div.course-cover > div > div.fr  span.bar-tip`);
        try {
            let progress = progressTag.innerText.trim();
            progress = progress.slice(0, -1);
            course.progress = progress;
        } catch (error) {
            course.progress = '未知';
        }

        coursesData.push(course); // 将提取到的当前课程对象添加到数组中
    }

    // console.log(coursesData)
    return coursesData;
}

module.exports = extractCoursesFromPageInBrowserContext;