let axiosIns = require('../../utils/axiosIns.js');
const CryptoJS = require('crypto-js');
let getAgent = require('../../utils/getAgent.js');

// 加密函数
function encrypt(data) {
    let key = 'zmzrazilo7no32zysvn0ugdfsfasfddf';
    let encrypted = CryptoJS.AES.encrypt(CryptoJS.enc.Utf8.parse(JSON.stringify(data)), CryptoJS.enc.Utf8.parse(key), {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
    }).toString();
    encrypted = encodeURIComponent(encrypted);
    return encrypted;
}
// 解密函数
function decrypt(encryptedData) {
    encryptedData = decodeURIComponent(encryptedData);
    let key = 'zmzrazilo7no32zysvn0ugdfsfasfddf';
    let decrypted = CryptoJS.AES.decrypt(encryptedData, CryptoJS.enc.Utf8.parse(key), {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
    });

    let decryptedData = JSON.parse(CryptoJS.enc.Utf8.stringify(decrypted));
    return decryptedData;
}

// 对axios进行封装，添加了代理，错误重试功能
async function genApifun(config, isAgent, infoLogger, apiName) {
    let res;
    config.timeout = 5000;
    let host = 'undefined';
    for (let i = 0; i < 3; i++) {
        // 获取代理放到trycatch外面，为了可以让错误向外传递，终止刷课进程
        if (isAgent) {
            let agent = await getAgent();
            config.httpsAgent = agent;
            host = agent.proxy.host;
        }
        try {
            res = await axiosIns(config);
            return res;
        } catch (error) {
            await infoLogger(
                `${apiName},服务器返回异常，错误内容：<span style='color:red'>${JSON.stringify(error)}</span>，1分钟后再试,agentHost:${host.slice(0, 5)}`
            );
            await new Promise(r => setTimeout(r, 61 * 1000));
            await infoLogger(`${apiName}开始第${i + 1}次重试,,agentHost:${host.slice(0, 5)}`);
        }
    }
    await infoLogger(`${apiName},服务器返回异常，3次重试依然失败`, 'red');
    // throw new Error(`${apiName},服务器返回异常，5次重试依然失败,agentHost:${host.slice(5)}`,'red')
    return false;
}

// 获取课件具体信息
exports.getCellInfo = async (globalStore, courseObj, cellObj) => {
    let config = {
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/studying/studying',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&cellId=${cellObj.id}&schoolCode=${globalStore.schoolCode}`,
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        code: 1,
        user: {
            userId: 'u6adatmx4yzhjfsrbsw-zw',
            userType: 2,
            avatorUrl: '',
            displayName: '张开彤',
            _socket_string: 'userName=u6adatmx4yzhjfsrbsw-zw&userType=2&schoolCode=062&campuszoneId=lab5admxsllatfywdtn4wa&classId=oogdatmxxldmpsi8je-lwq',
            noticeCount: 0,
            logoutUrl: '/trp/logout.aspx',
            schoolName: '池州职业技术学院',
        },
        cell: {
            id: '-j9vafmx7z9pmaaay1yhma',
            title: '03 记谱法的基础知识—音符、音符的写法、符点的写法',
            resType: 'video',
            docId: 'n0fbatexwixi8nhruiag8g',
            questions: [
                {
                    id: 'bkbvafmx-bnmjefk-rqdyg',
                    position: '104.6', // 出现位置
                    answered: false, // 是否已经答题
                },
            ],
            lastTime: 1459.584947,
            status: false,
        },
        prev: {
            id: '-j9vafmxx5bncaqdjffirw',
            title: '音一共有多少个分组，他们的组名叫什么？',
        },
        next: {
            id: 'fabvafmxqzxjc20gj4rimg',
            title: '单纯音符包含哪些音符？',
        },
    };
}

//修改视频播放进度
exports.recordVideoPosition = async (globalStore, courseObj, cellObj, duration) => {
    let encryptData = {
        courseOpenId: courseObj.courseOpenId,
        cellId: cellObj.id,
        schoolCode: globalStore.schoolCode,
        position: duration,
    };
    let data = `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}&data=${encrypt(encryptData)}`;
    let config = {
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/studying/recordVideoPosition',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data,
    };
    let positionRes = await axiosIns(config);

    return positionRes;
};
if (false) {
    res = { code: 1, msg: '记录成功', passed: false };
    res = { code: 1, msg: '记录成功', passed: true };
    res = { code: -2, msg: '请求次数过多，请一分钟后再试' };
    res = { code: -2, msg: '记录中的信息比当前的大 ，无需再做记录' };
    res = { code: -1, msg: '' };
}

// 成功 { code: 1, msg: "记录成功", passed: false };
// 成功 { code: 1, msg: '记录成功', passed: true}
// 失败{ code: -200, msg: '记录失败', passed: false }
// 失败{ code: -2, msg: '记录中的信息比当前的大 ，无需再做记录' }

// 获取视频问题
exports.getVideoQuestion = async (globalStore, courseObj, cellObj, duration) => {
    let config = {
        method: 'get',
        url: `https://main.ahjxjy.cn/study/studying/videoQuestion`,
        headers: {
            cookie: globalStore.cookieStr,
        },
        params: {
            courseOpenId: courseObj.courseOpenId,
            cellId: cellObj.id,
            position: duration,
            schoolCode: globalStore.schoolCode,
        },
    };
    let positionRes = await axiosIns(config);
    return positionRes;
};
if (false) {
    res = {
        code: 1,
        question: {
            id: 'bkbvafmxblfbu10cqu9vra',
            type: '单选题',
            content: '什么是单位拍？',
            options: [{ content: '用固定的音值来表示构成节拍的每一个时间片段。' }, { content: '用不固定的音值来表示构成节拍的每一个时间片段。' }],
        },
    };
}

// 提交视频问题答案
exports.submitVideoQuestion = async (globalStore, courseObj, answers, questionId) => {
    let positionRes = await axiosIns({
        method: 'post',
        url: `https://main.ahjxjy.cn/study/studying/submitVideoQuestion`,
        headers: {
            cookie: globalStore.cookieStr,
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        },
        data: `cellQuestionId=${questionId}&courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}&answer=${answers}`,
        // data,
    });
    return positionRes;
};
if (false) {
    res = { code: 1, success: 0, question: { answer: ['1'], analyze: '暂无解析' } };
}

//获取课程列表
exports.getCourseList = async (globalStore, taskObj) => {
    let term = taskObj.term || '';
    let year = taskObj.year || '';
    let courseList = [];
    let page = 1;
    // 这里只能用while循环的方式，不能用API参数，pageSize，limit都试过了不行
    while (true) {
        let courseListRes = await axiosIns({
            method: 'post',
            url: 'https://main.ahjxjy.cn/studentstudio/ajax-course-list',
            headers: {
                cookie: globalStore.cookieStr,
            },
            data: `type=studying&courseType=0&getschoolcode=${globalStore.schoolCode}&studyYear=${year}&studyTerm=${term}&courseName=&page=${page}`,
        });
        if (courseListRes.list.length == 0) {
            break;
        }
        courseList.push(...courseListRes.list);
        page++;
    }
    return courseList;
};
if (false) {
    courseListRes = {
        code: 1,
        paging: {
            total_count: 5,
            page_size: 8,
            page_index: 1,
        },
        orderbys: 'expiredTime',
        list: [
            {
                courseOpenId: 'hgayaiowh6tpneiclrtzxg',
                courseName: '思想道德修养与法律基础',
                studyYear: 2023,
                studyTerm: '秋季',
                displayName: '李旻',
                schedule: 100.0,
                isPracticeCourse: false,
                completed: 41,
                totalCount: 41,
                hoplinks: '/study/html/content/process/?courseOpenId=hgayaiowh6tpneiclrtzxg&schoolCode=026',
                noStudyNoExam: '0',
                expiredTime: '/Date(1709222399000)/',
            },
        ],
        IsEducation: true,
    };
}

//获取课程对应的所有课件信息
exports.getCellList = async (courseObj, globalStore) => {
    let designRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/design/design',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}`,
    });
    return designRes;
};
// designRes = {
//     code: 1,
//     list: [
//         //模块一
//         {
//             id: "e-n4aiqwiz9eq4cjwheqtq",
//             title: "思想道德修养与法律基础的基本要求",
//             sortOrder: 1,
//             lessons: [
//                 //任务1
//                 {
//                     id: "opt4aiqwc4vnp-xl0w91ng",
//                     title: "教学大纲",
//                     sortOrder: 1,
//                     status: 2,
//                     //课件列表
//                     cells: [
//                         {
//                             icon: "doc",
//                             title: "《思想道德修养与法律基础》课程教学大纲",
//                             id: "zrx5aiqwhjtbsw1jpcsj-w",
//                             sortOrder: 1,
//                             courseDocId: "3awkaaupj4zjwusngv8yvq",
//                             lectureId: null,
//                             status: true,
//                             lastLearned: false,
//                         },
//                     ],
//                 },
//                 //其余任务省略
//             ],
//         },
//         //其余模块省略
//     ],
//     teacherType: false,
//     format: {
//         module: '{"title":"模块 {0}","format":"hanZi","_title":"模块"}',
//         lesson: '{"title":"任务 {0}","format":"shuZi","_title":"任务"}',
//     },
//     virtualTestNewHref:
//         "http://main.ahjxjy.cn/virexp/lab/expfacilities/!makeExpFacilities?cardId=340881199706145625_026&account=stu_340881199706145625&userName=陈琼&lectureIdStr=",
//     NoBuy: false,
// };

//观看文档doc,text
exports.startDocument = async (globalStore, documentUrl) => {
    let studyingRes = await axiosIns({
        method: 'get',
        url: documentUrl,
        headers: {
            cookie: globalStore.cookieStr,
        },
    });
    return studyingRes;
};

//获取视频信息
exports.getVideoInfo = async (globalStore, courseObj, cellObj) => {
    let config = {
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/studying/playing',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&docId=${cellObj.courseDocId}&schoolCode=${globalStore.schoolCode}`,
    };
    let playingRes = await axiosIns(config);
    return playingRes;
};
if (false) {
    // 一个窗口
    res = {
        code: 1,
        docUrl: 'doc/b@28257E71F0B9ADC7E9EF3159E239892D.mp4',
        preview:
            '{"extension":"mp4","category":"video","metainfo":{"status":2,"args":{"_720p":"true","_360p":"true","duration":null,"original":"true","_480p":"true"}},"urls":{"status":null,"preview":"https://huaweicloudobs.ahjxjy.cn/28257E71F0B9ADC7E9EF3159E239892D.mp4","preview360p":"https://huaweicloud.ahjxjy.cn/asset/798f7048038490dd9f900488eff84165/play_video/a6eb82563931041416756bc74b7995d9_0.mp4","preview720p":"https://huaweicloud.ahjxjy.cn/asset/798f7048038490dd9f900488eff84165/play_video/a6eb82563931041416756bc74b7995d9_1.mp4","download":null,"preview_cloud":null,"isUseCloud":0}}',
        title: '会计电算化_第1讲_会计电算化相关概念.mp4',
    };
    // 三个窗口
    res = {
        code: 1,
        docUrl: 'doc/<EMAIL>',
        preview:
            '{"extension":"zipx","category":"threeScreen","metainfo":{"status":2,"args":{}},"urls":{"status":"https://main.ahjxjy.cn/api/Priview/getStatus?fileName=12C53F5C40DE36CC4A0E2FADB0D4065B.zipx","preview":"https://huaweicloudobs.ahjxjy.cn/12C53F5C40DE36CC4A0E2FADB0D4065B.zipx","preview360p":null,"preview720p":null,"download":null,"preview_cloud":null,"isUseCloud":0}}',
        title: '《资产评估》第二部分第一讲资产评估方法之成本法（一）.zipx',
    };
    preview = {
        extension: 'zipx',
        category: 'threeScreen',
        metainfo: { status: 2, args: {} },
        urls: {
            status: 'https://main.ahjxjy.cn/api/Priview/getStatus?fileName=12C53F5C40DE36CC4A0E2FADB0D4065B.zipx',
            preview: 'https://huaweicloudobs.ahjxjy.cn/12C53F5C40DE36CC4A0E2FADB0D4065B.zipx',
            preview360p: null,
            preview720p: null,
            download: null,
            preview_cloud: null,
            isUseCloud: 0,
        },
    };
}

// 个窗口
// playingRes = {
//     code: 1,
//     docUrl: "doc/b@9986D3A8D1177F0D067C72C74CAE0A2B.mp4",
//     preview:
//         '{"extension":"mp4","category":"video","urls":{"status":"https://main.ahjxjy.cn/api/Priview/getStatus?fileName=9986D3A8D1177F0D067C72C74CAE0A2B.mp4","preview":"https://huaweicloudobs.ahjxjy.cn/9986D3A8D1177F0D067C72C74CAE0A2B.mp4","preview360p":"https://huaweicloud.ahjxjy.cn/asset/9d779bbcf13445fd0bbfa6ce8056c3a9/play_video/f7e7da77720866ab0e91a87714756618_0.mp4","preview720p":"https://huaweicloud.ahjxjy.cn/asset/9d779bbcf13445fd0bbfa6ce8056c3a9/play_video/f7e7da77720866ab0e91a87714756618_1.mp4","download":null,"preview_cloud":null,"isUseCloud":0}}',
//     title: "建筑力学与结构_第1讲_导学.mp4",
// };
// //三个窗口
// playingRes = {
//     code: 1,
//     docUrl: "doc/<EMAIL>",
//     preview:
//         '{"extension":"rarx","category":"threeScreen","urls":{"status":"https://main.ahjxjy.cn/api/Priview/getStatus?fileName=CC94A1853BAF92CABC845D8DB5137652.rarx","preview":"https://huaweicloudobs.ahjxjy.cn/CC94A1853BAF92CABC845D8DB5137652.rarx","preview360p":null,"preview720p":null,"download":null,"preview_cloud":null,"isUseCloud":0}}',
//     title: "画法几何及建筑制图141022-1.rarx",
// };

//更改课程状态为已完成
exports.finishChapter = async (globalStore, courseObj, cellObj) => {
    let studiedRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/studying/studied',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&cellId=${cellObj.id}&schoolCode=${globalStore.schoolCode}`,
    });
    return studiedRes;
};
// 成功 {"code":1,"msg":"成功标记已学"}

//更改assignment作业为未开始
exports.resetOnlineAssignment = async (globalStore, courseObj, cellObj) => {
    let res = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/assignment/rewriteOnlineAssignment',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&cellId=${cellObj.id}&schoolCode=${globalStore.schoolCode}&type=3`,
    });
    return res;
};
//成功 {"code":1}
//失败 ,{"code":-1,"msg":"平时成绩已发布，不允许再提交作业。"}

//从服务器获取 test作业 题目数据
exports.getPaper = async (globalStore, courseObj, cellObj) => {
    let loadRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/test/paper',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&cellId=${cellObj.id}&schoolCode=${globalStore.schoolCode}`,
    });
    return loadRes;
};
if (false) {
    loadRes = {
        code: 1,
        canModify: true,
        isStudent: true,
        data: [
            {
                type: 1,
                name: '单选题',
                list: [
                    {
                        id: 'pvezacsxrzpm4c-luvemfw',
                        content:
                            '\u003cp style="margin-top: 0px; padding: 0px; border: 0px; list-style: none; word-wrap: normal; word-break: normal; line-height: 21px; color: rgb(73, 73, 73); font-family: simsun; font-size: 14px; white-space: normal; background-color: rgb(255, 255, 255);"\u003e\u003cspan style="font-size:16px;word-wrap: normal; word-break: normal; line-height: 24px;"\u003e马克思说：“作为确定人，现实的人，你就有规定，就有使命，就有任务，至于你是否意识到这一点，那都是无所谓的。”这里，确定的现实的人的“规定”、“使命”和“任务”指的是（　　）\u003c/span\u003e\u003c/p\u003e\u003cp style="margin-top: 0px; padding: 0px; border: 0px; list-style: none; word-wrap: normal; word-break: normal; line-height: 21px; color: rgb(73, 73, 73); font-family: simsun; font-size: 14px; white-space: normal; background-color: rgb(255, 255, 255);"\u003e\u003cbr/\u003e\u003c/p\u003e',
                        options: [
                            {
                                content:
                                    '\u003cp style="margin-top: 0px; white-space: normal; padding: 0px; border: 0px; list-style: none; word-wrap: normal; word-break: normal; line-height: 21px; color: rgb(73, 73, 73); font-family: simsun; font-size: 14px; background-color: rgb(255, 255, 255);"\u003e\u003cspan style="font-size: 16px; word-wrap: normal; word-break: normal; line-height: 24px;"\u003e人生责任&nbsp;\u003cwbr/\u003e&nbsp;\u003cwbr/\u003e&nbsp;　　\u003c/span\u003e\u003c/p\u003e',
                            },
                            {
                                content:
                                    '\u003cp style="margin-top: 0px; white-space: normal; padding: 0px; border: 0px; list-style: none; word-wrap: normal; word-break: normal; line-height: 21px; color: rgb(73, 73, 73); font-family: simsun; font-size: 14px; background-color: rgb(255, 255, 255);"\u003e\u003cspan style="font-size: 16px; word-wrap: normal; word-break: normal; line-height: 24px;"\u003e人生境界\u003c/span\u003e\u003c/p\u003e\u003cp style="margin-top: 0px; white-space: normal; padding: 0px; border: 0px; list-style: none; word-wrap: normal; word-break: normal; line-height: 21px; color: rgb(73, 73, 73); font-family: simsun; font-size: 14px; background-color: rgb(255, 255, 255);"\u003e\u003cbr/\u003e\u003c/p\u003e\u003cp\u003e\u003cbr/\u003e\u003c/p\u003e',
                            },
                            {
                                content:
                                    '\u003cp style="margin-top: 0px; white-space: normal; padding: 0px; border: 0px; list-style: none; word-wrap: normal; word-break: normal; line-height: 21px; color: rgb(73, 73, 73); font-family: simsun; font-size: 14px; background-color: rgb(255, 255, 255);"\u003e\u003cspan style="font-size: 16px; line-height: 24px;"\u003e人生存在&nbsp;\u003c/span\u003e\u003cwbr style="font-size: 16px; line-height: 24px;"/\u003e\u003cspan style="font-size: 16px; line-height: 24px;"\u003e&nbsp;\u003c/span\u003e\u003cwbr style="font-size: 16px; line-height: 24px;"/\u003e\u003cspan style="font-size: 16px; line-height: 24px;"\u003e&nbsp;　　　　\u003c/span\u003e\u003cbr/\u003e\u003c/p\u003e\u003cp\u003e\u003cbr/\u003e\u003c/p\u003e',
                            },
                            {
                                content:
                                    '\u003cp style="margin-top: 0px; white-space: normal; padding: 0px; border: 0px; list-style: none; word-wrap: normal; word-break: normal; line-height: 21px; color: rgb(73, 73, 73); font-family: simsun; font-size: 14px; background-color: rgb(255, 255, 255);"\u003e\u003cspan style="font-size: 16px; line-height: 24px;"\u003e人生信仰\u003c/span\u003e\u003cbr/\u003e\u003c/p\u003e\u003cp\u003e\u003cbr/\u003e\u003c/p\u003e',
                            },
                        ],
                        type: 1,
                        subQuesList: [],
                        studentAnswer: '',
                    },
                ],
            },
            {
                type: 2,
                name: '多选题',
                list: [
                    {
                        id: 'pvezacsxe5rlu5so7tulma',
                        content:
                            '\u003cp style="margin-top: 0px; padding: 0px; border: 0px; list-style: none; word-wrap: normal; word-break: normal; line-height: 21px; color: rgb(73, 73, 73); font-family: simsun; font-size: 14px; white-space: normal; background-color: rgb(255, 255, 255);"\u003e对于理想的错误认识有（\u003cspan style="font-family:Times New Roman;word-wrap: normal; word-break: normal;"\u003e&nbsp; &nbsp; &nbsp; &nbsp;\u003c/span\u003e）。\u003c/p\u003e\u003cp style="margin-top: 0px; padding: 0px; border: 0px; list-style: none; word-wrap: normal; word-break: normal; line-height: 21px; color: rgb(73, 73, 73); font-family: simsun; font-size: 14px; white-space: normal; background-color: rgb(255, 255, 255);"\u003e\u003cbr/\u003e\u003c/p\u003e\u003cp\u003e\u003cbr/\u003e\u003c/p\u003e',
                        options: [
                            {
                                content:
                                    '\u003cp style="margin-top: 0px; padding: 0px; border: 0px; list-style: none; word-wrap: normal; word-break: normal; line-height: 21px; color: rgb(73, 73, 73); font-family: simsun; font-size: 14px; white-space: normal; background-color: rgb(255, 255, 255);"\u003e理想理想，有利就想\u003cbr/\u003e\u003c/p\u003e\u003cp style="margin-top: 0px; padding: 0px; border: 0px; list-style: none; word-wrap: normal; word-break: normal; line-height: 21px; color: rgb(73, 73, 73); font-family: simsun; font-size: 14px; white-space: normal; background-color: rgb(255, 255, 255);"\u003e\u003cbr/\u003e\u003c/p\u003e',
                            },
                            {
                                content:
                                    '\u003cp style="margin-top: 0px; padding: 0px; border: 0px; list-style: none; word-wrap: normal; word-break: normal; line-height: 21px; color: rgb(73, 73, 73); font-family: simsun; font-size: 14px; white-space: normal; background-color: rgb(255, 255, 255);"\u003e没有理想的人一样生活得很开心\u003c/p\u003e',
                            },
                            {
                                content:
                                    '\u003cp style="margin-top: 0px; padding: 0px; border: 0px; list-style: none; word-wrap: normal; word-break: normal; line-height: 21px; color: rgb(73, 73, 73); font-family: simsun; font-size: 14px; white-space: normal; background-color: rgb(255, 255, 255);"\u003e理想是明天的，只要今天过得好就可以了\u003c/p\u003e',
                            },
                            {
                                content:
                                    '\u003cp style="margin-top: 0px; padding: 0px; border: 0px; list-style: none; word-wrap: normal; word-break: normal; line-height: 21px; color: rgb(73, 73, 73); font-family: simsun; font-size: 14px; white-space: normal; background-color: rgb(255, 255, 255);"\u003e凡是理想自然都可以实现\u003cbr/\u003e\u003c/p\u003e\u003cp\u003e\u003cbr/\u003e\u003c/p\u003e',
                            },
                        ],
                        type: 2,
                        subQuesList: [],
                        studentAnswer: '',
                    },
                ],
            },
        ],
        userType: 2,
    };
}

//从服务器获取 assignment作业 客观题题目数据
exports.loadOnlineAssignment = async (globalStore, courseObj, cellObj) => {
    let loadRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/assignment/loadOnlineAssignment',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&cellId=${cellObj.id}&schoolCode=${globalStore.schoolCode}`,
    });
    return loadRes;
};
if (false) {
    loadRes = {
        code: 1,
        data: [
            //题目类型
            {
                type: 1,
                name: '单选题',
                list: [
                    {
                        id: 'st0baqoxhqjhvv43wtdoya',
                        content: '安徽高校继续教育合作联盟哪一年成立？',
                        options: [
                            {
                                content: '2017',
                            },
                            {
                                content: '2018',
                            },
                            {
                                content: '2019',
                            },
                            {
                                content: '2020',
                            },
                        ],
                        type: 1,
                        totalScore: 25.0,
                        subQuesList: [],
                        studentAnswer: '',
                    },
                ],
            },
            {
                type: 3,
                name: '判断题',
                list: [
                    {
                        id: 'st0baqoxwpveh28ffqfrwg',
                        content: '园区建设实践获得2019年安徽省线上教学成果奖一等奖。',
                        options: null,
                        type: 3,
                        totalScore: 25.0,
                        subQuesList: [],
                        studentAnswer: '',
                    },
                ],
            },
        ],
        canModify: true,
        state: 'doing',
        firstAnaswer: true,
        _paperId: 'sd0baqoxolrhewt7-emuyw',
        prev_paperId: null,
        isSubjective: false,
        status: 0,
        totalScore: 0.0,
        endDate: 13502344.4508572,
        isPublishScore: false,
        isAllHasSubjective: false,
        userType: 2,
    };
}

//从服务器获取 assignment作业 细节
exports.getDetail = async (globalStore, courseObj, cellObj) => {
    let detailRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/assignment/detail',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&cellId=${cellObj.id}&schoolCode=${globalStore.schoolCode}`,
    });
    return detailRes;
};
// res = {
//     code: 1,
//     type: "online",
//     title: "作业一：计算机基础知识与操作系统",
//     endDate: "1719762900",
//     totalScore: 100,
//     ruleId: "6t1lar2xba1ka2-w0tmcpq",
// };

//提交 assignment作业 主观题答案
exports.submitAssignment = async (globalStore, courseObj, cellObj, answer) => {
    let res = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/assignment/submitAssignment',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&cellId=${cellObj.id}&schoolCode=${globalStore.schoolCode}&content=${answer}`,
    });
    return res;
};

//回答问题
exports.submitAnswer = async (globalStore, courseObj, questionObj, answers) => {
    // let submitPaperItem4UserRes = await axiosIns({
    //     method: 'post',
    //     url: 'https://main.ahjxjy.cn/study/assignment/submitPaperItem4User',
    //     headers: {
    //         cookie: globalStore.cookieStr,
    //     },
    //     data: `courseOpenId=${courseObj.courseOpenId}&questionId=${questionObj.questionId}&answer=${answers}&schoolCode=${globalStore.schoolCode}`,
    // });
    // return submitPaperItem4UserRes;

    let config = {
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/assignment/submitPaperItem4User',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&questionId=${questionObj.questionId}&answer=${answers}&schoolCode=${globalStore.schoolCode}`,
    };
    let infoLogger = () => Promise.resolve();
    let res = await genApifun(config, false, infoLogger, '获取课件具体信息');
    return res;
};

//提交test作业
exports.submitPaper = async (globalStore, courseObj, cellObj) => {
    let submitOnlineAssignmentRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/test/submitPaper',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&cellId=${cellObj.id}&schoolCode=${globalStore.schoolCode}&isConstraint=true`,
    });
    return submitOnlineAssignmentRes;
};

//提交assignment作业
exports.submitOnlineAssignment = async (globalStore, courseObj, cellObj) => {
    let submitOnlineAssignmentRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/assignment/submitOnlineAssignment',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&cellId=${cellObj.id}&schoolCode=${globalStore.schoolCode}&isConstraint=true`,
    });
    return submitOnlineAssignmentRes;
};
//{"code":-1,"msg":"平时成绩已发布，不允许再提交作业。"}

/**
 * ======================= 二、考试部分 =======================
 *
 * 2.获取每个课程对应的课件列表 cellList:  {cellName: '2.4 毛泽东思想的科学涵义',cellUrl: ''}
 */

//随学随考：获取考试列表
exports.getExamList = async (globalStore, taskObj) => {
    let term = taskObj.term || '';
    let year = taskObj.year || '';
    let courseListRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/studentstudio/ajax-exam-sxskTable',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `getschoolCode=${globalStore.schoolCode}&studyYear=${year}&studyTerm=${term}&pageSize=50`,
    });
    return courseListRes;
};
if (false) {
    courseListRes = {
        code: 1,
        list: [
            {
                courseOpenId: '-eakahywkonlivcgtkdr5w',
                courseName: 'Dreamweaver网页设计',
                studyYear: '2023',
                studyTerm: '秋季学期',
                examStatus: '0',
                deadLineDate: '2023-12-31',
                deadLineTime: '23:59',
                studySchedule: 100.0,
                examSchedule: 90,
                status: '1',
                submitCount: 1,
                doingPaperId: null,
                isexpired: false,
                ispayment: true,
            },
        ],
        paging: {
            total_count: 6,
            page_size: 10,
            page_index: 1,
        },
        orderbys: 'courseName,studyYear,studyTerm,deadLineTime,studySchedule,examSchedule,submitCount',
        getschoolcode: '086',
    };
}
//学校统考：获取考试列表
exports.getOnlineExamList = async (globalStore, taskObj) => {
    let term = taskObj.term || '';
    let year = taskObj.year || '';
    let courseListRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/studentstudio/ajax-exam-tkOnlineTable',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `getschoolCode=${globalStore.schoolCode}&studyYear=${year}&studyTerm=${term}&pageSize=50`,
    });
    return courseListRes;
};
if (false) {
    res = {
        code: 1,
        list: [
            {
                courseOpenId: 'r2b9amiwbjvh0doaewu-ua',
                courseName: '思想政治与法律基础',
                studyYear: '2024',
                studyTerm: '春季学期',
                examStatus: '1',
                deadLineDate: '2024-06-28',
                deadLineTime: '17:00',
                studySchedule: 0,
                examSchedule: 90,
                status: '1',
                submitCount: 1,
                doingPaperId: null,
                isexpired: false,
                ispayment: true,
            },
            {
                courseOpenId: 'wfz8amiwebtfbih8uiztsa',
                courseName: '中国特色社会主义理论',
                studyYear: '2024',
                studyTerm: '春季学期',
                examStatus: '1',
                deadLineDate: '2024-06-28',
                deadLineTime: '17:00',
                studySchedule: 0,
                examSchedule: 90,
                status: '',
                submitCount: 0,
                doingPaperId: null,
                isexpired: false,
                ispayment: true,
            },
        ],
        paging: {
            total_count: 5,
            page_size: 10,
            page_index: 1,
        },
        orderbys: 'courseName,studyYear,studyTerm,deadLineTime,studySchedule,examSchedule,submitCount',
        getschoolcode: '010',
    };
}
// 补考随学随考：获取考试列表
exports.getBkSxskExamList = async (globalStore, taskObj) => {
    let term = taskObj.term || '';
    let year = taskObj.year || '';
    let courseListRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/studentstudio/ajax-bkExam-bkSxskTable',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `getschoolCode=${globalStore.schoolCode}&studyYear=${year}&studyTerm=${term}&pageSize=50`,
    });
    return courseListRes;
};

// （准备废弃）随学随考：服务器获取当前考试课程的状态（是否可以答题）
exports.getSxsk = async (globalStore, courseObj) => {
    let sxskRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/sxsk/sxsk',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}`,
    });
    return sxskRes;
};
// （准备废弃）学校统考：服务器获取当前考试课程的状态（是否可以答题）
exports.getTK = async (globalStore, courseObj) => {
    let sxskRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/TKOnline/TKOnline',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}`,
    });
    return sxskRes;
};
// 服务器获取当前考试课程的状态（是否可以答题）
exports.getExamStatus = async (globalStore, courseObj) => {
    let url;
    switch (courseObj.type) {
        // 随学随考
        case 'sxsk':
            url = 'https://main.ahjxjy.cn/study/sxsk/sxsk';
            break;
        // 学校统考
        case 'tk':
            url = 'https://main.ahjxjy.cn/study/TKOnline/TKOnline';
            break;
        // 补考随学随考
        case 'bkSxsk':
            url = 'https://main.ahjxjy.cn/study/BKExam/sxsk';
            break;
    }
    let config = {
        method: 'post',
        url: url,
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}`,
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    sxskRes = {
        code: 1,
        state: 'readonly',
        data: {
            courseName: '土木工程力学（本）',
            examDuration: 90,
            savePhotoUrl: '',
            status: true,
            testOverTime: '1709222399',
            submitCount: 1,
        },
    };
}

// （准备废弃）学随学随考：从服务器获取考试题目数据
exports.loadSXSK = async (globalStore, courseObj) => {
    let loadSXSKRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/sxsk/loadSXSK',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}`,
    });
    return loadSXSKRes;
};
// （准备废弃）学校统考：从服务器获取考试题目数据
exports.loadTK = async (globalStore, courseObj) => {
    let loadSXSKRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/TKOnline/loadTKOnline',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}`,
    });
    return loadSXSKRes;
};
// 从服务器获取考试题目数据
exports.getQuestion = async (globalStore, courseObj) => {
    let url;
    switch (courseObj.type) {
        case 'sxsk':
            url = 'https://main.ahjxjy.cn/study/sxsk/loadSXSK';
            break;
        case 'tk':
            url = 'https://main.ahjxjy.cn/study/TKOnline/loadTKOnline';
            break;
        case 'bkSxsk':
            url = 'https://main.ahjxjy.cn/study/BKExam/loadSXSK';
            break;
    }
    let config = {
        method: 'post',
        url: url,
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}`,
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    let loadSXSKRes = {
        code: 1,
        data: [
            {
                type: 1,
                name: '单选题',
                list: [
                    {
                        id: '0k0gab2wgrlcdxztd5essg',
                        content: '在网页中不能添加的元素是（  ）。',
                        options: [
                            {
                                content: '文字、图像',
                            },
                            {
                                content: '表格、动画',
                            },
                            {
                                content: '声音视频',
                            },
                            {
                                content: '纸张等实物',
                            },
                        ],
                        type: 1,
                        totalScore: 4.0,
                        score: 4.0,
                        correctStatus: 1,
                        answers: '3',
                        analysis: '',
                        studentAnswer: '3',
                    },
                ],
            },
        ],
        submitCount: 1, //提交次数
        doingPaperId: '',
        markingPaperId: null,
        outTime: true,
        photosSeconds: -27643,
        isDone: false,
        totalScore: 100.0, //分数
        endDate: -221153.9264409,
    };
}

// （准备废弃）学随学随考：回答考试题目
exports.submitExamAnswer = async (globalStore, courseObj, questionObj) => {
    let res = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/sxsk/submitPaperItem4User',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&questionId=${questionObj.questionId}&answer=${questionObj.answers}&schoolCode=${globalStore.schoolCode}`,
    });
    return res;
};
// （准备废弃）学学校统考：回答考试题目
exports.submitTkExamAnswer = async (globalStore, courseObj, questionObj) => {
    let res = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/TKOnline/submitPaperTKOnline',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&questionId=${questionObj.questionId}&answer=${questionObj.answers}&schoolCode=${globalStore.schoolCode}`,
    });
    return res;
};
// 回答考试问题
exports.submitExamAnswer = async (globalStore, courseObj, questionObj) => {
    let url;
    switch (courseObj.type) {
        case 'sxsk':
            url = 'https://main.ahjxjy.cn/study/sxsk/submitPaperItem4User';
            break;
        case 'tk':
            url = 'https://main.ahjxjy.cn/study/TKOnline/submitPaperTKOnline';
            break;
        case 'bkSxsk':
            url = 'https://main.ahjxjy.cn/study/BKExam/submitPaperItem4User';
            break;
    }
    let config = {
        method: 'post',
        url: url,
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&questionId=${questionObj.questionId}&answer=${questionObj.answers}&schoolCode=${globalStore.schoolCode}`,
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = { code: 1, msg: '回答成功' };
}

//（准备废弃）随学随考：提交考试试卷
exports.submitSXSK = async (globalStore, courseObj) => {
    let finishRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/sxsk/submitSXSK',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}&isConstraint=true`,
    });
    return finishRes;
};
// （准备废弃）学校统考：提交考试试卷
exports.submitTK = async (globalStore, courseObj) => {
    let finishRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/TKOnline/submitTKOnline',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}&isConstraint=true`,
    });
    return finishRes;
};
// 提交考试试卷
exports.submitExam = async (globalStore, courseObj) => {
    let url;
    switch (courseObj.type) {
        case 'sxsk':
            url = 'https://main.ahjxjy.cn/study/sxsk/submitSXSK';
            break;
        case 'tk':
            url = 'https://main.ahjxjy.cn/study/TKOnline/submitTKOnline';
            break;
        case 'bkSxsk':
            url = 'https://main.ahjxjy.cn/study/BKExam/submitSXSK';
            break;
    }
    let config = {
        method: 'post',
        url: url,
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}&isConstraint=true`,
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    success = { code: 1, msg: '保存成功' };
    fail = { code: -6, msg: '您的操作过于频繁，请稍后再试。' };
}

// （准备废弃）随学随考：修改课程考试状态
exports.rewriteSXSK = async (globalStore, courseObj) => {
    let rewriteSXSKRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/sxsk/rewriteSXSK',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}&type=2`,
    });
    return rewriteSXSKRes;
};
// （准备废弃）学校统考：修改课程考试状态
exports.rewriteTK = async (globalStore, courseObj) => {
    let rewriteSXSKRes = await axiosIns({
        method: 'post',
        url: 'https://main.ahjxjy.cn/study/TKOnline/rewriteTKOnline',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}&type=2`,
    });
    return rewriteSXSKRes;
};
// 修改课程考试状态
exports.rewriteExam = async (globalStore, courseObj) => {
    let url;
    switch (courseObj.type) {
        case 'sxsk':
            url = 'https://main.ahjxjy.cn/study/sxsk/rewriteSXSK';
            break;
        case 'tk':
            url = 'https://main.ahjxjy.cn/study/TKOnline/rewriteTKOnline';
            break;
        case 'bkSxsk':
            url = 'https://main.ahjxjy.cn/study/BKExam/rewriteSXSK';
            break;
    }
    let config = {
        method: 'post',
        url: url,
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}&type=2`,
    };
    let res = await axiosIns(config);
    return res;
};

// 查询学费
exports.getPayment = async (globalStore, taskObj) => {
    let term = taskObj.term || '';
    let year = taskObj.year || '';
    let config = {
        method: 'post',
        url: 'https://main.ahjxjy.cn/studentstudio/ajax-payment-Info',
        headers: {
            cookie: globalStore.cookieStr,
        },
        data: `getschoolcode=${globalStore.schoolCode}&studyYear=${year}&studyTerm=${term}&page=1`,
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        code: 1,
        paging: {
            total_count: 1,
            page_size: 10,
            page_index: 1,
        },
        orderbys: '',
        list: [
            {
                DisplayName: '宋崇勇',
                IdCard: '321321198604167216',
                SchoolName: '安徽开放大学',
                CampusZoneName: '芜湖致达教育培训中心',
                MajorName: '行政管理',
                PaymentMoney: 1320,
                StudyYear: 2025,
                studyTerm: 1,
                EndTime: '2025-03-20 23:59:59',
                ISpayment: 0,
            },
        ],
        getschoolcode: '032',
    };
}
