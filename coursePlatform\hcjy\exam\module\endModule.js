let api = require("../../utils/api");
async function endModule(mainPage, globalStore) {
    //刷新页面
    await mainPage.goto(globalStore.mainUrl, {
        waitUntil: "networkidle0",
    });
    await new Promise((r) => setTimeout(r, 2000));
    let courseListRes = await api.getExamList(globalStore.cookieStr);
    // let courseList = courseListRes.items.filter((courseObj) => courseObj.status == 1);
    let courseList = courseListRes.items
    let finalResult = courseList.map((courseObj) => {
        return {
            progress: courseObj.score,
            courseName: courseObj.activityName,
        };
    });
    return finalResult;
}

module.exports = endModule;