// ========= 工具 =========
let pageTools = require('../../utils/pageTools.js');
let getMainPage = require('../../utils/getMainPage.js');

// ========= 组成模块 =========
let signinModule = require('../course/module/signinModule/index.js');
let getExamListModule = require('./module/getExamListModule.js');
let runExamModule = require('./module/runExamModule/index.js');
let endModule = require('./module/endModule.js');

async function cxxxtExam(taskObj, taskOptions) {
    if (!taskObj.others.includes('手机端')) {
        taskObj.others += '电脑端';
    }

    // taskOptions.isHeadless = false;

    let finalResult = [];

    let infoLogger = pageTools.getExamInfoLogger(taskObj.id, false);
    let { mainPage, browser } = await getMainPage(taskOptions);
    let globalStore = { taskObj };

    //放行所有request请求
    await mainPage.setRequestInterception(true);
    //添加中文请求头，应为用了puppeteer-extra-plugin-stealth之后，会导致英文请求头问题
    mainPage.on('request', request => {
        let headers = request.headers();
        headers['Accept-Language'] = 'zh-CN,zh;q=0.9';
        request.continue({ headers });
    });

    try {
        if (!process.env.HAS_PERMISSION) {
            return;
        }

        // 一、登录
        await signinModule(infoLogger, globalStore, mainPage);

        // 二、获取考试列表
        let courseList = await getExamListModule(taskObj, mainPage, infoLogger);

        // 三、开始考试
        await runExamModule(mainPage, taskObj, infoLogger, courseList, finalResult);

        // 四、考试结束
        finalResult = await endModule(globalStore, mainPage, infoLogger, taskObj);

        // 返回结果
        return { finalResult, warningMessage: '' };
    } catch (error) {
        await browser.close();
        browser = null;
        throw error;
    } finally {
        if (browser) {
            await browser.close();
            browser = null;
        }
    }
}

module.exports = cxxxtExam;
