let axios = require("axios");

let axiosIns = axios.create();
// 添加响应拦截器
axiosIns.interceptors.response.use(
    // 对响应数据做点什么
    function (response) {
        if (response.status === 200) {
            return response.data;
        } else {
            return Promise.reject(new Error("error"));
        }
    },
    // 对响应错误做点什么
    function (error) {
        return Promise.reject(error);
    }
);

// // 添加请求拦截器
// axiosIns.interceptors.request.use(
//     // 在发送请求之前做些什么
//     function (config) {
//         console.log(config.url)
//         return config;
//     },
//     // 对请求错误做些什么
//     function (error) {
//         return Promise.reject(error);
//     },
// );

module.exports = axiosIns;

// let res = fetch("https://exam.chinaedu.net/oxer/app/ots/TestActivity/StartAnswerPaper", {
//     headers: {
//         accept: "*/*",
//         "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
//         "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
//         "sec-ch-ua": '"Google Chrome";v="119", "Chromium";v="119", "Not?A_Brand";v="24"',
//         "sec-ch-ua-mobile": "?0",
//         "sec-ch-ua-platform": '"Windows"',
//         "sec-fetch-dest": "empty",
//         "sec-fetch-mode": "cors",
//         "sec-fetch-site": "same-origin",
//         "x-requested-with": "XMLHttpRequest",
//         cookie: "JSESSIONID=E272DA7E4EB382DBE90D69DE38836428; sid=8b123885-5cd6-4606-af24-9ba107e6fbd9; _pk_testcookie.5.72fe=1; _pk_ses.5.72fe=1; _pk_id.5.72fe=f7ca35d6533cdeb8.1700985856.6.1701010398.1701009966.",
//         Referer:
//             "https://exam.chinaedu.net/oxer/page/ots/examIndex.html?arrangementId=31c302d5-a21b-49f0-97e6-4f4844056000",
//         "Referrer-Policy": "strict-origin-when-cross-origin",
//     },
//     body: "arrangementId=31c302d5-a21b-49f0-97e6-4f4844056000",
//     method: "POST",
// });
// res.then((response) => {
//     if (!response.ok) {
//         throw new Error(`HTTP error! status: ${response.status}`);
//     }
//     return response.json();
// })
//     .then((data) => {
//         console.log(data);
//     })
//     .catch((error) => {
//         console.log("There was a problem with your fetch operation:", error.message);
//     });

// let config2 = {
//     headers: {
//         accept: "application/json, text/javascript, */*; q=0.01",
//         "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
//         "content-type": "application/json",
//         edurefurl: "http://czzycj.sccchina.net/student/",
//         edusign: "2673e1c5a74e6fedb101352348188584",
//         edutoken: "2EPir6J",
//         eduuid: "1721187378898403338",
//         "proxy-connection": "keep-alive",
//         "x-requested-with": "XMLHttpRequest",
//         cookie: ".CHINAEDUCLOUD=856c2fe372fb4f378c48ea0747bbbc72; _pk_testcookie.225.ae7a=1; _pk_id.225.ae7a=0385edb7ac7cad62.1700982203.1.1700982241.1700982203.",
//         Referer: "http://czzycj.sccchina.net/",
//         "Referrer-Policy": "origin",
//     },
//     data: '{"data":"1766929360919462019"}',
//     method: "POST",
//     url: "http://czzycj.sccchina.net/student/student/otsresult/getlist",
// };

// let config = {
//     url: "http://czzycj.sccchina.net/student/student/otsresult/getlist",
//     method: "POST",
//     headers: {
//         accept: "application/json, text/javascript, */*; q=0.01",
//         "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
//         "content-type": "application/json",
//         edurefurl: "http://czzycj.sccchina.net/student/",
//         edusign: "0a0d1c2c578be93e0f95ffd1963e15e8",
//         edutoken: "AWBir6J",
//         eduuid: "1721187378888966154",
//         metadatacode: "Student_StudentHome",
//         "proxy-connection": "keep-alive",
//         "x-requested-with": "XMLHttpRequest",
//         cookie: ".CHINAEDUCLOUD=ef2801809ff045488b88295d9bd4bf70",
//         Referer: "http://czzycj.sccchina.net/",
//         "Referrer-Policy": "origin",
//     },
//     body: '{"data":"1715844275802996808"}',
// };

// axiosIns(config2).then(
//     (val) => console.log(val),
//     (err) => console.log(err)
// );
