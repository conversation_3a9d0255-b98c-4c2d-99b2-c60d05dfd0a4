let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api');

//这里有两种方式，一种是用浏览器跳转，另一种是axios请求
async function forumTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr) {
    let durationUrl = `https://lms.ouchn.cn/api/course/activities-read/${taskObj.id}`;
    // 通过被动拦截的方式获取结果
    mainPage.promise.pageTaskPromise = new Promise(r => {
        mainPage.resolve.pageTaskResolve = r;
    });

    let onRequestFinished = async request => {
        //拦截验证码
        let url = request.url();

        //拦截  保存作业
        if (url.includes('lms.ouchn.cn/api/course/activities-read/')) {
            //处理响应体
            let response = await request.response();
            let contentType = response.headers()['content-type'];

            //响应体默认值
            let responseData = '';

            //如果响应体是json
            if (contentType && contentType.includes('application/json')) {
                responseData = await response.json();
            }

            //获取API数据
            mainPage.apiInfo.pageTask = {
                request: {
                    method: request.method(),
                    url: request.url(),
                    headers: request.headers(),
                    postData: request.postData(),
                    resourceType: request.resourceType(),
                },
                response: responseData,
            };
            //更改promise状态
            mainPage.resolve.pageTaskResolve();

            // 移除监听器 用once就会自动移除监听器
            mainPage.off('requestfinished', onRequestFinished);
        }
    };

    mainPage.on('requestfinished', onRequestFinished);

    // 打开课件页面
    let taskUrl = `https://lms.ouchn.cn/course/${courseObj.id}/learning-activity/full-screen#/${taskObj.id}`;
    // 这里会遇到 Requesting main frame too early! 这个错误，通过增加延迟解决
    await infoLogger(`${taskUrl}`, 'gray');
    // goto会出现 Navigation timeout of 30000 ms exceeded 所以封装了一个容错版本的goto方法
    await pageTools.gotoWithRetry(mainPage, taskUrl, { waitUntil: 'networkidle0' }, 3, infoLogger);
    // await mainPage.goto(taskUrl, { waitUntil: 'networkidle2' });
    await new Promise(r => setTimeout(r, 5 * 1000));

    let cookieStr = await pageTools.getPageCookies(mainPage);

    // let response = mainPage.apiInfo.pageTask.response;
    let response = mainPage.apiInfo.pageTask ? mainPage.apiInfo.pageTask.responsee : null;
    if (response && response.completeness) {
        await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 通过方式一已经完成`, 'green');
    } else {
        await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 任务未完成，将通过方式二完成任务 ${JSON.stringify(response)}`, 'gray');
        // 判断拦截结果

        let res = await api.updateDuration(infoLogger, taskObj.id, cookieStr, '{}');
        if (res) {
            await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 已经完成`, 'green');
        } else {
            await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 任务未完成`, 'red');
        }
    }

    // let getFormIdRes = await api.getFormId(infoLogger, taskObj, cookieStr);
    // let formId = getFormIdRes.topic_category.id;

    // 发表帖子，用API的方式总是提示to many request
    // let submitFormRes
    // try {
    // submitFormRes = await api.submitForm(infoLogger, courseObj.name, taskObj.title, formId, cookieStr);
    // } catch (error) {
    //     await new Promise(r => setTimeout(r, 5 * 1000));
    //     await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 帖子发表失败`,'red');
    //     return
    // }
    // if(submitFormRes&&submitFormRes=='内容重复'){
    //     await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 帖子已经发表过了`);
    // }else{
    //     await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 帖子发表成功`,'red');
    // }

    // try {
    //     // 用浏览器的方式发表帖子
    //     await mainPage.evaluate(
    //         async (courseName, title) => {
    //             await new Promise(r => setTimeout(r, 2 * 1000));

    //             let topicElement = document.querySelector('button.ivu-btn.ivu-btn-primary i');

    //             // 点击话题元素并等待一段时间
    //             topicElement.click();
    //             await new Promise(r => setTimeout(r, 1 * 1000));

    //             // 获取标题、内容和提交按钮元素
    //             let titleElem = document.querySelector(
    //                 '#add-topic-popup > div > div.topic-form-section.main-area > form > div:nth-child(1) > div.field > input'
    //             );
    //             let contentElem = document.querySelector(
    //                 '#add-topic-popup > div > div.topic-form-section.main-area .simditor-body.needsclick[contenteditable]'
    //             );
    //             let submitElem = document.querySelector('#add-topic-popup > div > div.popup-footer > div > button.button.button-green.medium');

    //             // 设置标题和内容 ${courseName}-${title}-我自己的感想
    //             // titleElem.value=`好好学习${Date.now()}`
    //             titleElem.value = `${courseName}-${title}-我自己的感想`;
    //             let event = new Event('change', {
    //                 bubbles: true, // 允许事件冒泡
    //                 cancelable: true, // 允许事件取消
    //             });

    //             // 触发 change 事件
    //             titleElem.dispatchEvent(event);
    //             await new Promise(r => setTimeout(r, 500));
    //             // 点击提交按钮并延迟一段时间后返回课程页面
    //             contentElem.innerHTML = `<p>好好学习，天天向上。${Date.now()}</p>`;
    //             await new Promise(r => setTimeout(r, 500));
    //             submitElem.click();
    //             await new Promise(r => setTimeout(r, 1000));
    //         },
    //         courseObj.name,
    //         taskObj.title
    //     );
    //     await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 帖子发表成功`, 'green');
    // } catch (error) {
    //     await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 帖子发表失败,${error.message}`, 'red');
    // }

    await new Promise(r => setTimeout(r, 5 * 1000));
}
module.exports = forumTask;
