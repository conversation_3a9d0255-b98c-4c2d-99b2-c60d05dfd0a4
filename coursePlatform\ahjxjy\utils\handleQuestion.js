let { v4 } = require('uuid');
let Model = require('../../../config/sequelize.config.js'); //用于搜题
let api = require('./api.js');
let getAnswerFromLocal = require('../../solveQuestion/getAnswerFromLocal.js');
let solveQuestion = require('../../solveQuestion/index.js');
let { handleImgs } = require('../../solveQuestion/format.js');

//01234转为ABCDE
function numbersToLetters(str) {
    let letters = '';
    for (let i = 0; i < str.length; i++) {
        let num = parseInt(str[i]);
        if (!isNaN(num)) {
            letters += String.fromCharCode(num + 17 + 48); // 加上 17 和 48 来得到对应字母的编码
        }
    }
    return letters;
}

//ABCDE转为01234
function lettersToNumbers(str) {
    let codes = [];
    for (let i = 0; i < str.length; i++) {
        codes.push(str.charCodeAt(i) - 65);
    }
    return codes.join(',');
}

function trim(s) {
    return (
        s
            //删除多余字符串
            .replace(/(<([^>]+)>)/gi, '') //删除html标签 <span>
            .replaceAll('\n', '') //删除所有的换行符
            .replaceAll('&nbsp;', '')
            .replace(/\s{2,}/g, ' ') //删除两个及以上的空格，保留单个空格
            .trim()
    );
}

function handleContent(str) {
    return trim(handleImgs(unescape(str))).slice(0, 3000);
}

// 处理翻译题内容  "A：100%；  B：30%；  C：70%；  "  结果是 B
function getFullPercentageOption(inputString) {
    // 使用正则表达式提取所有选项及其百分比
    const regex = /([A-Z])：(\d+)%；/g;
    let matches;

    // 循环查找所有匹配项
    while ((matches = regex.exec(inputString)) !== null) {
        const option = matches[1];
        const percentage = matches[2];

        // 查找百分比为100%的选项
        if (percentage === '100') {
            return option;
        }
    }

    return null; // 如果没有百分比为100%的选项
}

// 对原始API获取的题目进行格式化 其实应该更名为formatQuestion
function formatQuestion(questionObj, platform, courseObj) {
    courseObj.courseOpenId;
    let obj = {
        id: v4(), //题目id，临时生成
        options: [], //经过原始选项处理后
        platform: platform, //外部传入
        // courseid: courseObj.courseOpenId, //外部传入
        courseName: courseObj.courseName, //外部传入
        questionId: questionObj.id, //原始题目id
    };

    //处理题目内容 unescape可以去掉转义字符
    obj.content = trim(handleImgs(unescape(questionObj.content)));

    //处理题目类型 1单选 2多选 3判断 4填空 5解答
    obj.type = { 1: '单选题', 2: '多选题', 3: '判断题', 4: '填空题', 5: '简答题', 6: '完形填空题', 7: '阅读理解题', 8: '翻译题' }[questionObj.type];

    // 1: "单选题", 2: "多选题"
    if ('单选题|多选题'.includes(obj.type)) {
        // 处理选项
        questionObj.options.forEach((item, index) => {
            let optionKey = String.fromCharCode(65 + index);
            let optionValue = trim(handleImgs(unescape(item.content)));
            // 去掉开头的“ 和结尾的” ["A:“六个必须坚持”", "B:“十个明确”", "C:“十四个坚持”", "D:“十三个方面成就”"]
            // optionValue=optionValue.replace(/^“/,'').replace(/”$/,'') //考虑了一下，还是不能去掉，否则无法匹配
            obj.options.push(`${optionKey}:${optionValue}`);
        });
        // 处理答案
        if (questionObj.answers) obj.answers = numbersToLetters(questionObj.answers);
    }
    // 3: "判断题"
    if (obj.type == '判断题') {
        // 处理选项
        obj.options = ['A:对', 'B:错'];
        // 处理答案
        if (questionObj.answers) obj.answers = questionObj.answers == 'true' ? '对' : '错';
    }
    // 4: "填空题"
    if (obj.type == '填空题') {
        if (questionObj.answers) obj.answers = trim(handleImgs(unescape(questionObj.answers)));
    }
    //5: "简答题"
    if (obj.type == '简答题') {
        if (questionObj.answers) {
            obj.answers = trim(handleImgs(unescape(questionObj.answers)));
            obj.answers = obj.answers.slice(0, 3000);
        }
    }
    // 67: "完形填空题|阅读理解题"
    if (obj.type == '完形填空题' || obj.type == '阅读理解题') {
        // 处理选项
        let subQuesList = questionObj.subQuesList;
        for (let i = 0; i < subQuesList.length; i++) {
            let subQesObj = subQuesList[i];
            let selectArr = [];
            for (let j = 0; j < subQesObj.selectOption.length; j++) {
                let selectObj = subQesObj.selectOption[j];
                selectArr.push(`${selectObj.index}:${trim(handleImgs(unescape(selectObj.content)))}`);
            }
            obj.options.push([`${subQesObj.index}`, selectArr]);
        }
        // 处理答案
        if (questionObj.answers) obj.answers = trim(handleImgs(unescape(questionObj.answers)));
    }
    // 8: "翻译题"
    if (obj.type == '翻译题') {
        // 处理选项
        questionObj.options.forEach((item, index) => {
            obj.options.push(`${String.fromCharCode(65 + index)}:${trim(handleImgs(unescape(item.content)))}`);
        });
        if (questionObj.answers) obj.answers = getFullPercentageOption(questionObj.answers);
    }

    return obj;
}

//回答 作业 问题(assignmentTask testTask) （不是一次性提交，而是一题一题的提交）
async function handleAnswer(globalStore, courseObj, questionList) {
    let submitCount = 0;
    for (let k = 0; k < questionList.length; k++) {
        let questionObj = questionList[k];
        //查询答案
        let answers = await getAnswerFromLocal(questionObj);

        if (answers) {
            //ABCD转为0123
            if ('单选题多选题'.includes(questionObj.type)) {
                answers = lettersToNumbers(answers);
            }
            //对错转为true false
            if (questionObj.type == '判断题') {
                answers = answers == 'A' ? 'true' : 'false';
            }

            // 填空题分隔符转为"@@""
            if (questionObj.type == '填空题') {
                answers = answers.replaceAll('|', '@@');
            }

            //回答题目
            let submitPaperItem4UserRes = await api.submitAnswer(globalStore, courseObj, questionObj, answers);
            await new Promise(r => setTimeout(r, 1000));
            submitCount++;
        }
    }
    return submitCount;
}

//回答考试问题 （不是一次性提交，而是一题一题的提交）
async function handleExamAnswer(globalStore, courseObj, examList) {
    let submitCount = 0;
    for (let j = 0; j < examList.length; j++) {
        let questionObj = examList[j];

        let answers = await getAnswerFromLocal(questionObj);
        if (answers) {
            //ABCD转为0123
            if ('单选题多选题'.includes(questionObj.type)) {
                questionObj.answers = lettersToNumbers(answers);
            }
            //对错转为true false
            if (questionObj.type == '判断题') {
                questionObj.answers = answers == 'A' ? 'true' : 'false';
            }
            //简答题不做处理
            if (questionObj.type == '简答题') {
                questionObj.answers = answers;
            }
            //填空题待定
            if (questionObj.type == '填空题') {
                questionObj.answers = answers.replaceAll('|', '@@');
            }
        }

        //回答题目
        let submitPaperItem4UserRes = await api.submitExamAnswer(globalStore, courseObj, questionObj);
        await new Promise(r => setTimeout(r, 1000));
        if (submitPaperItem4UserRes.code == 1) {
            submitCount++;
        }
    }

    return submitCount;
}

//收集题目
async function collectQuestion(questionList) {
    let collectCount = 0;
    //收集题目
    for (questionObj of questionList) {
        try {
            if (!questionObj.answers) continue;

            let answersContent = [];
            switch (questionObj.type) {
                case '填空题': {
                    // 填空题默认的填空题分隔符是","这里转为"|"
                    questionObj.answers = questionObj.answers.replaceAll(',', '|');
                    answersContent.push(questionObj.answers);
                    break;
                }
                case '判断题': {
                    answersContent.push(questionObj.answers);
                    questionObj.answers = questionObj.answers == '对' ? 'A' : 'B';
                    break;
                }
                case '单选题':
                case '多选题': {
                    for (let str of questionObj.answers) {
                        let ansIndex = { A: 0, B: 1, C: 2, D: 3, E: 4, F: 5, G: 6, H: 7 }[str];
                        let queOption = questionObj.options[ansIndex];

                        let [queIndex, queValue] = queOption.split(/:(.+)/);
                        // 这里不能直接用:，因为字符串可能有多个:
                        // let [queIndex, queValue] = queOption.split(':');

                        answersContent = [...answersContent].sort((x, y) => x.localeCompare(y, 'zh-Hans-CN'));
                    }
                    break;
                }
                default: {
                    // 处理简答题答案为“略”的情况
                    if ('略'.includes(questionObj.answers) || '言之成理即可得分。'.includes(questionObj.answers)) {
                        questionObj.answers = await solveQuestion(questionObj);
                    } else {
                        questionObj.answers = handleContent(questionObj.answers);
                    }
                    answersContent.push(questionObj.answers);
                    break;
                }
            }

            let existAnswer = await Model.bank.findOne({
                where: {
                    content: questionObj.content,
                    type: questionObj.type,
                    platform: '安徽继续教育在线',
                },
            });
            // 如果题库中存在的题目，不是继续教育，柠檬文采，就重新收集
            if (!existAnswer) {
                await Model.bank.create({
                    id: questionObj.id,
                    content: questionObj.content,
                    options: JSON.stringify(questionObj.options),
                    type: questionObj.type,
                    answers: questionObj.answers,
                    answers_content: JSON.stringify(answersContent),
                    course_name: questionObj.courseName,
                    platform: questionObj.platform,
                    add_time: new Date(),
                });
                collectCount++;
            }
        } catch (error) {
            error.questionObj = questionObj;
            throw error;
        }

        // await new Promise((r) => setTimeout(r, 1000000));
    }
    return collectCount;
}

module.exports = { formatQuestion, handleAnswer, collectQuestion, handleExamAnswer };
