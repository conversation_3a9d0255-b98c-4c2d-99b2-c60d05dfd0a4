let api = require("../../../utils/api.js");

async function handleSlideValidate(mainPage, infoLogger) {

    let validateDivHandle = await mainPage.$("#eject > div");
    let bigImageBuffer = await validateDivHandle.screenshot({ type: "jpeg" });
    let bigImageBase64 = bigImageBuffer.toString("base64");

    let smallValidateDivHandle = await mainPage.$("#eject > div > div.cx_image_margin > div.cx_imgBg > div.cx_imgBtn > img");
    let smallImageBuffer = await smallValidateDivHandle.screenshot({ type: "jpeg" });
    let smallImageBase64 = smallImageBuffer.toString("base64");


    //获取图片缺口坐标
    let gapPositionRes = await api.slideValidate(bigImageBase64, smallImageBase64);
    // await new Promise(r=>setTimeout(r,300000))
    if (gapPositionRes.code !== 0) {
        infoLogger("滑动验证码图片识别失败", "red");
        throw Error("滑动验证码图片识别失败");
    }

    let recognition = gapPositionRes.data.recognition;
    let gapPositionX = recognition.split(",")[0];

    // 获取滑块的定位和尺寸
    let sliderHandle = await mainPage.$("#eject > div > div.cx_image_margin > div.cx_hkinnerWrap > div.cx_rightBtn");
    let boundingBox = await sliderHandle.boundingBox();

    // 计算滑块的起始位置和目标位置
    let startX = boundingBox.x + boundingBox.width / 2;
    let startY = boundingBox.y + boundingBox.height / 2;
    let endX = startX + gapPositionX*1-4; // 目标位置的X坐标，表示滑动的距离

    // 模拟鼠标拖动滑块
    await mainPage.mouse.move(startX, startY);
    await mainPage.mouse.down();
    await mainPage.mouse.move(endX, startY, { steps: 20 }); // 增加拖动的平滑度
    await mainPage.mouse.up();
}

module.exports = handleSlideValidate;
