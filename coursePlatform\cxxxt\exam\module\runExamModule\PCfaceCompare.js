async function faceCompare(mainPage, taskObj, infoLogger) {
    await infoLogger("当前考试需要人脸识别，接下来进行人脸识别");

    // //跳转到人脸识别页面
    // await mainPage.evaluate(() => {
    //     prevOrNext(this, 1);
    // });
    // await new Promise((resolve) => setTimeout(resolve, 1000));

    //特殊情况，如果没有上传照片，就结束
    if (!taskObj.photoBase64) {
        await infoLogger("未上传照片", "red");
        await browser.close(); //关闭浏览器
        throw new Error("未上传照"); //结束进程池任务
    }

    //1.构建formData
    let type = "camera";
    let formData = new FormData();
    formData.append("fileType", type);
    var data = taskObj.photoBase64;
    var fileName = "examface-" + new Date().getTime() + "." + type;
    formData.append("uploadtype", "temp");
    formData.append("fileName", fileName);
    // 移除Base64字符串的DataURL部分
    let base64Str = data.split(";base64,").pop();
    // 将Base64字符串转换为Buffer
    let fileBuffer = Buffer.from(base64Str, "base64");
    formData.append("filePart", fileBuffer, fileName);
    //从页面获取 currentTime uploadEnc uploadUid
    let { currentTime, uploadEnc, uploadUid, ServerHost } = await mainPage.evaluate(() => {
        var currentTime = $("#currentTime").val();
        var uploadEnc = $("#uploadEnc").val();
        var uploadUid = $("#uploadUid").val();
        return { currentTime, uploadEnc, uploadUid, ServerHost };
    });

    //2。向服务器发送照片请求
    let updateBase64Res;
    try {
        // https://pan-yz.chaoxing.com/upload?_token=19004f36dc48a2743726f34f67c1d0b2
        updateBase64Res = await axios({
            url: ServerHost.uploadDomain + "/edit/uploadBase64?uid=" + uploadUid + "&enc2=" + uploadEnc + "&t=" + currentTime,
            method: "post",
            data: formData,
            headers: {
                "Content-Type": "multipart/form-data",
            },
        });
    } catch (error) {
        await infoLogger("上传照片出错", "red");
        await browser.close(); //关闭浏览器
        throw new Error(error); //结束进程池任务
    }
    // updateBase64Res = {
    //     filename: "examface-1705372988080.camera",
    //     hsize: "50 KB",
    //     length: 52047,
    //     url: "http://d0.ananas.chaoxing.com/download/be0cb311d6ef3a4e6f6951475af5d609?at_=1705372989421&ak_=b57fdf349cac19b60ba257d4c7633b43&ad_=9a74392608ad7a7354017a24d9422ee2",
    //     fileType: "camera",
    //     objectid: "be0cb311d6ef3a4e6f6951475af5d609",
    //     status: true,
    // };

    //3.处理上传结果
    if (!updateBase64Res.status) {
        await infoLogger("上传照片失败", "red");
        await browser.close(); //关闭浏览器
        throw new Error("上传照片失败"); //结束进程池任务
    }

    //4.开始人脸识别
    let currentFaceId = updateBase64Res.objectid;
    let { courseId, classId, cpi, relationid } = await mainPage.evaluate(() => {
        var courseId = $("#courseId").val(),
            classId = $("#classId").val(),
            cpi = $("#cpi").val(),
            relationid = $("#tId").val();
        return { courseId, classId, cpi, relationid };
    });
    //向服务器发送人脸识别请求
    let faceCompareRes;
    try {
        faceCompareRes = await axios({
            url: "https://mooc1-api.chaoxing.com/exam-ans/mooc2/exam/face-compare",
            method: "get",
            params: {
                courseId,
                classId,
                cpi,
                relationid,
                currentFaceId,
            },
            headers: {
                cookie: cookieStr,
            },
        });
    } catch (error) {
        await infoLogger("人脸识别服务器出错", "red");
        await browser.close(); //关闭浏览器
        throw new Error(error); //结束进程池任务
    }
    // faceCompareRes = {
    //     data: {
    //         msg: "SUCCESS",
    //         score: 93.01,
    //         code: 1,
    //         facekey: "2760d2c6637ade9780f92be54fa34edf",
    //         origin: { msg: "调用成功", code: 200, data: [Object] },
    //         threshold: 70,
    //         logId: "bf062cd2af401e3214ab98d2ce7f1fdb",
    //         detail: {
    //             faceObjectId: "be0cb311d6ef3a4e6f6951475af5d609",
    //             collectObjectId: "423f7691f63a747a0de9a65534fdaba6",
    //             liveDetectionStatus: 1,
    //             score: "93.01",
    //         },
    //         enc: "25cbefa99726ca0c99db51fe0c4e3f1c",
    //         isSimilar: true,
    //     },
    //     status: true,
    // };
    //处理服务器结果
    if (faceCompareRes.data.code == 1 && faceCompareRes.data.isSimilar) {
        await infoLogger("人脸识别成功", "green");
        //更改html数据
        await mainPage.evaluate((_FaceCompareResult) => {
            $("#facekey").val(_FaceCompareResult.facekey || "");
            // prevOrNext(this, 1);
        }, faceCompareRes.data);
    } else {
        await infoLogger("人脸识别失败", "red");
        await browser.close(); //关闭浏览器
        throw new Error("人脸识别失败"); //结束进程池任务
    }

    //跳转到承诺书界面
    await mainPage.evaluate(() => {
        prevOrNext(this, 1);
    });
    await new Promise((resolve) => setTimeout(resolve, 2000)); //等待加载完成
}

module.exports = faceCompare;
