let api = require('../../utils/api.js');

async function getCourseListModule(globalStore, infoLogger, taskObj) {
    let courseListRes;
    try {
        courseListRes = await api.getExamList(globalStore.cookieStr);
    } catch (error) {
        await infoLogger('获取考试列表失败', 'red');
        throw Error('获取考试列表失败');
    }
    let courseList = courseListRes.items;
    if (courseList.length == 0) {
        await infoLogger('考试科目数量为0', 'red');
        throw Error('考试科目数量为0');
    }

    courseList.forEach(courseObj => {
        let [submitCount, totalCount] = courseObj.answerCount.split('/');
        courseObj.submitCount = submitCount * 1;
        courseObj.totalCount = totalCount * 1;
    });


    // 过滤课程
    if (taskObj.coursename) {
        await infoLogger(`指定课程为： ${taskObj.coursename} `);
        courseList = courseList.filter(item => item.activityName.includes(taskObj.coursename));
    }else{
        await infoLogger(`未指定课程，共有${courseList.length}门课程`);
    }

    return courseList;
}

module.exports = getCourseListModule;
