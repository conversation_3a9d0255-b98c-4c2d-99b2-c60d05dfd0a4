let api = require('../../../../../utils/api.js');
let { getVideoDurationInSeconds } = require('get-video-duration');
let handleVideoQuestion = require('./handelVideoQuestion.js');

let path = require('path');
let ffprobePath = path.join(path.dirname(process.execPath), 'static/ffprobe-static/ffprobe.exe');
if (process.pkg) {
    // pkg 打包环境
    ffprobePath = path.join(path.dirname(process.execPath), 'static/ffprobe-static/ffprobe.exe');
} else {
    // 开发环境
    ffprobePath = path.resolve('static/ffprobe-static/ffprobe.exe');
}

async function videoTask(infoLogger, globalStore, courseObj, cellObj, cellLogStr) {
    if (cellObj.status) {
        await infoLogger(`${cellLogStr}：当前课件已经完成，不需要重复 《${cellObj.title}》`);
        return;
    }

    await infoLogger(`${cellLogStr}：开始${cellObj.icon}视频任务 《${cellObj.title}》`);

    // 获取视频可见具体信息
    let getCellInfoRes = await api.getCellInfo(globalStore, courseObj, cellObj);
    let lastTime = getCellInfoRes.cell.lastTime;
    let cellInfo = getCellInfoRes.cell;
    if (cellInfo.questions.length > 0) {
        await infoLogger(`${cellLogStr}：需要回答视频问题`);
        await handleVideoQuestion(infoLogger, cellInfo, courseObj, globalStore, cellObj, cellLogStr);
        await infoLogger(`${cellLogStr}：问题回答完毕`);
        // await new Promise(r => setTimeout(r, 3600 * 1000));
        // lastTime = 0;
    }

    //获取视频信息
    let playingRes, preview;
    //获取视频信息。这里用以出错，这里做容错处理，多次获取
    for (let k = 0; k < 3; k++) {
        try {
            playingRes = await api.getVideoInfo(globalStore, courseObj, cellObj);
            // { code: -2, msg: '请求次数过多，请一分钟后再试' }
            preview = JSON.parse(playingRes.preview);
            break;
        } catch (error) {
            if (k === 2) {
                await infoLogger(`${cellLogStr}：从服务器获取视频信息失败，已经尝试三次 《${cellObj.title}》`, 'red');
                return;
            } else {
                await new Promise(res => setTimeout(res, 61 * 1000));
            }
        }
    }

    // 获取视频url，一个窗口
    let videoUrl = preview.urls.preview;
    // 三个窗口
    if (preview.urls.status) {
        videoUrl = preview.urls.preview + '/teacher.mp4';
    }

    // 获取视频长度
    let duration = await getVideoDurationInSeconds(videoUrl, ffprobePath);
    duration = Math.floor(duration * 1);

    let positionRes;

    // 秒刷
    positionRes = await api.recordVideoPosition(globalStore, courseObj, cellObj, duration);
    if (positionRes.code == 1 && positionRes.passed) {
        await infoLogger(`${cellLogStr}：视频已经完成`, 'green');
        return;
    }
    if (positionRes.msg && positionRes.msg.includes('记录中的信息比当前的大')) {
        try {
            await api.finishChapter(globalStore, courseObj, cellObj);
            await infoLogger(`${cellLogStr}：视频完成，《${cellObj.title}》`, 'green');
        } catch (e) {
            await infoLogger(`${cellLogStr}：更改视频状态失败，原因：${e.message} 《${cellObj.title}》`, 'red');
        } finally {
            return;
        }
    }

    if (positionRes.code != 1) {
        await infoLogger(`秒刷失败，10秒后进行一比一完成,${JSON.stringify(positionRes)}`);
        await new Promise(r => setTimeout(r, 10 * 1000));
    }

    lastTime = Math.floor(lastTime * 1);
    await infoLogger(`${cellLogStr}：上次看到${lastTime}秒`);
    for (let i = lastTime; i <= duration; i += baseTime) {
        positionRes = await api.recordVideoPosition(globalStore, courseObj, cellObj, i);

        // await infoLogger(`${JSON.stringify(positionRes)}`);

        if (positionRes.code == 1 && positionRes.passed) {
            await infoLogger(`${cellLogStr}：视频已经完成`, 'green');
            break;
        }

        if (positionRes.code == -1) {
            await infoLogger(`${cellLogStr}：视频更新失败,${JSON.stringify(positionRes)}`, 'red');
            return;
        }

        await infoLogger(`${cellLogStr}：视频已完成 ${i}秒 剩余 ${duration - i}秒`);
        await new Promise(r => setTimeout(r, 60 * 1000));

        // 确保最后一次能报告到duration
        if (i + baseTime > duration && i < duration) {
            // 只有一次小循环了
            i = duration - baseTime; // 保证下一次i+60正好等于duration
        }
    }

    try {
        await api.finishChapter(globalStore, courseObj, cellObj);
    } catch (e) {
        await infoLogger(`${cellLogStr}：更改视频状态失败，原因：${e.message} 《${cellObj.title}》`, 'red');
        return;
    }
}

module.exports = videoTask;
