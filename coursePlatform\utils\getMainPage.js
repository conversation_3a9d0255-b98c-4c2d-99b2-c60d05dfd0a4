// ========= 公共模块 =========
let UserAgent = require('user-agents');

let puppeteer = require('puppeteer-extra');
let StealthPlugin = require('puppeteer-extra-plugin-stealth');
puppeteer.use(StealthPlugin());

// let puppeteer=require('puppeteer');

// let AnonymizeUA = require('puppeteer-extra-plugin-anonymize-ua');
// puppeteer.use(AnonymizeUA());

// let path = require('path');
// let executablePath;
// if (process.pkg) {
//     // pkg 打包环境
//     executablePath = path.join(path.dirname(process.execPath), 'static/Chrome-bin/chrome.exe');
// } else {
//     // 开发环境
//     executablePath = path.resolve('static/Chrome-bin/chrome.exe');
// }

async function getMainPage(taskOptions) {
    let args = [
        '--mute-audio', //静音
        '--window-size=1920,1080', // 设置浏览器窗口的大小
        '--use-fake-ui-for-media-stream', //绕过摄像头，麦克风权限
    ];
    if (global.useProxy) args.push(`--proxy-server=http://${global.proxyObj.ip}:${global.proxyObj.port}`);

    // 创建浏览器实例browser
    let browser = await puppeteer.launch({
        headless: taskOptions.isHeadless, //显示浏览器窗口
        defaultViewport: {
            //设置浏览器视口尺寸
            width: 1920,
            height: 1080,
        },
        args: args,
        //利用本地chrome浏览器来测试
        executablePath: `C:/Program Files/Google/Chrome/Application/chrome.exe`,
        // executablePath: executablePath,
    });

    //创建页面实例
    let pages = await browser.pages();
    let mainPage = pages[0];

    if (global.useProxy) {
        // 设置代理认证信息
        await mainPage.authenticate({
            username: global.proxyObj.username + '', // 代理的用户名
            password: global.proxyObj.password + '', // 代理的密码
        });
    }

    let userAgent = new UserAgent({ deviceCategory: 'desktop' }).toString();
    let agentStr = userAgent.toString();

    //设置页面的userAgent
    // await mainPage.setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.181 Safari/537.36");
    await mainPage.setUserAgent(agentStr);
    return { mainPage, browser };
}

module.exports = getMainPage;

if (false) {
    (async () => {
        let taskOptions = {
            isHeadless: false,
            isProxy: true,
        };

        for (let i = 0; i < 1; i++) {
            let { mainPage, browser } = await getMainPage(taskOptions);
            //设置页面的userAgent
            // 设置请求头，表明接受中文内容
            await mainPage.setExtraHTTPHeaders({
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            });
        }

        // await mainPage.goto('http://www.baidu.com', { waitUntil: "networkidle0" });
    })();
}
