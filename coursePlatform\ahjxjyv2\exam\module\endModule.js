let api = require('../../utils/api.js');

async function endModule(infoLogger, mainPage, globalStore) {
    let taskObj = globalStore.taskObj;
    let storageObj = globalStore.storageObj;
    // // 从页面的session获取保存的token
    // let storageStr = await mainPage.evaluate(() => {
    //     let token = localStorage.getItem('token');
    //     token = JSON.parse(token);
    //     let studentId = localStorage.getItem('Student');
    //     let adminUserType = localStorage.getItem('AdminUserType');
    //     let storage = {
    //         token: 'Bearer ' + token.encryptedAccessToken,
    //         studentId: studentId,
    //         adminUserType: adminUserType,
    //     };
    //     return JSON.stringify(storage);
    // });
    // let storageObj = JSON.parse(storageStr);

    let examListRes = await api.getExamList(storageObj, taskObj.year, taskObj.term);
    let examList = examListRes.items;

    // 如果有课程名筛选条件，就筛选课程
    if (taskObj.coursename) {
        examList = examList.filter(item => item.courseName.includes(taskObj.coursename));
    }

    let examLength = examList.length;

    let finalResult = [];
    //获取进度
    for (let i = 0; i < examList.length; i++) {
        let courseObj = examList[i];
        if(courseObj.state==1){
            courseObj.score+='(有主观题)'
        }
        finalResult.push({
            courseName: courseObj.courseName,
            progress: courseObj.score,
        });
    }

    await infoLogger('任务已完成', 'green');

    //返回数据
    return {
        finalResult,
        warningMessage: globalStore.warningMessage,
    };
}

module.exports = endModule;