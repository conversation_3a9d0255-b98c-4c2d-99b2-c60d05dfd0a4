const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('course', {
    id: {
      type: DataTypes.STRING(255),
      allowNull: false,
      primaryKey: true,
      comment: "id，用uuid生成"
    },
    user: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "发起任务的人"
    },
    username: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "用户名"
    },
    password: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "密码"
    },
    schoolname: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "学校名称"
    },
    platform: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "平台"
    },
    year: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "学年"
    },
    term: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "学期"
    },
    coursename: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "课程名称"
    },
    others: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "其他信息：例如手机端人脸识别"
    },
    schoolurl: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "学校url"
    },
    state: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "状态（等待\/正在进行\/未全部完成\/完成\/失败\/中断）"
    },
    add_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "任务添加时间"
    },
    start_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "任务开始时间"
    },
    end_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "任务结束时间"
    },
    error_info: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: "错误信息"
    },
    final_result: {
      type: DataTypes.JSON,
      allowNull: true,
      comment: "最终结果"
    },
    pc_id: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "哪台机器在运行"
    },
    comment: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "备注"
    }
  }, {
    sequelize,
    tableName: 'course',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
    ]
  });
};
