let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api.js');

async function signinModule(infoLogger, mainPage, globalStore) {
    let taskObj = globalStore.taskObj;

    // 获取验证码
    let getCodeImageRes = await api.getCodeImage();
    let base64Image = getCodeImageRes.imageCode.slice(23);

    let code;
    try {
        // 获取验证码 自己部署
        let myValidateCodeRes = await api.myValidateCode(base64Image);
        code = myValidateCodeRes.result;
    } catch (error) {
        await infoLogger(`验证码识别失败，进行第三方API识别`, 'red');
        // 获取验证码 第三方API
        let validateCodeResult = await api.validateCode(base64Image);
        code = validateCodeResult.pic_str;
    }

    // 登录
    let loginRes;
    for (let i = 1; i <= 3; i++) {
        try {
            await infoLogger(`开始第${i}次登录`); //记录日志
            loginRes = await api.login(taskObj.username, taskObj.password, code, getCodeImageRes.id);
            break;
        } catch (error) {
            let errorMessage = error.message;
            if (errorMessage.includes('验证码错误')) {
                await infoLogger(`验证码识别错误`, 'red'); //记录日志
                getCodeImageRes = await api.getCodeImage();
                base64Image = getCodeImageRes.imageCode.slice(23);
                validateCodeResult = await api.validateCode(base64Image);
                code = validateCodeResult.pic_str;
                continue;
            } else if (errorMessage.includes('密码错误')) {
                await infoLogger(`登录失败，密码错误`, 'red'); //记录日志
                throw new Error('密码错误');
            } else {
                await infoLogger(`登录失败，未知错误`, 'red'); //记录日志
                throw new Error(error);
            }
        }
    }

    // 获取用户信息
    let currentUserTypeRes = await api.currentUserType(loginRes);

    let storageObj = {
        token: `Bearer ${loginRes.encryptedAccessToken}`,
        studentId: currentUserTypeRes[0].userIdentity[0].id,
        adminUserType: 4,
    };
    globalStore.storageObj = storageObj;

    await infoLogger(`登录成功`, 'green'); //记录日志

    // // 1.打开登录页面
    // await pageTools.gotoWithRetry(mainPage, 'https://jxjynew.ahjxjy.cn/login', { waitUntil: 'networkidle0' }, 3, infoLogger);

    // //2.输入用户信息
    // await mainPage.type('input[placeholder*=请输入用户]', taskObj.username); //输入用户名
    // await mainPage.type('input[placeholder*=请输入密码]', taskObj.password); //输入密码

    // // 获取验证码
    // let base64Image = await mainPage.evaluate(() => {
    //     let imgTag = document.querySelector('div.verify-img > img');
    //     return imgTag.src.slice(23);
    // });
    // let validateCodeResult = await api.validateCode(base64Image);

    // //  输入验证码
    // await mainPage.type('input[placeholder*=请输入图形]', validateCodeResult.pic_str);

    // //3.开始登录
    // let signinHandle = await mainPage.click('div.el-form-item.form_btn > div > button'); //获取登录按钮
    // // await signinHandle.click(); //点击登录
    // await new Promise(resolve => setTimeout(resolve, 3000)); //等待加载完成

    // // 从页面的session获取保存的token
    // let storageStr;
    // //判断是否登录成功，显示出来“学生空间按钮”，如果不出现这个界面，说明密码错误
    // try {
    //     storageStr = await mainPage.evaluate(() => {
    //         let token = localStorage.getItem('token');
    //         token = JSON.parse(token);
    //         let studentId = localStorage.getItem('Student');
    //         let adminUserType = localStorage.getItem('adminUserType');
    //         let storage = {
    //             token: 'Bearer ' + token.encryptedAccessToken,
    //             studentId: studentId,
    //             adminUserType: adminUserType,
    //         };
    //         return JSON.stringify(storage);
    //     });
    // } catch (error) {
    //     await infoLogger(`登录失败，密码错误`, 'red'); //记录日志
    //     throw new Error(error); //抛出错误，结束进程池
    // }

    // let storageObj = JSON.parse(storageStr);
    // globalStore.storageObj = storageObj;
}

module.exports = signinModule;
