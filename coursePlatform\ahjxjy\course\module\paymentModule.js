let api = require('../../utils/api.js');

async function paymentModule(infoLogger, globalStore, taskObj) {
    let finalResult = [];

    let paymentResult;
    //重新获取课程列表
    try {
        paymentResult = await api.getPayment(globalStore, taskObj);
    } catch (error) {
        await infoLogger('获取学费出错', 'red'); //记录日志
        throw error; //抛出错误，结束进程池
    }
    let paymentList = paymentResult.list;

    //获取进度
    for (let i = 0; i < paymentList.length; i++) {
        let paymentObj = paymentList[i];
        if (paymentObj.ISpayment == 1) paymentObj.status = '<span style="color:green">已缴费</span>';
        else paymentObj.status = '<span style="color:red">未缴费</span>';
        finalResult.push({
            courseName: `${paymentObj.StudyYear}学年第${paymentObj.studyTerm}学期：${paymentObj.status}`,
            progress: paymentObj.PaymentMoney,
        });
    }

    //返回数据
    return {
        finalResult,
        warningMessage: globalStore.warningMessage,
    };
}

module.exports = paymentModule;