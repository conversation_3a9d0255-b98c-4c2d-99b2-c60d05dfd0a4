let WebSocket = require('ws');
let pcId = require('../config/pcId');
let user = require('../config/user.js');
let webSocketServerUrl = require('../config/webSocketServerUrl');
let Model = require('../config/sequelize.config.js');

let ws = null;
let lastHeartbeatTime = Date.now(); // 记录最后一次“收到服务器消息/心跳”的时间戳
let heartbeatCheckInterval = null; // 客户端自己开的一个定时器，用于检测是否超时

// 重连相关
let reconnectAttempts = 0; // 当前已重连次数
const maxReconnectAttempts = 6; // 最多重连次数
const reconnectDelay = 30 * 1000; // 每次重连等待时间 (毫秒)
const heartbeatIntervalTime = 40 * 1000; // 心跳检测间隔时间 (毫秒)

// 连接失败后的处理函数
async function handleDisconnect(pool) {
    console.log(`${new Date().toLocaleString()}：开始中止程序操作`);
    // 所有正在进行的任务状态改为 服务器中断
    let clientTask = await Model.cluster.findAll({
        where: {
            pc_id: pcId,
        },
    });
    clientTask = clientTask ? clientTask : [];
    for (let task of clientTask) {
        task = task.get({ plain: true });
        let ModelFn;
        if (task.type == 'course') ModelFn = Model.course;
        if (task.type == 'exam') ModelFn = Model.exam;
        if (ModelFn) {
            await ModelFn.update(
                {
                    state: '服务器中断',
                },
                {
                    where: {
                        id: task.task_id,
                        state: '正在进行',
                    },
                }
            );
        }
    }

    // 从数据库删除所有进程
    await Model.cluster.destroy({
        where: {
            pc_id: pcId,
        },
    });

    // 结束所有进程
    pool.workerPool.forEach(poolWorker => {
        poolWorker.worker.kill();
    });

    clearInterval(heartbeatCheckInterval);

    console.log(`${new Date().toLocaleString()}：中止程序操作完成`);
    throw new Error('服务器断开连接或者断网，重连失败，请检查网络连接');
}

// 重连函数，只有确认服务器真的不行的时候，才能触发服务器的close事件
function reconnect(pool) {
    clearInterval(heartbeatCheckInterval);
    reconnectAttempts++;
    if (reconnectAttempts > maxReconnectAttempts) {
        console.log(`${new Date().toLocaleString()}：超过最大重连次数（${maxReconnectAttempts}）`);
        handleDisconnect(pool);
        return;
    }

    console.log(`${new Date().toLocaleString()}：30秒后，进行第 ${reconnectAttempts} 次重连...`);
    // 这里增加30秒延迟是因为连接失败会触发close事件，然后立马又会进行重连了
    setTimeout(() => {
        console.log(`${new Date().toLocaleString()}：正在进行第 ${reconnectAttempts} 次重连...`);
        webSocketClient(pool);
    }, reconnectDelay);
}

async function webSocketClient(pool) {
    let clientTask = await Model.cluster.findAll({
        where: {
            pc_id: pcId,
        },
    });
    clientTask = clientTask ? clientTask : [];
    for (let task of clientTask) {
        if (task.state == '客户端中断') {
            console.log(`${new Date().toLocaleString()}：服务器已经判断客户端中断，断开连接`);
            throw new Error('服务器已经判断客户端中断，断开连接');
        }
    }

    // 创建 WebSocket 客户端实例并连接到服务器
    ws = new WebSocket(webSocketServerUrl);
    pool.ws = ws;

    // 当连接成功时，把自己的id告诉服务器
    ws.on('open', () => {
        console.log(`${new Date().toLocaleString()}：服务器连接成功`);
        lastHeartbeatTime = Date.now(); // 避免还没等服务器发消息/心跳就被判定超时
        let dataObj = {
            pcId,
            user,
            wsType: 'connect',
            queueLength: pool.taskQueue.length,
            clusterSize: pool.size,
        };
        let data = JSON.stringify(dataObj);
        ws.send(data);
        reconnectAttempts = 0;
    });

    // 当接收到消息时runTask delWorker addWorker
    ws.on('message', data => {
        lastHeartbeatTime = Date.now();
        let dataObj = JSON.parse(data);
        switch (dataObj.wsType) {
            case 'runTask': {
                pool.enqueueTask(dataObj);
                break;
            }

            case 'resetWorker': {
                pool.resetWorker(dataObj);
                break;
            }

            case 'getTaskQueue': {
                let sendStr = JSON.stringify({
                    pcId,
                    user,
                    wsType: 'taskQueue',
                    taskQueue: pool.taskQueue,
                });
                ws.send(sendStr);
                break;
            }

            case 'close': {
                console.log(`${new Date().toLocaleString()}：服务器判定客户端已经关闭连接，将关闭所有工作进程`);
                pool.workerPool.forEach(poolWorker => {
                    poolWorker.worker.kill();
                });
                ws.close();
                throw new Error('服务器判定客户端已经关闭连接，将关闭所有工作进程');
                break;
            }

            default:
                break;
        }
    });

    ws.on('ping', () => {
        lastHeartbeatTime = Date.now();
        // 一般情况下 ws 会自动发 pong，不需要手动 ws.pong()
        // ws.pong();
    });

    // 当发生错误时
    ws.on('error', error => {
        console.error(`${new Date().toLocaleString()}：与服务器连接出错`);
    });

    // 服务器主动断开连接，客户端可以通过close检测
    ws.on('close', async (code, reason) => {
        console.log(`${new Date().toLocaleString()}：服务器断开连接，尝试重连`);
        reconnect(pool);
    });

    // 服务器长时间没有响应
    heartbeatCheckInterval = setInterval(() => {
        let now = Date.now();
        // 如果超过5分钟都没收到任何消息或服务器 ping，就认为断线
        if (now - lastHeartbeatTime > 5 * 60 * 1000) {
            console.log(`${new Date().toLocaleString()}：5分钟未收到服务器的消息或心跳`);
            reconnect(pool);
        }
    }, heartbeatIntervalTime);

    return ws;
}

module.exports = webSocketClient;
