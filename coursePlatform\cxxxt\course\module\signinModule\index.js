let handleClickValidate = require('./handleClickValidate.js');

async function signinModule(infoLogger, globalStore, mainPage) {
    let taskObj = globalStore.taskObj;
    await infoLogger('开始登录');
    let phoneRegex = /^1[3-9]\d{9}$/;
    // 如果用户名是手机号，就用手机号的页面登录
    if (phoneRegex.test(taskObj.username)) {
        taskObj.schoolurl = 'https://passport2.chaoxing.com/login';
        //1.跳转到登录页面
        await mainPage.goto(taskObj.schoolurl, { waitUntil: 'networkidle0' });

        if (!process.env.HAS_PERMISSION) {
            return;
        }

        //2.输入学校名称，用户名，密码
        await mainPage.type('#phone', taskObj.username); //输入用户名
        await mainPage.type('#pwd', taskObj.password); //输入密码

        //点击登录
        await mainPage.click('#loginBtn');
        await new Promise(r => setTimeout(r, 2000));
    }

    // 如果用户名不是手机号，就用机构账号登录
    if (!phoneRegex.test(taskObj.username)) {
        taskObj.schoolurl =
            'https://passport2.chaoxing.com/login?loginType=3&newversion=true&fid=-1&hidecompletephone=0&ebook=0&allowSkip=0&forbidotherlogin=0&refer=http%3A%2F%2Fi.mooc.chaoxing.com&accounttip=&pwdtip=&doubleFactorLogin=0&independentId=0';
        //1.跳转到登录页面
        await mainPage.goto(taskObj.schoolurl, { waitUntil: 'networkidle0' });

        //2.输入学校名称，用户名，密码
        await mainPage.type('#inputunitname', taskObj.schoolname); //输入学校名称
        await mainPage.type('#uname', taskObj.username); //输入用户名
        await mainPage.type('#password', taskObj.password); //输入密码

        //点击登录
        await mainPage.click('#loginBtn');
        //等带滑动验证图片加载完毕
        await new Promise(r => setTimeout(r, 2000));

        //3.处理滑动验证码
        await handleClickValidate(mainPage, infoLogger);
    }

    //有可能弹出输入手机号的界面
    try {
        await mainPage.waitForSelector('#setphone', {
            timeout: 2000,
        });
        await infoLogger('跳过设置手机号');
        await mainPage.evaluate(() => {
            skip();
        });
    } catch (error) {
        await infoLogger('无需设置手机号');
    }

    await new Promise(resolve => setTimeout(resolve, 3000)); //等待加载完成

    //判断是否登录成功
    try {
        await mainPage.waitForSelector('#space_nickname > p', {
            timeout: 2000,
        });
    } catch (error) {
        await infoLogger('登录失败', 'red');
        throw new Error('登录失败');
    }

    await infoLogger('登录成功', 'green');
    await new Promise(resolve => setTimeout(resolve, 2000)); //等待加载完成

    // // 校验学校名称
    // await mainPage.goto('https://passport2.chaoxing.com/mooc/accountManage', { waitUntil: 'networkidle0' });
    // let schoolName = await mainPage.evaluate(() => {
    //     return document.querySelector('#messageFid>li').innerText;
    // });
    // if (!schoolName.includes(taskObj.schoolname)) {
    //     await infoLogger(`学校名称不匹配，学习通默认学校为：${schoolName}，指定学校为：${taskObj.schoolname}`, 'red');
    //     throw new Error('学校名称不匹配');
    // }
}

module.exports = signinModule;
