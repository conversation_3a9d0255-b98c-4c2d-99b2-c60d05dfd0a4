let runPCExam = require('./runPCExam');
let runMobileExam = require('./runMobileExam');

async function runExamModule(mainPage, taskObj, infoLogger, courseList, finalResult) {
    for (let i = 0; i < courseList.length; i++) {
        // 取出一个考试对象
        let courseObj = courseList[i];
        let courseLogStr = `课程[${i + 1}/${courseList.length}] 《${courseObj.courseName}》`;

        // 分数
        if (courseObj.examScore >= 80) {
            await infoLogger(`${courseLogStr}，考试已经完成，分数为${courseObj.examScore}份，跳过`, 'green');
            continue;
        }

        // 考试终端限制
        if (courseObj.examType.includes('学习通')) {
            await infoLogger(`${courseLogStr} 本次考试教师已设置只允许手机APP考试，请使用手机APP参与考试`, 'red');
            continue;
        }

        if (taskObj.others.includes('手机端')) {
            await infoLogger(`${courseLogStr} 当前考试模式为手机端`);
            await runMobileExam(mainPage, taskObj, courseObj, infoLogger, finalResult, courseLogStr);
        }

        // 默认就是电脑端考试
        if (taskObj.others.includes('电脑端')) {
            await infoLogger(`${courseLogStr} 当前考试模式为电脑端`);
            await runPCExam(mainPage, taskObj, courseObj, infoLogger, finalResult, courseLogStr);
        }
    }
}

module.exports = runExamModule;
