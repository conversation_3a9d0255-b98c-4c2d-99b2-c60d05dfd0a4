let { numbersToLetters, lettersToNumbers } = require('../format.js');

function matchOption(answerStr, answerOptions, questionObj) {
    switch (questionObj.type) {
        case '判断题': {
            //特殊情况 answer可能包含空字符串 data: '√\n \n',
            answerStr = answerStr.replace(/\s+/g, '');
            //处理 判断题 的答案，统一转为对或错
            if ('正确是对√trueA'.includes(answerStr)) {
                return 'A';
            }
            if ('错误否错×falseB'.includes(answerStr)) {
                return 'B';
            }
            return false;
        }
        case '单选题': {
            if(!Array.isArray(answerOptions) || answerOptions.length == 0){
                return false;
            }
            // "A,B,D"=>"ABD" 去除掉所有不是选项的字符
            answerStr = answerStr.replace(/[^A-Za-z,]/g, '');
            answerStr = answerStr.toUpperCase();

            //单选题的答案长度只能是1
            if (answerStr.length != 1) {
                return false;
            }
            // //多选题答案只能是字母
            // if (!/^[A-Za-z]$/.test(answerStr)) {
            //     return false;
            // }
            // 题库的options有可能为空
            if (answerOptions.length == 0) {
                return false;
            }

            //获取题库答案的顺序，A->0 B->1 C->2 D->3
            // let index = { A: 0, B: 1, C: 2, D: 3, E: 4, F: 5, G: 6 }[answerStr];
            // let index=lettersToNumbers(answerStr);

            //根据顺序，获取答案选项对应的答案内容
            // let optionStr = answerOptions[index];
            let optionStr = answerOptions.find(item=>{
                let firstStr=item.slice(0,1);
                if(firstStr==answerStr){
                    return item
                }
            })
            if (!optionStr) {
                return false;
            }

            //用题库正确的答案选项,去匹配题目选项
            let sortedOptions = questionObj.options.slice().sort((a, b) =>  b.length- a.length);
            for (let j = 0; j < sortedOptions.length; j++) {
                // let [key, value] = sortedOptions[j].split(':');
                let [key, value] = sortedOptions[j].split(/:(.+)/);
                if (optionStr.includes(value)) {
                    return key;
                }
            }
        }
        case '多选题': {
            if(!Array.isArray(answerOptions) || answerOptions.length == 0){
                return false;
            }
            let resultArr = [];
            // "A,B,D"=>"ABD" 去除掉所有不是选项的字符
            answerStr = answerStr.replace(/[^A-Za-z,]/g, '');
            answerStr = answerStr.toUpperCase();

            if (answerStr.length <= 1) {
                return false;
            }

            //多选题答案只能是字母
            if (!/^[A-Za-z]+$/.test(answerStr)) {
                return false;
            }

            //2.用答案匹配所有问题选项
            answerOpLoop: for (let j = 0; j < answerStr.length; j++) {
                //1.从题库答案中，获取一个答案选项，例如A
                let ansOption = answerStr[j];
                ansOption = ansOption.toUpperCase();

                // //2.获取这个选项的顺序，例如A->0
                // let ansIndex = { A: 0, B: 1, C: 2, D: 3, E: 4, F: 5, G: 6 }[ansOption];

                // //3.根据顺序，获取这个答案对应的内容
                // let ansValue = answerOptions[ansIndex];

                let ansValue = answerOptions.find(item=>{
                    let firstStr=item.slice(0,1);
                    if(firstStr==ansOption){
                        return item
                    }
                })

                // 曾经遇到一个特殊情况，题库虚只有四个选项ABCD，但是答案是ABCDE
                if(!ansValue){
                    return false;
                }

                //三.用答案内容匹配所有问题选项
                // D:will have been working ->[ "A:will work","B:have been working","C:have worked","D:will have been working"]
                // 实际返回B,但正确答案应该是D,目前没有想到什么好的办法，可以考虑对选项进行排序
                let sortedOptions = questionObj.options.slice().sort((a, b) =>  b.length- a.length);
                optionsLoop: for (let k = 0; k < sortedOptions.length; k++) {
                    //取出一个选项 A:帮助青年学生掌握最新理论成果
                    let queOption = sortedOptions[k];
                    //把选项的 值和内容 分开
                    // let [queIndex, queValue] = queOption.split(':');
                    let [queIndex, queValue] = queOption.split(/:(.+)/);
                    //选项值是否匹配答案的值 
                    if (ansValue.includes(queValue)) {
                        //避免一个特殊情况，答案非主要矛盾，选项C主要矛盾，D非主要矛盾，这样会匹配到两个C
                        if (resultArr.includes(queIndex)) {
                            continue optionsLoop;
                        }
                        //如果匹配到了，说明就是正确答案
                        resultArr.push(queIndex);
                        continue answerOpLoop;
                    }
                }
                //用答案匹配的题目，如果有一个正确答案，不存在于问题里面，说明这个答案选项不对。
                resultArr = [];
                return false;
            }

            //对结果进行排序  [ 'a', 'b', 'e', 'd' ] =>[ 'a', 'b', 'd', 'e' ]
            resultArr = resultArr.slice().sort();
            let answers = resultArr.join('');
            if (answers.length > 1) {
                return answers;
            }
        }
        default:
            return answerStr;
    }
}

// 主要为了获取answerStr 和 answerOptions
function matchAnswerWithLocal(answersRes, questionObj) {
    for (let i = 0; i < answersRes.qlist.length; i++) {
        //取出一个答案对象
        let currentAnswerObj = answersRes.qlist[i];

        // 格式化答案字符串，本地题库采用的标准格式，不用处理
        let answerStr;
        switch (answersRes.source) {
            case 'aidian': {
                //处理答案格式，["A,B,D"] => "A,B,D"
                answerStr = currentAnswerObj.answer[0];
                break;
            }
            case 'local': {
                answerStr = currentAnswerObj.answers;
                break;
            }
            default: {
                answerStr = currentAnswerObj.answers;
                break;
            }
        }
        //有可能会遇到空答案
        if (!answerStr) return false;

        let answerOptions = currentAnswerObj.options;
        let result = matchOption(answerStr, answerOptions, questionObj);
        if (!result) {
            continue;
        } else {
            return result;
        }
    }
}

module.exports = matchAnswerWithLocal;

// 单元测试
if (false) {
    let questionObj = {
        content: '中石油采用规范的项目管理方法，由（)构成的三级项目管理体系。',
        type: '多选题',
        id: 'y2D1212HrdcarSYc4answer212003548',
        options: ['A:信息化工作领导小组', 'B:开发部', 'C:各项目指导委员会', 'D:各项目经理部', 'E:监理组'],
        platform: '超星学习通',
        courseid: '233789098',
    };
    let answersRes = {
        code: 1,
        msg: '获取成功',
        qlist: [
            {
                qid: 'kdCCLeLmJllnAFxiPTCZyg2N4NB8xqRx%2BtWG9v54Z3gw0ymKa46iOSOi863htBglsUCZA63P5DlP8KCHfug7dWCb49YrdO52YC18ItK2kxiv1pBKfRzRSM8YbOBUUtKluSqn5xoB05y8dbs48ks9CQ%3D%3D',
                question: '中石油采用规范的项目管理方法，由（)构成的三级项目管理体系。',
                options: ['A．开发部', 'B．各项目指导委员会', 'C．信息化工作领导小组', 'D．各项目经理部', 'E．监理组'],
                answer: ['BCD'],
            },
            {
                qid: 'kdCCLeLmJllnAFxiPTCZyixNx8L6a60qSWH4Xso3%2FIsEyxB5n4WwRN1BdE5sJ%2BhS3WqnxCiBTTqmuuzBBqJW%2FGqSFIzXRIMRRO6xIFrNgNEJfAjc5ha7PG92QFtgNZTuIGYeCHmrQqB9BhAfPj6Haw%3D%3D',
                question: '中石油采用规范的项目管理方法，由（）构成的三级项目管理体系。',
                options: ['A．开发部', 'B．各项目指导委员会', 'C．信息化工作领导小组', 'D．各项目经理部', 'E．监理组'],
                answer: [],
            },
            {
                qid: 'kdCCLeLmJllnAFxiPTCZytmcIE%2By%2BnMBurE1BFJa%2FAAZMmVCvnf7b3IWrVRmAm5Vilcw2gJ%2FvrqxLFlMtksUzv6LP2USBJRi94YexF1vlyQNGFnNUReiKBQE4l1hqsZShNtxW0Om5sAPLu%2FZNDTuYg%3D%3D',
                question: '例 3-2 中，中石油采用规范的项目管理方法，由（）构成的三级项目管理体系。',
                options: ['A、 各项目指导委员会', 'B、 信息化工作领导小组', 'C、 开发部', 'D、 各项目经理部', 'E、 监理组'],
                answer: ['ABD'],
            },
            {
                qid: 'kdCCLeLmJllnAFxiPTCZyp%2BQ4flhKssiCvETbhEqtzP5xBD83yrRZ%2F%2FijeA5S4HtD3orZDuP%2BY9Xe%2B8%2BlsMrLytbSR9Af1A26PcxF1YdnHLxURibT4TKAnr6Qd7YQ4l4wB9HZKmxsD6FRCY8tiTR0w%3D%3D',
                question: '在例3-2中，中石油采用规范的项目管理方法，由（ ）构成的三级项目管理体系。',
                options: ['信息化工作领导小组', '各项目指导委员会', '监理组', '各项目经理部', '开发部'],
                answer: ['信息化工作领导小组、各项目指导委员会'],
            },
            {
                qid: 'kdCCLeLmJllnAFxiPTCZyvwWTp07KnLK7x7lpaGaSVY8nMo9PBng1c0kO9qkne1HOrhSL0Fa32V499D%2FyCaLWOJfsUbjoI0w37MKNwmVGzfw%2F%2F%2Bn%2BgJlXhyfj2G1J9tyL7faf1Jjuj5eeBBXB3p8uQ%3D%3D',
                question: '采用项目管理方法的好处（）',
                options: ['A．经验的积累', 'B．达到预期效果', 'C．识别及应对风险', 'D．预见和规划未来'],
                answer: ['ABCD'],
            },
        ],
        source: 'aidian',
    };
    console.log(matchAnswer(answersRes, questionObj));
}
