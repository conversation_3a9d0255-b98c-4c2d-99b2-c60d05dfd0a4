const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('photo', {
    id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true
    },
    platform: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "组合唯一健"
    },
    school: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "组合唯一健"
    },
    username: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "组合唯一健"
    },
    photo_base64: {
      type: DataTypes.TEXT,
      allowNull: true
    }
  }, {
    sequelize,
    tableName: 'photo',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "platform",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "platform" },
          { name: "school" },
          { name: "username" },
        ]
      },
    ]
  });
};
