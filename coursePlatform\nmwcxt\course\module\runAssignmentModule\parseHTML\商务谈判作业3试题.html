<table id="tblDataList" onselectstart="return false" width="100%" cellpadding="3" _default_attr="1" cellspacing="1"><tbody><tr id="tr_tblDataList_0" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_0');}">
    <td scoreid="568359739610628103" id="_DataListTD_tblDataList_0"><table width="100%" border="0" id="tblItem_562420044654444561" islabel="1" keylist="" style="">
    <tbody><tr><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<b>一、单选题<\/b><SPAN IsScoreRemark=\"1\"><\/SPAN>"));</script><b>一、单选题</b><span isscoreremark="1">&nbsp;&nbsp;<font color="gray">(第1-3题每题5分)</font></span>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_1" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_1');}">
    <td scoreid="568359739610628104" id="_DataListTD_tblDataList_1"><table width="100%" border="0" id="tblItem_562420040359477264" islabel="0" keylist="lemonysoft_item_fumteipxlm_3" style="">
    <tbody><tr><td width="30" valign="top">1.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<table IsItem=1><tr><td>价格条款的谈判应由____承提。( 　)<tr><td><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_fumteipxlm_3_0 name=\"lemonysoft_item_fumteipxlm_3\" ><TD>(A)<TD><label for=lemonysoft_item_fumteipxlm_3_0>法律人员<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_fumteipxlm_3_1 name=\"lemonysoft_item_fumteipxlm_3\" ><TD>(B)<TD><label for=lemonysoft_item_fumteipxlm_3_1>商务人员<\/label><TR><TD><input type=radio value=\"C\" id=lemonysoft_item_fumteipxlm_3_2 name=\"lemonysoft_item_fumteipxlm_3\" ><TD>(C)<TD><label for=lemonysoft_item_fumteipxlm_3_2>财务人员<\/label><TR><TD><input type=radio value=\"D\" id=lemonysoft_item_fumteipxlm_3_3 name=\"lemonysoft_item_fumteipxlm_3\" ><TD>(D)<TD><label for=lemonysoft_item_fumteipxlm_3_3>技术人员<\/label><\/table><\/div><\/table>"));</script><table isitem="1"><tbody><tr><td>价格条款的谈判应由____承提。( 　)</td></tr><tr><td><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_fumteipxlm_3_0" name="lemonysoft_item_fumteipxlm_3"></td><td>(A)</td><td><label for="lemonysoft_item_fumteipxlm_3_0">法律人员</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_fumteipxlm_3_1" name="lemonysoft_item_fumteipxlm_3"></td><td>(B)</td><td><label for="lemonysoft_item_fumteipxlm_3_1">商务人员</label></td></tr><tr><td><input type="radio" value="C" id="lemonysoft_item_fumteipxlm_3_2" name="lemonysoft_item_fumteipxlm_3"></td><td>(C)</td><td><label for="lemonysoft_item_fumteipxlm_3_2">财务人员</label></td></tr><tr><td><input type="radio" value="D" id="lemonysoft_item_fumteipxlm_3_3" name="lemonysoft_item_fumteipxlm_3"></td><td>(D)</td><td><label for="lemonysoft_item_fumteipxlm_3_3">技术人员</label></td></tr></tbody></table></div></td></tr></tbody></table>
    <script language="javascript">showAnswer("lemonysoft_item_fumteipxlm_3","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_2" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_2');}">
    <td scoreid="568359739610628105" id="_DataListTD_tblDataList_2"><table width="100%" border="0" id="tblItem_562420040359477265" islabel="0" keylist="lemonysoft_item_jeyotc6qbr_2" style="">
    <tbody><tr><td width="30" valign="top">2.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<table IsItem=1><tr><td>在商务谈判中，双方地位平等是指双方在__上的平等。()　<tr><td><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_jeyotc6qbr_2_0 name=\"lemonysoft_item_jeyotc6qbr_2\" ><TD>(A)<TD><label for=lemonysoft_item_jeyotc6qbr_2_0>实力<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_jeyotc6qbr_2_1 name=\"lemonysoft_item_jeyotc6qbr_2\" ><TD>(B)<TD><label for=lemonysoft_item_jeyotc6qbr_2_1>经济利益<\/label><TR><TD><input type=radio value=\"C\" id=lemonysoft_item_jeyotc6qbr_2_2 name=\"lemonysoft_item_jeyotc6qbr_2\" ><TD>(C)<TD><label for=lemonysoft_item_jeyotc6qbr_2_2>法律<\/label><TR><TD><input type=radio value=\"D\" id=lemonysoft_item_jeyotc6qbr_2_3 name=\"lemonysoft_item_jeyotc6qbr_2\" ><TD>(D)<TD><label for=lemonysoft_item_jeyotc6qbr_2_3>级别<\/label><\/table><\/div><\/table>"));</script><table isitem="1"><tbody><tr><td>在商务谈判中，双方地位平等是指双方在__上的平等。()　</td></tr><tr><td><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_jeyotc6qbr_2_0" name="lemonysoft_item_jeyotc6qbr_2"></td><td>(A)</td><td><label for="lemonysoft_item_jeyotc6qbr_2_0">实力</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_jeyotc6qbr_2_1" name="lemonysoft_item_jeyotc6qbr_2"></td><td>(B)</td><td><label for="lemonysoft_item_jeyotc6qbr_2_1">经济利益</label></td></tr><tr><td><input type="radio" value="C" id="lemonysoft_item_jeyotc6qbr_2_2" name="lemonysoft_item_jeyotc6qbr_2"></td><td>(C)</td><td><label for="lemonysoft_item_jeyotc6qbr_2_2">法律</label></td></tr><tr><td><input type="radio" value="D" id="lemonysoft_item_jeyotc6qbr_2_3" name="lemonysoft_item_jeyotc6qbr_2"></td><td>(D)</td><td><label for="lemonysoft_item_jeyotc6qbr_2_3">级别</label></td></tr></tbody></table></div></td></tr></tbody></table>
    <script language="javascript">showAnswer("lemonysoft_item_jeyotc6qbr_2","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_3" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_3');}">
    <td scoreid="568359739610628106" id="_DataListTD_tblDataList_3"><table width="100%" border="0" id="tblItem_562420040359477266" islabel="0" keylist="lemonysoft_item_sfvh6x5iar_1" style="">
    <tbody><tr><td width="30" valign="top">3.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<table IsItem=1><tr><td>按谈判中双方所采取的的态度，可以将谈判分为立场型谈判、原则型谈判和()<tr><td><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_sfvh6x5iar_1_0 name=\"lemonysoft_item_sfvh6x5iar_1\" ><TD>(A)<TD><label for=lemonysoft_item_sfvh6x5iar_1_0>软式谈判<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_sfvh6x5iar_1_1 name=\"lemonysoft_item_sfvh6x5iar_1\" ><TD>(B)<TD><label for=lemonysoft_item_sfvh6x5iar_1_1>集体谈判<\/label><TR><TD><input type=radio value=\"C\" id=lemonysoft_item_sfvh6x5iar_1_2 name=\"lemonysoft_item_sfvh6x5iar_1\" ><TD>(C)<TD><label for=lemonysoft_item_sfvh6x5iar_1_2>横向谈判<\/label><TR><TD><input type=radio value=\"D\" id=lemonysoft_item_sfvh6x5iar_1_3 name=\"lemonysoft_item_sfvh6x5iar_1\" ><TD>(D)<TD><label for=lemonysoft_item_sfvh6x5iar_1_3>投资谈判<\/label><\/table><\/div><\/table>"));</script><table isitem="1"><tbody><tr><td>按谈判中双方所采取的的态度，可以将谈判分为立场型谈判、原则型谈判和()</td></tr><tr><td><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_sfvh6x5iar_1_0" name="lemonysoft_item_sfvh6x5iar_1"></td><td>(A)</td><td><label for="lemonysoft_item_sfvh6x5iar_1_0">软式谈判</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_sfvh6x5iar_1_1" name="lemonysoft_item_sfvh6x5iar_1"></td><td>(B)</td><td><label for="lemonysoft_item_sfvh6x5iar_1_1">集体谈判</label></td></tr><tr><td><input type="radio" value="C" id="lemonysoft_item_sfvh6x5iar_1_2" name="lemonysoft_item_sfvh6x5iar_1"></td><td>(C)</td><td><label for="lemonysoft_item_sfvh6x5iar_1_2">横向谈判</label></td></tr><tr><td><input type="radio" value="D" id="lemonysoft_item_sfvh6x5iar_1_3" name="lemonysoft_item_sfvh6x5iar_1"></td><td>(D)</td><td><label for="lemonysoft_item_sfvh6x5iar_1_3">投资谈判</label></td></tr></tbody></table></div></td></tr></tbody></table>
    <script language="javascript">showAnswer("lemonysoft_item_sfvh6x5iar_1","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_4" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_4');}">
    <td scoreid="568359739610628107" id="_DataListTD_tblDataList_4"><table width="100%" border="0" id="tblItem_562420044654444560" islabel="1" keylist="" style="">
    <script language="javascript">var oSpan=document.getElementsByTagName("SPAN");for(var i=oSpan.length-1;i>=0;i--){if(oSpan[i].getAttribute("IsScoreRemark")=="1"){oSpan[i].innerHTML="&nbsp;&nbsp;<font color=gray>(第1-3题每题5分)</font>";break;}}</script>
    <tbody><tr><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<b>二、判断题<\/b><SPAN IsScoreRemark=\"1\"><\/SPAN>"));</script><b>二、判断题</b><span isscoreremark="1">&nbsp;&nbsp;<font color="gray">(第1-8题每题5分)</font></span>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_5" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_5');}">
    <td scoreid="568359739610628108" id="_DataListTD_tblDataList_5"><table width="100%" border="0" id="tblItem_562420044654444546" islabel="0" keylist="lemonysoft_item_key_506735_53593" style="">
    <tbody><tr><td width="30" valign="top">1.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">一个国家企业的决策程序属于影响国际商务谈判中的法律因素。<div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_506735_53593_0 name=\"lemonysoft_item_key_506735_53593\" ><TD>(A)<TD><label for=lemonysoft_item_key_506735_53593_0>对<\/label><TD><input type=radio value=\"B\" id=lemonysoft_item_key_506735_53593_1 name=\"lemonysoft_item_key_506735_53593\" ><TD>(B)<TD><label for=lemonysoft_item_key_506735_53593_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">一个国家企业的决策程序属于影响国际商务谈判中的法律因素。<div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_506735_53593_0" name="lemonysoft_item_key_506735_53593"></td><td>(A)</td><td><label for="lemonysoft_item_key_506735_53593_0">对</label></td><td><input type="radio" value="B" id="lemonysoft_item_key_506735_53593_1" name="lemonysoft_item_key_506735_53593"></td><td>(B)</td><td><label for="lemonysoft_item_key_506735_53593_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506735_53593","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_6" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_6');}">
    <td scoreid="568359739610628109" id="_DataListTD_tblDataList_6"><table width="100%" border="0" id="tblItem_562420044654444547" islabel="0" keylist="lemonysoft_item_key_506734_32000" style="">
    <tbody><tr><td width="30" valign="top">2.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">一名合格的国际商务谈判人员，应具备\"X\"型的知识结构。<div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_506734_32000_0 name=\"lemonysoft_item_key_506734_32000\" ><TD>(A)<TD><label for=lemonysoft_item_key_506734_32000_0>对<\/label><TD><input type=radio value=\"B\" id=lemonysoft_item_key_506734_32000_1 name=\"lemonysoft_item_key_506734_32000\" ><TD>(B)<TD><label for=lemonysoft_item_key_506734_32000_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">一名合格的国际商务谈判人员，应具备"X"型的知识结构。<div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_506734_32000_0" name="lemonysoft_item_key_506734_32000"></td><td>(A)</td><td><label for="lemonysoft_item_key_506734_32000_0">对</label></td><td><input type="radio" value="B" id="lemonysoft_item_key_506734_32000_1" name="lemonysoft_item_key_506734_32000"></td><td>(B)</td><td><label for="lemonysoft_item_key_506734_32000_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506734_32000","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_7" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_7');}">
    <td scoreid="568359739610628110" id="_DataListTD_tblDataList_7"><table width="100%" border="0" id="tblItem_562420044654444548" islabel="0" keylist="lemonysoft_item_key_506733_11820" style="">
    <tbody><tr><td width="30" valign="top">3.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">受盘人在还盘之后，又表示接受，此项接受依然有效。<div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_506733_11820_0 name=\"lemonysoft_item_key_506733_11820\" ><TD>(A)<TD><label for=lemonysoft_item_key_506733_11820_0>对<\/label><TD><input type=radio value=\"B\" id=lemonysoft_item_key_506733_11820_1 name=\"lemonysoft_item_key_506733_11820\" ><TD>(B)<TD><label for=lemonysoft_item_key_506733_11820_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">受盘人在还盘之后，又表示接受，此项接受依然有效。<div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_506733_11820_0" name="lemonysoft_item_key_506733_11820"></td><td>(A)</td><td><label for="lemonysoft_item_key_506733_11820_0">对</label></td><td><input type="radio" value="B" id="lemonysoft_item_key_506733_11820_1" name="lemonysoft_item_key_506733_11820"></td><td>(B)</td><td><label for="lemonysoft_item_key_506733_11820_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506733_11820","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_8" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_8');}">
    <td scoreid="568359739610628111" id="_DataListTD_tblDataList_8"><table width="100%" border="0" id="tblItem_562420040359477267" islabel="0" keylist="lemonysoft_item_key_506741_59802" style="">
    <tbody><tr><td width="30" valign="top">4.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">法国人素有\"契约之民\"的雅称，他们崇尚契约，严守信用。<div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_506741_59802_0 name=\"lemonysoft_item_key_506741_59802\" ><TD>(A)<TD><label for=lemonysoft_item_key_506741_59802_0>对<\/label><TD><input type=radio value=\"B\" id=lemonysoft_item_key_506741_59802_1 name=\"lemonysoft_item_key_506741_59802\" ><TD>(B)<TD><label for=lemonysoft_item_key_506741_59802_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">法国人素有"契约之民"的雅称，他们崇尚契约，严守信用。<div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_506741_59802_0" name="lemonysoft_item_key_506741_59802"></td><td>(A)</td><td><label for="lemonysoft_item_key_506741_59802_0">对</label></td><td><input type="radio" value="B" id="lemonysoft_item_key_506741_59802_1" name="lemonysoft_item_key_506741_59802"></td><td>(B)</td><td><label for="lemonysoft_item_key_506741_59802_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506741_59802","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_9" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_9');}">
    <td scoreid="568359739610628112" id="_DataListTD_tblDataList_9"><table width="100%" border="0" id="tblItem_562420040359477268" islabel="0" keylist="lemonysoft_item_key_506740_63704" style="">
    <tbody><tr><td width="30" valign="top">5.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">对于权力型的谈判对手，不可以主动进攻。<div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_506740_63704_0 name=\"lemonysoft_item_key_506740_63704\" ><TD>(A)<TD><label for=lemonysoft_item_key_506740_63704_0>对<\/label><TD><input type=radio value=\"B\" id=lemonysoft_item_key_506740_63704_1 name=\"lemonysoft_item_key_506740_63704\" ><TD>(B)<TD><label for=lemonysoft_item_key_506740_63704_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">对于权力型的谈判对手，不可以主动进攻。<div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_506740_63704_0" name="lemonysoft_item_key_506740_63704"></td><td>(A)</td><td><label for="lemonysoft_item_key_506740_63704_0">对</label></td><td><input type="radio" value="B" id="lemonysoft_item_key_506740_63704_1" name="lemonysoft_item_key_506740_63704"></td><td>(B)</td><td><label for="lemonysoft_item_key_506740_63704_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506740_63704","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_10" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_10');}">
    <td scoreid="568359739610628113" id="_DataListTD_tblDataList_10"><table width="100%" border="0" id="tblItem_562420040359477269" islabel="0" keylist="lemonysoft_item_key_506738_64483" style="">
    <tbody><tr><td width="30" valign="top">6.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">对于谈判中的纯风险，应采取完全回避风险策略。<div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_506738_64483_0 name=\"lemonysoft_item_key_506738_64483\" ><TD>(A)<TD><label for=lemonysoft_item_key_506738_64483_0>对<\/label><TD><input type=radio value=\"B\" id=lemonysoft_item_key_506738_64483_1 name=\"lemonysoft_item_key_506738_64483\" ><TD>(B)<TD><label for=lemonysoft_item_key_506738_64483_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">对于谈判中的纯风险，应采取完全回避风险策略。<div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_506738_64483_0" name="lemonysoft_item_key_506738_64483"></td><td>(A)</td><td><label for="lemonysoft_item_key_506738_64483_0">对</label></td><td><input type="radio" value="B" id="lemonysoft_item_key_506738_64483_1" name="lemonysoft_item_key_506738_64483"></td><td>(B)</td><td><label for="lemonysoft_item_key_506738_64483_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506738_64483","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_11" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_11');}">
    <td scoreid="568359739610628114" id="_DataListTD_tblDataList_11"><table width="100%" border="0" id="tblItem_562420044654444544" islabel="0" keylist="lemonysoft_item_key_506737_3823" style="">
    <tbody><tr><td width="30" valign="top">7.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">谈判人员的注意力，在结束阶段处于最低水平。<div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_506737_3823_0 name=\"lemonysoft_item_key_506737_3823\" ><TD>(A)<TD><label for=lemonysoft_item_key_506737_3823_0>对<\/label><TD><input type=radio value=\"B\" id=lemonysoft_item_key_506737_3823_1 name=\"lemonysoft_item_key_506737_3823\" ><TD>(B)<TD><label for=lemonysoft_item_key_506737_3823_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">谈判人员的注意力，在结束阶段处于最低水平。<div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_506737_3823_0" name="lemonysoft_item_key_506737_3823"></td><td>(A)</td><td><label for="lemonysoft_item_key_506737_3823_0">对</label></td><td><input type="radio" value="B" id="lemonysoft_item_key_506737_3823_1" name="lemonysoft_item_key_506737_3823"></td><td>(B)</td><td><label for="lemonysoft_item_key_506737_3823_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506737_3823","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_12" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_12');}">
    <td scoreid="568359739610628115" id="_DataListTD_tblDataList_12"><table width="100%" border="0" id="tblItem_562420044654444545" islabel="0" keylist="lemonysoft_item_key_506736_56681" style="">
    <tbody><tr><td width="30" valign="top">8.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">谈判开局阶段的主要任务是确定成交价格。<div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_506736_56681_0 name=\"lemonysoft_item_key_506736_56681\" ><TD>(A)<TD><label for=lemonysoft_item_key_506736_56681_0>对<\/label><TD><input type=radio value=\"B\" id=lemonysoft_item_key_506736_56681_1 name=\"lemonysoft_item_key_506736_56681\" ><TD>(B)<TD><label for=lemonysoft_item_key_506736_56681_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">谈判开局阶段的主要任务是确定成交价格。<div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_506736_56681_0" name="lemonysoft_item_key_506736_56681"></td><td>(A)</td><td><label for="lemonysoft_item_key_506736_56681_0">对</label></td><td><input type="radio" value="B" id="lemonysoft_item_key_506736_56681_1" name="lemonysoft_item_key_506736_56681"></td><td>(B)</td><td><label for="lemonysoft_item_key_506736_56681_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506736_56681","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_13" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_13');}">
    <td scoreid="568359739610628116" id="_DataListTD_tblDataList_13"><table width="100%" border="0" id="tblItem_562420040359477263" islabel="1" keylist="" style="">
    <script language="javascript">var oSpan=document.getElementsByTagName("SPAN");for(var i=oSpan.length-1;i>=0;i--){if(oSpan[i].getAttribute("IsScoreRemark")=="1"){oSpan[i].innerHTML="&nbsp;&nbsp;<font color=gray>(第1-8题每题5分)</font>";break;}}</script>
    <tbody><tr><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<b>三、多选题<\/b><SPAN IsScoreRemark=\"1\"><\/SPAN>"));</script><b>三、多选题</b><span isscoreremark="1">&nbsp;&nbsp;<font color="gray">(第1-9题每题5分)</font></span>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_14" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_14');}">
    <td scoreid="568359739610628117" id="_DataListTD_tblDataList_14"><table width="100%" border="0" id="tblItem_562420044654444549" islabel="0" keylist="lemonysoft_item_key_506732_15228" style="">
    <tbody><tr><td width="30" valign="top">1.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">国际商务谈判中的市场风险具体有( 　 　 　) 　 　<div><table IsItemOption=1 OptionType=\"checkbox\"><TR><TD><input type=checkbox value=\"A\" id=lemonysoft_item_key_506732_15228_0 name=\"lemonysoft_item_key_506732_15228\" ><TD>(A)<TD><label for=lemonysoft_item_key_506732_15228_0>投资风险<\/label><TR><TD><input type=checkbox value=\"B\" id=lemonysoft_item_key_506732_15228_1 name=\"lemonysoft_item_key_506732_15228\" ><TD>(B)<TD><label for=lemonysoft_item_key_506732_15228_1>利率风险<\/label><TR><TD><input type=checkbox value=\"C\" id=lemonysoft_item_key_506732_15228_2 name=\"lemonysoft_item_key_506732_15228\" ><TD>(C)<TD><label for=lemonysoft_item_key_506732_15228_2>汇率风险<\/label><TR><TD><input type=checkbox value=\"D\" id=lemonysoft_item_key_506732_15228_3 name=\"lemonysoft_item_key_506732_15228\" ><TD>(D)<TD><label for=lemonysoft_item_key_506732_15228_3>价格风险<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">国际商务谈判中的市场风险具体有( 　 　 　) 　 　<div><table isitemoption="1" optiontype="checkbox"><tbody><tr><td><input type="checkbox" value="A" id="lemonysoft_item_key_506732_15228_0" name="lemonysoft_item_key_506732_15228"></td><td>(A)</td><td><label for="lemonysoft_item_key_506732_15228_0">投资风险</label></td></tr><tr><td><input type="checkbox" value="B" id="lemonysoft_item_key_506732_15228_1" name="lemonysoft_item_key_506732_15228"></td><td>(B)</td><td><label for="lemonysoft_item_key_506732_15228_1">利率风险</label></td></tr><tr><td><input type="checkbox" value="C" id="lemonysoft_item_key_506732_15228_2" name="lemonysoft_item_key_506732_15228"></td><td>(C)</td><td><label for="lemonysoft_item_key_506732_15228_2">汇率风险</label></td></tr><tr><td><input type="checkbox" value="D" id="lemonysoft_item_key_506732_15228_3" name="lemonysoft_item_key_506732_15228"></td><td>(D)</td><td><label for="lemonysoft_item_key_506732_15228_3">价格风险</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506732_15228","","4");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_15" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_15');}">
    <td scoreid="568359739610628118" id="_DataListTD_tblDataList_15"><table width="100%" border="0" id="tblItem_562420044654444550" islabel="0" keylist="lemonysoft_item_key_506731_57839" style="">
    <tbody><tr><td width="30" valign="top">2.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">谈判中迂回入题的方法有( 　 　 　 　 )<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"checkbox\"><TR><TD><input type=checkbox value=\"A\" id=lemonysoft_item_key_506731_57839_0 name=\"lemonysoft_item_key_506731_57839\" ><TD>(A)<TD><label for=lemonysoft_item_key_506731_57839_0>从题外语入题<\/label><TR><TD><input type=checkbox value=\"B\" id=lemonysoft_item_key_506731_57839_1 name=\"lemonysoft_item_key_506731_57839\" ><TD>(B)<TD><label for=lemonysoft_item_key_506731_57839_1>从自谦入题<\/label><TR><TD><input type=checkbox value=\"C\" id=lemonysoft_item_key_506731_57839_2 name=\"lemonysoft_item_key_506731_57839\" ><TD>(C)<TD><label for=lemonysoft_item_key_506731_57839_2>从确定议题入题<\/label><TR><TD><input type=checkbox value=\"D\" id=lemonysoft_item_key_506731_57839_3 name=\"lemonysoft_item_key_506731_57839\" ><TD>(D)<TD><label for=lemonysoft_item_key_506731_57839_3>从询问对方交易条件入题<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">谈判中迂回入题的方法有( 　 　 　 　 )</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="checkbox"><tbody><tr><td><input type="checkbox" value="A" id="lemonysoft_item_key_506731_57839_0" name="lemonysoft_item_key_506731_57839"></td><td>(A)</td><td><label for="lemonysoft_item_key_506731_57839_0">从题外语入题</label></td></tr><tr><td><input type="checkbox" value="B" id="lemonysoft_item_key_506731_57839_1" name="lemonysoft_item_key_506731_57839"></td><td>(B)</td><td><label for="lemonysoft_item_key_506731_57839_1">从自谦入题</label></td></tr><tr><td><input type="checkbox" value="C" id="lemonysoft_item_key_506731_57839_2" name="lemonysoft_item_key_506731_57839"></td><td>(C)</td><td><label for="lemonysoft_item_key_506731_57839_2">从确定议题入题</label></td></tr><tr><td><input type="checkbox" value="D" id="lemonysoft_item_key_506731_57839_3" name="lemonysoft_item_key_506731_57839"></td><td>(D)</td><td><label for="lemonysoft_item_key_506731_57839_3">从询问对方交易条件入题</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506731_57839","","4");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_16" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_16');}">
    <td scoreid="568359739610628119" id="_DataListTD_tblDataList_16"><table width="100%" border="0" id="tblItem_562420044654444551" islabel="0" keylist="lemonysoft_item_key_506730_16566" style="">
    <tbody><tr><td width="30" valign="top">3.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">选择自己所在单位作为谈判地点的优势有( 　 　 )<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"checkbox\"><TR><TD><input type=checkbox value=\"A\" id=lemonysoft_item_key_506730_16566_0 name=\"lemonysoft_item_key_506730_16566\" ><TD>(A)<TD><label for=lemonysoft_item_key_506730_16566_0>便于侦察对方<\/label><TR><TD><input type=checkbox value=\"B\" id=lemonysoft_item_key_506730_16566_1 name=\"lemonysoft_item_key_506730_16566\" ><TD>(B)<TD><label for=lemonysoft_item_key_506730_16566_1>容易寻找借口<\/label><TR><TD><input type=checkbox value=\"C\" id=lemonysoft_item_key_506730_16566_2 name=\"lemonysoft_item_key_506730_16566\" ><TD>(C)<TD><label for=lemonysoft_item_key_506730_16566_2>易向上级请示汇报<\/label><TR><TD><input type=checkbox value=\"D\" id=lemonysoft_item_key_506730_16566_3 name=\"lemonysoft_item_key_506730_16566\" ><TD>(D)<TD><label for=lemonysoft_item_key_506730_16566_3>方便查找资料与信息<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">选择自己所在单位作为谈判地点的优势有( 　 　 )</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="checkbox"><tbody><tr><td><input type="checkbox" value="A" id="lemonysoft_item_key_506730_16566_0" name="lemonysoft_item_key_506730_16566"></td><td>(A)</td><td><label for="lemonysoft_item_key_506730_16566_0">便于侦察对方</label></td></tr><tr><td><input type="checkbox" value="B" id="lemonysoft_item_key_506730_16566_1" name="lemonysoft_item_key_506730_16566"></td><td>(B)</td><td><label for="lemonysoft_item_key_506730_16566_1">容易寻找借口</label></td></tr><tr><td><input type="checkbox" value="C" id="lemonysoft_item_key_506730_16566_2" name="lemonysoft_item_key_506730_16566"></td><td>(C)</td><td><label for="lemonysoft_item_key_506730_16566_2">易向上级请示汇报</label></td></tr><tr><td><input type="checkbox" value="D" id="lemonysoft_item_key_506730_16566_3" name="lemonysoft_item_key_506730_16566"></td><td>(D)</td><td><label for="lemonysoft_item_key_506730_16566_3">方便查找资料与信息</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506730_16566","","4");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_17" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_17');}">
    <td scoreid="568359739610628120" id="_DataListTD_tblDataList_17"><table width="100%" border="0" id="tblItem_562420044654444553" islabel="0" keylist="lemonysoft_item_key_506729_23172" style="">
    <tbody><tr><td width="30" valign="top">4.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">迫使对方让步的主要策略有( 　 　 　 　 ) 　 　<div><table IsItemOption=1 OptionType=\"checkbox\"><TR><TD><input type=checkbox value=\"A\" id=lemonysoft_item_key_506729_23172_0 name=\"lemonysoft_item_key_506729_23172\" ><TD>(A)<TD><label for=lemonysoft_item_key_506729_23172_0>利用竞争<\/label><TR><TD><input type=checkbox value=\"B\" id=lemonysoft_item_key_506729_23172_1 name=\"lemonysoft_item_key_506729_23172\" ><TD>(B)<TD><label for=lemonysoft_item_key_506729_23172_1>最后通牒<\/label><TR><TD><input type=checkbox value=\"C\" id=lemonysoft_item_key_506729_23172_2 name=\"lemonysoft_item_key_506729_23172\" ><TD>(C)<TD><label for=lemonysoft_item_key_506729_23172_2>撤出谈判<\/label><TR><TD><input type=checkbox value=\"D\" id=lemonysoft_item_key_506729_23172_3 name=\"lemonysoft_item_key_506729_23172\" ><TD>(D)<TD><label for=lemonysoft_item_key_506729_23172_3>软硬兼施<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">迫使对方让步的主要策略有( 　 　 　 　 ) 　 　<div><table isitemoption="1" optiontype="checkbox"><tbody><tr><td><input type="checkbox" value="A" id="lemonysoft_item_key_506729_23172_0" name="lemonysoft_item_key_506729_23172"></td><td>(A)</td><td><label for="lemonysoft_item_key_506729_23172_0">利用竞争</label></td></tr><tr><td><input type="checkbox" value="B" id="lemonysoft_item_key_506729_23172_1" name="lemonysoft_item_key_506729_23172"></td><td>(B)</td><td><label for="lemonysoft_item_key_506729_23172_1">最后通牒</label></td></tr><tr><td><input type="checkbox" value="C" id="lemonysoft_item_key_506729_23172_2" name="lemonysoft_item_key_506729_23172"></td><td>(C)</td><td><label for="lemonysoft_item_key_506729_23172_2">撤出谈判</label></td></tr><tr><td><input type="checkbox" value="D" id="lemonysoft_item_key_506729_23172_3" name="lemonysoft_item_key_506729_23172"></td><td>(D)</td><td><label for="lemonysoft_item_key_506729_23172_3">软硬兼施</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506729_23172","","4");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_18" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_18');}">
    <td scoreid="568359739610628121" id="_DataListTD_tblDataList_18"><table width="100%" border="0" id="tblItem_562420044654444555" islabel="0" keylist="lemonysoft_item_key_506728_34166" style="">
    <tbody><tr><td width="30" valign="top">5.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">进行报价解释时必须遵循的原则是( 　 　 )　<div><table IsItemOption=1 OptionType=\"checkbox\"><TR><TD><input type=checkbox value=\"A\" id=lemonysoft_item_key_506728_34166_0 name=\"lemonysoft_item_key_506728_34166\" ><TD>(A)<TD><label for=lemonysoft_item_key_506728_34166_0>不问不答<\/label><TR><TD><input type=checkbox value=\"B\" id=lemonysoft_item_key_506728_34166_1 name=\"lemonysoft_item_key_506728_34166\" ><TD>(B)<TD><label for=lemonysoft_item_key_506728_34166_1>有问必答<\/label><TR><TD><input type=checkbox value=\"C\" id=lemonysoft_item_key_506728_34166_2 name=\"lemonysoft_item_key_506728_34166\" ><TD>(C)<TD><label for=lemonysoft_item_key_506728_34166_2>避实就虚<\/label><TR><TD><input type=checkbox value=\"D\" id=lemonysoft_item_key_506728_34166_3 name=\"lemonysoft_item_key_506728_34166\" ><TD>(D)<TD><label for=lemonysoft_item_key_506728_34166_3>能言不书<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">进行报价解释时必须遵循的原则是( 　 　 )　<div><table isitemoption="1" optiontype="checkbox"><tbody><tr><td><input type="checkbox" value="A" id="lemonysoft_item_key_506728_34166_0" name="lemonysoft_item_key_506728_34166"></td><td>(A)</td><td><label for="lemonysoft_item_key_506728_34166_0">不问不答</label></td></tr><tr><td><input type="checkbox" value="B" id="lemonysoft_item_key_506728_34166_1" name="lemonysoft_item_key_506728_34166"></td><td>(B)</td><td><label for="lemonysoft_item_key_506728_34166_1">有问必答</label></td></tr><tr><td><input type="checkbox" value="C" id="lemonysoft_item_key_506728_34166_2" name="lemonysoft_item_key_506728_34166"></td><td>(C)</td><td><label for="lemonysoft_item_key_506728_34166_2">避实就虚</label></td></tr><tr><td><input type="checkbox" value="D" id="lemonysoft_item_key_506728_34166_3" name="lemonysoft_item_key_506728_34166"></td><td>(D)</td><td><label for="lemonysoft_item_key_506728_34166_3">能言不书</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506728_34166","","4");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_19" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_19');}">
    <td scoreid="568359739610628122" id="_DataListTD_tblDataList_19"><table width="100%" border="0" id="tblItem_562420044654444556" islabel="0" keylist="lemonysoft_item_key_506727_46144" style="">
    <tbody><tr><td width="30" valign="top">6.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">谈判议程的内容包括( 　 　 )<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"checkbox\"><TR><TD><input type=checkbox value=\"A\" id=lemonysoft_item_key_506727_46144_0 name=\"lemonysoft_item_key_506727_46144\" ><TD>(A)<TD><label for=lemonysoft_item_key_506727_46144_0>模拟谈判<\/label><TR><TD><input type=checkbox value=\"B\" id=lemonysoft_item_key_506727_46144_1 name=\"lemonysoft_item_key_506727_46144\" ><TD>(B)<TD><label for=lemonysoft_item_key_506727_46144_1>时间安排<\/label><TR><TD><input type=checkbox value=\"C\" id=lemonysoft_item_key_506727_46144_2 name=\"lemonysoft_item_key_506727_46144\" ><TD>(C)<TD><label for=lemonysoft_item_key_506727_46144_2>确定谈判议题<\/label><TR><TD><input type=checkbox value=\"D\" id=lemonysoft_item_key_506727_46144_3 name=\"lemonysoft_item_key_506727_46144\" ><TD>(D)<TD><label for=lemonysoft_item_key_506727_46144_3>确定谈判人员<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">谈判议程的内容包括( 　 　 )</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="checkbox"><tbody><tr><td><input type="checkbox" value="A" id="lemonysoft_item_key_506727_46144_0" name="lemonysoft_item_key_506727_46144"></td><td>(A)</td><td><label for="lemonysoft_item_key_506727_46144_0">模拟谈判</label></td></tr><tr><td><input type="checkbox" value="B" id="lemonysoft_item_key_506727_46144_1" name="lemonysoft_item_key_506727_46144"></td><td>(B)</td><td><label for="lemonysoft_item_key_506727_46144_1">时间安排</label></td></tr><tr><td><input type="checkbox" value="C" id="lemonysoft_item_key_506727_46144_2" name="lemonysoft_item_key_506727_46144"></td><td>(C)</td><td><label for="lemonysoft_item_key_506727_46144_2">确定谈判议题</label></td></tr><tr><td><input type="checkbox" value="D" id="lemonysoft_item_key_506727_46144_3" name="lemonysoft_item_key_506727_46144"></td><td>(D)</td><td><label for="lemonysoft_item_key_506727_46144_3">确定谈判人员</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506727_46144","","4");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_20" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_20');}">
    <td scoreid="568359739610628123" id="_DataListTD_tblDataList_20"><table width="100%" border="0" id="tblItem_562420044654444557" islabel="0" keylist="lemonysoft_item_key_506726_49211" style="">
    <tbody><tr><td width="30" valign="top">7.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">谈判信息传递的基本方式有( 　 　 　 　 ) 　 　<div><table IsItemOption=1 OptionType=\"checkbox\"><TR><TD><input type=checkbox value=\"A\" id=lemonysoft_item_key_506726_49211_0 name=\"lemonysoft_item_key_506726_49211\" ><TD>(A)<TD><label for=lemonysoft_item_key_506726_49211_0>明示方式<\/label><TR><TD><input type=checkbox value=\"B\" id=lemonysoft_item_key_506726_49211_1 name=\"lemonysoft_item_key_506726_49211\" ><TD>(B)<TD><label for=lemonysoft_item_key_506726_49211_1>暗示方式<\/label><TR><TD><input type=checkbox value=\"C\" id=lemonysoft_item_key_506726_49211_2 name=\"lemonysoft_item_key_506726_49211\" ><TD>(C)<TD><label for=lemonysoft_item_key_506726_49211_2>广告方式<\/label><TR><TD><input type=checkbox value=\"D\" id=lemonysoft_item_key_506726_49211_3 name=\"lemonysoft_item_key_506726_49211\" ><TD>(D)<TD><label for=lemonysoft_item_key_506726_49211_3>意会方式<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">谈判信息传递的基本方式有( 　 　 　 　 ) 　 　<div><table isitemoption="1" optiontype="checkbox"><tbody><tr><td><input type="checkbox" value="A" id="lemonysoft_item_key_506726_49211_0" name="lemonysoft_item_key_506726_49211"></td><td>(A)</td><td><label for="lemonysoft_item_key_506726_49211_0">明示方式</label></td></tr><tr><td><input type="checkbox" value="B" id="lemonysoft_item_key_506726_49211_1" name="lemonysoft_item_key_506726_49211"></td><td>(B)</td><td><label for="lemonysoft_item_key_506726_49211_1">暗示方式</label></td></tr><tr><td><input type="checkbox" value="C" id="lemonysoft_item_key_506726_49211_2" name="lemonysoft_item_key_506726_49211"></td><td>(C)</td><td><label for="lemonysoft_item_key_506726_49211_2">广告方式</label></td></tr><tr><td><input type="checkbox" value="D" id="lemonysoft_item_key_506726_49211_3" name="lemonysoft_item_key_506726_49211"></td><td>(D)</td><td><label for="lemonysoft_item_key_506726_49211_3">意会方式</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506726_49211","","4");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_21" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_21');}">
    <td scoreid="568359739610628124" id="_DataListTD_tblDataList_21"><table width="100%" border="0" id="tblItem_562420044654444558" islabel="0" keylist="lemonysoft_item_key_506725_22315" style="">
    <tbody><tr><td width="30" valign="top">8.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">国际经济贸易活动中解决争端的普遍的、基本的方式是( 　 　 　 　 　) 　 　<div><table IsItemOption=1 OptionType=\"checkbox\"><TR><TD><input type=checkbox value=\"A\" id=lemonysoft_item_key_506725_22315_0 name=\"lemonysoft_item_key_506725_22315\" ><TD>(A)<TD><label for=lemonysoft_item_key_506725_22315_0>第三方协调<\/label><TR><TD><input type=checkbox value=\"B\" id=lemonysoft_item_key_506725_22315_1 name=\"lemonysoft_item_key_506725_22315\" ><TD>(B)<TD><label for=lemonysoft_item_key_506725_22315_1>仲裁<\/label><TR><TD><input type=checkbox value=\"C\" id=lemonysoft_item_key_506725_22315_2 name=\"lemonysoft_item_key_506725_22315\" ><TD>(C)<TD><label for=lemonysoft_item_key_506725_22315_2>诉讼<\/label><TR><TD><input type=checkbox value=\"D\" id=lemonysoft_item_key_506725_22315_3 name=\"lemonysoft_item_key_506725_22315\" ><TD>(D)<TD><label for=lemonysoft_item_key_506725_22315_3>贸易报复<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">国际经济贸易活动中解决争端的普遍的、基本的方式是( 　 　 　 　 　) 　 　<div><table isitemoption="1" optiontype="checkbox"><tbody><tr><td><input type="checkbox" value="A" id="lemonysoft_item_key_506725_22315_0" name="lemonysoft_item_key_506725_22315"></td><td>(A)</td><td><label for="lemonysoft_item_key_506725_22315_0">第三方协调</label></td></tr><tr><td><input type="checkbox" value="B" id="lemonysoft_item_key_506725_22315_1" name="lemonysoft_item_key_506725_22315"></td><td>(B)</td><td><label for="lemonysoft_item_key_506725_22315_1">仲裁</label></td></tr><tr><td><input type="checkbox" value="C" id="lemonysoft_item_key_506725_22315_2" name="lemonysoft_item_key_506725_22315"></td><td>(C)</td><td><label for="lemonysoft_item_key_506725_22315_2">诉讼</label></td></tr><tr><td><input type="checkbox" value="D" id="lemonysoft_item_key_506725_22315_3" name="lemonysoft_item_key_506725_22315"></td><td>(D)</td><td><label for="lemonysoft_item_key_506725_22315_3">贸易报复</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_506725_22315","","4");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_22" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_22');}">
    <td scoreid="568359739610628125" id="_DataListTD_tblDataList_22"><table width="100%" border="0" id="tblItem_562420044654444559" islabel="0" keylist="lemonysoft_item_uvozrv6v98_43" style="">
    <tbody><tr><td width="30" valign="top">9.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<table IsItem=1><tr><td>基于己方的立场,观点,方案等,通过陈述来表达对各种问题具体看法的沟通行为是( )<tr><td><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_uvozrv6v98_43_0 name=\"lemonysoft_item_uvozrv6v98_43\" ><TD>(A)<TD><label for=lemonysoft_item_uvozrv6v98_43_0>辩<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_uvozrv6v98_43_1 name=\"lemonysoft_item_uvozrv6v98_43\" ><TD>(B)<TD><label for=lemonysoft_item_uvozrv6v98_43_1>答<\/label><TR><TD><input type=radio value=\"C\" id=lemonysoft_item_uvozrv6v98_43_2 name=\"lemonysoft_item_uvozrv6v98_43\" ><TD>(C)<TD><label for=lemonysoft_item_uvozrv6v98_43_2>说服<\/label><TR><TD><input type=radio value=\"D\" id=lemonysoft_item_uvozrv6v98_43_3 name=\"lemonysoft_item_uvozrv6v98_43\" ><TD>(D)<TD><label for=lemonysoft_item_uvozrv6v98_43_3>叙<\/label><\/table><\/div><\/table>"));</script><table isitem="1"><tbody><tr><td>基于己方的立场,观点,方案等,通过陈述来表达对各种问题具体看法的沟通行为是( )</td></tr><tr><td><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_uvozrv6v98_43_0" name="lemonysoft_item_uvozrv6v98_43"></td><td>(A)</td><td><label for="lemonysoft_item_uvozrv6v98_43_0">辩</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_uvozrv6v98_43_1" name="lemonysoft_item_uvozrv6v98_43"></td><td>(B)</td><td><label for="lemonysoft_item_uvozrv6v98_43_1">答</label></td></tr><tr><td><input type="radio" value="C" id="lemonysoft_item_uvozrv6v98_43_2" name="lemonysoft_item_uvozrv6v98_43"></td><td>(C)</td><td><label for="lemonysoft_item_uvozrv6v98_43_2">说服</label></td></tr><tr><td><input type="radio" value="D" id="lemonysoft_item_uvozrv6v98_43_3" name="lemonysoft_item_uvozrv6v98_43"></td><td>(D)</td><td><label for="lemonysoft_item_uvozrv6v98_43_3">叙</label></td></tr></tbody></table></div></td></tr></tbody></table>
    <script language="javascript">showAnswer("lemonysoft_item_uvozrv6v98_43","","3");</script>
    </td></tr></tbody></table>
    
    <script language="javascript">var oSpan=document.getElementsByTagName("SPAN");for(var i=oSpan.length-1;i>=0;i--){if(oSpan[i].getAttribute("IsScoreRemark")=="1"){oSpan[i].innerHTML="&nbsp;&nbsp;<font color=gray>(第1-9题每题5分)</font>";break;}}</script></td></tr></tbody></table>