let axios = require('axios');

let tough = require('tough-cookie');
let { wrapper } = require('axios-cookiejar-support');

// 创建一个新的 CookieJar 实例
let cookieJar = new tough.CookieJar();

// let axiosIns = wrapper(
//     axios.create({
//         jar: cookieJar,
//         timeout: 60 * 1000, // 超时
//         withCredentials: true, // 添加此项
//     })
// );

let axiosIns = axios.create({
    timeout: 60 * 1000, // 超时
    withCredentials: true, // 添加此项
});

// 添加响应拦截器
axiosIns.interceptors.response.use(
    // 当相应状态码为200-299的时候走到这里
    response => {
        let setCookieHeaders = response.headers['set-cookie'] || response.headers['Set-Cookie'];
        if (setCookieHeaders) {
            // 将收到的 Cookie 存储到 CookieJar 中
            setCookieHeaders.forEach(cookie => cookieJar.setCookieSync(cookie, response.config.url));
        }
        return response.data;
    },
    // 其他情况都走到这里
    function (error) {
        // 请求已经发出，也收到了响应，但是状态码不在2xx范围内
        //  Axios 都会自动处理 302 重定向，不会抛出错误，也不会进入拦截器中 error 参数的处理逻辑
        if (error.response) {
            return Promise.reject(new Error(`响应状态码不在2xx范围内：status: ${error.response.status}, data: ${JSON.stringify(error.response.data)}`));
        }

        // 请求已发出但未收到响应
        if (error.request) {
            let errorMsg;
            switch (error.code) {
                case 'ECONNABORTED':
                    errorMsg = '请求超时';
                    break;
                case 'ENOTFOUND':
                    errorMsg = 'DNS 查找失败，无法找到服务器';
                    break;
                case 'ECONNREFUSED':
                    errorMsg = '连接被拒绝';
                    break;
                case 'EHOSTUNREACH':
                    errorMsg = '主机不可达';
                    break;
                case 'ECONNRESET':
                    errorMsg = '连接被重置';
                    break;
                case 'ETIMEDOUT':
                    errorMsg = '连接超时';
                    break;
                default:
                    errorMsg = '网络错误';
            }
            return Promise.reject(new Error(errorMsg));
        } else {
            return Promise.reject(new Error(`请求未发出：${error.message}`));
        }
    }
);

// 请求拦截器：设置 Cookie，放到api模块中自己根据需要设置
// axios.interceptors.request.use(async config => {
//     const cookies = await cookieJar.getCookieString(config.url);
//     config.headers.Cookie = cookies;
//     return config;
// });

axiosIns.cookieJar = cookieJar;

module.exports = axiosIns;
