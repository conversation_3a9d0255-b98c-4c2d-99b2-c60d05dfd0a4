let axiosIns = require('../utils/axiosIns.js');
let { lettersToNumbers, numbersToLetters } = require('./format.js');
let aiChat = require('./aiChat.js');
let { validateAnswer } = require('./format.js');
let collectQuestion = require('./collectQuestion.js');

function extractData(str) {
    // 正则表达式匹配 [URL] 和 [/URL] 之间的内容
    const urlRegex = /\[URL\](.*?)\[\/URL\]/g;

    let result = [];
    let lastIndex = 0;
    let match;

    // 查找所有的 [URL] 标签之间的内容
    while ((match = urlRegex.exec(str)) !== null) {
        // 提取 URL 内容
        const urlContent = match[1];

        // 将 [URL] 之前的文本部分作为 text 类型加入结果数组
        if (match.index > lastIndex) {
            result.push({
                type: 'text',
                text: str.slice(lastIndex, match.index),
            });
        }

        // 将 URL 内容作为 image_url 类型加入结果数组
        result.push({
            type: 'image_url',
            image_url: {
                url: urlContent,
            },
        });

        // 更新处理过的最后索引
        lastIndex = urlRegex.lastIndex;
    }

    // 添加剩余的文本部分（URL 后的文本）
    if (lastIndex < str.length) {
        result.push({
            type: 'text',
            text: str.slice(lastIndex),
        });
    }

    return result;
}
// let str = '下列函数在区间[URL]data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAqguHaqAcAxCvhdGBkxjsAAAAASUVORK5CYII=[/URL]上单调递减 的是（ ）．';
// console.log(extractData(str));

// 对AI回答结果进行格式处理
async function getAnserFromAIResult(questionObj, AIResult) {
    let deepSeekModel = 'gpt-4o-mini-2024-07-18';
    let answers;

    let promptStr = `
    这是一段AI回答的内容
        "${AIResult}"
    以上就是一段AI回答的内容了
    
    这个回答的内容是关于解题步骤的，我现在需要从这个回答的内容中获取最终答案，

    最终答案的格式要求如下：
        - 判断题：只回答一个大写字母，"A"或者"B"
        - 单选题：只回答一个大写字母，如"A"
        - 多选题：回答多个大写字母，如"BCD"
        - 填空题：多个答案用"|"分隔，如"答案1|答案2"
        - 简答题：给出简洁的描述性回答

    当前题目的类型是：${questionObj.type}，按照回答格式要求直接给出答案，不要包含任何解释、分析或多余文字，如果无法提取答案，请回答'false'
    `;

    answers = await aiChat(deepSeekModel, promptStr);
    if (answers != 'false') return answers;
}

// 国开url不是完整的url，只是一个路径，转为base64url
async function fillUrl(questionObj, messageArr) {
    // if (questionObj.platform != '国家开放大学') return;

    for (let item of messageArr) {
        if (item.type != 'image_url') continue;
        let url = item.image_url.url;
        if (questionObj.platform == '国家开放大学') {
            url = 'https://lms.ouchn.cn' + item.image_url.url;
        }
        let imageRes = await axiosIns({
            method: 'get',
            url: url,
            responseType: 'arraybuffer',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                Referer: 'http://www.chaoxing.com/',
                Accept: 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Accept-Encoding': 'gzip, deflate',
            },
        });
        let base64Image = Buffer.from(imageRes, 'binary').toString('base64');

        // 构建 Base64 图片的 Data URL
        let imageUrl = `data:image/jpeg;base64,${base64Image}`;
        item.image_url.url = imageUrl;
    }
}

async function getPicAnswerFromAI(questionObj) {
    if (questionObj.content.length > 4500) {
        return;
    }

    let contentArr = extractData(questionObj.content);
    let optionsArr = extractData(questionObj.options.join(''));
    let messageArr = [
        {
            type: 'text',
            text: `这是一个${questionObj.type}，题目内容如下：`,
        },
        ...contentArr,
        {
            type: 'text',
            text: `以上就是题目内容了，接下来我把选择题的选项发给你`,
        },
        ...optionsArr,
        {
            type: 'text',
            text: `以上就是题目内容了，请把这题的答案发给我`,
        },
    ];
    await fillUrl(questionObj, messageArr);

    let answerRes = await axiosIns({
        method: 'post',
        url: 'http://oaipro.mozhi0012.top/v1/chat/completions',
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer sk-JPTMbl93OqSRazlM50E9Ec94D75d48149dE46cF6Da908066`,
        },
        data: {
            model: 'gpt-4.1-2025-04-14',
            // model: 'o4-mini-2025-04-16',
            messages: [
                {
                    role: 'user',
                    content: messageArr,
                },
            ],
        },
    });
    let answerStr = answerRes.choices[0].message.content;

    let answers;
    if ('单选题多选题判断题填空题'.includes(questionObj.type)) {
        answers = await getAnserFromAIResult(questionObj, answerStr);
    } else {
        answers = answerStr;
    }

    // 校验题目类型
    let isFormat = validateAnswer(answers, questionObj.type);
    if (!isFormat) {
        return;
    }

    // 收集题目
    if (answers && answers != 'false') {
        await collectQuestion(questionObj, answers, 'getPicAnswerFromAI');
    }

    return answers;
}

module.exports = getPicAnswerFromAI;

if (false) {
    (async () => {
        let questionObj = {
            id: 'id-mauvk88p-52680inr5sigleQuestionDiv_884341303',
            content: '逻辑函数[URL]https://p.ananas.chaoxing.com/star3/origin/ea3034ea2d43d87af3f4e69a0fd87824.png[/URL]的最简结果为( )。',
            type: '单选题',
            options: [
                'A:[URL]https://p.ananas.chaoxing.com/star3/origin/68376d18dc941a89e6b89ac6290e6cad.png[/URL]',
                'B:[URL]https://p.ananas.chaoxing.com/star3/origin/fea9232699de246097543cfe81f991e7.png[/URL]',
                'C:[URL]https://p.ananas.chaoxing.com/star3/origin/5521df86a695ef49c4f65b07ac91c106.png[/URL]',
                'D:[URL]https://p.ananas.chaoxing.com/star3/origin/11cad1fe2cdbaf37e4346e6fd092c1fa.png[/URL]',
            ],
            platform: '超星学习通',
            courseName: 'test',
        };

        let res = await getPicAnswerFromAI(questionObj);
        console.log(res);
    })();
}
