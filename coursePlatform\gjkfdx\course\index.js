// ========= 工具模块 =========
let pageTools = require('../../utils/pageTools.js');
let getMainPage = require('../../utils/getMainPage.js');
let Model = require('../../../config/sequelize.config.js');
let axiosInsPromise  = require('../utils/getAxiosIns.js');

// ========= 组件模块 =========
let signinModule = require('./module/singinModule/index.js');
let getCourseListModule = require('./module/getCourseListModule.js');
let runCourseModule = require('./module/runCourseModule.js');
let endModule = require('./module/endModule.js');

async function gjkfdxCourse(taskObj, taskOptions) {
    if (!taskObj.others) taskObj.others = '';

    let globalStore = {
        taskObj,
        others: taskObj.others,
        warningMessage: '',
    };

    await axiosInsPromise; // 添加await来获取axios实例

    // 开启窗口模式
    // taskOptions.isHeadless = false;

    // 开启代理 放到api.js中控制
    // global.axiosIns = await getAxiosIns(true);
    // console.log('here')

    let infoLogger = pageTools.getCourseInfoLogger(taskObj.id, false);

    // 创建page示例，如果开启了isProxy，会给ttaskOptions赋值taskOptions.proxy = proxy;
    let { mainPage, browser } = await getMainPage(taskOptions);

    try {
        // 一、登录
        await signinModule(infoLogger, mainPage, globalStore);

        // 二、获取课程列表
        let courseList = await getCourseListModule(infoLogger, mainPage, globalStore);
        if (courseList.length == 0) {
            await infoLogger('课程数量为0', 'red');
            await browser.close();
            browser = null;
            return {
                finalResult: [{ courseName: '课程数量为0', progress: 0 }],
                warningMessage: '',
            };
        }

        // 三、开始刷课
        await runCourseModule(infoLogger, mainPage, courseList, globalStore);

        // 四、结尾工作,必须得重新登录，因长时间刷课，登录信息已经失效
        let finalResult = await endModule(infoLogger, mainPage, courseList, globalStore);

        return finalResult;
    } catch (error) {
        await browser.close();
        browser = null;
        throw error;
    } finally {
        if (global.useProxy) {
            await Model.proxy.update(
                {
                    state: 'idle',
                },
                {
                    where: {
                        ip: global.proxyObj.ip + '',
                    },
                }
            );
        }
        if (browser) {
            await browser.close();
            browser = null;
        }
    }
}

module.exports = gjkfdxCourse;
