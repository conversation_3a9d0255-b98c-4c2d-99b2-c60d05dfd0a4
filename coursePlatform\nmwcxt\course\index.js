// 工具模块
let pageTools = require('../../utils/pageTools.js');
let getMainPage = require('../../utils/getMainPage.js');

// ========= 组成模块 =========
let signinModule = require('./module/signinModule.js');
let getCourseListModule = require('./module/getCourseListModule.js');
let runCourseModule = require('./module/runCourseModule.js');
let runAssignmentModule = require('./module/runAssignmentModule/index.js');
let endModule = require('./module/endModule.js');

async function nmwcxtCourse(taskObj, taskOptions) {
    // 强制开启窗口
    // taskOptions.isHeadless=false

    let globalStore = {};

    //获取记录日志的函数
    let infoLogger = pageTools.getCourseInfoLogger(taskObj.id, false);

    //创建页面实例
    let { mainPage, browser } = await getMainPage(taskOptions);

    try {
        // 一、登录 mainPageCookieStr mainUrl
        await signinModule(infoLogger, mainPage, taskObj, globalStore);
        
        if(!process.env.HAS_PERMISSION){
            return
        }
        

        // 二、获取课程列表 termCode desktopInfoRes courseList schoolName
        await getCourseListModule(infoLogger, globalStore, taskObj);

        // 三、开始网课
        await runCourseModule(mainPage, globalStore,infoLogger) 

        // 四、开始作业
        await runAssignmentModule(mainPage, globalStore,infoLogger)

        // 五、收尾工作
        let finalResult = await endModule(infoLogger,globalStore);
        await browser.close();
        return { warningMessage: '', finalResult };
    } catch (error) {
        await browser.close();
        browser = null;
        throw error;
    } finally {
        if (browser) {
            await browser.close();
            browser = null;
        }
    }
}

module.exports = nmwcxtCourse;
