let axiosIns = require('./axiosIns');
let FormData = require('form-data');

/**
 *
 * POST /ocr OCR 识别
 * POST /slide_match 滑动验证码匹配
 * POST /detection 目标检测
 */
async function ddddocr(...args) {
    let image, background, type
    switch (args.length) {
        case 2:
            [image, type] = args;
            break;
        case 3:
            [image, background, type] = args;
            break;
    }
    let form = new FormData();
    let url = `http://origin.mozhi0012.top:8888/${type}`;

    switch (type) {
        // image	String	Base64 编码的图片字符串（可选）
        // probability	Boolean	是否返回概率（默认：false）
        // charsets	String	字符集（可选）
        // png_fix	Boolean	是否进行 PNG 修复（默认：false）
        case 'ocr': {
            form.append('image', image);
            form.append('probability', 'false');
            form.append('png_fix', 'false');
            break;
        }
        // arget	String	Base64 编码的目标图片字符串（可选） 
        // background	String	Base64 编码的背景图片字符串（可选） 
        // simple_target	Boolean	是否使用简单目标（默认：false）
        case 'slide_match':{
            form.append('target', image);
            form.append('background', background);
            form.append('simple_target', 'false');
            break;
        }
        // image	String	Base64 编码的图片字符串（可选）
        case 'detection':{
            form.append('image', image);
            break;
        }
    }

    let res = await axiosIns({
        url,
        method: 'post',
        data: form,
        headers: {
            ...form.getHeaders(),
        },
    });
    return res
}

module.exports = ddddocr;

// 单元测试
if (false) {
    (async () => {
       let image1=`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`
       let image2=`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`

        let res=await ddddocr(image2,image1,'slide_match');
        console.log(res)
    })();
}