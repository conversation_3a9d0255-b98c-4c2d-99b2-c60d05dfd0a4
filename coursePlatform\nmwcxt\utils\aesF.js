let qs = require('qs');

var Base64 = {
    _keyStr: 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=',
    encode: function (r) {
        var t,
            e,
            n,
            o,
            a,
            c,
            i,
            d = '',
            C = 0;
        for (r = Base64._utf8_encode(r); C < r.length; )
            (o = (t = r.charCodeAt(C++)) >> 2),
                (a = ((3 & t) << 4) | ((e = r.charCodeAt(C++)) >> 4)),
                (c = ((15 & e) << 2) | ((n = r.charCodeAt(C++)) >> 6)),
                (i = 63 & n),
                isNaN(e) ? (c = i = 64) : isNaN(n) && (i = 64),
                (d = d + this._keyStr.charAt(o) + this._keyStr.charAt(a) + this._keyStr.charAt(c) + this._keyStr.charAt(i));
        return d;
    },
    decode: function (r) {
        var t,
            e,
            n,
            o,
            a,
            c,
            i = '',
            d = 0;
        for (r = r.replace(/[^A-Za-z0-9\+\/\=]/g, ''); d < r.length; )
            (t = (this._keyStr.indexOf(r.charAt(d++)) << 2) | ((o = this._keyStr.indexOf(r.charAt(d++))) >> 4)),
                (e = ((15 & o) << 4) | ((a = this._keyStr.indexOf(r.charAt(d++))) >> 2)),
                (n = ((3 & a) << 6) | (c = this._keyStr.indexOf(r.charAt(d++)))),
                (i += String.fromCharCode(t)),
                64 != a && (i += String.fromCharCode(e)),
                64 != c && (i += String.fromCharCode(n));
        return (i = Base64._utf8_decode(i));
    },
    _utf8_encode: function (r) {
        r = r.replace(/\r\n/g, '\n');
        for (var t = '', e = 0; e < r.length; e++) {
            var n = r.charCodeAt(e);
            n < 128
                ? (t += String.fromCharCode(n))
                : n > 127 && n < 2048
                ? ((t += String.fromCharCode((n >> 6) | 192)), (t += String.fromCharCode((63 & n) | 128)))
                : ((t += String.fromCharCode((n >> 12) | 224)), (t += String.fromCharCode(((n >> 6) & 63) | 128)), (t += String.fromCharCode((63 & n) | 128)));
        }
        return t;
    },
    _utf8_decode: function (r) {
        for (var t = '', e = 0, n = (c1 = c2 = 0); e < r.length; )
            (n = r.charCodeAt(e)) < 128
                ? ((t += String.fromCharCode(n)), e++)
                : n > 191 && n < 224
                ? ((c2 = r.charCodeAt(e + 1)), (t += String.fromCharCode(((31 & n) << 6) | (63 & c2))), (e += 2))
                : ((c2 = r.charCodeAt(e + 1)),
                  (c3 = r.charCodeAt(e + 2)),
                  (t += String.fromCharCode(((15 & n) << 12) | ((63 & c2) << 6) | (63 & c3))),
                  (e += 3));
        return t;
    },
};

// 美化后的版本
let CryptoJS = require('./CryptoJS');
// Base64解码函数
function decodeBase64(str) {
    return atob(str);
}

// 获取AES加密的密钥 5165325946459632
function getEncryptionKey() {
    // 将几个Base64编码的字符串解码并拼接
    return decodeBase64('NTE2') + decodeBase64('NTMyNTk=') + decodeBase64('NDY0NQ==') + decodeBase64('OTYzMg==');
}

// 获取初始化向量（IV） 8655624543959233
function getIV() {
    // 将两个Base64编码的字符串解码并拼接
    return decodeBase64('ODY1NTYyNDU0Mw==') + decodeBase64('OTU5MjMz');
}

// 加密数据
function encryptData(data) {
    // 获取密钥和初始化向量
    const key = getEncryptionKey();
    const iv = getIV();

    // 若数据不是字符串，则将其转换为JSON字符串
    if (typeof data !== 'string') {
        data = JSON.stringify(data);
    }

    // 将密钥和初始化向量转换为CryptoJS可以理解的格式
    const parsedKey = CryptoJS.enc.Latin1.parse(key);
    const parsedIv = CryptoJS.enc.Latin1.parse(iv);

    // 执行AES加密
    const encrypted = CryptoJS.AES.encrypt(data, parsedKey, {
        iv: parsedIv,
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    });

    // 返回加密后的字符串
    return encrypted.toString();
}

// 解密数据
function decryptData(encryptedData) {
    // 获取密钥和初始化向量
    const key = getEncryptionKey();
    const iv = getIV();

    // 将密钥和初始化向量转换为CryptoJS可以理解的格式
    const parsedKey = CryptoJS.enc.Latin1.parse(key);
    const parsedIv = CryptoJS.enc.Latin1.parse(iv);

    // 执行AES解密
    const decrypted = CryptoJS.AES.decrypt(encryptedData, parsedKey, {
        iv: parsedIv,
        padding: CryptoJS.pad.Pkcs7,
    });

    // 返回解密后的原始字符串
    return CryptoJS.enc.Utf8.stringify(decrypted).toString();
}

/**
 * 解密数据，类似于查询字符串的形式
 * 1.把字符串转为对象 2.对象的value进行URI解码 3.解码后的数据进行解密 4.返回对象
 */
function decodeRequestData(data) {
    if (data === undefined) {
        return undefined;
    }
    if (data.includes('=')) {
        //进行转为字符串，并解码
        let obj = qs.parse(data, { decoder: str => decodeURIComponent(str) });
        //进行AES解密
        try {
            Object.keys(obj).forEach(key => {
                try {
                    let decryptValue = decryptData(obj[key]);
                    if (decryptValue) {
                        obj[key] = decryptData(obj[key]);
                    }
                } catch (error) {}
            });
            return obj;
        } catch (error) {
            console.log('decodeURIComponent解密出错', obj, error);
        }
    }
    return decryptData(data);
}

/**
 * 加密数据
 * 1.把对象的value进行AES加密。2.拼接成字符串 3.进行URI编码
 */
function encodeRequestData(data) {
    let newObj = {};
    //进行AES加密
    if (Object.prototype.toString.call(data) == '[object Object]') {
        Object.keys(data).forEach(key => {
            //对value进行AES加密
            try {
                newObj[key] = encryptData(data[key]);
            } catch (error) {}
        });
        //先编码，然后拼接为字符串
        let postData = qs.stringify(newObj);
        return postData;
    }
    return data;
}

// console.log(decodeRequestData( 'snowFlake=HVjqCzHKz3LlHgaA0yPPQg%3D%3D&student_no=LhFmDVxPlnpy4sbos2XKoQ%3D%3D'))

module.exports = { encryptData, decryptData, decodeRequestData, encodeRequestData };

if (false) {
    let str =
        "8GuCmyXJDSi93f/8eI4W4Q=="

    // let res=decodeRequestData(str)
    // console.log(res)

    // let str='vMdG0uq2bC114oxfD37j'

    // console.log(decryptData(str));
    console.log(decryptData(str));
}