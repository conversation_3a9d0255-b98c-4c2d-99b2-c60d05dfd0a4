let pageTools=require('../../../utils/pageTools.js');
let api=require('../../utils/api.js');
let { encryptData, decryptData, decodeRequestData, encodeRequestData } = require('../../utils/aesF.js');
async function getCourseListModule(mainPage,taskObj,globalStore,infoLogger) {

    //1.获取页面cookie
    let cookieStr = await pageTools.getPageCookies(mainPage,taskObj,infoLogger);
    globalStore.cookieStr=cookieStr

    //3.从服务器获取 “考试列表” courseExamActionRes 信息 包含用户信息
    let courseExamActionData = {
        term: taskObj.term,
        course_exam_status: '',
        page_size: '10',
        current_page: '1',
    };
    let courseExamActionRes = await api.courseExamAction(courseExamActionData, cookieStr);
    if(courseExamActionRes.totalCount==0){
        await infoLogger('考试列表为空', 'red'); //记录日志
        throw new Error('考试列表为空'); //结束进程池任务
    }
    let courseExamActionResData = decryptData(courseExamActionRes.data)
    //处理信息
    //判断是否成功
    let examCourseList = JSON.parse(courseExamActionResData);

    if (!(examCourseList && examCourseList.length > 0)) {
        await infoLogger('获取考试科目列表失败', 'red'); //记录日志
        throw new Error('获取考试科目列表失败'); //结束进程池任务
    }else{
        await infoLogger(`获取考试科目列表成功，共${examCourseList.length}门课程`, 'green'); //记录日志
    }
    globalStore.courseList=examCourseList
    globalStore.courseName=taskObj.coursename
    globalStore.others=taskObj.others;
}

module.exports = getCourseListModule;