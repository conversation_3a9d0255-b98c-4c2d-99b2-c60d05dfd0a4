let api = require('../../utils/api.js');

async function getCourseListModule(infoLogger, globalStore, taskObj) {
    //从服务器获取课程列表courseList
    let courseList;
    try {
        courseList = await api.getCourseList(globalStore, taskObj);
    } catch (error) {
        await infoLogger('获取课程列表出错', 'red'); //记录日志
        throw error; //抛出错误，结束进程池
    }

    // 如果有课程名筛选条件，就筛选课程
    if (taskObj.coursename) {
        courseList = courseList.filter(item => item.courseName.includes(taskObj.coursename));
    }

    //如果课程数量为0，就抛错
    if (courseList.length == 0) {
        return [];
    }

    let courseLength = courseList.length;

    await infoLogger(`获取课程列表成功，共${courseLength}门课程`, 'green'); //记录日志

    //获取课程对应的所有课件信息
    for (let i = 0; i < courseLength; i++) {
        let courseObj = courseList[i];
        courseObj.cellList = [];
        let designRes;
        try {
            designRes = await api.getCellList(courseObj, globalStore);
        } catch (error) {
            await infoLogger(`[${i + 1}/${courseLength}]${courseObj.courseName}：获取课件列表出错`, 'red'); //记录日志
            throw error; //抛出错误，结束进程池
        }

        if (!Array.isArray(designRes.list)) {
            await infoLogger(`[${i + 1}/${courseLength}]${courseObj.courseName}：当前课程无课件`, 'red'); //记录日志
            courseObj.cellList = [];
            continue;
        }

        designRes.list.forEach(module => {
            module.lessons.forEach(item => {
                item.cells.forEach(cell => {
                    courseObj.cellList.push(cell);
                });
            });
        });
        await infoLogger(`[${i + 1}/${courseLength}]${courseObj.courseName}：获取课件列表成功，共${courseObj.cellList.length}门课件`);
    }

    await infoLogger(`所有课程的课件都已经获取完成`, 'green'); //记录日志

    return courseList;
}

module.exports = getCourseListModule;