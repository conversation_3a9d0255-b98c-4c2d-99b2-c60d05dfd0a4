// let count = 20; //只能答题20次，超过这个次数，直接下一个
let { formatQuestion, answerQusetion, collectQuestion } = require('../../utils/handleQuestion.js');
let api = require('../../utils/api.js');

async function runAssignmentModule(infoLogger, mainPage, courseList, globalStore) {
    let storageObj = globalStore.storageObj;
    courseLoop: for (let i = 0; i < courseList.length; i++) {
        let courseObj = courseList[i];

        // // 进入到作业页面
        // let assignmentUrl = `https://jxjynew.ahjxjy.cn/app/jxjy-student-space-web#/myCourse/courseInfo?courseId=${courseObj.id}`;
        // await mainPage.goto(assignmentUrl, { waitUntil: 'networkidle0' });
        // await new Promise(r => setTimeout(r, 2000));


        // 获取作业列表
        let assignmentListRes = await api.getAssignmentList(storageObj, courseObj.id);

        await infoLogger(`课程[${i + 1}/${courseList.length}]-当前课程<${courseObj.courseName}>，共有${assignmentListRes.totalCount}门作业`, 'blue');

        // 作业最大尝试次数
        let maxCount = 10;

        for (let j = 0; j < assignmentListRes.items.length; j++) {
            let assignmentObj = assignmentListRes.items[j];
            let assignmentStr = `课程[${i + 1}/${courseList.length}]-作业[${j + 1}/${assignmentListRes.items.length}]`;

            // 首次判定是否完成，每次考试之后，还需要重新判定
            if (assignmentObj.score && assignmentObj.score > 90) {
                await infoLogger(`${assignmentStr} 作业已经完成,分数为${assignmentObj.score}分 <${assignmentObj.courseName}>`, 'green');
                continue;
            }

            // 首次判定是否完成，每次考试之后，还需要重新判定
            if (assignmentObj.score && assignmentObj.score == assignmentObj.objectiveScore) {
                await infoLogger(`${assignmentStr} 作业已经完成,分数为${assignmentObj.score}分，存在主观题未批阅。 <${assignmentObj.courseName}>`, 'green');
                continue;
            }

            let assignmentSubmitCount = 1;
            async function main() {
                // 获取题目列表数据
                let questionListRes = await api.getAssignmentQuestionList(storageObj, assignmentObj.courseId, assignmentObj.id);
                // 格式化题目列表
                let questionList = formatQuestion(questionListRes, assignmentObj.courseName);

                // 处理当前试卷状态questionListRes.state 1表示已完成为批阅 0表示考试中 2表示考试完成已批阅
                let assignmentState;
                switch (questionListRes.state) {
                    case 1: {
                        assignmentState = '已完成未批阅';
                        break;
                    }
                    case 0: {
                        assignmentState = '考试中';
                        break;
                    }
                    case 2: {
                        assignmentState = '已完成已批阅';
                        break;
                    }
                    default: {
                        assignmentState = '未知状态';
                        throw new Error(`未知作业状态：${questionListRes.state}`);
                        break;
                    }
                }

                await infoLogger(`${assignmentStr}，第${assignmentSubmitCount}次作业开始,当前分数为：${questionListRes.score}分 <${assignmentObj.title}>`);

                // 关于试卷状态questionListRes.state 1表示已完成为批阅 0表示考试中 2表示考试完成已批阅
                // 可以答题
                if (questionListRes.state == 0) {
                    await infoLogger(`${assignmentStr} 当前状态为：${assignmentState}，接下来开始答题`);

                    // 查找答案，回答问题
                    let submitCount = await answerQusetion(questionList, storageObj);
                    await infoLogger(` 共有${questionList.length}道题目，回答${submitCount}道题目，5分钟之后交卷`);

                    await new Promise(r => setTimeout(r, 5 * 60 * 1000));

                    // 提交作业
                    await api.submitAssignment(storageObj, courseObj.id, assignmentObj.id);
                    await infoLogger(`${assignmentStr} 作业已经提交`);
                    assignmentSubmitCount++;
                    await main();
                }

                // 不可以答题
                if (!(questionListRes.state == 0)) {
                    await infoLogger(`当前试卷状态为：${assignmentState}，接下来开始收集题目`);

                    // 收集题目
                    let collectCount = await collectQuestion(questionList);
                    await infoLogger(`${assignmentStr} 共有${questionList.length}道题目，收集${collectCount}道题目`);

                    if (assignmentSubmitCount >= maxCount) {
                        await infoLogger(`${assignmentStr} 作业已完成,分数:${questionListRes.score},已经达到最大尝试次数${maxCount}`);
                        return;
                    }

                    if (questionListRes.score >= 95) {
                        await infoLogger(`${assignmentStr} 作业已完成,分数:${questionListRes.score}`, 'green');
                        return;
                    }

                    // 更改答题状态
                    await api.redoAssignment(storageObj, courseObj.id, assignmentObj.id);

                    // 重新获取题目
                    await infoLogger(`${assignmentStr} 接下来重新答题`);
                    await main();
                }
            }

            await main();
        }
    }
}

module.exports = runAssignmentModule;
