let getAnswerFromLocal = require('../../../../../../solveQuestion/getAnswerFromLocal.js');
let Model = require('../../../../../../../config/sequelize.config.js');
let { v4 } = require('uuid');
let { lettersToNumbers, numbersToLetters } = require('../../../../../../solveQuestion/format.js');
let api = require('../../../../../utils/api.js');
async function handleVideoQuestion(infoLogger, cellInfo, courseObj, globalStore, cellObj, cellLogStr) {
    questionLoop: for (let i = 0; i < cellInfo.questions.length; i++) {
        item = cellInfo.questions[i];
        let questionLogStr = `题目[${i + 1}/${cellInfo.questions.length}]`;
        if (item.answered) {
            await infoLogger(`${questionLogStr}-已经完成，跳过`);
            continue;
        }
        let duration = item.position;
        let questionId = item.id;
        // 回答视频问题
        let getVideoQuestionRes = await api.getVideoQuestion(globalStore, courseObj, cellObj, duration);
        if (getVideoQuestionRes.code == 1) {
            // 格式化问题
            if (!Array.isArray(getVideoQuestionRes.question.options)) {
                getVideoQuestionRes.question.options = [];
            }
            let options = getVideoQuestionRes.question.options.map((item, index) => {
                // index转为字母
                let letter = String.fromCharCode(65 + index);
                return `${letter}:${item.content}`;
            });
            let questionId = getVideoQuestionRes.question.id;
            let questionObj = {
                type: getVideoQuestionRes.question.type,
                content: getVideoQuestionRes.question.content,
                options: options,
            };
            await infoLogger(`${questionLogStr}：开始回答问题：${JSON.stringify(questionObj)}`);

            // 从本地查找答案
            let answers = await getAnswerFromLocal(questionObj);
            if (answers) {
                switch (questionObj.type) {
                    case '单选题':
                    case '多选题': {
                        // 字母转为index
                        let answerIndex = lettersToNumbers(answers);
                        // 0123变成0,1,2,3
                        let submitAnswers = answerIndex.split('').join(',');
                        // 提交答案
                        let submitRes = await api.submitVideoQuestion(globalStore, courseObj, submitAnswers, questionId);
                        await infoLogger(`找到答案${submitAnswers}，回答结果：${JSON.stringify(submitRes)}`);
                        continue questionLoop;
                        break;
                    }
                    case '判断题': {
                        let submitAnswers = answers == 'A' ? 'true' : 'false';
                        // 提交答案
                        let submitRes = await api.submitVideoQuestion(globalStore, courseObj, submitAnswers, questionId);
                        await infoLogger(`找到答案${submitAnswers}，回答结果：${JSON.stringify(submitRes)}`);
                        continue questionLoop;
                        break;
                    }
                }
            }

            if (!answers) {
                await infoLogger(`${cellLogStr}：没有找到答案`);
                // 用0作为答案提交一次
                let submitRes = await api.submitVideoQuestion(globalStore, courseObj, '0', questionId);
                await infoLogger(`尝试回答A，结果：${JSON.stringify(submitRes)}`);
                // 如果0蒙对了
                if (submitRes.success == 1) {
                    await infoLogger('A为正确答案');
                    answers = 'A';
                } else {
                    await infoLogger(`A不是正确答案，正确答案${submitRes.question.answer}`);
                    switch (questionObj.type) {
                        case '单选题':
                        case '多选题': {
                            let answersIndex = submitRes.question.answer.join('');
                            answers = numbersToLetters(answersIndex);
                            let submitAnswers = submitRes.question.answer.join(',');

                            // 重新提交答案
                            submitRes = await api.submitVideoQuestion(globalStore, courseObj, submitAnswers, questionId);
                            break;
                        }
                        case '判断题': {
                            let answersIndex = submitRes.question.answer[0];
                            answers = answersIndex == 'true' ? 'A' : 'B';
                            // 重新提交答案
                            submitRes = await api.submitVideoQuestion(globalStore, courseObj, answersIndex, questionId);
                            break;
                        }
                    }
                }
                await infoLogger(`最终答案为${answers}，回答结果：${JSON.stringify(submitRes)}`);

                // 收集题目
                let answersContent = [];
                for (let option of options) {
                    let optionContent = option.split(/:(.+)/)[1];
                    answersContent.push(optionContent);
                }

                // 收集题目
                await Model.bank.create({
                    id: v4(),
                    content: questionObj.content,
                    options: JSON.stringify(options),
                    type: questionObj.type,
                    answers: answers,
                    answers_content: JSON.stringify(answersContent),
                    course_name: courseObj.courseName,
                    platform: '安徽继续教育在线',
                    add_time: new Date(),
                    comment: '视频题目',
                });
            }
        }
    }
}

module.exports = handleVideoQuestion;
