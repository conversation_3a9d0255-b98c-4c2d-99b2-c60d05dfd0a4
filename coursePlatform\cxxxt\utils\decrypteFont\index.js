// //加密字体原理
// //1.首先我们假设一个正常的字体，主要包含unicode编码以及自行坐标svgPoint，其中的font和md5都是我自己辅助添加的
// let TTF=[
//     {font:'电',unicode:'uni7535',svgPoint:[1,2,532,23,64],md5:'79809648a895c7bab739a5b5ad330d10'}
//     {font:'嬤',unicode:'uni5B24',svgPoint:[8,1,112,23,64],md5:'d1b6fe73b98a0c953c6b8b85390ab6e4'}
// ]
// //2.然后我们把这两个字符的unicode交换，只是unicode交换，其余不变，就得到了一个加密后的字体
// let cryptoTTF=[
//     {font:'电',unicode:'uni5B24',svgPoint:[1,2,532,23,64],md5:'79809648a895c7bab739a5b5ad330d10'}
//     {font:'嬤',unicode:'uni7535',svgPoint:[8,1,112,23,64],md5:'d1b6fe73b98a0c953c6b8b85390ab6e4'}
// ]
// // 3.学习通会选用部分加密字体，组成一个新的加密字体，这样用于优化
// //4.“电话”，我们给它变成“嬤话”，然后再用加密字体，就能显示出来“电话”，但是复制的还是“嬤话”的unicode
// let content=[
//     {font:'电',unicode:'uni7535',svgPoint:[1,2,532,23,64],md5:'79809648a895c7bab739a5b5ad330d10'}
//     {font:'话',unicode:'uni8A13',svgPoint:[4,34,12,13,44],md5:'02306cd83a42fee8a4e24f13c1ee67d9'}
// ]
// let cryptoContent=[
//     {font:'嬤',unicode:'uni5B24',svgPoint:[8,1,112,23,64],md5:'d1b6fe73b98a0c953c6b8b85390ab6e4'}
//     {font:'话',unicode:'uni8A13',svgPoint:[4,34,12,13,44],md5:'02306cd83a42fee8a4e24f13c1ee67d9'}
// ]

// //解密字体原理
// // 1.首先获取字“嬤话”符的unicode，然后从加密字体中找到对应的字形md5，“嬤”的unicode是错的，但是字形md5是对的
// cryptoTTF=[
//     {font:'电',unicode:'uni5B24',svgPoint:[1,2,532,23,64],md5:'79809648a895c7bab739a5b5ad330d10'}
//     {font:'嬤',unicode:'uni7535',svgPoint:[8,1,112,23,64],md5:'d1b6fe73b98a0c953c6b8b85390ab6e4'}
// ]
// // 2.然后从正常字体中，通过md5找到对应的unicode


const fontkit = require("fontkit");
const crypto = require("crypto")
let fs=require('fs');;
// 原始字体的哈希表
let originMap = require("./originalFontHashes");

function decryptFont(base64Font, decryptText) {
    if(!base64Font) return decryptText
    // 1.根据加密字体创建fontkit实例
    //把base64字符串转为buffer
    let fontBuffer = Buffer.from(base64Font, "base64");
    //把buffer转为fontkit实例
    let fontIns = fontkit.create(fontBuffer);

    // 2.把fontkit实例转为哈希表 {uni51D8: 'f098443a13b8be26f84da83f6405d424'}
    let hashes = {};
    fontIns.characterSet.forEach((codePoint) => {
        const glyph = fontIns.glyphForCodePoint(codePoint); // 获取字符的字形
        // 创建一个字符串来存储字形的点信息
        const dataString = glyph.path.toSVG();
        // const hash = CryptoJS.MD5(dataString).toString(); // 为字形生成MD5哈希
        const hash = crypto.createHash("md5").update(dataString).digest("hex"); // 为字形生成MD5哈希
        const unicodeHex = codePoint.toString(16).toUpperCase(); // 将Unicode代码点转换为十六进制
        hashes[`uni${unicodeHex}`] = hash; // 将哈希信息存储在对象中
    });

    //3.进一步处理哈希表，根据字形md5找到原始的unicode
    // uni51D8: {
    //     originKey: 'uni514B',
    //     originValue: 'f098443a13b8be26f84da83f6405d424'
    //   },
    let encryptedMap = {};
    // 遍历加密字体的哈希表
    for (let encryptedKey in hashes) {
        // 取出加密字体对应的字形md5
        let encryptedValue = hashes[encryptedKey];
        //遍历原始未加密字体哈希表
        for (let originKey in originMap) {
            // 从未加密的字体哈希表中，找到对应的字形
            let originValue = originMap[originKey];
            if (encryptedValue == originValue) {
                encryptedMap[encryptedKey] = {
                    originKey: originKey,
                    originValue: originValue,
                };
                break;
            }
        }
    }

    // 4.还原加密文字
    //1.历题目每个字符，收集原始的unicode
    let textUnicodeArr = [];
    for (let i = 0; i < decryptText.length; i++) {

        //获取每个字符对应的unicode
        let strUnicode = decryptText.charCodeAt(i).toString(16);
        strUnicode = strUnicode.toUpperCase(); //转为大写
        strUnicode = "uni" + strUnicode;
        // strUnicode=strUnicode.toUpperCase()
        //看看这个字符有没有加密
        let originMap = encryptedMap[strUnicode];
        if (originMap) {
            //如果加密了，就解密后再放入数组
            textUnicodeArr.push(originMap.originKey);
        } else {
            //没加密，直接放入数组
            textUnicodeArr.push(strUnicode);
        }
    }
    //2.还原unicode到字符
    let result = [];
    textUnicodeArr.forEach((unicode) => {
        unicode = unicode.replace("uni", "");
        unicode = parseInt(unicode, 16);
        result.push(String.fromCodePoint(unicode));
    });
    return result.join("");
}

module.exports=decryptFont

//测试
//题目内容
// let question = "（ ）撛丝獞精神是“一撠一獞”撜议撛撡要撙撝";

// //获取 style标签字符串
// let htmlContent = fs.readFileSync(path.resolve(__dirname, "1.html"), "utf-8");
// // let htmlContent=document.querySelector('#cxSecretStyle').innerHTML //在html页面中选择

// //根据style标签字符串转为哈希表 加密unicode:{原始unicode:md5}
// let encryptHashMap = styleToHash(htmlContent); // 提取加密字体的哈希信息

// //获取字体解密函数
// let decryptTextFn = decryptText(encryptHashMap);

// let result = decryptTextFn(question);

// let path=require('path');

// let htmlContent = fs.readFileSync(path.resolve(__dirname, "1.txt"), "utf-8");
