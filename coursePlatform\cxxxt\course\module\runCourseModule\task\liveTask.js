let pageTools=require('../../../../../utils/pageTools.js');
let api = require("../../../../utils/api.js");
async function liveTask(infoLogger, mainPage, attachment, mArgJson, attachLogStr, chapterCookieStr) {
    //等待5秒，让文档加载完成
    await new Promise((r) => setTimeout(r, 2000));

    let livePath = "https://mooc1.chaoxing.com/ananas/live/liveinfo";
    let liveParams = {
        clazzid: mArgJson.defaults.clazzId,
        courseid: mArgJson.defaults.courseid,
        jobid: attachment.jobid,
        knowledgeid: mArgJson.defaults.knowledgeid,
        liveid: attachment.property.liveId,
        userid: mArgJson.defaults.userid,
        ut: "s",
    };
    let liveUrl = pageTools.buildUrl(livePath, liveParams);

    let rt = attachment.property.rt ? parseFloat(attachment.property["rt"]) : 0.9;

    let liveInfoRes = await api.getLiveInfo(liveUrl, chapterCookieStr);

    let duration = liveInfoRes["temp"]["data"]["duration"];
    let timeLongValue = liveInfoRes["temp"]["data"]["timeLongValue"] * 60;
    let liveStatus = liveInfoRes["temp"]["data"]["liveStatus"];

    if (liveStatus != 4) {
        await infoLogger(`${attachLogStr}直播不允许回看，无法播放,${attachment.property.title}`, "red");
        return;
    }

    let indexPath = `https://zhibo.chaoxing.com/${attachment.property.liveId}`;
    let indexParams = {
        courseId: mArgJson.defaults.courseid,
        classId: mArgJson.defaults.clazzId,
        knowledgeId: mArgJson.defaults.knowledgeid,
        jobId: attachment.jobid,
        userId: mArgJson.defaults.userid,
        rt: rt,
        livesetenc: attachment.liveSetEnc,
        isjob: "true",
        watchingInCourse: "1",
        customPara1: `${mArgJson.defaults.clazzId}_${mArgJson.defaults.courseid}`,
        customPara2: attachment.authEnc,
        isNotDrag: "1",
        jobfs: "0",
    };
    let indexUrl = pageTools.buildUrl(indexPath, indexParams);
    let liveIndexRes = await api.liveIndex(indexUrl, chapterCookieStr);

    if (rt <= 0.9) {
        duration = duration * (rt + 0.1);
    }

    let playTime = 0;
    if (timeLongValue > duration) {
        await infoLogger(`${attachLogStr}直播时长已达标，无需继续观看,${attachment.property.title}`, "green");
        return;
    } else {
        // duration -= timeLongValue;
        playTime += timeLongValue;
    }

    let isStart = "0";

    let lreportUrl, reportR;
    //https://zhibo.chaoxing.com/saveTimePc?userId=325949540&courseId=242364601&streamName=NEWLIVEJ6Bf45Khvdoid234726624230t1&vdoid=vdoid234726624230t1&isStart=1

    while (playTime <= duration) {
        lreportUrl = `https://zhibo.chaoxing.com/saveTimePc?userId=${mArgJson.defaults.userid}&courseId=${mArgJson.defaults.courseid}&streamName=${attachment.property.streamName}&vdoid=${attachment.property.vdoid}&isStart=${isStart}`;

        isStart = "1";

        reportR = await api.updateLive(lreportUrl, chapterCookieStr);

        if (reportR.includes("success")) {
            await infoLogger(
                `${attachLogStr} 已经观看${Math.ceil(playTime / 60)}分钟,剩余${Math.ceil((duration - playTime) / 60)}分钟  ${
                    attachment.property.title
                }`
            );
        } else {
            await infoLogger(`${attachLogStr} 上报进度失败  ${attachment.property.title}`, "red");
        }

        if (playTime == duration) {
            await infoLogger(`${attachLogStr} 直播回放完成 ${attachment.property.title}`, "green");
            return;
        }
        playTime += 60;
        if (playTime > duration) {
            playTime = duration;
        }
        await new Promise((r) => setTimeout(r, 60 * 1000));
    }

    // //跳转到直播页面
    // let livePath = `https://zhibo.chaoxing.com/${attachment.property.liveId}`;
    // let liveParams = {
    //     classId: mArgJson.defaults.clazzId,
    //     courseId: mArgJson.defaults.courseid,
    //     customPara1: `${mArgJson.defaults.clazzId}_${mArgJson.defaults.courseid}`,
    //     customPara2: attachment.authEnc,
    //     ds: "0",
    //     isNotDrag: "1",
    //     isjob: "true",
    //     jobId: attachment.jobid,
    //     jobfs: "0",
    //     knowledgeId: mArgJson.defaults.knowledgeid,
    //     livedragenc: attachment.liveDragEnc,
    //     livesetenc: attachment.liveSetEnc,
    //     liveswdsenc: attachment.liveSwDsEnc,
    //     rt: "0.9",
    //     sw: "0",
    //     userId: mArgJson.defaults.userid,
    //     watchingInCourse: "1",
    // };
    // let liveUrl = courseTools.buildUrl(livePath, liveParams);

    // await new Promise((r) => setTimeout(r, 300000));

    // await mainPage.goto(liveUrl, { waitUntil: "networkidle0" });

    // await mainPage.evaluate(async () => {
    //     function handleWatchMoment(watchMomentV) {
    //         if (liveStatus == 4) {
    //             $.ajax({
    //                 url: "/apis/live/put/watchMoment",
    //                 type: "get",
    //                 data: {
    //                     liveId: 26332022,
    //                     streamName: streamName,
    //                     vdoid: vodid,
    //                     watchMoment: watchMomentV,
    //                     t: new Date().getTime(),
    //                     u: uInfo,
    //                 },
    //                 success: function (data) {},
    //             });
    //         }
    //     }

    //     let playing = 0;
    //     while (playing < lineVideo.duration) {
    //         //发送打点信息
    //         record(userId);
    //         // 更改进度
    //         handleWatchMoment(playing);
    //         await new Promise((r) => setTimeout(r, 1000));
    //         playing += 50;
    //     }

    // record(userId);
    // // 更改进度
    // handleWatchMoment(lineVideo.duration);
    // // });
}

module.exports = liveTask;
