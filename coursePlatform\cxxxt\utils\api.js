let axiosIns = require('./axios');

//滑动验证码
exports.slideValidate = (bigImageBase64, smallImageBase64) => {
    // 创建 URLSearchParams 对象
    const params = new URLSearchParams();
    params.append('username', 'mozhi0012');
    params.append('password', 'qfQw4h4WzSKt');
    params.append('captchaData', bigImageBase64);
    // params.append("subCaptchaData", smallImageBase64);
    params.append('captchaType', '1326');
    return axiosIns({
        url: 'https://www.bingtop.com/ocr/upload/',
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: params,
    });
};
exports.clickValidate = bigImageBase64 => {
    // 创建 URLSearchParams 对象
    const params = new URLSearchParams();
    params.append('username', 'mozhi0012');
    params.append('password', 'qfQw4h4WzSKt');
    params.append('captchaData', bigImageBase64);
    params.append('captchaType', '2315');
    return axiosIns({
        url: 'https://www.bingtop.com/ocr/upload/',
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: params,
    });
};

// 获取课程进度
// https://mooc2-ans.chaoxing.com/mooc2-ans/mycourse/stu-job-info
exports.getCourseInfo = async (info, cookie) => {
    let config = {
        url: 'https://mooc2-ans.chaoxing.com/mooc2-ans/mycourse/stu-job-info',
        method: 'get',
        headers: {
            cookie,
        },
        params: {
            clazzPersonStr: info,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        jobArray: [
            {
                jobFinishCount: 40,
                clazzId: 118351476,
                jobRate: 100.0,
                jobCount: 40,
            },
        ],
        status: true,
    };
}

// 获取课程视频信息
exports.getVideoInfo = async (url, cookie) => {
    let config = {
        url,
        method: 'get',
        headers: {
            cookie,
            Referer: 'https://mooc1.chaoxing.com/ananas/modules/video/index.html',
        },
    };
    let res = await axiosIns(config);
    return res;
};
//成功响应
if (false) {
    let videoInfoResult = {
        length: 11490821,
        screenshot: 'https://p2.ananas.chaoxing.com/sv-w2/video/aa/da/f2/fb794c54e1832bfa80c9a375d176c30c/snapshot.jpg',
        cdn: [],
        dtoken: '36edf829b50ad1738bd624a0a4eed8f3',
        duration: 733,
        mp3: 'https://s1.ananas.chaoxing.com/sv-w2/video/aa/da/f2/fb794c54e1832bfa80c9a375d176c30c/mp3/',
        download:
            'http://d0.ananas.chaoxing.com/download/fb794c54e1832bfa80c9a375d176c30c?at_=1703849049502&ak_=38f64531c81b97404395b164bf6b322b&ad_=8f38785f97b16f52ad117e8bbd9c3c39',
        filename: '大学英语三 第一单元 15 综合练习 四.mp4',
        crc: '0ba4239f153d232e5666309be1473954',
        http: 'https://s1.ananas.chaoxing.com/sv-w2/video/aa/da/f2/fb794c54e1832bfa80c9a375d176c30c/sd.mp4?at_=1703849049503&ak_=2f5902a5b87682537ba4a0324d0d0d7d&ad_=856f9c7b32a83a9dea000849eeddb5ec',
        thumbnails: 'https://p2.ananas.chaoxing.com/sv-w2/video/aa/da/f2/fb794c54e1832bfa80c9a375d176c30c/thumbnails/',
        objectid: 'fb794c54e1832bfa80c9a375d176c30c',
        key: '52cbedb80fbca758f2dd37fec5097edb',
        status: 'success',
    };
}

// 修改课程视频长度 （代理）
exports.updateDuration = async (taskObj, videoUrl, chapterCookieStr, isdrag, infoLogger) => {
    let config = {
        url: videoUrl,
        method: 'get',
        headers: {
            cookie: chapterCookieStr,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = { isPassed: false, videoTimeLimit: false, hasJobLimit: false };
}

// 提交作业
exports.submitWorkidTask = async (url, submitStr, cookie) => {
    let config = {
        url,
        headers: {
            cookie,
            'content-type': 'application/x-www-form-urlencoded; charset=UTF-8',
        },
        data: submitStr,
        method: 'post',
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        msg: 'success!',
        stuStatus: 4,
        backUrl: '',
        url: '/mooc-ans/api/work?courseid=217879072&workId=009fca0fe7c64059a6f01637d99133cf&clazzId=93815647&knowledgeid=427581929&ut=s&type=&submit=true&jobid=&enc=cd627baa1d6490bba201e58cb830d0c0&ktoken=216d10f0a07eed786937f035b57d59a2&mooc2=1&skipHeader=true',
        status: true,
    };
    res = { msg: '无效的参数：code-2！', status: false };
}

// 提交文档进度(documentTask,bookTask,linkTask)
exports.submitDocumentTask = async (url, cookie) => {
    let config = {
        url,
        headers: {
            cookie,
        },
        method: 'get',
    };
    let res = await axiosIns(config);
    return res;
};

// 提交文档进度(documentTask,bookTask,linkTask)
exports.getLiveInfo = async (url, cookie) => {
    let config = {
        url,
        headers: {
            cookie,
        },
        method: 'get',
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        msg: '获取成功',
        temp: {
            data: {
                timeLong: 2177223,
                rtmpPullUrl: 'rtmp://ksrtmpplay-rk.chaoxing.com/live/365859410764992513',
                liveType: 1,
                isYunShi: 1,
                createLiveTime: '2024-03-30 13:54:55.0',
                activeId: 29844835,
                source: 24,
                title: '3月30日下午14：00',
                type: 3,
                anchorName: '江章应',
                duration: 9349,
                playbackTimelong: 2177223,
                percentValue: 23.29,
                reviewCount: 0,
                viewUrl:
                    'https://live.superlib.com/courseLive/pclive?streamName=NEWLIVEJ6Bf45Khvdoid234726624230t1_vdoid234726624230t1&vdoid=vdoid234726624230t1',
                state: '4',
                createUid: '87919525',
                liveStatus: 4,
                ifReview: 0,
                chartRoomId: '-2',
                introduce: '%E7%9B%B4%E6%92%AD%E4%BB%8B%E7%BB%8D',
                playbackUrl: 'https://live-rk.chaoxing.com/a/365859410764992513',
                m3u8PullUrl: 'http://kshlsplay-rk.chaoxing.com/live/365859410764992513/index.m3u8',
                timeLongValue: 36.29,
                viewerUrl: 'https://live-rk.chaoxing.com/a/365859410764992513?cxurl=https%3A%2F%2Fzhibo.chaoxing.com%2F4000263323072421&isSuspendDoc=1',
                liveId: 26332022,
                streamName: 'NEWLIVEJ6Bf45Khvdoid234726624230t1_vdoid234726624230t1',
                flvPullUrl: 'rtmp://ksrtmpplay-rk.chaoxing.com/live/365859410764992513',
                vdoid: 'vdoid234726624230t1',
                modeType: 0,
                livingTimelong: 0,
                mp4Url: 'https://pstore-rk.chaoxing.com/365859410764992513.mp4',
                m3u8Url: 'https://pstore-rk.chaoxing.com/record/live/365859410764992513/hls/365859410764992513-365859410764992513.m3u8',
            },
            message: 'success',
            status: 1,
        },
        u: '5BB46501C92AAAB9BD612106CDAF96B4',
        token: '0a5f62f982ca2ccb0a377a933439a37c',
        status: true,
    };
}

// 提交文档进度(documentTask,bookTask,linkTask)
exports.liveIndex = async (url, cookie) => {
    let config = {
        url,
        headers: {
            cookie,
        },
        method: 'get',
    };
    let res = await axiosIns(config);
    return res;
};

// 提交文档进度(documentTask,bookTask,linkTask)
exports.updateLive = async (url, cookie) => {
    let config = {
        url,
        headers: {
            cookie,
        },
        method: 'get',
    };
    let res = await axiosIns(config);
    return res;
};
