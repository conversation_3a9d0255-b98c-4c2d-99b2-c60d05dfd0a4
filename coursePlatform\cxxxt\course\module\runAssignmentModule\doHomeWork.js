let solveQuestion = require('../../../../solveQuestion/index.js');
let api = require('../../../utils/api.js');
let qs = require('qs');
let pageTools = require('../../../../utils/pageTools.js');
async function doHomeWork(infoLogger, globalStore, mainPage, courseObj, homeWorkObj, homeWorkLogStr) {
    let courseName = courseObj.courseName;
    //1.进入到答题页面
    await mainPage.goto(homeWorkObj.url, { waitUntil: 'networkidle0' });

    //2.获取题目列表
    let questionList = await mainPage.evaluate(courseName => {
        function handleImgs(s) {
            // 匹配所有 img 标签，捕获 src 属性的 URL
            let imgEs = s.match(/<img\s+[^>]*src\s*=\s*['"]([^'"]+)['"][^>]*>/gi);
            if (imgEs) {
                for (let j = 0; j < imgEs.length; j++) {
                    // 匹配 img 标签中的 src 属性
                    let urlMatch = imgEs[j].match(/src\s*=\s*['"]([^'"]+)['"]/i);
                    if (urlMatch && urlMatch[1]) {
                        let url = urlMatch[1]; // 获取完整 URL
                        // 用 [URL] 格式替换 img 标签为 URL
                        s = s.replace(imgEs[j], `[URL]${url}[/URL]`);
                    }
                }
            }
            return s;
        }

        function trim(s) {
            return (
                s
                    //删除多余字符串
                    .replace(/(<([^>]+)>)/gi, '') //去掉所有的html标记 <a> </a>
                    .replaceAll('&nbsp;', '') //删除&nbsp;
                    .replaceAll('\n', '') //删除所有的换行符
                    .replace(/\s{2,}/g, ' ') //删除两个及以上的空格，保留单个空格
                    .trim()
                    .replace(/^\d+\s*【.*?】/, '') //删除开头 1 【单选题】
                    .replace(/^\d+.+\(.*\)/, '') // 删除开头 1. (填空题)
                    .replace(/\s{2,}/g, ' ') //删除两个及以上的空格，保留单个空格
                    .trim()
            );
        }

        function handleContent(s) {
            return trim(handleImgs(s)).slice(0, 3000);
        }

        //生成一个唯一id
        function generateUniqueId() {
            return 'id-' + new Date().getTime().toString(36) + '-' + Math.random().toString(36).substr(2, 9);
        }

        let questionList = [];
        let TimuList = document.querySelectorAll('.mark_table form .questionLi');
        for (let i = 0; i < TimuList.length; i++) {
            let timuDom = TimuList[i];
            let questionFull = timuDom.querySelector('.mark_name').innerHTML;

            //题目类型
            let type = timuDom.getAttribute('typename');

            //题目内容
            let content = handleContent(questionFull);

            //获取题目id
            let questionId = '0';
            // 问题对应的答案标签 填空题的答案标签可能不止一个
            let idElements = timuDom.getElementsByTagName('input');
            //如果没有答案，就跳到下一题
            if (idElements.length == 0) {
                continue;
            }
            //通过答案标签获取问题的id
            for (let z = 0; z < idElements.length; z++) {
                try {
                    if (idElements[z].getAttribute('name').indexOf('answer') >= 0) {
                        questionId = idElements[z].getAttribute('name').replace('type', '');
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }
            //如果id为0或者不存在，就进行下一题
            if (questionId == '0' || content == '') {
                continue;
            }

            //4.获取题目选项
            let options = [];
            switch (type) {
                case '单选题':
                case '多选题':
                    let optionList = timuDom.querySelectorAll('.answerBg');
                    for (let j = 0; j < optionList.length; j++) {
                        let optionContent = optionList[j].querySelector('p').innerText;
                        let optionKey = optionList[j].querySelector('span').innerText;
                        optionContent = optionKey + ':' + trim(handleImgs(optionContent));
                        options.push(optionContent);
                    }
                    break;
                case '判断题':
                    options = ['A:对', 'B:错'];
                    break;
                default:
                    options = [];
                    break;
            }

            //收集题目对象
            questionList.push({
                id: generateUniqueId() + questionId, //id-ltf3k9bz-qnqw2bq3qanswer401584769
                content: content,
                type: type,
                options: options,
                platform: '超星学习通',
                courseName: courseName,
            });
        }
        return questionList;
    }, courseName);

    if (questionList.length == 0) {
        await infoLogger(`${homeWorkLogStr}，获取题目列表失败`);
        return;
    }
    await infoLogger(`${homeWorkLogStr}，获取题目列表成功，共${questionList.length}题`);

    // 从浏览器页面获取的提交数据 confirmSubmitWork
    let requestData = await mainPage.evaluate(() => {
        var ajax_url = version($('#submitForm').attr('action'));
        var ajax_type = $('#submitForm').attr('method');
        var ajax_data = $('#submitForm').serialize();

        let obj = {
            type: ajax_type,
            url: 'https://mooc1.chaoxing.com' + ajax_url,
            data: ajax_data,
            dataType: 'json',
        };
        return obj;
    });

    let submitObj = qs.parse(requestData.data);
    submitObj.answerwqbid = [];

    //3.查找答案
    // 查找答案
    let answerCount = 0;
    for (let i = 0; i < questionList.length; i++) {
        //1.取出一个问题对象
        let questionObj = questionList[i];
        let id = questionObj.id.slice(21); // answer403098146
        submitObj.answerwqbid.push(id.replace('answer', ''));
        let answers;
        try {
            answers = await solveQuestion(questionObj);
        } catch (error) {
            continue;
        }
        if (answers) {
            questionObj.answers = answers;
            switch (questionObj.type) {
                // type:0
                case '单选题': {
                    submitObj[id] = answers;
                    break;
                }
                // type:1
                case '多选题': {
                    submitObj[id] = answers;
                    break;
                }
                // type:3
                case '判断题': {
                    submitObj[id] = answers == 'A' ? true : false;
                    break;
                }
                //
                case '简答题': {
                    submitObj[id] = `<p>${answers}</p>`;
                    break;
                }
                // type:2
                case '填空题': {
                    let numId = id.slice(6);
                    let tiankongsize = submitObj[`tiankongsize${numId}`] * 1;
                    let answerArr = answers.split('|');
                    for (let i = 0; i < tiankongsize; i++) {
                        if (!answerArr[i]) break;
                        submitObj[`answerEditor${numId}${i + 1}`] = answerArr[i];
                    }
                    break;
                }
                default:
                    await infoLogger(`${homeWorkLogStr} 未知题型：${questionObj.type}`, 'red');
                    throw new Error('未知题型');
                    break;
            }
            answerCount++;
        }
    }
    submitObj.answerwqbid = submitObj.answerwqbid.join(',') + ',';

    if (answerCount == 0) {
        await infoLogger(`${homeWorkLogStr}，一题都没找到答案`);
        return;
    }
    await infoLogger(`${homeWorkLogStr}，共找到${answerCount}题答案`);

    // 放检测机制，产生一个4-8随机数
    let watiTimeCount = Math.floor(Math.random() * 4) + 4;
    // watiTimeCount=3
    await infoLogger(`${homeWorkLogStr}，等待${watiTimeCount}分钟后提交`);
    await new Promise(resolve => setTimeout(resolve, 60000 * watiTimeCount));

    // 把对象转回请求字符串
    let submitStr = qs.stringify(submitObj);
    let cookieStr = await pageTools.getPageCookies(mainPage);
    let submitRes = await api.submitWorkidTask(requestData.url, submitStr, cookieStr);
    await infoLogger(`${homeWorkLogStr}，作业已提交`);
    return submitRes;
}

module.exports = doHomeWork;
