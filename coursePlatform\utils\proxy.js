let Model = require('../../config/sequelize.config');
let { v4 } = require('uuid');
let { HttpsProxyAgent } = require('https-proxy-agent');
let axiosIns = require('../utils/axiosIns');
let { Op } = require('sequelize');

// 解析多行字符串数据并返回对象数组
function parseProxyDataFromString(proxyString) {
    const lines = proxyString.trim().split('\n'); // 按行分割
    return lines
        .map(line => {
            // 使用正则表达式从每一行数据中提取 IP、端口、用户名、密码
            const regex = /([\d\.]+):(\d+)\s+(\S+)\s+(\S+)\s+(\S+)/;
            const match = line.match(regex);

            if (match) {
                const [_, ip, port, username, password] = match;
                return {
                    ip,
                    port,
                    username,
                    password,
                    state: 'idle', // 初始状态为 'idle'
                };
            }
            return null; // 如果格式不匹配，返回 null
        })
        .filter(item => item !== null); // 过滤掉 null 数据
}

// 批量插入到数据库
async function batchInsertProxies(proxies) {
    try {
        await Model.proxy.bulkCreate(proxies);
        console.log('批量插入成功');
    } catch (error) {
        console.error('批量插入失败:', error);
    }
}

// 主逻辑：读取代理字符串并批量写入数据库
async function processProxies(proxyString) {
    const proxies = parseProxyDataFromString(proxyString);
    if (proxies.length > 0) {
        await batchInsertProxies(proxies);
    } else {
        console.log('没有有效的代理数据');
    }
}
// **************:1010 lqxj17q4 NapAwgMu 2025-03-24
// **************:1238 lqxj17q5 kdnSepTh 2025-03-24

// 假设你有一个多行的代理数据字符串
const proxyString = `
114.98.237.138:7754 fpyl01n1 xdgfkU3i 2025-07-01
223.247.222.99:10080 fpyl01n2 nrHIf8dx 2025-07-01
114.98.233.109:5200 fpyl01n3 qedNfI7k 2025-07-01
60.167.170.26:5678 fpyl01n4 8pvqkfmM 2025-07-01
114.96.90.169:1081 fpyl01n5 fuPx6Re5 2025-07-01
114.96.90.63:8544 fpyl01n6 kWybvHft 2025-07-01
114.96.84.196:2233 fpyl01n7 Xqf2SpmI 2025-07-01
114.96.73.35:1112 fpyl01n8 TKtHwnsr 2025-07-01
114.96.115.204:10080 fpyl01n9 WThAPpHU 2025-07-01
114.96.123.203:7522 fpyl01n10 zKgYqEmN 2025-07-01
114.96.120.125:5126 fpyl01n11 HY4NPTuM 2025-07-01
114.96.117.204:2018 fpyl01n12 DdXstHnQ 2025-07-01
223.247.143.249:9518 fpyl01n13 Kwmi5h8P 2025-07-01
223.242.46.142:2027 fpyl01n14 R3JFQVUm 2025-07-01
************:2010 fpyl01n15 emIVupdA 2025-07-01
************:5888 fpyl01n16 AJXupqdt 2025-07-01
***************:7766 fpyl01n17 TI6VxRYd 2025-07-01
***************:2233 fpyl01n18 xBsuD2ed 2025-07-01
***************:7778 fpyl01n19 MIQWzfUq 2025-07-01
*************:6588 fpyl01n20 W2Iwrj5X 2025-07-01
***************:1008 fpyl01n21 KxEfVSHh 2025-07-01
**************:2828 fpyl01n22 6fZI8Pdb 2025-07-01
**************:1287 fpyl01n23 eEYtHS7g 2025-07-01
***************:4544 fpyl01n24 9HszViP7 2025-07-01
**************:5918 fpyl01n25 NJfDp9iu 2025-07-01
**************:1287 fpyl01n26 g2hH3dI4 2025-07-01
***************:1122 fpyl01n27 HQaqBn28 2025-07-01
**************:5522 fpyl01n28 EiFNegfr 2025-07-01
**************:6018 fpyl01n29 Q7dJ5IgW 2025-07-01
*************:9198 fpyl01n30 xGyRgirX 2025-07-01
*************:7783 fpyl01n31 FAUrw6P8 2025-07-01
*************:2009 fpyl01n32 fwMmJhUY 2025-07-01
***************:5678 fpyl01n33 4SeyJZED 2025-07-01
**************:8787 fpyl01n34 bvJXGDZf 2025-07-01
**************:12755 fpyl01n35 tJ7sWfRv 2025-07-01
**************:5126 fpyl01n36 DEHuQKma 2025-07-01
*************:1008 fpyl01n37 xejkiyDd 2025-07-01
***********:3352 fpyl01n38 t9iAeYrQ 2025-07-01
**************:9518 fpyl01n39 zMf4kSGC 2025-07-01
**************:5262 fpyl01n40 uXbeA3dn 2025-07-01
**************:2019 fpyl01n41 iUAn7M5q 2025-07-01
*************:3123 fpyl01n42 7TM3pYbR 2025-07-01
**************:5477 fpyl01n43 yM9I72um 2025-07-01
**************:9555 fpyl01n44 9fPIkMwE 2025-07-01
223.247.207.117:6685 fpyl01n45 cv6sXxU3 2025-07-01
223.240.122.72:2009 fpyl01n46 QXt4BnHi 2025-07-01
223.247.129.130:1212 fpyl01n47 nd46F5kp 2025-07-01
114.98.228.131:2059 fpyl01n48 PtNF7Kyn 2025-07-01
114.96.74.8:8755 fpyl01n49 jiBEF39J 2025-07-01
114.96.86.198:2030 fpyl01n50 X6myNBTC 2025-07-01
223.244.31.153:3655 fpyl01n51 urdNxqKC 2025-07-01
223.244.30.0:7451 fpyl01n52 aqPGUQRu 2025-07-01
223.244.28.95:3598 fpyl01n53 783ABPHm 2025-07-01
223.244.29.168:6685 fpyl01n54 4raT2FdS 2025-07-01
223.244.30.125:10086 fpyl01n55 v8zPtAg7 2025-07-01
223.244.31.63:9555 fpyl01n56 YPfrmyuA 2025-07-01
223.244.29.219:8755 fpyl01n57 S8k4Hcn2 2025-07-01
223.244.34.97:2881 fpyl01n58 QTRkFAej 2025-07-01
223.244.35.204:2030 fpyl01n59 8P6igr7q 2025-07-01
223.244.33.112:3548 fpyl01n60 ZxIjhnGu 2025-07-01
223.244.34.220:9998 fpyl01n61 RueayKDH 2025-07-01
223.244.35.11:6789 fpyl01n62 U2Ab8Iva 2025-07-01
223.244.32.57:1583 fpyl01n63 hBFScX8Z 2025-07-01
223.244.32.4:5656 fpyl01n64 XJUKuC5H 2025-07-01
223.244.32.108:6541 fpyl01n65 Q5GPjHkS 2025-07-01
223.244.33.236:5858 fpyl01n66 8pnEzJNS 2025-07-01
223.244.32.202:5655 fpyl01n67 xyrHaWjD 2025-07-01
223.244.34.228:6352 fpyl01n68 ZSgbIXVF 2025-07-01
223.244.32.215:2233 fpyl01n69 zMTbXdyK 2025-07-01
223.244.33.164:1110 fpyl01n70 bVwcAMFJ 2025-07-01
223.244.32.53:3548 fpyl01n71 JmiCuKaH 2025-07-01
223.244.27.121:2072 fpyl01n72 KBgAHdMU 2025-07-01
223.244.26.160:2025 fpyl01n73 v2xucVIt 2025-07-01
223.244.25.139:7777 fpyl01n74 mBhYFybC 2025-07-01
223.244.24.7:1890 fpyl01n75 myBGic7b 2025-07-01
223.244.26.146:5888 fpyl01n76 b9mAVjDg 2025-07-01
223.244.25.111:9555 fpyl01n77 mzKTnwNi 2025-07-01
223.244.26.238:9695 fpyl01n78 TJSUF9wE 2025-07-01
223.244.34.248:1090 fpyl01n79 IinyWtNx 2025-07-01
223.244.34.170:2027 fpyl01n80 JDfNAjKy 2025-07-01
223.244.33.201:2028 fpyl01n81 3dC94iSu 2025-07-01
223.244.33.217:3598 fpyl01n82 yatiIBXZ 2025-07-01
223.244.34.234:5555 fpyl01n83 Pz53eZpt 2025-07-01
223.244.34.229:2030 fpyl01n84 a2EuynUS 2025-07-01
223.244.35.116:2221 fpyl01n85 y5hE9pkX 2025-07-01
223.244.17.174:2729 fpyl01n86 2kdYcXvZ 2025-07-01
223.244.18.140:2828 fpyl01n87 7J2PdUW6 2025-07-01
223.244.22.133:8787 fpyl01n88 2W6iuTRe 2025-07-01
223.244.23.34:1090 fpyl01n89 FKaNsBUy 2025-07-01
223.244.23.184:2028 fpyl01n90 aI56YvgJ 2025-07-01
223.244.22.255:3655 fpyl01n91 Dw2CQ5xX 2025-07-01
223.244.17.75:1007 fpyl01n92 HdVakJzr 2025-07-01
223.244.33.156:5555 fpyl01n93 c3WAPg9B 2025-07-01
223.244.35.83:6541 fpyl01n94 nbx4waeZ 2025-07-01
223.244.33.102:2019 fpyl01n95 Vxy9jDH8 2025-07-01
223.244.32.126:8899 fpyl01n96 TetJnFUu 2025-07-01
223.244.32.198:3570 fpyl01n97 T6sWpwb8 2025-07-01
223.244.32.194:6522 fpyl01n98 QGinDcM9 2025-07-01
223.244.32.158:5555 fpyl01n99 tKhuriUx 2025-07-01
223.244.23.172:5477 fpyl01n100 gGnvQmZ8 2025-07-01
117.68.75.148:2111 fpyl01n101 jARSdrwV 2025-07-01
117.68.75.153:2028 fpyl01n102 2kwvcRSZ 2025-07-01
117.68.75.154:1212 fpyl01n103 5FDRyzd9 2025-07-01
117.68.75.149:3313 fpyl01n104 XfeZGuBN 2025-07-01
117.68.75.146:5858 fpyl01n105 K5UeiM8c 2025-07-01
117.68.75.145:2030 fpyl01n106 gCeanbAZ 2025-07-01
117.68.75.156:7777 fpyl01n107 I5twyFhD 2025-07-01
117.68.75.158:6018 fpyl01n108 ANyZISmG 2025-07-01
117.68.75.155:5333 fpyl01n109 f2GgrvRh 2025-07-01
117.68.75.159:8225 fpyl01n110 p8ESmIZi 2025-07-01
223.244.19.199:5555 fpyl01n111 NTKcBSwy 2025-07-01
223.244.21.196:6652 fpyl01n112 THPmdKDg 2025-07-01
223.244.19.93:2087 fpyl01n113 JvAEhGFw 2025-07-01
223.244.19.71:5212 fpyl01n114 QUZVamkG 2025-07-01
223.244.23.124:8525 fpyl01n115 NmSfAhHy 2025-07-01
223.244.20.109:1212 fpyl01n116 tdnKMERb 2025-07-01
223.244.17.33:5200 fpyl01n117 TBzRs8Kw 2025-07-01
223.244.16.145:1066 fpyl01n118 Amfz6Q7c 2025-07-01
223.244.18.65:4211 fpyl01n119 TNVEGZS2 2025-07-01
223.244.18.22:6685 fpyl01n120 CApbnIWc 2025-07-01
223.244.7.130:8225 fpyl01n121 WNkMq2Se 2025-07-01
223.244.7.112:1181 fpyl01n122 Ge9aYKWM 2025-07-01
223.244.7.162:5662 fpyl01n123 XmkA5p4T 2025-07-01
223.244.7.116:9695 fpyl01n124 nWdVhtQX 2025-07-01
223.244.7.14:2828 fpyl01n125 3na4SW7e 2025-07-01
223.244.7.81:1006 fpyl01n126 fvjxzUyW 2025-07-01
223.244.7.131:2027 fpyl01n127 6pABhbCs 2025-07-01
223.244.7.154:2081 fpyl01n128 s8zCXMRx 2025-07-01
223.244.7.69:6652 fpyl01n129 q6imZEQ8 2025-07-01
223.244.7.85:2881 fpyl01n130 d6BNs3ED 2025-07-01
114.96.86.93:9998 fpyl01n131 TfyM3Vt5 2025-07-01
114.96.85.115:9877 fpyl01n132 PIGpXVHc 2025-07-01
114.96.93.47:6588 fpyl01n133 Fb9yDgT8 2025-07-01
114.96.75.94:1118 fpyl01n134 fkAt2CDV 2025-07-01
114.96.68.120:8544 fpyl01n135 XtreCShb 2025-07-01
114.96.95.163:6018 fpyl01n136 Px6e53Zf 2025-07-01
114.96.74.208:10080 fpyl01n137 jsmyICvZ 2025-07-01
114.96.81.60:6018 fpyl01n138 6hrEYFfR 2025-07-01
114.96.88.199:7754 fpyl01n139 84TWUXvt 2025-07-01
114.96.64.241:1890 fpyl01n140 VgXP74pI 2025-07-01

`;

// 调用处理函数
processProxies(proxyString);

(async () => {

    // // 重置状态
    // await Model.proxy.update({
    //     state: 'idle',
    // },{
    //     where:{
    //         state:'busy'
    //     }
    // });

    // // 测试是否有效
    // let proxyArr = await Model.proxy.findAll();
    // for (let i = 0; i < proxyArr.length; i++) {
    //     let proxy = proxyArr[i];
    //     let agent = new HttpsProxyAgent(`http://${proxy.username}:${proxy.password}@${proxy.ip}:${proxy.port}`);
    //     let ipcnRes;
    //     try {
    //         ipcnRes = await axiosIns({
    //             method: 'get',
    //             url: 'http://course.mozhi0012.top:7099/ip',
    //             httpAgent: agent,
    //             // httpsAgent:agent
    //         });
    //     } catch (error) {
    //         console.log(`第${i}个IP,${proxy.ip}，无法访问`);
    //     }
    //     if (ipcnRes.code == 0) {
    //         console.log(`第${i}个IP,${proxy.ip}，正常访问,${ipcnRes.data.ip == proxy.ip}`);
    //     }
    // }
    

})();
