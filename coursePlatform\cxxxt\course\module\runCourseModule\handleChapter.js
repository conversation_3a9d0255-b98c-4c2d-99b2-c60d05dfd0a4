let pageTools = require('../../../../utils/pageTools.js');
let handlePage = require('./handlePage.js'); // 引入处理页面的模块

/**
 * 遍历课程的章节列表
 * @param {object} taskObj - 全局任务配置对象
 * @param {object} globalStore - 全局存储对象，包含 courseStore
 * @param {function} infoLogger - 日志记录函数
 * @param {object} mainPage - Puppeteer 的 Page 对象
 * @param {Array} chapterList - 课程的章节列表
 * @param {string} courseLogStr - 当前课程的日志前缀字符串
 */
async function chapterHandler(globalStore, infoLogger, mainPage, courseLogStr) {
    let courseStore = globalStore.courseStore; // 从 globalStore 中获取 courseStore
    let taskObj = globalStore.taskObj;

    // 跳转到章节chapter纯文字页面 获取章节列表，
    let coursePath = 'https://mooc1.chaoxing.com/mycourse/studentstudycourselist';
    let courseParams = {
        courseId: courseStore.courseId,
        clazzid: courseStore.classId,
        mooc2: '1',
    };
    let courseUrl = pageTools.buildUrl(coursePath, courseParams);
    // await mainPage.goto(courseUrl, { waitUntil: 'networkidle0' });
    await infoLogger(`进入到章节的文字界面`);
    await pageTools.gotoWithRetry(mainPage, courseUrl, { waitUntil: 'networkidle0' }, 3, infoLogger);

    // 获取章节列表 也就是chapterId数组 posCatalog_select firstLayer
    let chapterList = await mainPage.evaluate(() => {
        //获取所有课件信息
        let divs = document.querySelectorAll('.posCatalog_select:not(.firstLayer)');
        //添加任务列表
        let chapterListEval = [];
        for (let i = 0, l = divs.length; i < l; i++) {
            // 把所有任务都统计，包含已完成的任务
            chapterListEval.push({ id: divs[i].id.replace('cur', ''), name: divs[i].innerText });
        }
        return chapterListEval;
    });

    await infoLogger(`${courseLogStr} 《${courseStore.courseName}》课程，共${chapterList.length}个章节`);

    // 遍历章节chapter列表
    loopChapter: for (let chapterIndex = 0; chapterIndex < chapterList.length; chapterIndex++) {
        let chapterLogStr = `${courseLogStr}-章节[${chapterIndex + 1}/${chapterList.length}]`;

        // 1.取出一个章节ID,chapterId
        let chapterObj = chapterList[chapterIndex];
        let chapterId = chapterObj.id;
        await infoLogger(`${chapterLogStr}：开始章节：${chapterObj.name}`);

        // 跳过已完成课程
        if (chapterObj.name.includes('已完成')) {
            await infoLogger(`${chapterLogStr}：当前章节已完成，跳过`, 'green');
            continue loopChapter;
        }

        // // 处理因前置任务未完成而锁定的章节
        // if (chapterObj.name.includes('需完成之前闯关任务点，该章节才可解锁')) {
        //     await infoLogger(`${chapterLogStr}：当前章节 “${chapterObj.name}” 因前置任务未完成而锁定。`);

        //     // 检查此章节是否已经回滚过
        //     if (chapterObj.hasRolledBack) {
        //         // 检查标记属性
        //         await infoLogger(`${chapterLogStr}：章节 “${chapterObj.name}” (ID: ${chapterId}) 已经尝试过回滚，但仍未解锁，将跳过此章节。`, 'red');
        //         throw new Error(`当前课程为闯关模式，章节《${chapterObj.name}》前一章任务没有完成，无法继续`);
        //     }

        //     // 如果是第一个章节，无法回滚
        //     if (chapterIndex === 0) {
        //         await infoLogger(`${chapterLogStr}：当前是第一个章节且已锁定，无法回滚`);
        //         continue loopChapter;
        //     } else {
        //         // 执行回滚
        //         await infoLogger(`${chapterLogStr}：尝试回滚到上一个章节重新处理。`);
        //         chapterObj.hasRolledBack = true; // 给当前章节对象添加标记
        //         chapterIndex--; // 回滚到上一个章节索引
        //         continue loopChapter; // 继续下一次循环，此时会处理上一个章节
        //     }
        // }

        // // 指定章节 (根据需要取消注释)
        // if (chapterId != '692584230') { // 示例 chapterId
        //     await infoLogger(`${chapterLogStr}：章节ID：${chapterId}，跳过`);
        //     continue loopChapter;
        // }

        // 进入到章节页面（用于获取任务卡片的基础链接）
        let chapterUrl = `https://mooc1.chaoxing.com/mooc-ans/mycourse/studentstudy?chapterId=${chapterId}&courseId=${courseStore.courseId}&clazzid=${courseStore.classId}&enc=${courseStore.oldenc}`;
        await infoLogger(`进入章节页面`);
        await pageTools.gotoWithRetry(mainPage, chapterUrl, { waitUntil: 'networkidle0' }, 3, infoLogger);
        let pageCount = await mainPage.evaluate(() => {
            let pageCountList = document.querySelectorAll('#mainid > div.tabtags>span');
            if (pageCountList.length > 0) {
                return pageCountList.length;
            }
            return 5;
        });
        await infoLogger(`当前章节有${pageCount}个页面`);

        // 调用 loopPage 处理当前章节的页面
        // loopPage 会返回一个对象，例如 { skipChapter: true } 来指示是否需要跳过当前章节
        let pageResult = await handlePage(globalStore, infoLogger, mainPage, chapterLogStr, chapterId,pageCount);

        if (pageResult && pageResult.skipChapter) {
            await infoLogger(`${chapterLogStr}：根据页面处理结果，跳过当前章节 ${chapterList[chapterIndex].name}`);
            continue loopChapter;
        }
    }
}

module.exports = chapterHandler;
