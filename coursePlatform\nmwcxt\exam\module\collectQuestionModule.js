let { v4 } = require('uuid');
let Model = require('../../../../config/sequelize.config');
let { encryptData, decryptData, decodeRequestData, encodeRequestData } = require('../../utils/aesF.js');
let api = require('../../utils/api.js');
let { handleImgs } = require('../../../solveQuestion/format.js');
let {trim,convertHttpToHttps}=require('../../utils/tools.js');

// 结合 handleImgs 和 trim，处理题目内容
function handleContent(str) {
    let res = trim(handleImgs(str)).slice(0, 3000);
    res = convertHttpToHttps(res);
    return res;
}

// 主要部分抽离出来，方便单元测试
async function collection(itemInfoList, courseName, globalStore) {
    // 收集试题
    collectCount = 0;
    itemLoop: for (let item of itemInfoList) {
        //确定题目类型
        let questionType = {
            1: '填空题',
            2: '简答题',
            3: '单选题',
            4: '多选题',
        }[item.itemType];

        if (Array.isArray(item.optionNodes) && item.optionNodes.length == 2) {
            questionType = '判断题';
        }

        //处理题目选项和答案
        let optionNodes = item.optionNodes;
        let options = [];
        let answers = '';
        let answersContent = [];
        switch (questionType) {
            case '填空题': {
                options = [];
                answersContent = answers = item.itemAnswer.map(item => item.optionContent).join('|');
                break;
            }

            case '简答题': {
                options = [];
                answers = item.itemAnswer[0].optionContent.replaceAll('答案要点', '答：');
                answersContent = [answers];
                break;
            }

            // 选择题，有可能API返回的答案是错的
            case '判断题': {
                if (globalStore.others.includes('内置')) {
                    options = ['A:对', 'B:错'];
                    answers = item.itemAnswer[0].optionContent;
                    answersContent = answers == 'A' ? ['对'] : ['错'];
                }
                break;
            }

            case '单选题': {
                if (globalStore.others.includes('内置')) {
                    options = optionNodes.map(item => `${item.option}:${handleContent(item.optionContent)}`);
                    answers = item.itemAnswer[0].optionContent;
                    for (let str of answers) {
                        let ansIndex = {A: 0, B: 1, C: 2, D: 3, E: 4, F: 5, G: 6, H: 7 }[str];
                        // console.log(ansIndex)
                        if (ansIndex == undefined) continue itemLoop;
                        let queOption = options[ansIndex];
                        // let [queIndex, queValue] = queOption.split(':');
                        let [queIndex, queValue] = queOption.split(/:(.+)/);
                        answersContent.push(queValue);
                        answersContent = [...answersContent].sort((x, y) => x.localeCompare(y, 'zh-Hans-CN'));
                    }
                }
                break;
            }

            case '多选题': {
                if (globalStore.others.includes('内置')) {
                    options = optionNodes.map(item => `${item.option}:${handleContent(item.optionContent)}`);
                    answers = item.itemAnswer.map(item => item.optionContent).join('');
                    for (let str of answers) {
                        try {
                            let ansIndex = { A: 0, B: 1, C: 2, D: 3, E: 4, F: 5, G: 6, H: 7 }[str];
                            let queOption = options[ansIndex];
                            // let [queIndex, queValue] = queOption.split(':');
                            let [queIndex, queValue] = queOption.split(/:(.+)/);
                            answersContent.push(queValue);
                            answersContent = [...answersContent].sort((x, y) => x.localeCompare(y, 'zh-Hans-CN'));
                        } catch (error) {
                            continue itemLoop;
                        }
                    }
                }
                break;
            }
        }

        if (!answers) {
            continue;
        }

        let questionObj = {
            id: v4(),
            type: questionType,
            options: options,
            content: handleContent(item.itemName),
            answers,
            answersContent,
            platform: '柠檬文采学堂',
            courseName: courseName,
            commit: '',
        };
        // console.log(questionObj);

        let existAnswer = await Model.bank.findOne({
            where: {
                content: questionObj.content,
                type: questionObj.type,
                answers_content: JSON.stringify(answersContent),
            },
        });
        // 如果题库中存在的题目
        if (!existAnswer) {
            await Model.bank.create({
                id: questionObj.id,
                content: questionObj.content,
                options: JSON.stringify(questionObj.options),
                type: questionObj.type,
                answers: questionObj.answers,
                answers_content: JSON.stringify(answersContent),
                course_name: courseName,
                platform: questionObj.platform,
                add_time: new Date(),
            });
            collectCount++;
        }
    }
    return collectCount;
}

async function collectQuestionModule(globalStore, taskObj, infoLogger) {
    let { examModuleObj, userInfoVo } = globalStore;
    // 获取题目列表
    let getExamListDataObj = { user_info: JSON.stringify(userInfoVo), exam_id: examModuleObj.examId };
    getExamListRes = await api.getItemList(getExamListDataObj);
    let examListData = decryptData(getExamListRes.data);
    examListData = JSON.parse(examListData);
    let itemInfoList = examListData.itemInfoList;
    globalStore.itemInfoList = itemInfoList;

    let collectCount = await collection(itemInfoList, taskObj.coursename, globalStore);
    await infoLogger(`共有${itemInfoList.length}道题目，收集${collectCount}道题目`);
}

module.exports = collectQuestionModule;

// 单元测试
if (false) {
    let examData = [
        {
            itemNo: 1,
            itemId: '469695952749330434',
            examScoreDetailId: '473401964076466231',
            itemName: '从应用和企业层角度来解决安全问题，以面向任务的观点，从任务的角度来建立安全模型和实现安全机制的访问控制模型是(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '强制访问控制模型' },
                { option: 'B', optionNo: '1', optionContent: '基于角色的访问控制模型' },
                { option: 'C', optionNo: '2', optionContent: '基于对象的访问控制模型' },
                { option: 'D', optionNo: '3', optionContent: '基于任务的访问控制模型' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'D', myOption: '', smallStatus: 0, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_m7ile43w6s_15' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 2,
            itemId: '469696038653919233',
            examScoreDetailId: '473401964076466234',
            itemName: '下面哪种算法只可用于数字签名(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: 'DES' },
                { option: 'B', optionNo: '1', optionContent: 'DSA' },
                { option: 'C', optionNo: '2', optionContent: 'RSA' },
            ],
            itemAnswer: [
                {
                    option: '1',
                    optionContent: 'SHA',
                    myOption: 'SHA',
                    smallStatus: 1,
                    itemStatus: 0,
                    score: '0.0',
                    myOptionKey: 'lemonysoft_item_0erly1nz9c_57',
                },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 3,
            itemId: '469695931274493952',
            examScoreDetailId: '473401964076466235',
            itemName: '下列属于DDoS攻击的是(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: 'Land' },
                { option: 'B', optionNo: '1', optionContent: 'Ping of Death' },
                { option: 'C', optionNo: '2', optionContent: 'TFN' },
                { option: 'D', optionNo: '3', optionContent: 'Smurf' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'C', myOption: 'C', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_kuafa9jkpa_50' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 4,
            itemId: '469695944159395840',
            examScoreDetailId: '473401964076466237',
            itemName: '下面关于信息的特征方面，错误说法是(     )',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '信息来源于物质，也是物质本身' },
                { option: 'B', optionNo: '1', optionContent: '信息来源于精神世界' },
                { option: 'C', optionNo: '2', optionContent: '信息来源于物质，又不是物质本身' },
                { option: 'D', optionNo: '3', optionContent: '信息与能量息息相关' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'A', myOption: 'A', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_h1tullzwuk_31' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 5,
            itemId: '469695948454363140',
            examScoreDetailId: '473401964076466239',
            itemName: '基于网络低层协议、利用协议或操作系统实现时的漏洞来达到攻击目的，这种攻击方式称为(     )',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '服务攻击' },
                { option: 'B', optionNo: '1', optionContent: '拒绝服务攻击' },
                { option: 'C', optionNo: '2', optionContent: '被动攻击' },
                { option: 'D', optionNo: '3', optionContent: '非服务攻击' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'B', myOption: 'B', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_cjzeeqn6uh_18' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 6,
            itemId: '469695944159395848',
            examScoreDetailId: '473401964076466240',
            itemName: '代理服务作为防火墙技术主要在OSI的哪一层实现(     )',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '数据链路层' },
                { option: 'B', optionNo: '1', optionContent: '网络层' },
                { option: 'C', optionNo: '2', optionContent: '应用层' },
                { option: 'D', optionNo: '3', optionContent: '传输层' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'C', myOption: 'C', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_b1fkf8mevx_25' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 7,
            itemId: '469695939864428544',
            examScoreDetailId: '473401964076466241',
            itemName: 'Web从Web服务器方面和浏览器方面受到的威胁主要来自(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '浏览器和Web服务器的通信方面存在漏洞' },
                { option: 'B', optionNo: '1', optionContent: 'Web服务器的安全漏洞' },
                { option: 'C', optionNo: '2', optionContent: '服务器端脚本的安全漏洞' },
                { option: 'D', optionNo: '3', optionContent: '以上全是' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'D', myOption: 'D', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_tlodmt4y5d_37' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 8,
            itemId: '469695944159395843',
            examScoreDetailId: '473401964076466242',
            itemName: '不能防止计算机感染病毒的措施是(     )',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '定时备份重要文件' },
                { option: 'B', optionNo: '1', optionContent: '经常更新操作系统' },
                { option: 'C', optionNo: '2', optionContent: '除非确切知道附件内容，否则不要打开电子邮件附件' },
                { option: 'D', optionNo: '3', optionContent: '重要部门的计算机尽量专机专用与外界隔绝' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'A', myOption: 'A', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_pfu4u2n7ua_30' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 9,
            itemId: '469695948454363138',
            examScoreDetailId: '473401964076466243',
            itemName: '如果发送方使用的加密密钥和接收方使用的解密密钥不相同，从其中一个密钥难以推出另一个密钥，这样的系统称为(     )',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '常规加密系统' },
                { option: 'B', optionNo: '1', optionContent: '单密钥加密系统' },
                { option: 'C', optionNo: '2', optionContent: '公钥加密系统' },
                { option: 'D', optionNo: '3', optionContent: '对称加密系统' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'C', myOption: 'C', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_poyc7bqvil_20' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 10,
            itemId: '469695935569461255',
            examScoreDetailId: '473401964076466245',
            itemName: '当数据库损坏时，数据库管理员可通过何种方式恢复数据库(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '事务日志文件' },
                { option: 'B', optionNo: '1', optionContent: '主数据文件' },
                { option: 'C', optionNo: '2', optionContent: 'DELETE语句' },
                { option: 'D', optionNo: '3', optionContent: '联机帮助文件' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'A', myOption: 'A', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_1a3sncabfy_38' },
            ],
            itemType: '3',
            itemRemark: '',
        },

        {
            itemNo: 11,
            itemId: '469695939864428548',
            examScoreDetailId: '473401964076466246',
            itemName: '强制口令破解不包括以下(     )',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '猜解简单口令' },
                { option: 'B', optionNo: '1', optionContent: '字典攻击' },
                { option: 'C', optionNo: '2', optionContent: '窥视输入的口令' },
                { option: 'D', optionNo: '3', optionContent: '暴力猜解' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'C', myOption: 'C', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_14wvbvwd9t_33' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 12,
            itemId: '469695939864428545',
            examScoreDetailId: '473401964076466248',
            itemName: '以下关于CA认证中心说法正确的是(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: 'CA认证是使用对称密钥机制的认证方法' },
                { option: 'B', optionNo: '1', optionContent: 'CA认证中心只负责签名，不负责证书的产生' },
                { option: 'C', optionNo: '2', optionContent: 'CA认证中心负责证书的颁发和管理、并依靠证书证明一个用户的身份' },
                { option: 'D', optionNo: '3', optionContent: 'CA认证中心不用保持中立，可以随便找一个用户来做为CA认证中心' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'C', myOption: 'C', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_4qsnbqwsgw_36' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 13,
            itemId: '469695926979526662',
            examScoreDetailId: '473401964076466249',
            itemName: 'IDEA算法的密钥是(     )位。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '56' },
                { option: 'B', optionNo: '1', optionContent: '48' },
                { option: 'C', optionNo: '2', optionContent: '128' },
                { option: 'D', optionNo: '3', optionContent: '256' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'C', myOption: 'C', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_jogltbmchy_53' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 14,
            itemId: '469695931274493953',
            examScoreDetailId: '473401964076466250',
            itemName: 'IPSec是属于(     )的安全机制。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '传输层' },
                { option: 'B', optionNo: '1', optionContent: '应用层' },
                { option: 'C', optionNo: '2', optionContent: '数据链路层' },
                { option: 'D', optionNo: '3', optionContent: '网络层' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'D', myOption: 'D', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_meapuxx2e9_49' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 15,
            itemId: '469695952749330435',
            examScoreDetailId: '473401964076466251',
            itemName: '对于同一个事物，不同的观察者所能获得的信息量可能不同，这反映了信息的(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '普遍性' },
                { option: 'B', optionNo: '1', optionContent: '相对性' },
                { option: 'C', optionNo: '2', optionContent: '传递性' },
                { option: 'D', optionNo: '3', optionContent: '变换性' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'B', myOption: '', smallStatus: 0, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_buwxeepx6d_14' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 16,
            itemId: '469695926979526663',
            examScoreDetailId: '473401964076466254',
            itemName: 'DES算法中的S盒是将(     )的变换。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '48位变换为32位' },
                { option: 'B', optionNo: '1', optionContent: '32位变换为16位' },
                { option: 'C', optionNo: '2', optionContent: '56位变换为48位' },
                { option: 'D', optionNo: '3', optionContent: '64位变换为56位' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'A', myOption: 'A', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_tc9b5mqqdh_52' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 17,
            itemId: '469696038653919232',
            examScoreDetailId: '473401964076466256',
            itemName: '下面属于分组密码算法的是(     )算法。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: 'ECC' },
                { option: 'B', optionNo: '1', optionContent: 'IDEA' },
                { option: 'C', optionNo: '2', optionContent: 'RC4' },
            ],
            itemAnswer: [
                {
                    option: '1',
                    optionContent: 'RSA',
                    myOption: 'RSA',
                    smallStatus: 1,
                    itemStatus: 0,
                    score: '0.0',
                    myOptionKey: 'lemonysoft_item_946iriqm7j_65',
                },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 18,
            itemId: '469695948454363141',
            examScoreDetailId: '473401964076466257',
            itemName: '美国国防部安全标准定义了4个安全级别，其中最高安全级提供了最全面的安全支持，它是(     )',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: 'a级' },
                { option: 'B', optionNo: '1', optionContent: 'b级' },
                { option: 'C', optionNo: '2', optionContent: 'c级' },
                { option: 'D', optionNo: '3', optionContent: '级' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'A', myOption: 'A', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_ortgtilonp_17' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 19,
            itemId: '469695926979526661',
            examScoreDetailId: '473401964076466258',
            itemName: '我们称Hash函数为单向Hash函数，原因在于(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '输入x可以为任意长度' },
                { option: 'B', optionNo: '1', optionContent: '输出数据长度固定' },
                { option: 'C', optionNo: '2', optionContent: '给出一个Hash值，很难反向计算出原始输入' },
                { option: 'D', optionNo: '3', optionContent: '难以找到两个不同的输入会得到相同的Hash输出值' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'C', myOption: 'C', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_ypoywurpqg_54' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 20,
            itemId: '469695931274493954',
            examScoreDetailId: '473401964076466259',
            itemName: '下面哪种算法只可用于数字签名(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: 'DES' },
                { option: 'B', optionNo: '1', optionContent: 'DSA' },
                { option: 'C', optionNo: '2', optionContent: 'RSA' },
                { option: 'D', optionNo: '3', optionContent: 'SHA' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'B', myOption: '', smallStatus: 0, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_a4hcmsdx3h_48' },
            ],
            itemType: '3',
            itemRemark: '',
        },

        {
            itemNo: 21,
            itemId: '469695935569461250',
            examScoreDetailId: '473401964076466261',
            itemName: '下列病毒种类中，不是按感染系统进行分类的是(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: 'DOS病毒' },
                { option: 'B', optionNo: '1', optionContent: '宏病毒' },
                { option: 'C', optionNo: '2', optionContent: 'Windows病毒' },
                { option: 'D', optionNo: '3', optionContent: '引导型病毒' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'B', myOption: 'B', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_yz36vf6r70_42' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 22,
            itemId: '469695935569461254',
            examScoreDetailId: '473401964076466262',
            itemName: '文件型病毒传染的对象主要是哪类文件(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '.EXE和.WPS' },
                { option: 'B', optionNo: '1', optionContent: '.COM和.EXE' },
                { option: 'C', optionNo: '2', optionContent: '.WPS' },
                { option: 'D', optionNo: '3', optionContent: '. DBF' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'B', myOption: 'B', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_boont44tlj_39' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 23,
            itemId: '469695939864428546',
            examScoreDetailId: '473401964076466264',
            itemName: '为防止外界干扰，主动红外探测器发射机所发出的(     )必须经过调制。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '声波' },
                { option: 'B', optionNo: '1', optionContent: '噪音' },
                { option: 'C', optionNo: '2', optionContent: '激光' },
                { option: 'D', optionNo: '3', optionContent: '红外辐射' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'D', myOption: 'D', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_j5wcpb07t6_35' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 24,
            itemId: '469695944159395845',
            examScoreDetailId: '473401964076466265',
            itemName: '下面不属于令牌的是(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '时间令牌' },
                { option: 'B', optionNo: '1', optionContent: '出入证件' },
                { option: 'C', optionNo: '2', optionContent: '为每一次认证产生不同认证值的小型电子设备' },
                { option: 'D', optionNo: '3', optionContent: '挑战应答令牌' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'B', myOption: 'B', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_gr3emitnrj_28' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 25,
            itemId: '469695952749330433',
            examScoreDetailId: '473401964076466266',
            itemName: '攻击者发送大量声称来自银行或其他知名机构的欺骗性垃圾邮件，意图引诱收信人给出敏感信息的攻击手段是(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '社会工程学' },
                { option: 'B', optionNo: '1', optionContent: '网络钓鱼' },
                { option: 'C', optionNo: '2', optionContent: '旁路攻击' },
                { option: 'D', optionNo: '3', optionContent: '授权侵犯' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'B', myOption: 'B', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_ixsckqhpjc_16' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 26,
            itemId: '469695944159395851',
            examScoreDetailId: '473401964076466269',
            itemName: '网络安全中，中断指攻击者破坏网络系统的资源，使之变成无效的或无用的。这是对(     )',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '可用性的攻击' },
                { option: 'B', optionNo: '1', optionContent: '保密性的攻击' },
                { option: 'C', optionNo: '2', optionContent: '完整性的攻击' },
                { option: 'D', optionNo: '3', optionContent: '真实性的攻击' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'A', myOption: 'A', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_zsydca6gy1_23' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 27,
            itemId: '469695944159395850',
            examScoreDetailId: '473401964076466272',
            itemName: '以下哪一项不是分组密码的优点(     )',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '良好的扩散性' },
                { option: 'B', optionNo: '1', optionContent: '对插入的敏感程度高' },
                { option: 'C', optionNo: '2', optionContent: '不需要密钥同步' },
                { option: 'D', optionNo: '3', optionContent: '加密速度快' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'D', myOption: 'D', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_05wfoesccg_24' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 28,
            itemId: '469695944159395847',
            examScoreDetailId: '473401964076466275',
            itemName: '下面关于隔离网闸的说法，正确的是(     )',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '能够发现已知的数据库漏洞' },
                { option: 'B', optionNo: '1', optionContent: '可以通过协议代理的方法，穿透网闸的安全控制' },
                { option: 'C', optionNo: '2', optionContent: '任何时刻，网闸两端的网络之间不存在物理连接' },
                { option: 'D', optionNo: '3', optionContent: '在OSI的二层以上发挥作用' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'C', myOption: 'C', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_fkg9vrupaa_26' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 29,
            itemId: '469695935569461251',
            examScoreDetailId: '473401964076466277',
            itemName: '以下数据加密算法属于对称密码体制的是(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: 'RSA算法' },
                { option: 'B', optionNo: '1', optionContent: 'AES算法' },
                { option: 'C', optionNo: '2', optionContent: 'ECC算法' },
                { option: 'D', optionNo: '3', optionContent: 'Elgamal算法' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'B', myOption: '', smallStatus: 0, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_7j9gi1tajb_41' },
            ],
            itemType: '3',
            itemRemark: '',
        },
        {
            itemNo: 30,
            itemId: '469695926979526656',
            examScoreDetailId: '473401964076466278',
            itemName: 'DES入口参数中的Mode表示DES的工作方式，有两种：(     )。',
            optionNodes: [
                { option: 'A', optionNo: '0', optionContent: '加密或混淆' },
                { option: 'B', optionNo: '1', optionContent: '加密或解密' },
                { option: 'C', optionNo: '2', optionContent: '混淆或解密' },
                { option: 'D', optionNo: '3', optionContent: '加密或扩散' },
            ],
            itemAnswer: [
                { option: '1', optionContent: 'B', myOption: 'B', smallStatus: 1, itemStatus: 0, score: '0.0', myOptionKey: 'lemonysoft_item_viugcnfujn_55' },
            ],
            itemType: '3',
            itemRemark: '',
        },
    ];
    collection(examData, '信息素养').then(r => console.log(r));
}
