let fs = require('fs');
let crypto = require('crypto');
let path = require('path');
let pageTools = require('../../../../utils/pageTools.js');
let api = require('../../../utils/api.js');

// 根据文件扩展名获取Content-Type
function getContentType(fileName) {
    let ext = path.extname(fileName).toLowerCase();
    let contentTypeMap = {
        '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        '.doc': 'application/msword',
        '.pdf': 'application/pdf',
        '.zip': 'application/zip',
        '.rar': 'application/x-rar-compressed',
        '.txt': 'text/plain',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.png': 'image/png',
        '.gif': 'image/gif',
        '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        '.xls': 'application/vnd.ms-excel',
        '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        '.ppt': 'application/vnd.ms-powerpoint',
    };

    return contentTypeMap[ext] || 'application/octet-stream';
}

// 上传或者查找文件，并且返回id
async function uploadFile(infoLogger, globalStore, taskObj, homeWorkLogStr, formatName, cookieStr, baseFilePath, courseObj) {
    // 确定本地有对应的资源
    if (!fs.existsSync(baseFilePath)) {
        await infoLogger(`${homeWorkLogStr} 大作业本地没有目录`, 'red');
        globalStore.warningMessage += `${courseObj.name} 大作业本地没有目录`;
        return;
    }
    let files = fs.readdirSync(baseFilePath);
    if (files.length == 0) {
        await infoLogger(`${homeWorkLogStr}:大作业本地目录为空`, 'red');
        globalStore.warningMessage += `${courseObj.name} 大作业本地目录为空`;
        return;
    }

    let fileObjArr = []; // 存储上传成功的文件信息

    // 一个作业可能要上传多个文件
    for (let i = 0; i < files.length; i++) {
        let fileName = files[i];
        let temFilePath = path.resolve(`${baseFilePath}/${fileName}`);

        // 1. 读取文件和准备数据
        let fileBuffer = fs.readFileSync(temFilePath);
        let fileSize = fs.statSync(temFilePath).size;

        // 计算文件的MD5值（Base64格式）
        let md5Hash = crypto.createHash('md5');
        md5Hash.update(fileBuffer);
        let md5Base64 = md5Hash.digest('base64');

        await infoLogger(`${homeWorkLogStr}-[${i + 1}/${files.length}]准备上传文件: ${fileName}`);

        // 第一步：创建上传任务，获取上传凭证
        let uploadInitRes = await api.uploads(fileName, fileSize, cookieStr);

        if (!uploadInitRes || !uploadInitRes.upload_url) {
            await infoLogger(`${homeWorkLogStr}-[${i + 1}/${files.length}] 文件 ${fileName} 初始化上传失败`, 'red');
            continue;
        }

        let uploadUrl = uploadInitRes.upload_url; // 华为云OBS上传URL
        let fileKey = uploadInitRes.id || uploadInitRes.key; // 文件的唯一标识

        await infoLogger(`${homeWorkLogStr}-[${i + 1}/${files.length}] 文件 ${fileName} 获取上传凭证成功，文件ID: ${fileKey}`);
        await new Promise(r => setTimeout(r, 1000));

        // 第二步：上传文件到华为云OBS
        let contentType = getContentType(fileName);
        await api.uploadFileToObs(uploadUrl, contentType, md5Base64, fileBuffer);
        await infoLogger(`${homeWorkLogStr}-[${i + 1}/${files.length}]文件 ${fileName} 上传到云存储成功`);
        await new Promise(r => setTimeout(r, 1000));

        // 第三步：回调确认上传完成
        let callbackRes = await api.uploadFileCallback(fileKey, cookieStr);
        await infoLogger(`${homeWorkLogStr}-[${i + 1}/${files.length}] 文件 ${fileName} 上传回调成功`);
        await new Promise(r => setTimeout(r, 1000));

        // 保存上传成功的文件信息
        fileObjArr.push({
            id: fileKey,
            name: fileName,
            size: fileSize,
        });
        await new Promise(r => setTimeout(r, 3000));
    }

    // 提交作业
    if (fileObjArr.length > 0) {
        let uploads = fileObjArr.map(item => item.id);
        await infoLogger(`${homeWorkLogStr}  找到资源，准备提交`);
        let submitRes = await api.submitHomework(taskObj.id, '', uploads, cookieStr);
        if (submitRes && submitRes.message == '作业已经交付') {
            await infoLogger(`${homeWorkLogStr} 作业提交成功`, 'green');
        } else {
            await infoLogger(`${homeWorkLogStr}  作业提交失败`, 'red');
        }
    }
}

module.exports = uploadFile;
