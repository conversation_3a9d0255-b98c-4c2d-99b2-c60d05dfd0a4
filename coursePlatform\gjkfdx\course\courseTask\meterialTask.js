let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api');
async function meterialTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr) {
    await infoLogger(`${taskLogStr}<${taskObj.title}> ${taskObj.type} 课件开始`);
    let cookieStr = await pageTools.getPageCookies(mainPage);

    //打开附件时候发送
    let uploads = taskObj.uploads;
    for (let i = 0; i < uploads.length; i++) {
        let uploadLogStr = `${taskLogStr}-附件[${i + 1}/${uploads.length}]`;
        let upload = uploads[i];
        let data = JSON.stringify({ upload_id: upload.id });
        let activitesRes = await api.updateDuration(taskObj.id, cookieStr, data);
        if (activitesRes.completeness == 'full') {
            await infoLogger(`${uploadLogStr} 已经完成`, 'green');
        }
        await new Promise(r => setTimeout(r, 10*1000));
    }

    await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 附件任务全部完成，10秒后进行下一个课件`, 'green');
    await new Promise(r => setTimeout(r, 10*1000));
}

module.exports = meterialTask;