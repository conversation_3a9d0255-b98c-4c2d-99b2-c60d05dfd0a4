// ========= 私有模块 =========
let pageTools = require('../../utils/pageTools.js');
let getMainPage = require('../../utils/getMainPage.js');

// 考试模块
let signinModule = require('../exam/module/signinModule.js');
let getCourseListModule = require('./module/getCourseListModule.js');
let runExamModule = require('./module/runExamModule.js');
let endModule = require('./module/endModule.js');

// ========= 任务开始 ====  =====
async function gkrjExam(taskObj, taskOptions) {
    if (!process.env.HAS_PERMISSION) {
        return;
    }

    // 临时在这里指定，正常应该在服务器指定，服务器在跑课程
    taskObj.schoolurl = 'http://test2024b.souchn.cn/';

    // taskOptions.isHeadless = false;

    let infoLogger = pageTools.getExamInfoLogger(taskObj.id, false);
    let globalStore = {};
    let { mainPage, browser } = await getMainPage(taskOptions);
    await mainPage.goto(taskObj.schoolurl, { waitUntil: 'networkidle0' });

    try {
        // 一、登录
        await signinModule(infoLogger, mainPage, globalStore, taskObj);


        // 二、获取课程列表
        let courseList = await getCourseListModule(infoLogger, mainPage,globalStore, taskObj);

        if (!taskObj.others.includes('跳过考试')) {
            // 三、开始考试
            await runExamModule(infoLogger, mainPage, globalStore, taskObj, courseList);
        }

        // 五、收尾工作
        let finalResult = await endModule(mainPage,infoLogger,taskObj);
        await browser.close();
        return { finalResult, warningMessage: '' };
    } catch (error) {
        await browser.close();
        browser = null;
        throw error;
    } finally {
        if (browser) {
            await browser.close();
            browser = null;
        }
    }
}

module.exports = gkrjExam;