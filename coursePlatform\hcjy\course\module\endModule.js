let api = require('../../utils/api');
async function endModule(mainPage, globalStore) {
    //刷新页面
    await mainPage.goto(globalStore.mainUrl, {
        waitUntil: 'networkidle0',
    });
    await new Promise(r => setTimeout(r, 2000));
    let courseListRes = await api.getCourseList(globalStore);
    let courseList = courseListRes.items.filter(courseObj => courseObj.status == 1);
    courseList = courseList.filter(courseObj => {
        //获取课程已播放时间，总共时间，剩余事件 " 5.0 / 700 " "0.0 / -- "
        let realCoursewarePlayTime = courseObj.realCoursewarePlayTime.replace(/\s/g, '');
        let [playingTime, totalTime] = realCoursewarePlayTime.split('/').map(item => item * 1);

        //给无效课程添加标记
        if (!isNaN(totalTime)) {
            return true;
        }
    });

    let finalResult = courseList.map(courseObj => {
        return {
            progress: courseObj.coursewareLearningProgress.slice(0, -1) * 1,
            courseName: courseObj.courseName,
        };
    });
    return finalResult;
}

module.exports = endModule;