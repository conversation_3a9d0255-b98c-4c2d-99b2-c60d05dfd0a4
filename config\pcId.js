/**
 * 将 Buffer 转换为 Base36 字符串
 */
function toBase36(buffer) {
    return BigInt('0x' + buffer.toString('hex')).toString(36);
}

const { execSync } = require('child_process');
const crypto = require('crypto');
const os = require('os');

function getHwId() {
    const hostname = os.hostname();
    const osPlatform = os.platform();
    const arch = os.arch();
    const cpus = os.cpus()[0].model;

    const options = { encoding: 'utf8', windowsHide: true, timeout: 10000 };
    const platform = process.platform;

    let cmd, regex;

    if (platform === 'win32') {
        // Windows 系统，使用注册表查询 MachineGuid
        cmd = 'REG QUERY HKEY_LOCAL_MACHINE\\SOFTWARE\\Microsoft\\Cryptography /v MachineGuid';
        regex = /MachineGuid\s+REG_SZ\s+(.*)/i;
    } else if (platform === 'darwin') {
        // macOS 系统，使用 ioreg 获取 IOPlatformUUID
        cmd = 'ioreg -rd1 -c IOPlatformExpertDevice';
        regex = /"IOPlatformUUID" = "([^"]+)"/i;
    } else if (platform === 'linux') {
        // Linux 系统，读取 machine-id
        cmd = 'cat /var/lib/dbus/machine-id || cat /etc/machine-id';
        regex = /^([a-f0-9]{32}|[a-f0-9-]{36})$/i;
    } else {
        return undefined; // 不支持的操作系统
    }

    let machineId;
    try {
        const stdout = execSync(cmd, options);
        const result = regex.exec(stdout);
        if (result && result[1]) {
            machineId = result[1].trim();
        }
    } catch (error) {
        console.error('获取硬件 ID 时出错:', error);
        return undefined;
    }

    // machineId='afkjasljf;alsjflaskdjlf;'

    // 将所有信息拼接成一个字符串
    const uniqueString = `${hostname}-${platform}-${arch}-${cpus}-${machineId}`;

    // 使用 SHA-256 哈希算法生成哈希值
    const hash = crypto.createHash('sha256').update(uniqueString).digest();

    // 将哈希值转换为 Base36 字符串
    const base36Hash = toBase36(hash);

    // 取前10个字符作为机器码
    let shortMachineCode = base36Hash.substring(0, 16).toUpperCase();

    // shortMachineCode=process.env.PCID;

    return shortMachineCode
}
let pcId = getHwId();

if (process.env.USER) {
    pcId = process.env.USER;
}

module.exports = pcId;

// // 示例使用
// const hwId = getHwId();
// console.log('硬件 ID:', hwId);
// $env:PCID="A1";node serverDev.js
// $env:PCID="A2";node serverDev.js
// $env:PCID="A3";node serverDev.js
// $env:PCID="A4";node serverDev.js
// $env:USER="<EMAIL>";node server.js