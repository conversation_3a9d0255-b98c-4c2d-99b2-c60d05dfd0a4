module.exports = {
  printWidth: 160, //一行的字符数，如果超过会进行换行，默认为80
  tabWidth: 4, // 一个tab代表几个空格数，默认为2
  useTabs: false, // 是否使用tab进行缩进，默认为false，表示用空格进行缩减
  semi: true, // 行位是否使用分号，默认为true
  singleQuote: true, // 字符串是否使用单引号，默认为false，使用双引号
  trailingComma: "es5", // 是否使用尾逗号，有三个可选值"<none|es5|all>"
  bracketSpacing: true, // 对象大括号直接是否有空格，默认为true，效果：{ foo: bar }
  jsxBracketSameLine: false, // jsx > 是否另起一行
  arrowParens: "avoid", // (x) => {} 是否要有小括号
  endOfLine: "lf", // 换行符使用lf
  quoteProps: "as-needed", //对象的key是否用引号引起来
  jsxSingleQuote: false, // jsx属性是否使用单引号
  htmlWhitespaceSensitivity: "css", // 指定HTML文件的全局空白区域敏感度
  proseWrap: "preserve", // 是否要换行
  embeddedLanguageFormatting: "auto", // 是否格式化嵌入式代码
  requirePragma: false, // 是否需要写注释来决定是否格式化代码
  insertPragma: false, // 是否要注释来决定是否格式化代码
};
