let { encryptData, decryptData, decodeRequestData, encodeRequestData } = require('../../utils/aesF.js');
let api = require('../../utils/api.js');
let pageTools = require('../../../utils/pageTools.js');

async function submitExamModule(globalStore,infoLogger){
    let {examModuleDate,examUrl,userInfoVo}=globalStore
    //获取试卷的examId
    examModuleRes = await api.examInfo(examModuleDate, examUrl);
    examModuleStr = decryptData(examModuleRes.data);
    examModuleArr = JSON.parse(examModuleStr);
    examModuleObj = examModuleArr[0];

    // 等待20-35分钟，每分钟执行一次await new Promise((r) => setTimeout(r, 60000));
    let submitExamTime=pageTools.getRandomInt(15,30)
    for (let i = 0; i < submitExamTime; i++) {
        await infoLogger(`等待${submitExamTime - i}分钟，后开始交卷`);
        await new Promise(r => setTimeout(r, 60000));
    }

    //时间到，向服务器提交交卷请求
    let submitExamObj = {
        user_info: JSON.stringify(userInfoVo),
        exam_id: examModuleObj.examId,
        exam_score_id: examModuleObj.scoreId,
    };
    let submitExamRes = await api.examSubmitAction(encodeRequestData(submitExamObj));

    //处理服务器返回结果
    if (submitExamRes && submitExamRes.code == 1000) {
        await infoLogger('交卷成功', 'green');
    } else {
        await infoLogger(`交卷失败，${JSON.stringify(submitExamRes)}`, 'red');
        throw new Error('交卷失败'); //结束进程池任务
    }

    //交卷后要等待2分钟，等待批阅完成
    await infoLogger(`等待两分钟，等批阅完成，然后再获取分数`);
    await new Promise(r => setTimeout(r, 120000));
}

module.exports = submitExamModule;
