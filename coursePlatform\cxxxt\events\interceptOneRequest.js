//拦截验证码
exports.validateCodeInterception= async mainPage => {
	mainPage.on('requestfinished', async request => {
		//拦截验证码
		let url = request.url();
        
		if ( 
			url.includes('https://passport2.chaoxing.com/num/code')
		) {
			//处理响应体
			const response = await request.response();
			const contentType = response.headers()['content-type'];

			//响应体默认值
			let responseData = await response.text();

			//如果响应体是json
			if (contentType && contentType.includes('application/json')) {
				responseData = await response.json();
			}

			//如果响应体是图片
			if (contentType && contentType.includes('image')) {
				responseData = await response.buffer();
			}
			mainPage.apiInfo.validateCode = {
				request: {
					method: request.method(),
					url: request.url(),
					headers: request.headers(),
					postData: request.postData(),
					resourceType: request.resourceType(),
				},
				response: responseData,
			};
			mainPage.resolve.validateCodeResolve();
		}

        //拦截课程信息
        if (
			url.includes('https://mooc1.chaoxing.com/mooc-ans/multimedia/log/a')
		) {
			//处理响应体
			const response = await request.response();
			const contentType = response.headers()['content-type'];

			//响应体默认值
			let responseData = ''

			//如果响应体是json
			if (contentType && contentType.includes('application/json')) {
				responseData = await response.json();
			}

            //获取API数据
			mainPage.apiInfo.cell = {
				request: {
					method: request.method(),
					url: request.url(),
					headers: request.headers(),
					postData: request.postData(),
					resourceType: request.resourceType(),
				},
				response: responseData,
			};
            //更改promise状态
			mainPage.resolve.cellResolve();
		}

        //拦截  保存作业
           if (
			// url.includes('http://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNew?')
			url.includes('https://mooc1.chaoxing.com/mooc-ans/work/addStudentWorkNew')
		) {
			//处理响应体
			const response = await request.response();
			const contentType = response.headers()['content-type'];

			//响应体默认值
			let responseData = ''

			//如果响应体是json
			if (contentType && contentType.includes('application/json')) {
				responseData = await response.json();
			}

            //获取API数据
			mainPage.apiInfo.assignment = {
				request: {
					method: request.method(),
					url: request.url(),
					headers: request.headers(),
					postData: request.postData(),
					resourceType: request.resourceType(),
				},
				response: responseData,
			};
            //更改promise状态
			mainPage.resolve.assignmentResolve();
		}
	});
};
