let api = require('../../../utils/api.js');

async function main(mainPage, infoLogger) {
    // //获取图片base64
    // let validateDivHandle = await mainPage.$('div.geetest_bg');
    // let base64Image = await validateDivHandle.screenshot({ type: 'jpeg', encoding: 'base64' });

    // 获取背景图片和目标图片的base64
    let { bgcImageUrl, targetImageUrl } = await mainPage.evaluate(() => {
        // 背景图片
        let bgcTag = document.querySelector('div.geetest_bg');
        let bgcImageUrl = bgcTag.style.backgroundImage;
        bgcImageUrl = bgcImageUrl.slice(5, -2);

        // eetest_slice_bg
        let targetTag = document.querySelector('div.geetest_slice_bg');
        let targetImageUrl = targetTag.style.backgroundImage;
        targetImageUrl = targetImageUrl.slice(5, -2);
        return { bgcImageUrl, targetImageUrl };
    });
    let bgcImageBase64 = await api.getImageBase64(bgcImageUrl);
    let targetImageBase64 = await api.getImageBase64(targetImageUrl);

    let gapPositionX;

    try {
        //获取图片缺口坐标（自己部署）
        let myPositionRes = await api.mySlideValidate(bgcImageBase64, targetImageBase64);
        gapPositionX = myPositionRes.result.target[0] + 10;
    } catch (error) {
        await infoLogger(`方式一识别失败，进行方式二识别`, 'red');
        //获取图片缺口坐标（第三方API）
        let gapPositionRes = await api.slideValidate(bgcImageBase64);
        if (gapPositionRes.code !== 0) {
            infoLogger('滑动验证码图片识别失败', 'red');
            throw Error('滑动验证码图片识别失败');
        }
        gapPositionX = gapPositionRes.data.recognition.split(',')[0];
    }

    // 获取滑块的定位和尺寸
    let sliderHandle = await mainPage.$('div.geetest_btn');
    let boundingBox = await sliderHandle.boundingBox();

    // 计算滑块的起始位置和目标位置
    let startX = boundingBox.x + boundingBox.width / 2;
    let startY = boundingBox.y + boundingBox.height / 2;
    let endX = startX + gapPositionX * 1 - 9; // 目标位置的X坐标，表示滑动的距离
    endX = parseInt(endX);
    startX = parseInt(startX);
    startY = parseInt(startY);

    // 模拟鼠标拖动滑块
    await mainPage.mouse.move(startX, startY);
    await mainPage.mouse.down();
    await mainPage.mouse.move(endX, startY, { steps: 20 }); // 增加拖动的平滑度
    await new Promise(r => setTimeout(r, 500));
    await mainPage.mouse.up();
    await new Promise(r => setTimeout(r, 5000));
}

async function handleSlideValidate(mainPage, infoLogger) {
    //等待图片加载完毕，这里用的是无脑等待3秒
    // await new Promise(r => setTimeout(r, 3000));
    for (let i = 0; i < 10; i++) {
        await new Promise(r => setTimeout(r, 5000));
        let validateDivHandle = await mainPage.$('div.geetest_bg');
        if (validateDivHandle) {
            await main(mainPage, infoLogger);
        } else {
            return;
        }
        infoLogger('滑动验证码图片识别失败，进行重试', 'gray');
    }
    throw new Error('10次验证码识别都失败了');
}

module.exports = handleSlideValidate;
