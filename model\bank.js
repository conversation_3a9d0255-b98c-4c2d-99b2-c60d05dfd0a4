const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('bank', {
    id: {
      type: DataTypes.STRING(255),
      allowNull: false,
      primaryKey: true
    },
    content: {
      type: DataTypes.STRING(5000),
      allowNull: true,
      comment: "问题内容 "
    },
    options: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "答案选项"
    },
    type: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "问题类型 0单选 1多选 2天空 3判断 4简答 5完型 6阅读"
    },
    answers: {
      type: DataTypes.STRING(5000),
      allowNull: true,
      comment: "答案 字符类型"
    },
    platform: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "平台"
    },
    answers_content: {
      type: DataTypes.TEXT,
      allowNull: true,
      comment: "答案 数组类型"
    },
    comment: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "备注"
    },
    course_name: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "课程名称"
    },
    add_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "添加时间，方便调试"
    }
  }, {
    sequelize,
    tableName: 'bank',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "id" },
        ]
      },
      {
        name: "content",
        type: "FULLTEXT",
        fields: [
          { name: "content" },
        ]
      },
    ]
  });
};
