let pageTools = require('../../../../utils/pageTools.js');
let api = require('../../../utils/api.js');
let handleQuestionList = require('./handleQuestionList.js');
async function doExam(infoLogger, mainPage, courseObj, examObj, examLogStr) {
    let examId = examObj.id;

    //跳转到答题页面 https://lms.ouchn.cn/exam/10000376501/subjects#/take`
    let subjectsUrl = `https://lms.ouchn.cn/exam/${examId}/subjects#/take`;
    await pageTools.gotoWithRetry(mainPage, subjectsUrl, { waitUntil: 'networkidle0' }, 3, infoLogger);
    await new Promise(r => setTimeout(r, 3 * 1000));
    let cookieStr = await pageTools.getPageCookies(mainPage);
    await infoLogger(`${examLogStr} 跳转到答题页面，开始答题：${subjectsUrl}`);

    // 获取storageRes
    let storageRes;
    try {
        storageRes = await api.getStorage(examId, cookieStr);
    } catch (error) {
        await infoLogger(`${examLogStr} 获取试卷信息出错`, 'red');
        return;
    }

    //获取题目数据 https://lms.ouchn.cn/api/exams/${examId}/distribute
    let questionListRes = await api.getQuestionList(examId, cookieStr);
    let questionList = questionListRes.subjects;
    if (!Array.isArray(questionList)) {
        await infoLogger(`${examLogStr} 题目列表为空`, 'red');
        return;
    }

    //查找答案，处理题目
    let subjects = await handleQuestionList(questionList, courseObj.name, infoLogger, examObj);
    if (!subjects || subjects.length == 0) {
        await infoLogger(`${examLogStr} 答案为空`, 'red');
        return;
    }
    let submitData = {
        exam_paper_instance_id: storageRes.instance_id,
        exam_submission_id: storageRes.id,
        subjects: subjects,
        reason: 'user',
    };
    await infoLogger(`${examLogStr} 30秒后提交答案`);
    await new Promise(r => setTimeout(r, 30 * 1000));

    // 提交答案
    // https://lms.ouchn.cn/api/exams/${examId}/submissions
    let submissionsRes = await api.submitExam(examId, cookieStr, submitData);
    if (!submissionsRes) {
        await infoLogger(`${examLogStr} 作业提交失败`);
        return;
    }
    // 等待10秒，不然成绩出不来
    await infoLogger(`${examLogStr} 作业提交成功，等待10秒后获取分数`);
    await new Promise(r => setTimeout(r, 10 * 1000));

    // 获取分数
    let lastSubmissionsRes = await api.getSubmissions(examId, cookieStr);
    let lastScore = lastSubmissionsRes.exam_score;

    await infoLogger(`${examLogStr}，最终分数:${lastScore}，等待60秒后进行下一个课件`, 'green');
    await new Promise(r => setTimeout(r, 60 * 1000));
}

module.exports = doExam;
