let api = require('../../../utils/api.js');

async function main(mainPage, infoLogger) {
    // 修改为直接获取验证图片本身
    let validateDivHandle = await mainPage.$('#eject');

    // 调试信息，查看元素是否存在
    let isElementVisible = await validateDivHandle.evaluate(el => {
        return {
            isVisible: el !== null && el.offsetWidth > 0 && el.offsetHeight > 0,
            width: el.offsetWidth,
            height: el.offsetHeight,
        };
    });
    // infoLogger(`验证元素状态: ${JSON.stringify(isElementVisible)}`, 'blue');

    // 使用Puppeteer推荐的直接获取base64方式，避免Buffer转换问题
    let bigImageBase64 = await validateDivHandle.screenshot({
        type: 'jpeg',
        encoding: 'base64',
    });

    //获取图片缺口坐标
    let gapPositionRes = await api.clickValidate(bigImageBase64);

    if (gapPositionRes.code !== 0) {
        infoLogger('获取验证码验证数据失败', 'red');
        throw Error('获取验证码验证数据失败');
    }

    // 解析所有坐标点
    let recognition = gapPositionRes.data.recognition;
    let coordinatesArray = recognition.split('|');
    // infoLogger(`获取到 ${coordinatesArray.length} 个点击坐标: ${recognition}`, 'green');

    // 获取验证图片的位置和大小，用于计算实际点击坐标
    let canvasBoundingBox = await validateDivHandle.boundingBox();
    // infoLogger(`验证区域位置: ${JSON.stringify(canvasBoundingBox)}`, 'blue');

    // 先在所有点上显示小红点，帮助调试 - 使用更可靠的方式
    await mainPage.evaluate(
        (coordinatesArray, canvasBBox) => {
            // 先删除任何现有的调试点
            document.querySelectorAll('.debug-dot').forEach(dot => dot.remove());

            // 创建容器，确保点在正确的位置
            const container = document.createElement('div');
            container.style.position = 'fixed';
            container.style.top = '0';
            container.style.left = '0';
            container.style.width = '100%';
            container.style.height = '100%';
            container.style.zIndex = '2147483647'; // 最大z-index
            container.style.pointerEvents = 'none';
            container.id = 'debug-dots-container';

            // 添加每个坐标点的红点
            coordinatesArray.forEach((coord, index) => {
                const [x, y] = coord.split(',').map(Number);

                // 创建更大、更明显的红点
                const dot = document.createElement('div');
                dot.className = 'debug-dot';
                dot.style.position = 'absolute';
                dot.style.width = '20px'; // 更大的点
                dot.style.height = '20px';
                dot.style.borderRadius = '50%';
                dot.style.backgroundColor = 'red';
                dot.style.border = '2px solid white';
                dot.style.left = canvasBBox.x + x - 10 + 'px'; // 调整位置，确保红点居中
                dot.style.top = canvasBBox.y + y - 10 + 'px';
                dot.style.color = 'white';
                dot.style.fontWeight = 'bold';
                dot.style.fontSize = '14px';
                dot.style.textAlign = 'center';
                dot.style.lineHeight = '20px';
                dot.innerText = index + 1;

                // 添加到容器
                container.appendChild(dot);

                // 调试 - 在控制台输出位置信息
            });

            // 将容器添加到body
            document.body.appendChild(container);

            return true; // 返回成功标志
        },
        coordinatesArray,
        canvasBoundingBox
    );

    // 依次点击每个坐标点
    for (let i = 0; i < coordinatesArray.length; i++) {
        let [x, y] = coordinatesArray[i].split(',').map(Number);

        // 计算在页面上的实际点击位置
        let clickX = canvasBoundingBox.x + x;
        let clickY = canvasBoundingBox.y + y;

        // infoLogger(`点击第 ${i + 1} 个坐标点: (${x}, ${y}) => 页面位置(${clickX}, ${clickY})`, 'blue');

        // 模拟鼠标点击
        await mainPage.mouse.move(clickX, clickY, { steps: 5 });
        await mainPage.mouse.down();
        await new Promise(r => setTimeout(r, 100)); // 短暂按下
        await mainPage.mouse.up();

        // 两次点击之间添加随机延迟，模拟人工操作
        if (i < coordinatesArray.length - 1) {
            let randomDelay = Math.floor(Math.random() * 300) + 300; // 300-600ms 随机延迟
            await new Promise(r => setTimeout(r, randomDelay));
        }
    }

    // 点击完成后等待验证结果
    await new Promise(r => setTimeout(r, 2000));

    // 清除调试用的小红点
    await mainPage.evaluate(() => {
        const container = document.getElementById('debug-dots-container');
        if (container) container.remove();
        document.querySelectorAll('.debug-dot').forEach(dot => dot.remove());
    });
}

async function handleSlideValidate(mainPage, infoLogger) {
    let maxCount = 5;
    for (let i = 0; i < maxCount; i++) {
        await new Promise(r => setTimeout(r, 5000));
        let validateDivHandle = await mainPage.$('#eject');
        if (validateDivHandle) {
            infoLogger(`进行第${i+1}次验证码验证`);
            await main(mainPage, infoLogger);
            continue;
        } else {
            return;
        }
        infoLogger('滑动验证码图片识别失败，进行重试', 'gray');
    }
    throw new Error(`${maxCount}次验证码识别都失败了`);
}

module.exports = handleSlideValidate;
