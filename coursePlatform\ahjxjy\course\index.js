// ========= 私有模块 =========
let pageTools = require('../../utils/pageTools.js');
let getMainPage = require('../../utils/getMainPage.js');

// 考试模块
let signinModule = require('./module/signinModule.js');
let getCourseListModule = require('./module/getCourseListModule.js');
let runCourseModule = require('./module/runCourseModule/index.js');
let endModule = require('./module/endModule.js');
let paymentModule = require('./module/paymentModule.js');

// ========= 任务开始 =========
async function ahjxjyCourse(taskObj, taskOptions) {
    // await new Promise((r) => setTimeout(r, 2*60*1000));
    // return [];
    // taskOptions.isHeadless = false;
    let infoLogger = pageTools.getCourseInfoLogger(taskObj.id, false);
    let globalStore = { taskObj };
    let { mainPage, browser } = await getMainPage(taskOptions);
    try {
        // await new Promise((r) => setTimeout(r, 20*1000));
        // return [];
        // 一、登录 schoolCode,cookieStr
        await signinModule(infoLogger, mainPage, globalStore, taskObj);

        // 查学费
        if (globalStore.taskObj.others.includes('查学费')) {
            let result = await paymentModule(infoLogger, globalStore, taskObj);
            return result;
        }

        // 二、获取课程列表 courseList
        let courseList = await getCourseListModule(infoLogger, globalStore, taskObj);
        if (courseList.length == 0) {
            await infoLogger('课程数量为0', 'red');
            await browser.close();
            browser = null;
            return {
                finalResult: [{ courseName: '课程数量为0', progress: 0 }],
                warningMessage: '',
            };
        }

        if (!taskObj.others.includes('跳过网课')) {
            // 三、开始刷课
            await runCourseModule(infoLogger, courseList, globalStore, taskObj);
        }

        // 五、收尾工作
        let result = await endModule(infoLogger, globalStore, taskObj);
        await browser.close();
        return result;
    } catch (error) {
        await browser.close();
        browser = null;
        throw error;
    } finally {
        if (browser) {
            await browser.close();
            browser = null;
        }
    }
}
module.exports = ahjxjyCourse;

//测试用
if (false) {
    let taskOptions = {
        isVideoEnabled: true, //视频
        isAssignmentEnabled: true, //作业
        isHeadless: 'new', //浏览器模式 false ,'new'
    };
    let taskObj = {
        username: '142730199701051573',
        password: '051573',
        schoolname: '安徽现代信息工程职业学院',
        runCourse: true,
        runAssignment: true,
        // id: "00622e79-6ad2-4188-959c-c75aef6d3ffb",
    };

    ahjxjyCourse(taskObj, taskOptions).then(
        val => console.log('success'),
        err => {
            console.log(err);
        }
    );
}
