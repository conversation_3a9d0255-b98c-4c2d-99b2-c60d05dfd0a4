async function endModule(globalStore, mainPage, infoLogger,taskObj) {
    //跳转到考试界面
    await mainPage.goto('https://mooc1-api.chaoxing.com/exam/test/examcode', {
        waitUntil: 'networkidle0',
    });

    // 跳转到 全部
    await mainPage.select('select[name="status"]', '-1');
    await new Promise(r => setTimeout(r, 2000));

    //获取考试列表
    courseList = await mainPage.evaluate(() => {
        function updateCourseStatus(courseObj) {
            // 确保格式正确
            if (!courseObj.startTime || !courseObj.endTime) {
                courseObj.startTime = courseObj.examDate.split('至')[0].trim();
                courseObj.endTime = courseObj.examDate.split('至')[1].trim();
            }

            // 获取当前时间
            let now = new Date();

            // 使用更安全的方式解析日期
            try {
                // 解析开始时间
                let startDate = new Date(courseObj.startTime);

                // 解析结束时间
                let endDate = new Date(courseObj.endTime);

                // 检查日期是否有效
                if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
                    // 如果日期解析失败，尝试更严格的格式解析
                    let parseCustomDate = function (dateStr) {
                        // 格式: '2025-02-01 00:00'
                        let parts = dateStr.split(' ');
                        if (parts.length !== 2) return null;

                        let dateParts = parts[0].split('-');
                        let timeParts = parts[1].split(':');

                        if (dateParts.length !== 3 || timeParts.length < 2) return null;

                        // 注意：月份从0开始，所以要减1
                        return new Date(Number(dateParts[0]), Number(dateParts[1]) - 1, Number(dateParts[2]), Number(timeParts[0]), Number(timeParts[1]));
                    };

                    startDate = parseCustomDate(courseObj.startTime);
                    endDate = parseCustomDate(courseObj.endTime);

                    if (!startDate || !endDate) {
                        // 如果还是解析失败，设置默认值
                        courseObj.isStart = null;
                        courseObj.isExpired = null;
                        return courseObj;
                    }
                }
                // 判断课程是否有效
                courseObj.isValid = now >= startDate && now <= endDate;
            } catch (error) {
                console.error('更新课程状态时出错:', error);
                // 出错时设置默认值
                courseObj.isStart = false;
                courseObj.isExpired = false;
            }

            return courseObj;
        }

        let courseList = [];
        let courseTrList = document.querySelectorAll('body > div.dr_box > div > div.dr_table > table > tbody > tr[tabindex]');
        let keyArr = ['courseIndex', 'courseName', 'examDate', 'examDuration', 'examState', 'examScore', 'examType'];
        courseTrList.forEach(courseTr => {
            let courseObj = {};
            let tdList = courseTr.querySelectorAll('td');
            //遍历tdList
            for (let i = 0; i < tdList.length - 1; i++) {
                let courseTd = tdList[i];
                let key = keyArr[i];
                courseObj[key] = courseTd.textContent.trim();
            }

            // 处理时间  "2025-02-01 00:00 至 2025-03-31 23:59"
            courseObj.startTime = courseObj.examDate.split('至')[0];
            courseObj.endTime = courseObj.examDate.split('至')[1];
            courseObj = updateCourseStatus(courseObj);

            //最后一个td特殊处理，分理处url和operation
            let lastTd = tdList[tdList.length - 1];
            let aTag = lastTd.querySelector('a'); //<a class="col_blue" href=>
            let onclickAttr = aTag.getAttribute('onclick'); //go('/exam=ans/mycourse/tran....')
            let examUrl = onclickAttr.match(/go\('(.*)'\)/)[1];
            courseObj.examUrl = location.origin + examUrl;
            courseObj.opration = aTag.textContent.trim();

            if (courseObj.isValid) {
                courseList.push(courseObj);
            }
        });

        return courseList;
    });

    // 根据课程名称过滤课程
    courseList = courseList.filter(courseObj => courseObj.courseName.includes(taskObj.coursename));

    let finalResult = courseList.map(courseObj => {
        return { courseName: courseObj.courseName, progress: courseObj.examScore };
    });

    return finalResult;
}

module.exports = endModule;