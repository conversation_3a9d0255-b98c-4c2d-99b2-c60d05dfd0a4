let api = require('../../utils/api.js');
let Model = require('../../../../config/sequelize.config.js');
let { encryptData, decryptData, decodeRequestData, encodeRequestData } = require('../../utils/aesF.js');

/**
 * 完成 URL 拼接操作。
 *
 * 如果传入的是相对路径，则会自动将其与基础 URL 拼接，返回完整的 URL；
 * 如果传入的是完整的 URL，则直接返回。
 *
 * @param {string} url - 需要处理的路径或完整的 URL。
 * @returns {string} 完整的 URL。
 *
 * @example
 * // 相对路径转换为完整 URL
 * completeUrl('/files2015/upload/student/data/current/1/121015.jpg');
 * // 返回 'https://jw.wencaischool.net/files2015/upload/student/data/current/1/121015.jpg'
 *
 * @example
 * // 完整 URL 原路返回
 * completeUrl('https://jw.wencaischool.net/files2015/upload/student/data/current/1/121015.jpg');
 * // 返回 'https://jw.wencaischool.net/files2015/upload/student/data/current/1/121015.jpg'
 */
function completeUrl(url) {
    const baseUrl = 'https://jw.wencaischool.net';

    // 检查是否为完整的 URL
    const isFullUrl = /^https?:\/\//i.test(url);

    if (isFullUrl) {
        // 如果已经是完整 URL，直接返回
        return url;
    } else {
        // 如果是相对路径，拼接 baseUrl 和 path
        return baseUrl + url;
    }
}

async function faceCompareModule(taskObj, globalStore, infoLogger) {
    let { examModuleObj, userInfoVo, desktopInfoRes } = globalStore;

    // 1.从服务器验证人脸识别结果
    let faceCheckPostObj = {
        user_info: JSON.stringify(userInfoVo),
        exam_type: 'ks',
        exam_id: examModuleObj.examId,
    };
    let faceCheckRes = await api.getFaceCheckResult(faceCheckPostObj);

    // console.log(faceCheckRes.code == 1000);
    // await new Promise(r=>setTimeout(r,1000000));

    // 2.如果没有进行人脸识别，进行人脸识别
    if (faceCheckRes.code != 1000) {
        await infoLogger('进行人脸识别');

        // // 获取人脸识别所需二维码
        // let getFaceCheckPicRes = await api.getFaceCheckPic(faceCheckPostObj);

        //准备发送API请求
        let desktopInfoDebugData = desktopInfoRes.debugData;
        let postdata = {
            school_code: desktopInfoDebugData.schoolCode, //
            grade_code: desktopInfoDebugData.gradeCode, //
            learning_user_id: desktopInfoDebugData.learningUserId, //
            exam_id: examModuleObj.examId,
            student_type: 'student',
            current_version: '131',
            app_release: 'wencaixuetang',
            phone_type: 'android',
            type: '1',
        };
        // console.log(postdata);
        let postdataStr = encodeRequestData(postdata);

        let { photoUrl } = userInfoVo;
        photoUrl = completeUrl(photoUrl);

        let picContent;
        let picRes;

        try {
            //从服务器获取 pic_content
            picRes = await Model.photo.findOne({
                where: {
                    username: taskObj.username,
                    platform: taskObj.platform,
                    school: taskObj.schoolname,
                },
            });

            if (picRes) {
                picRes = picRes.get();
                picContent = picRes.photo_base64;
            } else {
                picContent = await api.getOfficialFacePic(photoUrl);
            }
        } catch (error) {
            await infoLogger('获取人脸识别图片失败', 'red');
            throw new Error(error); //结束进程池任务
        }


        picContent = 'pic_content=' + encodeURIComponent(picContent);

        //拼接最后的请求体
        postdataStr = postdataStr + '&' + picContent;

        //老是失败，有可能没有点击promise 考试承诺
        //向服务器发送人脸识别请求
        try {
            faceCheckRes = await api.faceRecognitionExam(postdataStr,720,1560);
            // console.log(faceCheckRes);
            // await new Promise(r=>setTimeout(r,1000000));
        } catch (error) {
            await infoLogger('进行人脸识别，服务器出错', 'red');
            throw new Error(error); //结束进程池任务
        }
    }

    //3.判断人脸识别结果
    if (faceCheckRes.code == 1000) {
        await infoLogger('人脸识别成功', 'green');
    } else {
        await infoLogger('人脸识别失败', 'red');
        throw new Error(`人脸识别失败,${JSON.stringify(faceCheckRes)}`); //结束进程池任务
    }
}

module.exports = faceCompareModule;
