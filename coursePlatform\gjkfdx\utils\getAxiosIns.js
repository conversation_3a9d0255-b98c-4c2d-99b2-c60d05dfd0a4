let axios = require('axios');
let { HttpsProxyAgent } = require('https-proxy-agent');
let Model = require('../../../config/sequelize.config.js');

async function getAxiosIns(useProxy = true) {
    let axiosIns, proxyObj;
    if (useProxy) {
        const t = await Model.sequelize.transaction(); // 开始事务
        try {
            // 查询并加锁状态为 'idle' 的代理
            let proxyRes = await Model.proxy.findOne({
                where: {
                    state: 'idle',
                },
                lock: t.LOCK.UPDATE, // 行级锁
                transaction: t, // 使用事务
            });

            if (!proxyRes) {
                throw new Error('没有可用的代理ip');
            }

            proxyObj = proxyRes.get({ plain: true });

            global.useProxy = true;
            global.proxyObj = proxyObj;

            // 更新代理状态为 'busy'
            await Model.proxy.update(
                { state: 'busy' },
                {
                    where: {
                        ip: proxyObj.ip + '', // 根据代理的IP来更新
                        state: 'idle', // 确保状态仍为 idle
                    },
                    transaction: t, // 使用同一个事务
                }
            );

            await t.commit(); // 提交事务，释放锁
        } catch (error) {
            console.log('error', error);
            await t.rollback(); // 回滚事务，撤销所有操作
            throw new Error('更新状态出错');
        }

        let agent = new HttpsProxyAgent(`http://${proxyObj.username}:${proxyObj.password}@${proxyObj.ip}:${proxyObj.port}`);
        axiosIns = axios.create({
            httpsAgent: agent,
            timeout: 60 * 1000, // 超时
        });
    }

    if (!useProxy) {
        axiosIns = axios.create({
            timeout: 60 * 1000, // 超时
        });
    }

    // 添加响应拦截器
    axiosIns.interceptors.response.use(
        // 当相应状态码为200-299的时候走到这里
        res => res.data,
        // 其他情况都走到这里
        function (error) {
            // 请求已经发出，也收到了响应，但是状态码不在2xx范围内
            //  Axios 都会自动处理 302 重定向，不会抛出错误，也不会进入拦截器中 error 参数的处理逻辑
            if (error.response) {
                return Promise.reject(new Error(`响应状态码不在2xx范围内：status: ${error.response.status}, data: ${JSON.stringify(error.response.data)}`));
            }

            // 请求已发出但未收到响应
            if (error.request) {
                let errorMsg;
                switch (error.code) {
                    case 'ECONNABORTED':
                        errorMsg = '请求超时';
                        break;
                    case 'ENOTFOUND':
                        errorMsg = 'DNS 查找失败，无法找到服务器';
                        break;
                    case 'ECONNREFUSED':
                        errorMsg = '连接被拒绝';
                        break;
                    case 'EHOSTUNREACH':
                        errorMsg = '主机不可达';
                        break;
                    case 'ECONNRESET':
                        errorMsg = '连接被重置';
                        break;
                    case 'ETIMEDOUT':
                        errorMsg = '连接超时';
                        break;
                    default:
                        errorMsg = '网络错误';
                }
                return Promise.reject(new Error(errorMsg));
            }
        }
    );

    return axiosIns;
}

module.exports = getAxiosIns();

if (false) {
    (async () => {
        let axiosIns = await getAxiosIns(true);
        let config = {
            method: 'get',
            url: 'https://s21.ax1x.com/2024/12/10/pAH3J91.png',
            responseType: 'arraybuffer',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                Referer: 'http://www.chaoxing.com/',
                Accept: 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Accept-Encoding': 'gzip, deflate',
            },
        };
        let res = await axiosIns(config);
        let base64Image = Buffer.from(res, 'binary').toString('base64');
        console.log('base64Image', base64Image);
    })();
}