let axiosIns = require("../utils/axios");
async function processVerify(mainPage, infoLogger) {
    await infoLogger('调用processVerify处理拦截页面')
    await handleValidateCode(mainPage, infoLogger)
    //获取当前页面url
    let cuurentUrl = mainPage.url();
    if (cuurentUrl.includes("processVerify.ac")) {
        await infoLogger("处理失败，有可能验证码错误");
        await handleValidateCode(mainPage, infoLogger)
    }
}

async function handleValidateCode(mainPage, infoLogger){
    await infoLogger('调用handleValidateCode处理拦截页面')
    let cuurentUrl = mainPage.url();
    await new Promise((r) => setTimeout(r, 2000));
    await infoLogger('等待两秒后，调用handleValidateCode处理拦截页面')
    //#ccc
    //把div标签变成base64
    let validateDivHandle = await mainPage.$("#ccc");
    let imageBuffer = await validateDivHandle.screenshot({ type: "jpeg" });
    let base64Image = imageBuffer.toString("base64");

    // 创建 URLSearchParams 对象
    const params = new URLSearchParams();
    params.append("username", "mozhi0012");
    params.append("password", "qfQw4h4WzSKt");
    params.append("captchaData", base64Image);
    params.append("captchaType", "1000");
    let validateCodeRes = await axiosIns({
        url: "https://www.bingtop.com/ocr/upload/",
        method: "post",
        headers: {
            "Content-Type": "application/x-www-form-urlencoded",
        },
        data: params,
    });
    await infoLogger(`验证码为，${validateCodeRes.data.recognition},url为：${cuurentUrl}`);
    await mainPage.type("#ucode", validateCodeRes.data.recognition);
    await new Promise((resolve) => setTimeout(resolve, 3 * 24 * 60 * 1000)); //等待加载完成
    // await mainPage.click("body > form > input");
    await mainPage.evaluate(() => {
        let submitTag = document.querySelector("body > form > input");
        submitTag.click();
    });
    await new Promise((resolve) => setTimeout(resolve, 2000)); //等待加载完成
}

module.exports=processVerify