// ========= 私有模块 =========
let pageTools = require('../../utils/pageTools.js');
let getMainPage = require('../../utils/getMainPage.js');

let signinModule = require('./module/signinModule.js');
let getCourseListModule = require('./module/getCourseListModule.js');
let runCourseModule = require('./module/runCourseModule/index.js');
let runAssignmentModule = require('./module/runAssignmentModule.js');
let endModule = require('./module/endModule.js');

// ========= 任务开始 =========
async function ahjxjyCourse(taskObj, taskOptions) {
    // 开启窗口
    // taskOptions.isHeadless = false;

    // 初始化日志
    let infoLogger = pageTools.getCourseInfoLogger(taskObj.id, false);
    // 初始化全局对象
    let globalStore = { taskObj };
    // 初始化页面
    let { mainPage, browser } = await getMainPage(taskOptions);

    // 任务开始
    try {
        // 一、登录 schoolCode,cookieStr
        await signinModule(infoLogger, mainPage, globalStore);

        // // 二、获取课程列表 courseList
        let courseList = await getCourseListModule(infoLogger, mainPage, globalStore);
        if (courseList.length == 0) {
            await infoLogger('课程数量为0', 'red');
            await browser.close();
            browser = null;
            return {
                finalResult: [{ courseName: '课程数量为0', progress: 0 }],
                warningMessage: '',
            };
        }

        // 三、开始刷课
        if (!taskObj.others.includes('跳过网课')) {
            await runCourseModule(infoLogger, mainPage, courseList, globalStore);
        }

        // 四、开始作业
        if (!taskObj.others.includes('跳过作业')) {
            await runAssignmentModule(infoLogger, mainPage, courseList, globalStore);
        }

        // 五、收尾工作
        let result = await endModule(infoLogger, mainPage, globalStore);
        await browser.close();
        browser = null;
        return result;
    } catch (error) {
        await browser.close();
        browser = null;
        throw error;
    } finally {
        if (browser) {
            await browser.close();
            browser = null;
        }
    }
}
module.exports = ahjxjyCourse;
