let mysqlConfig = require('./mysql.config.js');
const { Sequelize } = require('sequelize');

const sequelize = new Sequelize(mysqlConfig.database, mysqlConfig.username, mysqlConfig.password, {
    host: mysqlConfig.host,
    dialect: mysqlConfig.dialect,
    timezone: '+08:00', // 你的本地时区
    logging: false,
    pool: {
        max: 10, // 连接池中允许的最大连接数量
        min: 0, // 连接池中允许的最小连接数量
        acquire: 30000, // 在放弃之前，连接池尝试获取连接的最长时间（毫秒）
        idle: 10000, // 一个连接在被释放之前，可以保持空闲状态的最长时间（毫秒）
    },
    retry: {
        max: 3, // 最大重试次数
        backoffBase: 1000, // 每次重试的基础间隔时间（毫秒）
        backoffExponent: 1.5, // 每次重试的间隔增加的指数系数
        timeout: 60000, // 最大重试时间
        match: [
            Sequelize.ConnectionError,
            Sequelize.ConnectionRefusedError,
            Sequelize.AccessDeniedError,
            Sequelize.HostNotFoundError,
            Sequelize.HostNotReachableError,
            Sequelize.InvalidConnectionError,
            'ETIMEDOUT',
        ],
    },
});
let initModels = require('../model/init-models');
let Model = initModels(sequelize);
Model.sequelize = sequelize;

module.exports = Model;

// 测试代码
if (false) {
    sequelize
        .authenticate()
        .then(() => {
            console.log('数据库连接成功');
        })
        .catch(err => {
            console.error('无法连接到数据库:', err);
        });
}
