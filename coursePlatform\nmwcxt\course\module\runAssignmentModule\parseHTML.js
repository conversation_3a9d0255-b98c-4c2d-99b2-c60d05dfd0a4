let jsdom = require('jsdom');
let { JSDOM } = jsdom;
let { handleImgs } = require('../../../../solveQuestion/format.js');
let { decode } = require('html-entities');
let { trim } = require('../../../utils/tools.js');

// 结合 handleImgs 和 trim，处理题目内容
function handleContent(str) {
    let res = trim(handleImgs(str)).slice(0, 3000);
    // res = convertHttpToHttps(res);
    res = decode(res);
    return res;
}

/**
 * 获取 <td> 下直接子节点的文本和 <img> src
 * @param {HTMLTableCellElement} td - 需要处理的 <td> DOM 节点
 * @returns {string}
 */
function getTextAndImagesFromTd(td) {
    let result = '';

    // 遍历 td 的所有子节点，但不进入下一层
    td.childNodes.forEach(child => {
        // 1. 文本节点（nodeType Node.TEXT_NODE === 3）
        if (child.nodeType === 3) {
            const textContent = child.textContent.trim();
            if (textContent) {
                result += trim(textContent);
            }
        }
        // 2. 元素节点（nodeType Node.ELEMENT_NODE === 1）
        else if (child.nodeType === 1) {
            // 只需要判断是不是 img
            if (child.tagName.toLowerCase() === 'img') {
                const src = child.getAttribute('src') || '';
                // 需求里要输出 [url]xxx[/url] 这样的格式
                result += `[URL]${src}[/URL]`;
            }
            // 如果是其他标签（div、table等），直接跳过
        }
    });
    return result;
}

function parseHTML(html) {
    //初始化DOM环境
    let dom = new JSDOM(html);
    let document = dom.window.document;
    // 用于存储最终结果的数组
    let result = [];

    //给tbody增加id属性，方便获取tr
    let tbodyRoot = document.querySelector('tbody');
    tbodyRoot.setAttribute('id', 'tbodyRoot');

    //获取第一层<tr>，多个。一个tr就是一个完整的题目
    let trs = document.querySelectorAll('#tbodyRoot>tr');
    if (trs.length == 0) throw new Error('解析html题目出错1');

    // 取出所有的tr
    trs.forEach((tr, index) => {
        //给tr增加id属性
        let trId = `trRoot${index}`;
        tr.id = trId;

        // 用tr去一个个匹配trTypeObj的查询字符串
        let trTypeObj;
        for (let i = 0; i < trTypeArr.length; i++) {
            let item = trTypeArr[i];
            // 通过tr+查询特征值来判断是否符合 #trRoot1>td>table>tbody>tr>td>table[isitem="1"]'
            let query = `#${trId}` + '>' + item.query;
            let newTr = document.querySelector(query);
            // 如果可以匹配，就调用函数进行解析，所以这里要保证选择器的唯一性
            if (document.querySelector(query)) {
                trTypeObj = item;
                break;
            }
        }

        // 如果找到对应的模块，就进行解析，找不到就自动跳过
        if (trTypeObj) {
            // console.log(trTypeObj.type);

            // 调用解析模块中的解析函数对tr进行解析
            let questionObj = trTypeObj.fn(tr);

            //特殊情况，报错
            if (questionObj.options.length == 1) {
                throw new Error('解析出错');
            }
            formatQuestion(questionObj);

            // console.log(questionObj);
            // console.log('------------------------');

            result.push(questionObj);
        }
    });

    return result;
}

function formatQuestion(questionObj) {
    //处理类型
    if (questionObj.type == 'radio' && questionObj.options.length == 2) {
        questionObj.type = '判断题';
    }
    if (questionObj.type == 'checkbox') {
        questionObj.type = '多选题';
    }
    if (questionObj.type == 'radio' && questionObj.options.length > 2) {
        questionObj.type = '单选题';
    }

    //处理选项
    switch (questionObj.type) {
        case '判断题':
            questionObj.options = ['A:对', 'B:错'];
            break;

        case '单选题':
        case '多选题':
            questionObj.options = questionObj.options.map(option => option.value + ':' + option.content);
            break;
    }

    //处理答案
    if (questionObj.type == '判断题' && questionObj.answers) {
        questionObj.answers = questionObj.answers == 'A' ? '对' : '错';
    }
}

//case3选择题试题
function case3SelectQuestion(tr) {
    let obj = { options: [] };
    let trId = tr.id;

    // 找到关键节点 itemTable
    let itemTableTag = tr.querySelector(`#${trId}>td>table>tbody>tr>td>table[isitem="1"]`);
    itemTableTag.id = 'itemTable';

    //题目内容
    let contentHTML = itemTableTag.querySelector('td').innerHTML;
    obj.content = handleContent(contentHTML);

    //题目类型和id
    let optionTable = tr.querySelector('#itemTable table[isitemoption="1"]');
    let firstInput = optionTable.querySelector('input');
    obj.id = firstInput.getAttribute('name');
    obj.type = firstInput.getAttribute('type');

    //选项
    let inputArr = optionTable.querySelectorAll('input');
    let labelArr = optionTable.querySelectorAll('label');
    for (let i = 0; i < inputArr.length; i++) {
        let option = {};
        option.value = inputArr[i].getAttribute('value');
        option.id = inputArr[i].getAttribute('id');
        // option.content = trim(handleImgs(labelArr[i].innerHTML));
        option.content = handleContent(labelArr[i].innerHTML);
        obj.options.push(option);
    }

    return obj;
}

//case4选择题试题
function case4selectQuestion(tr) {
    // 初始化问题对象
    let obj = { options: [] };

    let trId = tr.id;
    let optionsTable = tr.querySelector(`#${trId} table[isitemoption="1"]`);

    //选项
    let optionInputs = optionsTable.querySelectorAll('input');
    let optionLabels = optionsTable.querySelectorAll('label');
    if (optionInputs.length > 0 && optionLabels.length > 0) {
        for (let j = 0; j < optionInputs.length; j++) {
            let option = {};
            option.id = optionInputs[j].getAttribute('id');
            option.value = optionInputs[j].getAttribute('value');
            // option.content = trim(handleImgs(optionLabels[j].innerHTML));
            option.content = handleContent(optionLabels[j].innerHTML);
            obj.options.push(option);
        }
    }

    //获取题目id和类型
    let firstInput = optionsTable.querySelector('table[isitemoption="1"] input');
    if (firstInput) {
        obj.id = firstInput.getAttribute('name');
        obj.type = firstInput.getAttribute('type');
    }

    //题目内容
    //删除所有script标签
    let scriptTags = tr.querySelectorAll(`#${trId}>td>table>tbody>tr>td>script`);
    scriptTags.forEach(scriptTag => scriptTag.remove());
    //删除所有display none标签 #qb-sougou-search
    let noneDivs = tr.querySelectorAll(`#${trId}>td>table>tbody>tr>td div[style*="display: none"]`);
    noneDivs.forEach(noneDiv => noneDiv.remove());

    //题目内容 这里题目div的位置有两种可能
    let contentDivs = tr.querySelectorAll(`#${trId}>td>table>tbody>tr>td>div`);
    if (contentDivs.length >= 2) {
        //删除带选项的div
        contentDivs.forEach(div => {
            if (div.querySelector('table[isitemoption="1"] input')) {
                div.remove();
            }
        });

        let questionTd = tr.querySelector(`#${trId}>td>table>tbody>tr>td:not([valign])`);
        // obj.content = trim(handleImgs(questionTd.innerHTML));
        obj.content = handleContent(questionTd.innerHTML);
    }
    if (contentDivs.length == 1) {
        let contentDiv = tr.querySelector(`#${trId}>td>table>tbody>tr>td>div`);

        let divs = contentDiv.querySelectorAll('div');
        divs.forEach(div => {
            if (div.querySelector('table[isitemoption="1"] input')) {
                div.remove();
            }
            if (div.querySelector('div[style]')) {
                div.remove();
            }
        });

        // obj.content = trim(handleImgs(contentDiv.innerHTML));
        obj.content = handleContent(contentDiv.innerHTML);
    }

    //处理类型
    if (obj.type == 'radio' && obj.options.length == 2) {
        obj.type = '判断题';
    }
    if (obj.type == 'checkbox') {
        obj.type = '多选题';
    }
    if (obj.type == 'radio' && obj.options.length > 2) {
        obj.type = '单选题';
    }

    return obj;
}

//case1选择题答案
function case1SelectAnswer(tr) {
    // 初始化问题对象
    let obj = { options: [] };

    let trId = tr.id;
    // let questionTable = tr.querySelector(`#${trId}>td>table[isitem="1"]`);

    // 获取两个关键的tr标签 题目内容tr 和选项tr
    let questionTrs = tr.querySelectorAll('table[isitem="1"]>tbody >tr');
    if (questionTrs.length != 2) return;
    let [contenTr, optionsTr] = questionTrs;
    contenTr.id = 'contenTr'; //为了后续方便选择，这里主动添加一个id
    optionsTr.id = 'optionsTr'; //为了后续方便选择，这里主动添加一个id

    // 根据题目内容tr contenTr 获取问题内容
    let contentTrHTML = contenTr.innerHTML;
    obj.content = handleContent(contentTrHTML);

    // 根据选项tr optionsTr处理题目选项
    let optionsTrs = tr.querySelectorAll('#optionsTr table[isitemoption="1"]>tbody>tr');
    //这里有两种情况
    if (optionsTrs.length == 1) {
        let optionsTr = optionsTrs[0];
        let inputs = optionsTr.querySelectorAll('input');
        let labels = optionsTr.querySelectorAll('label');
        for (let i = 0; i < inputs.length; i++) {
            obj.options.push({
                id: inputs[i].getAttribute('id'),
                value: inputs[i].getAttribute('value'),
                // content: trim(handleImgs(labels[i].innerHTML)),
                content: handleContent(labels[i].innerHTML),
            });
        }
    }

    if (optionsTrs.length > 1) {
        optionsTrs.forEach(optionsTr => {
            let input = optionsTr.querySelector('input');
            let label = optionsTr.querySelector('label');
            obj.options.push({
                id: input.getAttribute('id'),
                value: input.getAttribute('value'),
                // content: trim(handleImgs(label.innerHTML)),
                content: handleContent(label.innerHTML),
            });
        });
    }

    //获取问题答案
    let answerDiv = tr.querySelector('#optionsTr>td div[style="color: darkred; font-size: 10pt"]');
    if (!answerDiv) {
        answerDiv = tr.querySelector('#optionsTr>td div[style="color:darkred;font-size:10pt"]');
    }
    if (answerDiv) {
        obj.answers = answerDiv.innerHTML.trim().match(/\[参考答案：(.*)\]/)[1];
    }

    //获取问题id和type
    let firstOptionsInput = tr.querySelector('#optionsTr table[isitemoption="1"]>tbody>tr input');
    if (firstOptionsInput) {
        obj.id = firstOptionsInput.getAttribute('name');
        obj.type = firstOptionsInput.getAttribute('type');
    }

    return obj;
}

//case2选择题答案 有多种可能
function case2selectAnswer(tr) {
    // 初始化问题对象
    let obj = { options: [] };

    let trId = tr.id;

    // 特征值标签
    let uniqueTag = tr.querySelector(`#${trId}>td div>table[isitemoption="1"]`);

    // 特征值标签 获取 选项
    let optionInputs = uniqueTag.querySelectorAll('input');
    let optionLabels = uniqueTag.querySelectorAll('label');
    if (optionInputs.length > 0 && optionLabels.length > 0) {
        for (let j = 0; j < optionInputs.length; j++) {
            let option = {};
            option.id = optionInputs[j].getAttribute('id');
            option.value = optionInputs[j].getAttribute('value');
            // option.content = getTextAndImagesFromTd(optionLabels[j]);
            // let optionContent = optionLabels[j].innerHTML;
            option.content = handleContent(optionLabels[j].innerHTML);
            obj.options.push(option);
        }
    }

    //答案
    let answerDiv = tr.querySelector(`#${trId}>td div[style="color:darkred;font-size:10pt"]`);
    if (!answerDiv) {
        answerDiv = tr.querySelector(`#${trId}>td div[style="color: darkred; font-size: 10pt"]`);
    }
    let answers = answerDiv.innerHTML.trim();

    obj.answers = answers.match(/\[参考答案\s*：(.*)\]/)[1]; //[参考答案：B]  分值：5  [参考答案 ：B]  分值：5
    // answers.match(/\[参考答案：(.*)\]/)[1]

    //获取题目id和类型
    let firstInput = uniqueTag.querySelector('table[isitemoption="1"] input');
    if (firstInput) {
        obj.id = firstInput.getAttribute('name');
        obj.type = firstInput.getAttribute('type');
    }

    // 题目内容，有多种情况：有可能被div包裹 有可能被p包裹 被P包裹的目前都没有图片
    let styleTag = tr.querySelector(`#${trId}>td div[style="line-height:20px;font-size:10pt"]`);
    if (!styleTag) {
        styleTag = tr.querySelector(`#${trId}>td div[style="line-height: 20px; font-size: 10pt"]`);
    }
    if (styleTag) {
        // console.log('情况四 最外层被div[style="line-height:20px;font-size:10pt"]包裹')
        obj.content = getTextAndImagesFromTd(styleTag);
        return obj;
    }

    let classP = tr.querySelector(`#${trId}>td>p[class="MsoNormal"]`);
    if (classP) {
        // console.log('情况二 题目内容在p[class=MsoNormal]')
        obj.content = trim(classP.innerHTML);
        return obj;
    }

    let fontTag = tr.querySelector(`#${trId}>td>font`);
    if (fontTag) {
        // console.log('情况三 题目内容在 font')
        obj.content = trim(fontTag.innerHTML);
        return obj;
    }

    // console.log('情况一 题目内容在题干')
    let scriptTag = tr.querySelector(`#${trId}>td>script`);
    let contentTag = scriptTag.parentElement;
    obj.content = getTextAndImagesFromTd(contentTag);
    return obj;
}

//case7填空题试题
function case7fillQuestion(tr) {
    let obj = { options: [], type: '填空题' };

    let trId = tr.id;
    let input = tr.querySelector(`#${trId}>td>table>tbody>tr>td>div>input[id]`);
    obj.id = input.getAttribute('id');

    let contentDiv = input.parentElement;
    // obj.content = trim(handleImgs(contentDiv.innerHTML));
    obj.content = handleContent(contentDiv.innerHTML);

    return obj;
}

//case5填空题答案
function case5fillAnswer(tr) {
    let obj = { options: [], type: '填空题' };
    let trId = tr.id;
    let input = tr.querySelector(`#${trId}>td>div>input[id]`);
    let contentDiv = input.parentElement;

    //题目内容
    // obj.content = trim(handleImgs(contentDiv.innerHTML));
    obj.content = handleContent(contentDiv.innerHTML);

    //获取答案
    let answerFonts = contentDiv.querySelectorAll('nobr>font');
    let answerArr = [];
    answerFonts.forEach(answerFont => {
        let answers = answerFont.innerHTML.trim(); //[参考答案：切应力]
        answers = answers.match(/\[参考答案\s*：(.*)\]/)[1];
        answerArr.push(answers);
    });
    obj.answers = answerArr.join('|');

    //获取题目id,type
    obj.id = input.getAttribute('id');

    return obj;
}

//case6填空题试题
function case6fillQuestion(tr) {
    let obj = { options: [], type: '填空题' };

    let trId = tr.id;
    let input = tr.querySelector(`#${trId}>td>table>tbody>tr>td>table[isitem] input[id]`);
    obj.id = input.getAttribute('id');

    let contentDiv = input.parentElement;
    // obj.content = trim(handleImgs(contentDiv.innerHTML));
    obj.content = handleContent(contentDiv.innerHTML);

    return obj;
}

//case10填空题答案
function case10fillAnswer(tr) {
    let obj = { options: [], type: '填空题' };
    let trId = tr.id;
    let input = tr.querySelector(`#${trId}>td>table[isitem]>tbody>tr>td>input[id]`);
    let contentDiv = input.parentElement;

    //题目内容
    // obj.content = trim(handleImgs(contentDiv.innerHTML));
    obj.content = handleContent(contentDiv.innerHTML);

    //获取答案
    let answerFont = contentDiv.querySelector('nobr>font');
    let answers = answerFont.innerHTML.trim(); //[参考答案：切应力]
    obj.answers = answers.match(/\[参考答案\s*：(.*)\]/)[1];

    //获取题目id,type
    obj.id = input.getAttribute('id');

    return obj;
}

//case9简答题试题
function case9shortQuestion(tr) {
    let obj = { options: [], type: '简答题' };

    let trId = tr.id;
    let textarea = tr.querySelector(`#${trId}>td>table>tbody>tr>td>table[isitem="1"] textarea`);
    obj.id = textarea.getAttribute('name');

    let contentDiv = tr.querySelector(`#${trId}>td>table>tbody>tr>td>table[isitem="1"] tr[issubitem] td`);
    // obj.content = trim(handleImgs(contentDiv.innerHTML));
    obj.content = handleContent(contentDiv.innerHTML);

    return obj;
}

//case8简答题答案
function case8shortAnswer(tr) {
    let obj = { options: [], type: '简答题' };
    let trId = tr.id;
    let firstTr = tr.querySelector(`#${trId}>td>table[isitem='1']>tbody>tr[issubitem='1']`);

    //题目内容
    // obj.content = trim(handleImgs(firstTr.innerHTML));
    obj.content = handleContent(firstTr.innerHTML);

    let secondTr = firstTr.nextElementSibling;

    //获取题目id
    let firstDiv = secondTr.querySelector('td div:first-child');
    let idTd = firstDiv.querySelector('td[id]');
    obj.id = idTd.getAttribute('id');

    // 获取答案
    let sedcondDiv = secondTr.querySelector('td div:last-child');
    let answerTd = sedcondDiv.querySelector('td[id]');
    // obj.answers = trim(handleImgs(answerTd.innerHTML));
    obj.answers = handleContent(answerTd.innerHTML);

    return obj;
}

//case11简答题答案
function case11shortAnswer(tr) {
    let obj = { options: [], type: '简答题' };
    let trId = tr.id;
    let contentTag;
    let contentTags = tr.querySelectorAll(`#${trId}>td>div`);
    if (contentTags.length == 2) {
        contentTag = contentTags[0];
        //题目内容
        // obj.content = trim(handleImgs(contentTag.innerHTML));
        obj.content = handleContent(contentTag.innerHTML);
    }
    if (contentTags.length == 1) {
        contentTag = contentTags[0];
        // obj.content = trim(handleImgs(contentTag.childNodes[0].nodeValue.trim()));
        obj.content = handleContent(contentTag.childNodes[0].nodeValue.trim());
    }

    //获取题目id
    let idTag = tr.querySelector(`#${trId}>td>div>div>table[border]>tbody>tr>td[id]`);
    obj.id = idTag.getAttribute('id');

    // 获取答案
    // obj.answers = trim(handleImgs(idTag.innerHTML));
    obj.answers = handleContent(idTag.innerHTML);

    return obj;
}

// 解析模块
let trTypeArr = [
    // 选择题 第一组
    {
        type: 'case3选择题试题',
        query: "td>table>tbody>tr>td>table[isitem='1'] table[isitemoption]",
        fn: case3SelectQuestion,
    },
    {
        type: 'case1选择题答案',
        query: 'td>table[isitem="1"]>tbody>tr>td div>table[isitemoption="1"]',
        fn: case1SelectAnswer,
    },
    // 选择题 第二组
    {
        type: 'case4选择题试题',
        query: 'td>table>tbody>tr>td>div>div table[isitemoption]',
        fn: case4selectQuestion,
    },
    {
        type: 'case2选择题答案',
        query: "td div>div>table[isitemoption='1']",
        fn: case2selectAnswer,
    },
    // 填空题 第一组
    {
        type: 'case7填空题试题',
        query: 'td>table>tbody>tr>td>div>input[id]',
        fn: case7fillQuestion,
        case: '材料力学试题.html',
    },
    {
        type: 'case5填空题答案',
        query: 'td>div>input[id]',
        fn: case5fillAnswer,
        case: '材料力学答案.html',
    },
    // 填空题第二组
    {
        type: 'case6填空题试题',
        query: 'td>table>tbody>tr>td>table[isitem]>tbody>tr[issubitem]>td>input[id]',
        fn: case6fillQuestion,
        case: '桥梁工程试题.html',
    },
    {
        type: 'case10填空题答案',
        query: 'td>table[isitem]>tbody>tr>td>input[id]',
        fn: case10fillAnswer,
        case: '桥梁工程答案.html',
    },
    // 简答题 第一组
    {
        type: 'case9简答题试题',
        query: "td>table>tbody>tr>td>table[isitem='1']>tbody>tr>td>textarea",
        fn: case9shortQuestion,
        case: '形式与政策作业3试题.html',
    },
    {
        type: 'case8简答题答案',
        query: "td>table[isitem='1']>tbody>tr>td div>table>tbody>tr>td[id]",
        fn: case8shortAnswer,
        case: '形式与政策作业3答案.html',
    },
    // 简答题 第二组
    {
        type: 'case11简答题答案',
        query: 'td>div>div>table[border]>tbody>tr>td[id]',
        fn: case11shortAnswer,
        // case: '形式与政策作业3答案.html',
    },
];

module.exports = parseHTML;

// 测试
if (false) {
    let fs = require('fs');
    let path = require('path');

    // 读取parseHTML这个目录下所有的html文件内容
    let htmlFiles = fs.readdirSync(path.resolve('coursePlatform/nmwcxt/course/module/runAssignmentModule/parseHTML'));
    htmlFiles.forEach(file => {
        // 输出当前文件名称
        console.log(file);
        let str = fs.readFileSync(path.resolve('coursePlatform/nmwcxt/course/module/runAssignmentModule/parseHTML', file), 'utf-8');
        let resultArr = parseHTML(str);

        // console.log(resultArr);

        // resultArr.forEach((obj, index) => {
        //     // console.log(obj);
        //     if (obj.type == '多选题') {
        //         console.log(obj.options.length);
        //     }
        //     // console.log(index, '-----', obj.answers);
        //     // console.log(index, '-----', obj.options.length);
        //     // console.log('------------------------');
        // });
    });

    // // 综合测试
    // let str = fs.readFileSync(path.resolve('coursePlatform/nmwcxt/course/module/runAssignmentModule/parseHTML/综合测试.html'), 'utf-8');
    // let resultArr = parseHTML(str);
    // console.log(resultArr);
}
