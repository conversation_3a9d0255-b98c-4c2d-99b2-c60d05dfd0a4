let handleFaceCheck = require('./handleFaceCheck.js');
async function skipFacecheck(mainPage, courseLogStr,infoLogger) {
    // 进入课程页面的时候会提示 人脸识别，这里进行处理，进行认证也能直接进入课程
    // 注意：强行绕过会出现：学生课程中采集的人脸图像与本人档案图像匹配度低于50%”，教师可操作清除学习进度或取消学习成绩
    try {
        await mainPage.waitForSelector('body > div > div.popDiv.wid640.faceCollectQrPop.popClass', {
            timeout: 3000,
        });
        await infoLogger(`${courseLogStr}：开始进行人脸识别`);
        let faceRes = await handleFaceCheck(mainPage);
        await new Promise(resolve => setTimeout(resolve, 3000));
        // 通过oldenc.value判断是否需要人脸识别
        let oldenc = await mainPage.evaluate(() => {
            if(oldenc&&oldenc.value){
                return oldenc.value
            }
        });
        if (oldenc) {
            await infoLogger(`${courseLogStr}：人脸识别通过`,'green');
        } else {
            let currentUrl = await mainPage.url()
            await infoLogger(`${courseLogStr}：人脸识别未通过,${currentUrl}`,'red');
            throw new Error('人脸识别未通过');
        }
    } catch (error) {
        await infoLogger(`${courseLogStr}：无需人脸识别`);
    }
}
module.exports = skipFacecheck;