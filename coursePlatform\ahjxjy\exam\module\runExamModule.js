let { formatQuestion, handleAnswer, collectQuestion, handleExamAnswer } = require('../../utils/handleQuestion.js');
let api = require('../../utils/api.js');

async function runExamModule(infoLogger, globalStore, courseList) {
    let allowCount = 7;
    let allowCountState = true;

    // globalStore.taskObj.others:最大考试次数<7>跳过网课
    // 根据globalStore.taskObj.others的值，来决定allowCount的值
    if (globalStore.taskObj.others.includes('最大考试次数')) {
        allowCount = globalStore.taskObj.others.match(/最大考试次数<(\d+)>/)[1];
    }

    for (let i = 0; i < courseList.length; i++) {
        //取出当前的课程对象
        let courseObj = courseList[i];
        let courseLogStr = `[${i + 1}/${courseList.length}]${courseObj.courseName}`;

        async function submitExam() {
            //1.从服务器获取当前课程的状态（是否可以答题）
            let stateRes = await api.getExamStatus(globalStore, courseObj);
            let canModify = stateRes.state == 'readonly' ? false : true;
            await infoLogger(`${courseLogStr}：是否可答题：${canModify}`);

            //2.从服务器获取试题数据
            let loadSXSKRes = await api.getQuestion(globalStore, courseObj);
            if (loadSXSKRes.code != 1) {
                await infoLogger(`${courseLogStr}：从服务器获取试题失败,${JSON.stringify(loadSXSKRes)}`, 'red'); //记录日志
                return;
            }
            //扁平化试题 data:[{},{}]
            let examList = [];
            loadSXSKRes.data.forEach(item => {
                item.list.forEach(questionObj => {
                    let newObj = formatQuestion(questionObj, '安徽继续教育在线', courseObj);
                    examList.push(newObj);
                });
            });
            await infoLogger(`${courseLogStr}：已答题次数：${loadSXSKRes.submitCount}，当前分数：${loadSXSKRes.totalScore}，试卷题量：${examList.length}`);

            // 可以答题，会进入人脸识别界面
            if (canModify) {
                let submitCount = await handleExamAnswer(globalStore, courseObj, examList);

                await infoLogger(`${courseLogStr}：共回答${submitCount}道题目，60秒后交卷`); //记录日志
                //等待60秒钟
                await new Promise(resolve => setTimeout(resolve, 60000));
                // await new Promise(resolve => setTimeout(resolve, 3600*1000));

                //提交试卷
                let finishRes = await api.submitExam(globalStore, courseObj);
                let awaitTime = 60;
                for (let j = 0; j < 4; j++) {
                    if (finishRes.code == 1) break;
                    if (!finishRes.msg.includes('过于频繁')) {
                        await infoLogger(`${finishRes.msg}`, 'red'); //记录日志
                        break;
                    }
                    // 产生一个60-180的随机数
                    let randomTime = Math.floor(Math.random() * 120) + 60;
                    awaitTime += randomTime;
                    await infoLogger(`${courseLogStr}：提交过于频繁，等待${awaitTime}秒重新提交`, 'gray'); //记录日志
                    await new Promise(resolve => setTimeout(resolve, awaitTime * 1000)); //等待30秒秒钟
                    finishRes = await api.submitExam(globalStore, courseObj);
                    awaitTime = awaitTime * 2;
                }

                if (finishRes.code == 1) {
                    await infoLogger(`${courseLogStr}：试卷提交成功`); //记录日志
                    await new Promise(resolve => setTimeout(resolve, 2000));
                    await submitExam();
                } else {
                    await infoLogger(`${courseLogStr}：试卷提交失败`, 'red'); //记录日志
                }
            }

            // 不可以答题，直接进入试卷查看界面
            if (!canModify) {
                //递归函数结束条件：不可以答题，并且分数大于90
                if (loadSXSKRes.totalScore > 95) {
                    await infoLogger(`${courseLogStr}：考试已经完成。分数为：${loadSXSKRes.totalScore}，考试次数：${loadSXSKRes.submitCount}`, 'green'); //记录日志
                    return;
                }

                //如果分数小于90，就重新答题
                if (loadSXSKRes.totalScore <= 95) {
                    await infoLogger(`${courseLogStr}：分数${loadSXSKRes.totalScore}没有达到要求，将进行重新考试。`);

                    //收集试题
                    let collectCount = await collectQuestion(examList);
                    await infoLogger(`${courseLogStr}：共收集${collectCount}道题目。`);

                    if (globalStore.taskObj.others.includes('强制考试') && loadSXSKRes.submitCount < 9 && allowCountState) {
                        allowCount = loadSXSKRes.submitCount + 1;
                        allowCountState = false;
                        await infoLogger(`${courseLogStr}：强制修改最大考试次数为：${allowCount}`, 'orange');
                    }

                    //递归结束条件，答题次数，放到这里，是为了收集最后一次的题目
                    if (loadSXSKRes.submitCount >= allowCount) {
                        await infoLogger(`${courseLogStr}：答题次数超过5次，仍没有达到要求，当前分数为${loadSXSKRes.totalScore}`, 'red'); //记录日志
                        return;
                    }

                    //修改试卷状态
                    let rewriteSXSKRes = await api.rewriteExam(globalStore, courseObj);

                    if (rewriteSXSKRes.code === 1) {
                        await infoLogger(`${courseLogStr}：修改试卷状态为重新答题成功`); //记录日志
                        //重新答题
                        await submitExam();
                    } else {
                        await infoLogger(`${courseLogStr}：修改试卷状态为重新答题失败,${JSON.stringify(rewriteSXSKRes)}`, 'red');
                        return;
                    }
                }
            }
        }
        await submitExam();
    }
}

module.exports = runExamModule;
