let axiosIns = require('../../utils/axiosIns.js');
let crypto = require('../utils/crypto.js');
let nodeCrypto = require('crypto');

//验证码判断
exports.validateCode = base64Image => {
    return axiosIns.post('http://upload.chaojiying.net/Upload/Processing.php', {
        user: 'mozhi0012',
        pass: 'rVYBbAw8vDF5@V6',
        softid: 953643,
        codetype: 1004,
        file_base64: base64Image,
    });
};
if (false) {
    let success = { err_no: 0, pic_str: '8934' };
    let fail = { err_no: -10061, err_str: '不是有效的图片文件', pic_str: '' };
}

// 我的验证码
exports.myValidateCode = async (base64Image) => {
    let config = {
        method: 'post',
        url: 'https://ddddocr.mozhi0012.top/ocr/classification',
        data: {
            beta: true,
            image_base64: base64Image,
            png_fix: false,
            probability: false,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        result: 'Hga2',
    };
}

// 登录
exports.login = async (username, password, code, id) => {
    let config = {
        method: 'post',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-identity-service/api/account/login',
        headers: {
            'content-type': 'application/json',
        },
        data: {
            account: username,
            password: nodeCrypto.createHash('md5').update(password).digest('hex'),
            tenantCode: '',
            code: code,
            id: id,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        accessToken:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6IjNhMTc0YzUyLTgzMDgtZjM0ZC01OWFiLTVmZmM1MDQ5ZDczZiIsImh0dHA6Ly9zY2hlbWFzLnhtbHNvYXAub3JnL3dzLzIwMDUvMDUvaWRlbnRpdHkvY2xhaW1zL25hbWUiOiIzNDEyMjEyMDA0MDgyMzY2MDYiLCJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9zdXJuYW1lIjoiMzQxMjIxMjAwNDA4MjM2NjA2IiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvZ2l2ZW5uYW1lIjoi6Zm26LSd6LSdIiwidGVuYW50aWQiOiIiLCJ0b2tlbklkIjoiMDU5MjQ2ZWQtY2Q1Ny00ZTY5LWE5MzQtNjc4ZGY2OTJhNjdkIiwidHlwZSI6IjAiLCJodHRwOi8vc2NoZW1hcy5taWNyb3NvZnQuY29tL3dzLzIwMDgvMDYvaWRlbnRpdHkvY2xhaW1zL3JvbGUiOiI0OCIsInN1YiI6IjNhMTc0YzUyLTgzMDgtZjM0ZC01OWFiLTVmZmM1MDQ5ZDczZiIsImp0aSI6ImYwZjdmYmRkLTI3M2EtNGY4MC04NTliLWRiZmFiMDUyMDA4MiIsImlhdCI6MTc0NzY3MTI0OSwibmJmIjoxNzQ3NjcxMjQ5LCJleHAiOjE3NDc3MDcyNDksImlzcyI6Inl6bCIsImF1ZCI6ImFwaSJ9.z2vF2Jk2tENAvMQXeeE-FiUdOpLNlDGyOj0MlKRDM3A',
        encryptedAccessToken:
            'EzSvyJrLn2yJEQWS6ugKH+NMecCu0fCIpcC+NpP7/rRhrNSCnuBbnfZDkPm7Mk/ItIbww4h25LpTr+Q4AQ39e6JoRlJgDLt3YMQKAItnr7lhTPngQuHURYQ8TexAUrNZp/bA/Ghf/J+YlcHf7J5NJ5agPGigurLnPSo9WUsYHoWCBVgH+i9GFo49RHdsiV7iVTmCebND1ACu8y/nAhq3plHPeJP9nGuvnzVbaKpx9ANXBs4DTUmnSCTy5z4DHzLZmCJQ7xilM1wF2J5Lg6FR+L3P8pPhyNTYcmnjxQiA2iS28y/BwNTV3z6M0PcUQJCILv88HXB2nVp8mqajcICLjkLQtRw8agRR/QbEwB1uBXg+K0D/EeePS5BmLDP8rV/djlYQIHF5ae/guXEKw35iNcKsjeoEX4Rzxl2Cv2AMYgPqsXTzwwkWk3tUHQ9yfZH6H3w9zV0ZXvLYMzFhsLigaa1MiZW04axcKyAR43qn5/cjapzv2XlIkdXD7nE2f0PQrUw/+vOA4A9DciN/wkrz/lX6TIBIWeQGImD3GAv5vNp2Pxwt2UdcLpzNkpd7xAFfkk4dlUvi1C2QncWibg4A6ZwFhhtfHZtbHpREDo9ldtBPSxKLJSdbhudyM/R0ktzSLDGV2hfwxvwQTayIHhgT872JowFUAdC0nhGsiGLM08zaQhbOHKHnS4cMV+tiJNc5fY8umJF6j5jO4XNUBn7bcbTVCBTZNByxd0sEDEKDuN3pf0k8/TrnixzfK2Q4LbXQzAi5GPJ2LTuFKq7QOZFOyfvdP6d9TcJkjt+VjbRA8ffukezFvNpbiANM/dUXX5jDVZCIaJ5enBX6K+JvTdGBFWiVM3j7SSW3dyHuOgxqXPj/WcnrdFRoHEqFaq0D0tscjBCG4vn83JvUfIdzyVpjD6byGz2mBCco3yHHcMWeFEXARxsRqhGNT+krnIlsTR6l5ujc4DcusuyPYDAc0VScgIWFXIYt35no6gW+8veI4uxVfziNwReYVTYALcAeo8HwAh8I+c+lHXXp10cG7ZEqvoWMTkR0Hh0o6d/rz3cfUJjjCkJQ6Vxr5B0HzH/uf+GKisjpcOL5lEjUcFsQ1DlV+nfYrC0drG9ZdHaqrLkP92/kVcqTdR/pTo0wRyEF5SOCO54bcnp8e9RMufklUgDiBc3AqQpVKVnmVc8OPDCX46yxKNZtwwrcD+Cuaudt/3WEQOgm/Kj1rEOL0SHDk4+FRd6WJ6t7tqPFkEPLf9Pg8tJJ2r5mjGeCmTOeN7BjGDptGm+MJXpQKKjmKvbYRq41qA==',
        refreshToken:
            'EzSvyJrLn2yJEQWS6ugKH+NMecCu0fCIpcC+NpP7/rRhrNSCnuBbnfZDkPm7Mk/ItIbww4h25LpTr+Q4AQ39e6JoRlJgDLt3YMQKAItnr7lhTPngQuHURYQ8TexAUrNZp/bA/Ghf/J+YlcHf7J5NJ5agPGigurLnPSo9WUsYHoWCBVgH+i9GFo49RHdsiV7iVTmCebND1ACu8y/nAhq3plHPeJP9nGuvnzVbaKpx9AN4bqBSLO0yVt0OQNumf9ki5FbkQfd8ZfRYwFErE8ea+c2ydl4LnEjLyqch9X57aDxNsp/fzEdlWbLZYswGKhJnVxTslnIoc9PZeb1i49vafW41opoK0ZEq5GfzXwiWaYzrAi9eQ8iBE4PUVUZc0te1jvP9T8aiJpAKg/J2hAFvsjvOqE5e5s6fBrHZJa5QiFa9ThIsENg7K6b94w4DvwH8u1FHomUsiLOU83sAtdHa3Lt+ZeXWM5dyr3QdaZwgY7wSTtYJHm1zY1DdHNR59FRBkItVUw+xS7jMK/+YULW0Qrv+r+2dbsKAi5pMqQJG4bncqa9Sa50PCIn0i8RGCMxw/Rz2uRBkhTn7h3IMogZ6MRJWlL+MR4SKidy9hM+QP8opqMtnY9INJPRLLpl5zI8vMgSCGlRysamTXmly0ApQP3AT30tPVJG5Eag9Sros39AGyepwWXbw2xWbWg+TCwzy0gYIvruXCNjco+RmMYZBVA==',
        expireInSeconds: 36000,
        userId: '3a174c52-8308-f34d-59ab-5ffc5049d73f',
        name: '341221200408236606',
        tenantId: null,
        tenantCode: '',
    };
}

exports.currentUserType = async loginRes => {
    let config = {
        method: 'get',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-basic-data-service/api/common/current-user-type',
        headers: {
            authorization: `Bearer ${loginRes.encryptedAccessToken}`,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = [
        {
            userType: 4,
            userIdentity: [
                {
                    id: '3a174c4e-e45c-5776-53e2-111d33da74dc',
                    userId: '3a174c5c-1fa4-5016-f208-62ed0212e6db',
                    relevantId: '********-0000-0000-0000-********0000',
                    schoolName: '安徽开放大学',
                    shortPinyin: 'ahkfdx',
                    studentStatus: '2025年春季-工商管理(专升本-业余)-利辛县',
                },
            ],
        },
    ];
}

exports.getCodeImage = async () => {
    let config = {
        method: 'get',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-identity-service/api/account/codeimage',
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        id: 'fc420218-b608-481d-9b40-47e6cc50f09e',
        imageCode: 'data:image/jpeg;base64,iVBORw0NXHQV1L',
    };
}

// 获取token
exports.getToken = async key => {
    let config = {
        method: 'get',
        url: `https://jxjynew.ahjxjy.cn/v1/jxjy-system-service/api/user-info/login-info?key=${key}`,
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        accessToken:
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9uYW1laWRlbnRpZmllciI6IjNhMTc0YzUzLWMxNWUtNGFmYy0yNWVlLTdkZjkxNTAxMTQwYSIsImh0dHA6Ly9zY2hlbWFzLnhtbHNvYXAub3JnL3dzLzIwMDUvMDUvaWRlbnRpdHkvY2xhaW1zL25hbWUiOiIzNDE2MjEyMDA2MDMwODEzMzAiLCJodHRwOi8vc2NoZW1hcy54bWxzb2FwLm9yZy93cy8yMDA1LzA1L2lkZW50aXR5L2NsYWltcy9zdXJuYW1lIjoiMzQxNjIxMjAwNjAzMDgxMzMwIiwiaHR0cDovL3NjaGVtYXMueG1sc29hcC5vcmcvd3MvMjAwNS8wNS9pZGVudGl0eS9jbGFpbXMvZ2l2ZW5uYW1lIjoi546L5qKm6LGqIiwidGVuYW50aWQiOiIiLCJ0b2tlbklkIjoiZGZiNjc3ZWItZDY3Yi00ZTMyLWJiYzQtYzViYThkOTYwOGQ2IiwidHlwZSI6IjAiLCJodHRwOi8vc2NoZW1hcy5taWNyb3NvZnQuY29tL3dzLzIwMDgvMDYvaWRlbnRpdHkvY2xhaW1zL3JvbGUiOiI0OCIsInN1YiI6IjNhMTc0YzUzLWMxNWUtNGFmYy0yNWVlLTdkZjkxNTAxMTQwYSIsImp0aSI6ImE4ZGJhMDVmLWExMTgtNGI0Ni1hNDYxLTU3MTc1ZmE1MGQxYiIsImlhdCI6MTc0NzQ5MTEyNCwibmJmIjoxNzQ3NDkxMTI0LCJleHAiOjE3NDc1MjcxMjQsImlzcyI6Inl6bCIsImF1ZCI6ImFwaSJ9.ueKad9Ea7jFO7T_00XmaGq5_DNMX5MQQKWvxhZus9J0',
        encryptedAccessToken:
            'EzSvyJrLn2yJEQWS6ugKH+NMecCu0fCIpcC+NpP7/rRhrNSCnuBbnfZDkPm7Mk/ItIbww4h25LpTr+Q4AQ39e6JoRlJgDLt3YMQKAItnr7lhTPngQuHURYQ8TexAUrNZp/bA/Ghf/J+YlcHf7J5NJ5agPGigurLnPSo9WUsYHoWCBVgH+i9GFo49RHdsiV7icPKhduQQcIDDQqM7iCJyFO56VgCMQWdRzp1R+igpzf5YgGSg32YnzmpFCIc/Nk6mNdBkPmv0YCRt5IyitEhWaoTf8f0VsAP4InBKFrwIlFQoH127dwDbeABUtM6I4qDbLb7CZGndb5am0RypWcnE/TYoHkGp/k0yXk+H1bYFukHB8gSN3Y25Z3118Bw8hzf9UkvAZPwbOA6fx5ADC/9AnkhN5/hBM/qvUNsyodP48DUAyOh2Kd+E/AnOZwCAthxj1rfR+H6dnNcjZL9SjyzVq29LCi85dvkim+T3VHRJ1qD0Gvzw/E/yH3NQYEIczA+IYqSEyk90q0pDAMSZFA9p4p9JbrTrkRRBbPKmZ/d1oKEKmTmbC69PAjz1BCVUuHywmIbQu1sPYn66AbZcgnuk2ldH2uIdfmguzG8cg7R+UzjwvF5aN23TtNAGCD6RUNiIELJH6Yv4HF2oEyqED2tiSNmMkL3iG+QqFY2hLYirmsdUJGqgI3Mjtszq81cstOifk6rVAZwPHauxKxy0E8holxm4gnuI4b2+cPVNAnC4Li87twYLkBT8hZC7VTyidAowgUxVrkOwVcGGbFQEYQov+w/zfmoEqpD+uBw3S5GJjxPGLLkz2mWogoSACUGIfkm97gMZhb+VkBkUxyCMRiBL7WS4Cqbr2UoSSnGyM3sunxfaFaDLVmpp6AT8mNwgVvT95tsC8d7V/ZMCBtXj/yDVrcO+jWIa/WBY+Ta6dzJ6TvPtSqL6oKP6i00g/Jk2Xa3z4I+yxSF2iYGdrVO9o+udg7nJ7VooalEKxyyZxE6ArCDL+PeX0QZHhHO6i9lvITpoqO80nYiVD3mzSQD3bkijqdqVa2ZD6ehbaq77mBnS+Zqycp5KmPJRI1o2uug23F1X6gdtQxj4m8eAF5mM4M7iAGppqBLKHLhFs3COCpw5rvgDByd7aN/qqwLoT/WEsDcck+3wPMO9tLINJoydgsX1won4LbYV43do928QoUpBPRS15/PfYq/iVKhBLt0KTHslKgSMwKWs/CQBfWJc2s1TfIhv1zwTGt8gYG2DtA9ZDsXIQ0XHWuKgzaue4Ks0HjHAlsAn6ECsTBLbsGWXuHk8VQ==',
        refreshToken:
            'EzSvyJrLn2yJEQWS6ugKH+NMecCu0fCIpcC+NpP7/rRhrNSCnuBbnfZDkPm7Mk/ItIbww4h25LpTr+Q4AQ39e6JoRlJgDLt3YMQKAItnr7lhTPngQuHURYQ8TexAUrNZp/bA/Ghf/J+YlcHf7J5NJ5agPGigurLnPSo9WUsYHoWCBVgH+i9GFo49RHdsiV7icPKhduQQcIDDQqM7iCJyFO56VgCMQWdRzp1R+igpzf7NTDw9EIuuZ/f1nAaUgfqL+v8nyxiCLRzZ7/8SYc/ZFaFiOL51P0QGyHBNsNXwofDTgCSaYoQuNK/wBvqAkukynphsVYK9BgXeL48N02CiH7rffD5FHiDY01mvvv9Os8I/xAPq0o3KKDLlSjNxDd5cP+4zkdtATZt9T1CtfVBdAE8cMHoxqzfOYw9DcMCwM5ZWGhxUNm/QTNXtwlXKkvsGogPmIYLBcGHQsqMrKhXJDCjZS+4KeU2kJ831DsH8TKVI2FEHU69zvYANvsGV31OBLHCQKrRAsU4+YDUgGgm02JqHC7bSuNbZLU/yhVpM2wPZkFswgMBermUrAuvJHwBbn4gFyx8Le9JIYhQ0+tsYcst4EeSruyqV3A1xiMEur3b8wOWV4P7g1Z5f9yReqFp8P+zk32QaDyfNyMSdqteSC7e1xXDA9jTtcOxf9LEhxPIhseMqmRx1b23VF2NMLI/y0kQuARl5aY6pB9tRIw/X6w==',
        expireInSeconds: 36000,
        userId: '3a174c53-c15e-4afc-25ee-7df91501140a',
        name: '341621200603081330',
        tenantId: null,
        tenantCode: '',
        error: null,
        body: null,
        isError: false,
    };
}

// 获取课程列表
// https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/course-info/stu-my-course-list
exports.getCourseList = async storageObj => {
    let config = {
        method: 'get',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/course-info/stu-my-course-list',
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
        params: {
            Type: 1,
            SkipCount: 0,
            MaxResultCount: 15,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        totalCount: 8,
        items: [
            {
                courseLibraryId: '3a1755f1-e995-8b8e-8e56-dcc49962866b',
                studyCourseLibraryId: '3a1755fa-c4d8-6b4a-8200-16082aa2391e',
                courseName: '计算机应用基础',
                personId: '6c10440e-cbef-11ef-8810-d25f582012af',
                personName: '张月蓉',
                studyYear: 2025,
                studyTerm: 1,
                courseEndTime: '2025-06-30T00:00:00',
                coverUrl: 'https://huaweicloudobs.ahjxjy.cn/a52c7a6830fc748dbcb2b5be18bd1b4e.png',
                taskCount: 33,
                schedule: null, // 进度
                isPayment: true,
                id: '3a1755fa-c4e5-8820-f133-73eeb3e680fa',
            },
        ],
    };
}

// 获取课程信息
// https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/course-info/${courseId}
exports.getCourseInfo = async (storageObj, courseId) => {
    let config = {
        method: 'get',
        url: `https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/course-info/${courseId}`,
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        type: 1,
        courseName: '计算机应用基础',
        antiBrushType: 0,
        studyYear: 2025,
        studyTerm: 1,
        courseEndTime: '2025-07-15T00:00:00',
        courseIsEnd: false,
        videoUrl: null,
        resume: null,
        isRightCanPlay: 1,
        isAllowDownload: 0,
        isOpenDiscussion: 1,
        isAbleDrop: 0,
        coverUrl: 'https://huaweicloudobs.ahjxjy.cn/a52c7a6830fc748dbcb2b5be18bd1b4e.png',
        isPublish: 1,
        auditState: 1,
        recommendation: '',
        firstDirectoryId: '3a0daa1f-2887-8db1-df48-eefc9e275338',
        secondDirectoryId: '3a0daa20-727f-174c-b83f-a529562cca64',
        personName: '张月蓉',
        personImg: '',
        isPayment: true,
        id: '3a1755fa-c4e5-8820-f133-73eeb3e680fa',
    };
}

// 获取课程的所有课件
// https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/course-module/stu-design-list/${courseId}
exports.getCellList = async (storageObj, courseId) => {
    let config = {
        method: 'get',
        url: `https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/course-module/stu-design-list/${courseId}`,
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        directoryList: [
            {
                showName: '第一章',
                directoryKey: '第一章',
                directoryValue: 'hanZi',
                directoryName: '第{0}章',
                directoryLevel: 1,
                courseId: '********-0000-0000-0000-********0000',
                creationTime: '0001-01-01T00:00:00',
                id: '3a0daa1f-2887-8db1-df48-eefc9e275338',
            },
            {
                showName: '第一节',
                directoryKey: '第一节',
                directoryValue: 'hanZi',
                directoryName: '第{0}节',
                directoryLevel: 2,
                courseId: '********-0000-0000-0000-********0000',
                creationTime: '0001-01-01T00:00:00',
                id: '3a0daa20-727f-174c-b83f-a529562cca64',
            },
        ],
        list: [
            // 章节
            {
                id: '3a177643-6345-671f-61cd-5e4e2a682910',
                title: '导学与计算机基础知识',
                sort: 1,
                studyCount: 0,
                lessonList: [
                    // 小节
                    {
                        id: '3a177643-9d8a-6e1f-a1fb-e3e118653dae',
                        title: '导学',
                        sort: 1,
                        studyCount: 0,
                        cellList: [
                            // 课件
                            {
                                id: '3a177647-b1a1-120b-0c91-ecd1efb183e1',
                                title: '《计算机应用基础》课程教学大纲.doc',
                                nodePath: '3a177643-6345-671f-61cd-5e4e2a682910/3a177643-9d8a-6e1f-a1fb-e3e118653dae',
                                smallIcon: '/file-web/img/Word_24.png',
                                bigIcon: '/file-web/img/Word_54.png',
                                resType: 2, // 课件类型： 1：视频 2:文档 7：作业
                                extension: 'doc',
                                sort: 1,
                                relevantId: 'e9878fc4-703e-8d60-f816-7708387e19d4',
                                studyCount: 0, //学习进度
                                isLast: 0,
                                isAllowDownload: 0,
                            },
                            {
                                id: '3a17a558-aa0d-9fed-1e6b-607c655bb0f8',
                                title: '综合练习2',
                                nodePath: '3a17a558-3c2b-e0cf-6ef0-0399c2ebdf44/3a17a558-53eb-f797-8170-006cba84dc96',
                                smallIcon: null,
                                bigIcon: null,
                                resType: 7,
                                extension: null,
                                sort: 1,
                                relevantId: '3a17a558-aa0c-e90b-6379-87efb727565a',
                                studyCount: 0,
                                isLast: 0,
                                isAllowDownload: 0,
                            },
                        ],
                    },
                ],
            },
        ],
    };
}

// 获取视频课件信息
// https://jxjynew.ahjxjy.cn/v1/file-service/api/preview/${relevantId}/preview-resource
exports.getPreviewResource = async (storageObj, relevantId) => {
    let config = {
        method: 'post',
        url: `https://jxjynew.ahjxjy.cn/v1/file-service/api/preview/${relevantId}/preview-resource`,
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        thumbnail: 'https://huaweicloud.ahjxjy.cn/asset/27bf2e534a296f37cddb7f8bdd17f2b6/cover/Cover0.jpg',
        previewUrl:
            '[{"Name":"KA","Value":"https://huaweicloud.ahjxjy.cn/asset/27bf2e534a296f37cddb7f8bdd17f2b6/6f7e6494d08774e36b67eff87d612125.avi"},{"Name":"HD","Value":"https://huaweicloud.ahjxjy.cn/asset/27bf2e534a296f37cddb7f8bdd17f2b6/play_video/c484b1cce6228b501ec2cfb54f934a1b_0.mp4"},{"Name":"SD","Value":"https://huaweicloud.ahjxjy.cn/asset/27bf2e534a296f37cddb7f8bdd17f2b6/play_video/c484b1cce6228b501ec2cfb54f934a1b_1.mp4"}]',
        previewType: 2,
        videoDuration: null,
    };
}

// 获取当前课件信息
exports.studyingLenard = async (storageObj, courseId, cellId) => {
    let config = {
        method: 'post',
        url: `https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/study/studying-lenard`,
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
        data: {
            cellId: cellId,
            courseId: courseId,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        cells: {
            id: '3a177746-0d82-b407-fda4-5545d6894a9b',
            isStudy: 0,
            title: 'Word4-word中的表格.mp4',
            relevantId: '117b1736-e7b4-404e-6478-0eaf449abe12',
            resType: 1,
            videoProgress: 90,
            isRightCanPlay: 1,
            isAllowDownload: 0,
            lastTime: '0',
            sort: 0,
            questions: [],
        },
        nexts: null,
        prevNexts: null,
    };
}

// 教师记录信息，感觉是获取上次播放进度数据
exports.teacherRecord = async (storageObj, cellId, courseId) => {
    let config = {
        method: 'get',
        url: `https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/study/video-record?cellId=${cellId}&courseId=${courseId}`,
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
    };
    let res = await axiosIns(config);
    return crypto.decryptCourse(res);
};
if (false) {
    res = 'T8M7nihrZG1UOfa55mEW3A+nf1UYRAOm+61Eo/9sti50UNZCkXwRsi//yKrf0pP3';
    // 解密后
    res = { MaxRecord: '85', CurrentRecord: '85' };
}

// 修改视频进度
exports.recordVideoPosition = async (storageObj, cellId, courseId, position) => {
    let dataObj = {
        cellId: cellId,
        courseId: courseId,
        position: position,
    };
    let dataEnc = crypto.encryptCourse(dataObj);
    let config = {
        method: 'post',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/study/study-video-record',
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
        data: {
            data: dataEnc,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = { code: 'allow', isStudy: false };
    res = { code: 'nalmc', isStudy: false };
    res = { code: 'notallow', isStudy: false };
}

// 更改课程状态为已完成
exports.finishCell = async (storageObj, courseId, cellId) => {
    let dataObj = {
        courseId: courseId,
        cellId: cellId,
    };
    let dataStr = crypto.encryptCourse(dataObj);
    let config = {
        method: 'post',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/study/study-lenard',
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
            'Content-Type': 'application/json',
        },
        data: `{"data":"${dataStr}"}`,
    };
    // console.log('config', config);
    let res = await axiosIns(config);
    return res;
};
if (false) {
    let res = {
        code: 'allow',
        isStudy: true,
    };
}

// 获取题目列表（test作业）
exports.getTestQuestionList = async (storageObj, courseId, cellId) => {
    let config = {
        method: 'post',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/paper/load-test-record',
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
        data: {
            courseId: courseId,
            courseCellId: cellId,
        },
    };
    let res = await axiosIns(config);
    res.paperObj = crypto.decryptAssignment(res.paperJson);
    return res;
};
if (false) {
    res = {
        paperId: '3a18f9c4-46b4-f0a4-7d8d-d7b7686f2476',
        courseId: '3a175196-a62b-8de8-b0e5-03eb8e11092e',
        examDuration: null,
        submitCount: 0,
        score: null,
        state: 0,
        comment: null,
        paperState: 1,
        objectiveType: 1,
        isEnd: false,
        ruleId: '3a17a558-2095-6885-07d4-8e8968f8a31b',
        countDown: null,
        photosSeconds: 0,
        courseRule: {
            cutScreenNum: null,
            isUseCamera: 0,
            isAllowUpload: null,
            isShowPaper: null,
            isShowAnalysis: 0,
            isShowAnswers: 0,
            isShowCutScreen: 0,
            isFaceVerification: null,
            isForbidRightclick: 0,
            isForbidCutScreen: 0,
            isForbidCopypaste: 0,
            answerNum: 1,
            isQuestionDisorder: 0,
            isOptionDisorder: 0,
        },
        paperJson: '',
        paperList: null,
        photos: null,
    };
    // paperJson解密后
    res.paperJson = [
        {
            QuestionType: 1,
            QuestionTypeId: '3a1727b3-9394-d5dd-c46b-93906c290bef',
            QuestionTypeName: '单选题',
            IsSubjective: false,
            GetPaperList: [
                {
                    Id: '3a18f9c4-46b4-1c46-d86d-284c72861670',
                    CourseId: '********-0000-0000-0000-********0000',
                    Title: '(  )是党领导人民治理国家的基本方式。',
                    Content: '(  )是党领导人民治理国家的基本方式。',
                    Analysis: '略',
                    Remark: null,
                    Labels: null,
                    Useage: null,
                    StudentAnswer: '',
                    RightAnswer: '3',
                    TotalScore: 0,
                    QuestionType: 1,
                    QuestionTypeId: '3a1727b3-9394-d5dd-c46b-93906c290bef',
                    DifficultyLevel: null,
                    CorrectState: 0,
                    AnswerState: 0,
                    Score: 0,
                    CourseSubQuestionList: null,
                    CourseQuestionOptionList: [
                        {
                            Id: '3a17a4e6-7308-15c5-20f7-f35b251bb792',
                            Title: '以人民为中心',
                            RightAnswer: null,
                            Answer: null,
                            Content: '以人民为中心',
                            IsAnswer: 0,
                            Sort: 1,
                        },
                        {
                            Id: '3a17a4e6-7308-2a74-34d8-9ee6ddbe5a0a',
                            Title: '绿色发展',
                            RightAnswer: null,
                            Answer: null,
                            Content: '绿色发展',
                            IsAnswer: 0,
                            Sort: 2,
                        },
                    ],
                    LeftList: null,
                    RightList: null,
                    OptionsSortArray: null,
                },
            ],
        },
        {
            QuestionType: 12,
            QuestionTypeId: '3a173c0b-084b-fc25-93e1-77982ce23169',
            QuestionTypeName: '填空题',
            IsSubjective: true,
            GetPaperList: [
                {
                    Id: '3a19fb8a-f26d-e5a6-f0b0-a73275f7a821',
                    CourseId: '********-0000-0000-0000-********0000',
                    Title: '在switch语句中，每个语句标号所含保留字case后面的表达式必须是【填空1】&nbsp;。',
                    Content:
                        '在switch语句中，每个语句标号所含保留字case后面的表达式必须是<button type="button" contenteditable="false" class="btn_fill" style="margin-right:5px;">【填空1】</button>&nbsp;。',
                    Analysis: '',
                    Remark: null,
                    Labels: null,
                    Useage: null,
                    StudentAnswer: '',
                    RightAnswer: '整型',
                    TotalScore: 0,
                    QuestionType: 12,
                    QuestionTypeId: '3a173c0b-084b-fc25-93e1-77982ce23169',
                    DifficultyLevel: null,
                    CorrectState: 0,
                    AnswerState: 0,
                    Score: 0,
                    CourseSubQuestionList: null,
                    CourseQuestionOptionList: [
                        { Id: '3a17b379-ff0e-94f0-b01a-9ddcfaa45869', Title: '整型', RightAnswer: null, Answer: null, Content: '整型', IsAnswer: 1, Sort: 1 },
                    ],
                    LeftList: null,
                    RightList: null,
                    OptionsSortArray: null,
                },
            ],
        },
    ];
    // 答题之后
    res.paperJson = [
        {
            Id: '3a18f9c4-46b4-0f65-873b-8769162b849a',
            CourseId: '********-0000-0000-0000-********0000',
            Title: '建设社会主义文化强国、推动社会主义文化繁荣兴盛，关键在于（）。',
            Content: '建设社会主义文化强国、推动社会主义文化繁荣兴盛，关键在于（）。',
            Analysis: '',
            Remark: null,
            Labels: null,
            Useage: null,
            StudentAnswer: '5',
            RightAnswer: '',
            TotalScore: 0,
            QuestionType: 1,
            QuestionTypeId: '3a1727b3-9394-d5dd-c46b-93906c290bef',
            DifficultyLevel: null,
            CorrectState: 0,
            AnswerState: 1,
            Score: 0,
            CourseSubQuestionList: null,
            CourseQuestionOptionList: [
                {
                    Id: '3a17a4e6-7308-6626-eee8-25a45112c330',
                    Title: '坚定中国特色社会主义',
                    RightAnswer: '',
                    Answer: null,
                    Content: '坚定中国特色社会主义',
                    IsAnswer: null,
                    Sort: 1,
                },
                {
                    Id: '3a17a4e6-7308-5763-d070-0704e5805f6b',
                    Title: '文化自信',
                    RightAnswer: '',
                    Answer: null,
                    Content: '文化自信',
                    IsAnswer: null,
                    Sort: 2,
                },
                {
                    Id: '3a17a4e6-7308-361f-87d4-e4101eee67a2',
                    Title: '高质量发展',
                    RightAnswer: '',
                    Answer: null,
                    Content: '高质量发展',
                    IsAnswer: null,
                    Sort: 3,
                },
                {
                    Id: '3a17a4e6-7308-3939-ac63-52d12b010d4e',
                    Title: '提高综合国力',
                    RightAnswer: '',
                    Answer: null,
                    Content: '提高综合国力',
                    IsAnswer: null,
                    Sort: 4,
                },
                {
                    Id: '3a17a4e6-7308-3d05-da9a-96ca25835450',
                    Title: '贯彻新发展理念',
                    RightAnswer: '',
                    Answer: null,
                    Content: '贯彻新发展理念',
                    IsAnswer: null,
                    Sort: 5,
                },
            ],
            LeftList: null,
            RightList: null,
            OptionsSortArray: null,
        },
    ];
}
// 获取题目列表（assignment作业）
exports.getAssignmentQuestionList = async (storageObj, courseId, assignmentId) => {
    let config = {
        method: 'post',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/paper/load-assignment-record',
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
        data: {
            courseId: courseId,
            assignmentId: assignmentId,
        },
    };
    let res = await axiosIns(config);
    res.paperObj = crypto.decryptAssignment(res.paperJson);
    return res;
};
if (false) {
    res = {
        paperId: '3a18fd39-745f-c764-14b4-d0255c02002a',
        courseId: '3a175196-a62b-8de8-b0e5-03eb8e11092e',
        examDuration: 130021,
        submitCount: 1, // 提交次数
        score: 100, // 分数
        state: 2, // 作业状态 2:已提交
        comment: null,
        paperState: 2,
        objectiveType: 1,
        isEnd: false,
        ruleId: '3a17a559-465d-a1d0-e410-d0944661141c',
        countDown: null,
        photosSeconds: 0,
        courseRule: {
            cutScreenNum: null,
            isUseCamera: 0,
            isAllowUpload: null,
            isShowPaper: null,
            isShowAnalysis: 0,
            isShowAnswers: 1,
            isShowCutScreen: 0,
            isFaceVerification: null,
            isForbidRightclick: 1,
            isForbidCutScreen: 0,
            isForbidCopypaste: 1,
            answerNum: null,
            isQuestionDisorder: 0,
            isOptionDisorder: 0,
        },
        paperJson: '', // 和上面一样,不重复了
        paperList: null,
        photos: null,
    };
}
// 获取题目列表（exam考试）
exports.getExamQuestionList = async (storageObj, courseId, examId) => {
    let config = {
        method: 'post',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/paper/load-exam-record',
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
        data: {
            courseId: courseId,
            examId: examId,
        },
    };
    let res = await axiosIns(config);
    res.paperObj = crypto.decryptAssignment(res.paperJson);
    return res;
};
if (false) {
    res = '';
}

// 回答问题 test assignment exam 通用
exports.answerQuestion = async (storageObj, answersObj) => {
    let config = {
        method: 'post',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/paper/submit-paper-answer',
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
        data: {
            data: crypto.encryptAssignment(answersObj),
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = '';
}

// 提交test作业
exports.submitTest = async (storageObj, courseId, cellId) => {
    let config = {
        method: 'post',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/test-answer/submit-test',
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
        data: {
            courseId: courseId,
            cellId: cellId,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = '';
}
// 提交assignment作业
exports.submitAssignment = async (storageObj, courseId, assignmentId) => {
    let config = {
        method: 'post',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/assignment-answer/submit-online-assignment',
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
        data: {
            courseId: courseId,
            assignmentId: assignmentId,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = '';
}
// 提交exam考试
exports.submitExam = async (storageObj, courseId, examId) => {
    let config = {
        method: 'post',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/exam-answer/submit-exam',
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
        data: {
            courseId: courseId,
            examId: examId,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = '';
}

// 获取assignment作业列表
// https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/assignment-rule/stu-assignment-list
exports.getAssignmentList = async (storageObj, courseId) => {
    let config = {
        method: 'get',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/assignment-rule/stu-assignment-list',
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
        params: {
            SkipCount: 0,
            MaxResultCount: 999,
            CourseId: courseId,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        totalCount: 2,
        items: [
            {
                answerId: '3a18fd39-7441-d8e0-b632-cffca406bbab',
                assignmentId: '3a17a559-465d-5328-1290-dca81390613c',
                courseId: '3a175196-a62b-8de8-b0e5-03eb8e11092e',
                courseName: '习近平新时代中国特色社会主义思想概论',
                title: '综合练习2',
                description: '',
                startTime: null,
                endTime: '2025-07-01T00:00:00',
                objectiveType: 1,
                sort: null,
                assignmentType: 1,
                answerTimes: 0,
                answerNum: null,
                state: 0, // null表示“未答题” 0表示“正在答题”
                paperState: 1,
                timeState: null,
                score: null,
                studyYear: 2025,
                studyTerm: 1,
                subjectiveScore: null,
                objectiveScore: null,
                isPayment: true,
                creationTime: '2025-01-23T20:39:10',
                id: '3a17a559-465d-5328-1290-dca81390613c',
            },
            {
                answerId: null,
                assignmentId: '3a17a558-f9e4-1bc6-19e3-ed3cbad1bd6c',
                courseId: '3a175196-a62b-8de8-b0e5-03eb8e11092e',
                courseName: '习近平新时代中国特色社会主义思想概论',
                title: '综合练习1',
                description: '',
                startTime: null,
                endTime: '2025-07-01T00:00:00',
                objectiveType: 1,
                sort: null,
                assignmentType: 1,
                answerTimes: null,
                answerNum: null,
                state: null,
                paperState: null,
                timeState: null,
                score: null,
                studyYear: 2025,
                studyTerm: 1,
                subjectiveScore: null,
                objectiveScore: null,
                isPayment: true,
                creationTime: '2025-01-23T20:38:51',
                id: '3a17a558-f9e4-1bc6-19e3-ed3cbad1bd6c',
            },
        ],
    };
}
// 获取exam考试列表
// https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/exam-rule/stu-exam-list?StudyYear=${year}&StudyTerm=${term}
exports.getExamList = async (storageObj, year = '', term = '') => {
    let config = {
        method: 'get',
        url: `https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/exam-rule/stu-exam-list?StudyYear=${year}&StudyTerm=${term}`,
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        totalCount: 7,
        items: [
            // 有answerId，已经开始了
            {
                answerId: '3a19bc0b-ca39-365f-3af3-7551005322cf',
                examId: '********-0000-0000-0000-********0000',
                courseId: '3a175196-a62b-8de8-b0e5-03eb8e11092e',
                courseName: '习近平新时代中国特色社会主义思想概论',
                title: '考试',
                schedule: 90,
                description: null,
                startTime: null,
                endTime: '2025-06-30T00:00:00',
                makeUpStartTime: null,
                makeUpEndTime: null,
                objectiveType: 1,
                sort: null,
                answerTimes: 0, // 已答题次数
                answerNum: 10, // 可以答题次数
                state: 0, // 0:未开始 2:已结束
                timeState: 1,
                paperState: 1,
                studyYear: 2025,
                studyTerm: 1,
                score: 0, // 分数
                subjectiveScore: null,
                objectiveScore: null,
                examType: 0,
                examDuration: 90,
                nosubmitted: 0,
                reviewed: 0,
                noReviewed: 0,
                creationTime: '2025-01-23T20:55:28',
                isPayment: true,
                randomExamRuleId: '3a17a568-31c0-7788-6f9b-769a934a5686',
                id: '3a17a568-31c0-7788-6f9b-769a934a5686',
            },
            // 没有answerId，未开始
            {
                answerId: null,
                examId: '********-0000-0000-0000-********0000',
                courseId: '3a175197-7b7f-1628-1798-de43968ae2c1',
                courseName: '大学英语（本）1',
                title: '期末考试',
                schedule: 90,
                description: null,
                startTime: null,
                endTime: '2025-06-30T00:00:00',
                makeUpStartTime: null,
                makeUpEndTime: null,
                objectiveType: 1,
                sort: null,
                answerTimes: null,
                answerNum: 10,
                state: 0,
                timeState: 1,
                paperState: null,
                studyYear: 2025,
                studyTerm: 1,
                score: null,
                subjectiveScore: null,
                objectiveScore: null,
                examType: 0,
                examDuration: 90,
                nosubmitted: 0,
                reviewed: 0,
                noReviewed: 0,
                creationTime: '2025-01-23T10:50:26',
                isPayment: true,
                randomExamRuleId: '3a17a33e-46f0-a1fa-60ef-ae7f41c84dbc',
                id: '3a17a33e-46f0-a1fa-60ef-ae7f41c84dbc',
            },
        ],
    };
}

// 更改答题状态（assignment作业）
exports.redoAssignment = async (storageObj, courseId, assignmentId) => {
    let config = {
        method: 'post',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/assignment-answer/redo-assignment',
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
        data: {
            courseId: courseId,
            assignmentId: assignmentId,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = '';
}
// 更改答题状态（exam考试）
exports.redoExam = async (storageObj, courseId, examId, ruleId) => {
    let config = {
        method: 'post',
        url: 'https://jxjynew.ahjxjy.cn/v1/jxjy-teacher-space-service/api/exam-answer/redo-exam',
        headers: {
            authorization: storageObj.token,
            student: storageObj.studentId,
            'user-type': storageObj.adminUserType,
        },
        data: {
            courseId: courseId,
            examId: examId,
            ruleId: ruleId,
        },
    };
    let res = await axiosIns(config);
    return res;
};
