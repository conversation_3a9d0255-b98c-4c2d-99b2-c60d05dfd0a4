let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api.js');

async function getExamListModule(infoLogger, mainPage, globalStore) {
    let taskObj = globalStore.taskObj;
    let storageObj = globalStore.storageObj;

    // // 从页面的session获取保存的token
    // let storageStr = await mainPage.evaluate(() => {
    //     let token = localStorage.getItem('token');
    //     token = JSON.parse(token);
    //     let studentId = localStorage.getItem('Student');
    //     let adminUserType = localStorage.getItem('AdminUserType');
    //     let storage = {
    //         token: 'Bearer ' + token.encryptedAccessToken,
    //         studentId: studentId,
    //         adminUserType: adminUserType,
    //     };
    //     return JSON.stringify(storage);
    // });
    // let storageObj = JSON.parse(storageStr);

    let examListRes = await api.getExamList(storageObj, taskObj.year, taskObj.term);
    let examList = examListRes.items;

    // 如果有课程名筛选条件，就筛选课程
    if (taskObj.coursename) {
        examList = examList.filter(item => item.courseName.includes(taskObj.coursename));
    }

    let examLength = examList.length;
    await infoLogger(`获取考试列表成功，共${examLength}门考试`, 'green'); //记录日志

    // 如果考试列表为空，则返回空数组
    if (examList.length == 0) {
        return [];
    }

    return examList;
}

module.exports = getExamListModule;