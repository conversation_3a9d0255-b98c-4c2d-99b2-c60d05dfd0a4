// ========= 任务模块 =========
let videoTask = require('./courseTask/videoTask/index.js');
let documentTask = require('./courseTask/documentTask.js');
let testTask = require('./courseTask/testTask.js');
let assignmentTask = require('./courseTask/assignmentTask.js');
let questionTask = require('./courseTask/questionTask.js');
let virtualtestnewTask = require('./courseTask/virtualtestnewTask.js');

async function runCourseModule(infoLogger, courseList, globalStore, taskObj) {
    //遍历课程
    for (let i = 0; i < courseList.length; i++) {
        let courseObj = courseList[i];
        let cellList = courseObj.cellList;
        await infoLogger(`课程：[${i + 1}/${courseList.length}]：开始课程《${courseObj.courseName}》，共有${cellList.length}个课件`, 'blue');

        if (courseObj.schedule * 1 == 100) {
            await infoLogger(`课程：[${i + 1}/${courseList.length}]：课程已经完成，跳过`, 'green');
            continue;
        }

        //遍历课件
        secondLoop: for (let j = 0; j < cellList.length; j++) {
            let cellObj = cellList[j];
            let cellLogStr = `课程${i + 1}/${courseList.length}]-章节[${j + 1}/${cellList.length}]`;

            // 跳过已完成课程放到了task里面

            switch (cellObj.icon) {
                // video视频课程任务
                case 'video': {
                    if (!taskObj.runCourse) break;
                    await videoTask(infoLogger, globalStore, courseObj, cellObj, cellLogStr);
                    break;
                }
                // doc,text文档任务
                case 'doc':
                case 'text': {
                    if (!taskObj.runCourse) break;
                    await documentTask(infoLogger, globalStore, courseObj, cellObj, cellLogStr);
                    break;
                }
                // question回复任务,直接修改进度
                case 'question': {
                    if (!taskObj.runCourse) break;
                    await questionTask(infoLogger, globalStore, courseObj, cellObj, cellLogStr);
                    break;
                }

                // // virtualtestnew虚拟实现，直接修改进度也不行，暂时搁置
                // case "virtualtestnew": {
                //     if (!taskObj.runCourse) break;
                //     await virtualtestnewTask(globalStore, courseObj, cellObj, courseLength, cellLength, infoLogger, i, j);
                //     break;
                // }

                // test作业任务，只能回答一次
                case 'test': {
                    if (!taskObj.runAssignment) break;
                    await testTask(infoLogger, globalStore, courseObj, cellObj, cellLogStr);
                    break;
                }
                // assignment作业任务
                case 'assignment': {
                    if (!taskObj.runAssignment) break;
                    let count = 1;
                    await assignmentTask(count, infoLogger, globalStore, courseObj, cellObj, cellLogStr);
                    break;
                }
                default: {
                    await infoLogger(`${cellLogStr}：当前任务点为不支持的类型： ${cellObj.icon} 《${cellObj.title}》`, 'red');
                    break;
                }
            }
        }
    }
}

module.exports = runCourseModule;
