let pageTools=require('../../../utils/pageTools.js');
let api = require("../../utils/api");

async function linkTask(infoLogger, mainPage, taskObj, taskLogStr) {
    let cookieStr = await pageTools.getPageCookies(mainPage);
    let res=await api.updateDuration(infoLogger, taskObj.id, cookieStr, "{}");
    if(res=='未完成'){
        await new Promise((r) => setTimeout(r, 2000));
        await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 五次重试依然失败`, "red");
        return
    }
    await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 已经完成`, "green");
}

module.exports = linkTask;

// fetch("https://lms.ouchn.cn/api/course/activities-read/10002894294", {
//   "headers": {
//     "accept": "*/*",
//     "accept-language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
//     "content-type": "application/json",
//     "priority": "u=1, i",
//     "sec-ch-ua": "\"Google Chrome\";v=\"125\", \"Chromium\";v=\"125\", \"Not.A/Brand\";v=\"24\"",
//     "sec-ch-ua-mobile": "?0",
//     "sec-ch-ua-platform": "\"Windows\"",
//     "sec-fetch-dest": "empty",
//     "sec-fetch-mode": "cors",
//     "sec-fetch-site": "same-origin",
//     "x-requested-with": "XMLHttpRequest",
//     "cookie": "HWWAFSESTIME=1716657179430; HWWAFSESID=7fb1d15a336e1afca2f; session=V2-10000000002-814390b8-49d6-4f5c-abcf-b0f4024a0ecb.MTAwMDA1NDY1MjQ.1716770838372.jxZR2-uMfW0_9WF5-DHOBv9MqcA",
//     "Referer": "https://lms.ouchn.cn/course/10000049190/learning-activity/full-screen",
//     "Referrer-Policy": "strict-origin-when-cross-origin"
//   },
//   "body": "{}",
//   "method": "POST"
// });
