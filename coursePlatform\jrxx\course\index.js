// ========= 公共模块 =========
let puppeteer = require("puppeteer-extra");
let StealthPlugin = require("puppeteer-extra-plugin-stealth");
puppeteer.use(StealthPlugin());

let querystring = require("querystring");
let path = require("path");

// ========= 私有模块 =========
let axios = require("../utils/axios");
let courseTools = require("./courseTools.js");

async function jrxxCourse(taskObj, taskOptions) {
    let infoLogger = courseTools.getInfoLogger(taskObj.id, false);
    let finalResult = []; //任务进度
    let warningMessage = ""; //错误日志

    /**
     *  ======================= 一、初始化界面 =======================
     * 1.创建浏览器实例，创建页面实例
     * 2.开启拦截
     */

    //创建浏览器实例
    let browser = await puppeteer.launch({
        headless: taskOptions.isHeadless, //显示浏览器窗口
        defaultViewport: {
            //设置浏览器视口尺寸
            width: 1920,
            height: 1080,
        },
        args: [
            "--mute-audio", //静音
            "--start-maximized", //最大化启动浏览器
            "--use-fake-ui-for-media-stream", //绕过摄像头，麦克风权限
        ],
        //利用本地chrome浏览器来测试
        // executablePath: `C:/Program Files/Google/Chrome/Application/chrome.exe`,
    });

    //创建页面实例
    // let mainPage = await browser.newPage();
    let pages = await browser.pages();
    let mainPage = pages[0];


    /**
     * ======================= 二、登录 =======================
     * 1.打开到用户登录界面
     * 2.输入用户名，密码，验证码
     * 3.登录，等待登录完成
     */

    //打开网址
    await mainPage.goto("https://www.junruizx.com/indexNew.do", {
        waitUntil: "networkidle0",
    });

    // 点击 "登录" 按钮
    let signinHandle = await mainPage.$("#login");
    await signinHandle.click();
    //输入用户名
    let usernameInputHandle = await mainPage.$("#userid");
    await usernameInputHandle.type(taskObj.username);
    //输入密码
    let passwordInputHandle = await mainPage.$("#password");
    await passwordInputHandle.type(taskObj.password);
    //点击登录
    let submitHandle = await mainPage.$("#loginForm > div > div.form > table > tbody > tr:nth-child(4) > td > button");
    await submitHandle.click();
    await new Promise((r) => setTimeout(r, 3000));

    //等待跳转完成
    try {
        await mainPage.waitForSelector("body > div.mainhead > div > ul.e > li:nth-child(2) > a.ic1", { timeout: 5000 });
    } catch (error) {
        await infoLogger(`登录失败：${error.message}`, "red"); //记录日志
        await browser.close(); //关闭浏览器
        throw new Error("登录失败"); //抛出错误，结束进程池
    }
    await infoLogger(`登录成功`, "green"); //记录日志

    /**
     * ======================= 三、获取课程列表 =======================
     *
     */

    //点击 “在线课堂”
    let onlineCourseHandle = await mainPage.$("body > table > tbody > tr:nth-child(1) > td.mleft1 > div > dl > dd:nth-child(11) > a");
    await onlineCourseHandle.click();

    await new Promise((r) => setTimeout(r, 3000));

    //收集课件列表
    let courseList = await mainPage.evaluate(() => {
        let courseList = [];
        let courseTagList = document.querySelectorAll(
            "body > table > tbody > tr:nth-child(1) > td.mright1 > table.st1 > tbody > tr > td > table > tbody > tr"
        );
        courseTagList.forEach((courseTag) => {
            //获取课程名字
            let courseNameTag = courseTag.querySelector("td.t1");
            let courseName = courseNameTag.textContent.trim();

            //获取课程视频时长
            let courseDurationTag = courseTag.querySelector("td:nth-child(3)");
            let courseDuration = courseDurationTag.textContent.trim();

            //获取视频进度
            let courseProgressTag = courseTag.querySelector("td:nth-child(4)");
            let courseProgress = courseProgressTag.textContent.trim().replace("%", "") * 1;

            //获取视频id
            let courseIdTag = courseTag.querySelector("th > input");
            let onclickContent = courseIdTag.getAttribute("onclick"); //toLearnCourse('96178','365821');
            onclickContent = onclickContent.match(/toLearnCourse\((.*)\);/)[1];
            let [chapterId, courseId] = onclickContent.split(",");

            //整合所有数据
            courseList.push({
                courseName,
                courseDuration,
                courseProgress,
                courseId: courseId.replace("'", "").replace("'", ""),
                chapterId: chapterId.replace("'", "").replace("'", ""),
            });
        });
        return courseList;
    });
    // courseList = [
    //     { courseName: "安全生产管理", courseDuration: "00:48:59", courseProgress: 0, courseId: "390837", chapterId: "102540" }
    // ];
    await infoLogger(`获取课程列表成功，共${courseList.length}门课程`, "green"); //记录日志



    /**
     * ======================= 四、刷课准备工作=======================
     */

    //获取cookie
    let cookieStr = await courseTools.getPageCookies(mainPage);

    //进行初次人脸识别
    //从服务器获取人脸识别图片
    let faceDate = taskObj.photoBase64.replace("data:image/jpeg;base64,", "");
    faceDate = encodeURIComponent(faceDate);
    faceDate = "img=" + faceDate;
    //向服务器发送识别请求
    let updateFaceRes = await axios({
        method: "POST",
        url: "https://www.aqscpx.com/user/updatePhotoTemplate.do",
        headers: {
            "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
            cookie: cookieStr,
        },
        data: faceDate,
    });
    // updateFaceRes={ message: '成功', photoId: '14218172', entity: null, code: 200 }
    //判断结果
    if (updateFaceRes.code == 200) {
        await infoLogger(`初次人脸识别成功`, "green");
    }

    /**
     * ======================= 五、开始刷课=======================
     */

    //遍历课程列表 courseList.length
    for (let i = 0; i < courseList.length; i++) {
        let courseObj = courseList[i];
        await infoLogger(`[${i + 1}/${courseList.length}]开始课程：《${courseObj.courseName}》`,'blue');

        //获取课程具体信息
        let courseInfoRes = await axios({
            method: "get",
            url: `https://www.aqscpx.com/user/toAudio.do?code=${courseObj.courseId}&kjid=${courseObj.chapterId}`,
            headers: {
                cookie: cookieStr,
            },
        });
        let playParamsMatch = courseInfoRes.match(/var playParams = ({[\s\S]*})/)[1];
        let playParams = eval("(" + playParamsMatch + ")");
        let chapterInfo = JSON.parse(playParams.chapterInfo);
        // var chapterInfo = {
        //     totalSeconds: 2801,
        //     videoSign:
        //         "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJmaWxlSWQiOiI1NTc2Njc4MDIxMDk0MDgyNzA5IiwiYXBwSWQiOjEyNTE3Nzg3MzUsImN1cnJlbnRUaW1lU3RhbXAiOjE2OTk4NDQ1MzYsInBjZmciOiJtZWRpYSJ9.qECUaHYAe7HCSskZxrC4zbujKcy4X8d88m1ki_KdCJU",
        //     chapterId: 390760,
        //     audioSign: "",
        //     requestId: "812867d6-862f-473a-bcc9-6b018bff58fe",
        //     videoId: "5576678021094082709",
        //     progressRateNum: 45.0,
        //     mediaName: "102539/102539_08",
        //     chapterName: "触电事故及现场救护-触电急救方法及注意事项",
        //     progressRate: "45.00%",
        //     lastPlayTime: 1229,
        //     totalTime: "00:46:41",
        // };

        // 如果进度大于98，就进行下一个视频
        if (chapterInfo.progressRateNum >= 99) {
            await infoLogger(`[${i + 1}/${courseList.length}]《${courseObj.courseName}》：进度大于99%，跳过`,'green');
            continue;
        }

        let count = Math.floor((chapterInfo.totalSeconds - chapterInfo.lastPlayTime) / 30);
        await infoLogger(
            `[${i + 1}/${courseList.length}]《${courseObj.courseName}》：总时长：${chapterInfo.totalSeconds}秒，共需要播放${count}次,上次播放时长：${
                chapterInfo.lastPlayTime
            }秒`
        );
        // //打断点
        // await new Promise((r) => setTimeout(r, 1000000));

        let currentTime = chapterInfo.lastPlayTime + 30;
        for (let j = 0; j < count; j++) {
            let updateRecordRes = await await axios({
                method: "GET",
                url:
                    "https://www.aqscpx.com/user/updateRecord.do?courseId=" +
                    playParams.courseId +
                    "&chapterId=" +
                    playParams.chapterId +
                    "&currSecond=" +
                    parseInt(currentTime) +
                    "&timeStep=30" +
                    "&requestId=" +
                    chapterInfo.requestId,
                headers: {
                    cookie: cookieStr,
                },
            });
            // updateRecordRes={ message: '成功', entity: null, code: 200 }

            if (updateRecordRes.code == 200) {
                // //更新课程进度信息
                // courseInfoRes = await axios({
                //     method: "get",
                //     url: `https://www.aqscpx.com/user/toAudio.do?code=${courseObj.courseId}&kjid=${courseObj.chapterId}`,
                //     headers: {
                //         cookie: cookieStr,
                //     },
                // });
                // playParamsMatch = courseInfoRes.match(/var playParams = ({[\s\S]*})/)[1];
                // playParams = eval("(" + playParamsMatch + ")");
                // chapterInfo = JSON.parse(playParams.chapterInfo);

                await infoLogger(`[${i + 1}/${courseList.length}]《${courseObj.courseName}》第${j + 1}次播放成功，还剩余${count - j - 1}次`);
                await new Promise((r) => setTimeout(r, 500));
                currentTime = currentTime + 30;
            }

            //可能会出现人脸识别 { message: '拍照验证', entity: null, code: 40510 }
            if (updateRecordRes.code != 200 && updateRecordRes.message == "拍照验证") {
                await infoLogger(`[${i + 1}/${courseList.length}]《${courseObj.courseName}》第${j + 1}次播放，需要拍照验证`);
                let verifyPhotoRes = await axios({
                    method: "POST",
                    url: "https://www.aqscpx.com/user/verifyPhoto.do",
                    headers: {
                        "content-type": "application/x-www-form-urlencoded; charset=UTF-8",
                        cookie: cookieStr,
                    },
                    data: faceDate,
                });
                if (verifyPhotoRes.code == 200) {
                    await infoLogger(`[${i + 1}/${courseList.length}]《${courseObj.courseName}》第${j + 1}次播放，拍照验证成功`);
                    continue;
                }
                if (verifyPhotoRes.code != 200) {
                    await infoLogger(`[${i + 1}/${courseList.length}]《${courseObj.courseName}》第${j + 1}次播放，拍照验证失败`,'red');
                    await browser.close(); //关闭浏览器
                    throw new Error(`拍照验证失败，服务器返回值${JSON.stringify(verifyPhotoRes)}`); //抛出错误，结束进程池
                }
                // verifyPhotoRes={ message: '成功', photoId: '14229078', entity: null, code: 200 }
            }

            if (updateRecordRes.code != 200 && updateRecordRes.message != "拍照验证") {
                await infoLogger(
                    `[${i + 1}/${courseList.length}]《${courseObj.courseName}》第${j + 1}次播放失败，错误信息：${updateRecordRes.message}`,'red'
                );
                await browser.close(); //关闭浏览器
                throw new Error(`播放失败，服务器返回值${JSON.stringify(updateRecordRes)}`); //抛出错误，结束进程池
            }
        }
        await infoLogger(`[${i + 1}/${courseList.length}]《${courseObj.courseName}》：播放完成`,'green');
    }

    /**
     * ======================= 五、收尾工作 =======================
     */

    //打开网址
    await mainPage.goto("https://www.aqscpx.com/user/userCenter.do", {
        waitUntil: "networkidle0",
    });
    //点击 “在线课堂”
    onlineCourseHandle = await mainPage.$("body > table > tbody > tr:nth-child(1) > td.mleft1 > div > dl > dd:nth-child(11) > a");
    await onlineCourseHandle.click();

    await new Promise((r) => setTimeout(r, 3000));

    //收集课件列表
    courseList = await mainPage.evaluate(() => {
        let courseList = [];
        let courseTagList = document.querySelectorAll(
            "body > table > tbody > tr:nth-child(1) > td.mright1 > table.st1 > tbody > tr > td > table > tbody > tr"
        );
        courseTagList.forEach((courseTag) => {
            //获取课程名字
            let courseNameTag = courseTag.querySelector("td.t1");
            let courseName = courseNameTag.textContent.trim();

            //获取课程视频时长
            let courseDurationTag = courseTag.querySelector("td:nth-child(3)");
            let courseDuration = courseDurationTag.textContent.trim();

            //获取视频进度
            let courseProgressTag = courseTag.querySelector("td:nth-child(4)");
            let courseProgress = courseProgressTag.textContent.trim().replace("%", "") * 1;

            //获取视频id
            let courseIdTag = courseTag.querySelector("th > input");
            let onclickContent = courseIdTag.getAttribute("onclick"); //toLearnCourse('96178','365821');
            onclickContent = onclickContent.match(/toLearnCourse\((.*)\);/)[1];
            let [chapterId, courseId] = onclickContent.split(",");

            //整合所有数据
            courseList.push({
                courseName,
                courseDuration,
                courseProgress,
                courseId: courseId.replace("'", "").replace("'", ""),
                chapterId: chapterId.replace("'", "").replace("'", ""),
            });
        });
        return courseList;
    });
    // courseList = [
    //     { courseName: "安全生产管理", courseDuration: "00:48:59", courseProgress: 0, courseId: "390837", chapterId: "102540" }
    // ];
    let completeCount = 0;
    courseList.forEach((item) => {
        if (item.courseProgress >= 98) {
            completeCount++;
        }
    });
    finalResult = [{ courseName: "课程统计", progress: `共完成${completeCount}/${courseList.length}门课程` }];
    return  { finalResult, warningMessage };
}

module.exports = jrxxCourse;

// //测试专用 340323198610201754，ZL123456
// let taskOptions = {
//     isHeadless: "new" && false, //浏览器模式 false ,'new'
// };
// let taskObj = {
//     username: "13485509298",
//     password: "ZL123456",
// "https://www.junruizx.com/indexNew.do"
// };

// jrxxCourse(taskObj, taskOptions).then(
//     (val) => {
//         console.log("success");
//     },
//     (err) => {
//         console.log(err);
//     }
// );
