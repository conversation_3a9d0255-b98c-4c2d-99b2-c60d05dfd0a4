let { HttpsProxyAgent } = require('https-proxy-agent');
let axiosIns = require('./axiosIns.js');


async function getAgent() {
    let res;
    let proxyInfo;
    try {
        // 多米代理
        res = await axiosIns({
            url: 'http://need1.dmdaili.com:7771/dmgetip.asp?apikey=c92e98bd&pwd=22193af90e7284bd39e440c8960e1d3f&getnum=1&httptype=1&geshi=2&fenge=1&fengefu=&Contenttype=2&operate=all',
            method: 'get',
            timeout: 5000,
        });

        proxyInfo = res.data[0];
        if (!proxyInfo) {
            throw new Error('代理1出错');
        }
    } catch (error) {
        // 用其他代理
        res = await axiosIns({
            url: 'http://ecs.hailiangip.com:8422/api/getIpEncrypt?dataType=0&encryptParam=SlDyzgfgDW12vuaMHmQkMz9pKEmWH7kDAoD1ZC4KkxpHXuvLm%2B3L9xWaasGPtq4TOj4RbltfYgoMC5a7FzqLLwNG8xQEizS3mHQLaScnz%2FbKRT6pSc9tBbFqufMkqx7tCKWVsT2h68WdIUX%2FicuP%2Bjb37eJ1ZK6mYsahgpXXq5IfFaJxyBo8680oTdrXHKDt0AOtabf92lu9Nd5cdhcIj9zbJ280PfgKxcbaM9RRtYAGY3mYgkn434O0KJtB9%2Fqe',
            method: 'get',
            timeout: 5000,
        });
        proxyInfo = {
            ip:res.data[0].ip,
            port:res.data[0].port,
        }
        if (!proxyInfo) {
            throw new Error('代理2出错');
        }
    }
    // console.log('proxyInfo:', proxyInfo);
    let agent = new HttpsProxyAgent(`http://${proxyInfo.ip}:${proxyInfo.port}`);
    return agent;
}

module.exports = getAgent;

// 测试
if (false) {
    (async () => {
        let res = await getAgent()
        console.log(res);
    })();
}
