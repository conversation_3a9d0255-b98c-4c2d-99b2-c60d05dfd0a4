// let count = 20; //只能答题20次，超过这个次数，直接下一个
let api = require('../../../../utils/api.js');
let { formatQuestion, answerQusetion, collectQuestion } = require('../../../../utils/handleQuestion.js');
let pageTools = require('../../../../../utils/pageTools.js');

async function sleep(t) {
    return new Promise(r => setTimeout(r, t * 1000));
}

function trim(s) {
    return (
        s
            //删除html标签 <span>
            .replace(/(<([^>]+)>)/gi, '')
            //删除所有的换行符
            .replaceAll('\n', '')
            // 把Unicode 转义序列转为正常字符，例如\u0026->&
            .replace(/\\u([0-9a-fA-F]{4})/g, function (match, p1) {
                return String.fromCharCode(parseInt(p1, 16));
            })
            //把&nbsp转为一个空格
            .replaceAll('&nbsp;', '')
            //删除两个及以上的空格，保留单个空格
            .replace(/\s{2,}/g, ' ')
            // 删除前后字符串
            .trim()
    );
}

// 从api获取题目 -> 对题目进行格式化(formatQuestion) -> 回答题目(handleAnswer) 或者 收集题目(collectQuestion) -> 提交试卷
async function testTask(infoLogger, mainPage, courseObj, cellObj, cellLogStr, globalStore) {
    let storageObj = globalStore.storageObj;

    // 获取题目列表
    let questionListRes = await api.getTestQuestionList(storageObj, courseObj.id, cellObj.id);
    let questionList = formatQuestion(questionListRes, courseObj.courseName);

    // 处理当前试卷状态questionListRes.state 1表示已完成为批阅 0表示考试中 2表示考试完成已批阅
    let testState;
    switch (questionListRes.state) {
        case 1: {
            testState = '已完成未批阅';
            break;
        }
        case 0: {
            testState = '考试中';
            break;
        }
        case 2: {
            testState = '已完成已批阅';
            break;
        }
        default: {
            testState = '未知状态';
            throw new Error(`未知考试状态：${questionListRes.state}`);
            break;
        }
    }

    await infoLogger(`${cellLogStr}：开始随堂测试任务 《${cellObj.title}》`);

    if (questionListRes.state == 0) {
        await infoLogger(`当前试卷状态为：${testState}，接下来开始答题`);
        // 查找答案，回答问题
        let submitCount = await answerQusetion(questionList, storageObj);
        await infoLogger(`共有${questionList.length}道题目，回答${submitCount}道题目`);

        // 产生一个1-3的随机值
        let randomTime = pageTools.getRandomInt(3, 5);
        await infoLogger(`等待${randomTime}分钟后提交试卷`);
        await new Promise(r => setTimeout(r, randomTime * 60 * 1000));

        // 提交作业，添加容错机制
        for (let i = 0; i < 3; i++) {
            try {
                await api.submitTest(storageObj, courseObj.id, cellObj.id);
                await infoLogger(`作业已经提交`, 'green');
                break;
            } catch (error) {
                await infoLogger(`作业提交失败，重试第${i + 1}次`, 'gray');
                await new Promise(r => setTimeout(r, 3000));
                // 最后一次尝试失败
                if (i === 2) {
                    await infoLogger(`作业提交失败，已尝试3次`, 'red');
                }
            }
        }

        // 收集题目
        let collectCount = await collectQuestion(questionList);
        await infoLogger(`共有${questionList.length}道题目，收集${collectCount}道题目`);
    }

    if (!(questionListRes.state == 0)) {
        await infoLogger(`当前试卷状态为：${testState}`);
        // // 收集题目
        // let collectCount = await collectQuestion(questionList);
        // await infoLogger(`共有${questionList.length}道题目，收集${collectCount}道题目`);
    }

    //更改课程状态为已完成
    await api.studyingLenard(storageObj, courseObj.id, cellObj.id);
    let finishCellRes = await api.finishCell(storageObj, courseObj.id, cellObj.id);
    if (finishCellRes.code == 'allow') {
        await infoLogger(`${cellLogStr}：随堂测试任务完成 《${cellObj.title}》`, 'green');
    } else {
        await infoLogger(`${cellLogStr}：随堂测试任务失败《${cellObj.title}》,${JSON.stringify(finishCellRes)}`, 'red');
    }
}

module.exports = testTask;
