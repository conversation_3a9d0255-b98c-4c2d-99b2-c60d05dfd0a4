let pageTools=require('../../../../../utils/pageTools.js');
let api = require("../../../../utils/api.js");
async function hyperlinkTask(courseStore, infoLogger, attachment, mArgJson, attachLogStr, chapterCookieStr, chapterId) {
    await new Promise((r) => setTimeout(r, 2000));

    let attachmentName = attachment.property.name || attachment.property.title;

    let linkPath = "https://mooc1.chaoxing.com/ananas/job/hyperlink";

    let linkParams = {
        jobid: attachment.jobid,
        knowledgeid: chapterId,
        courseid: courseStore.courseId,
        clazzid: courseStore.classId,
        jtoken: attachment.jtoken,
    };

    let linkUrl = pageTools.buildUrl(linkPath, linkParams);

    let linkRes = await api.submitDocumentTask(linkUrl, chapterCookieStr);

    if(linkRes&&linkRes.status){
        await infoLogger(`${attachLogStr}阅读链接完成：${attachmentName}`, "green");
        return;
    }else{
        await infoLogger(`${attachLogStr}阅读链接失败，跳过任务：${attachmentName}`, "red");
        return;
    }

    // if (!linkResult) {
    //     await infoLogger(`[${j + 1}/${chapterLength}]阅读链接失败，跳过任务：${attachment.property.name}`, "red");
    //     return;
    // }
    // try {
    //     let doLinkJson = JSON.parse(linkResult);
    //     if (doLinkJson.status) {
    //         await infoLogger(`[${j + 1}/${chapterLength}]链接任务完成：${attachment.property.name}`, "green");
    //         return;
    //     } else {
    //         await infoLogger(`[${j + 1}/${chapterLength}]链接任务失败：${attachment.property.name}`, "red");
    //         return;
    //     }
    // } catch (e) {
    //     await infoLogger(`[${j + 1}/${chapterLength}]解析链接内容失败：${attachment.property.name}`, "red");
    //     return;
    // }
}

module.exports = hyperlinkTask;
