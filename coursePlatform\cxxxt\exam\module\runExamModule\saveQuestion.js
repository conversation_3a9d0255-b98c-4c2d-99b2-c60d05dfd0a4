async function saveQuestion(infoLogger, mainPage, questionList, taskObj) {
    if (taskObj.others.includes('电脑端')) {
        try {
            await mainPage.evaluate(async questionList => {
                function handleImgs(s) {
                    // 匹配所有 img 标签，捕获 src 属性的 URL
                    let imgEs = s.match(/<img\s+[^>]*src\s*=\s*['"]([^'"]+)['"][^>]*>/gi);

                    if (imgEs) {
                        for (let j = 0; j < imgEs.length; j++) {
                            // 匹配 img 标签中的 src 属性
                            let urlMatch = imgEs[j].match(/src\s*=\s*['"]([^'"]+)['"]/i);
                            if (urlMatch && urlMatch[1]) {
                                let url = urlMatch[1]; // 获取完整 URL
                                // 用 [URL] 格式替换 img 标签为 URL
                                s = s.replace(imgEs[j], `[URL]${url}[/URL]`);
                            }
                        }
                    }

                    return s;
                }

                function trim(s) {
                    return (
                        s
                            //删除多余字符串
                            .replace(/(<([^>]+)>)/gi, '') //去掉所有的html标记 <a> </a>
                            .replace(/^\d+[\.、]/, '') //删除所有1.、1、的内容
                            .replace(/\(.{3}\)/, '') //删除所有（xx题）的内容
                            .replaceAll('&nbsp;', '')
                            .replaceAll('\n', '') //删除所有的换行符
                            .replace(/\s{2,}/g, ' ') //删除两个及以上的空格，保留单个空格
                            .trim()
                    );
                }

                //生成一个唯一id  #fanyaMarking > div.marking_content.ans-cc > div.mark_table > div.whiteDiv > div.questionLi
                function generateUniqueId() {
                    return 'id-' + new Date().getTime().toString(36) + '-' + Math.random().toString(36).substr(2, 9);
                }

                let TimuList = document.querySelectorAll('#fanyaMarking > div.marking_content.ans-cc > div.mark_table > div.whiteDiv > div.questionLi');
                for (let i = 0; i < TimuList.length; i++) {
                    let timuDom = TimuList[i];
                    // 题目内容
                    let questionFull = timuDom.querySelector(':scope>h3>div').innerHTML;
                    let content = trim(handleImgs(questionFull));

                    //题目类型
                    let type = timuDom.querySelector(':scope>h3>span').innerHTML;
                    type = type.match(/\(([^,]+),/)[1];

                    //获取题目id
                    let questionId = timuDom.getAttribute('id');

                    //查找答案
                    let answers;
                    questionList.forEach(questionObj => {
                        if (questionObj.id.slice(21) === questionId && questionObj.answers) {
                            answers = questionObj.answers;
                        }
                    });
                    if (!answers) {
                        continue;
                    }

                    //2.遍历这个题目dom的所有选项dom
                    switch (type) {
                        case '单选题': {
                            //遍历这个题目dom的所有选项dom，收集正确答案，放到数组中
                            let answerTmpArr = timuDom.querySelectorAll('div.stem_answer>div.answerBg>span');
                            answerTmpArr.forEach(spanTag => {
                                //获取一个选项dom的值
                                let optionKey = trim(handleImgs(spanTag.innerText));
                                if (optionKey == answers) {
                                    //如果包含check_answer 这个css样式说明已经选择了
                                    let spanClass = spanTag.getAttribute('class');
                                    if (!spanClass.includes('check_answer')) spanTag.parentElement.click();
                                }
                            });
                            break;
                        }
                        case '多选题': {
                            //遍历这个题目dom的所有选项dom，收集正确答案，放到数组中
                            let answerTmpArr = timuDom.querySelectorAll('.stem_answer span[data]');
                            for (let spanTag of answerTmpArr) {
                                //获取一个选项dom的值
                                let optionKey = trim(handleImgs(spanTag.innerText));
                                if (answers.includes(optionKey)) {
                                    let spanClass = spanTag.getAttribute('class');
                                    if (!spanClass.includes('check_answer')) spanTag.parentElement.click();
                                    await new Promise(r => setTimeout(r, 500));
                                }
                            }
                            break;
                        }
                        case '判断题': {
                            // answers = answers == '对' ? 'A' : 'B';
                            //遍历这个题目dom的所有选项dom，收集正确答案，放到数组中
                            let answerTmpArr = timuDom.querySelectorAll('.stem_answer span');
                            answerTmpArr.forEach(spanTag => {
                                //获取一个选项dom的值
                                let optionKey = trim(handleImgs(spanTag.innerText));
                                if (optionKey == answers) {
                                    let spanClass = spanTag.getAttribute('class');
                                    if (!spanClass.includes('check_answer')) spanTag.parentElement.click();
                                }
                            });
                            break;
                        }
                        case '填空题': {
                            //遍历这个题目dom的所有选项dom，收集正确答案，放到数组中
                            let textareaList = timuDom.querySelectorAll('.stem_answer .Answer .divText .textDIV textarea');

                            if (textareaList.length == 0) {
                                break;
                            }

                            if (textareaList.length == 1) {
                                let fillContent = answers.split('|').join('；');
                                let textareaTag = textareaList[0];
                                let id = textareaTag.getAttribute('id');
                                UE.getEditor(id).setContent(fillContent);
                                let numId = id.replace('answerEditor', '');
                                let parentId = numId.slice(0, -1);
                                saveQuestion(parentId, numId);
                                break;
                            }

                            if (textareaList.length > 1) {
                                let answerArr = answers.split('|');
                                for (let i = 0; i < textareaList.length; i++) {
                                    let textareaTag = textareaList[i];
                                    let id = textareaTag.getAttribute('id');
                                    let fillContent = answerArr[i];
                                    if (!fillContent) fillContent = '';
                                    UE.getEditor(id).setContent(fillContent);
                                    let numId = id.replace('answerEditor', '');
                                    let parentId = numId.slice(0, -1);
                                    saveQuestion(parentId, numId);
                                    await new Promise(r => setTimeout(r, 1000));
                                }

                                break;
                            }
                        }
                        case '简答题': {
                            //遍历这个题目dom的所有选项dom，收集正确答案，放到数组中
                            let textareaTag = timuDom.querySelector('.subEditor textarea');
                            if (!(textareaTag && answers)) break;
                            let id = textareaTag.getAttribute('id');
                            UE.getEditor(id).setContent(answers);
                            let numId = id.replace('answer', '');
                            saveQuestion(numId, numId);
                            break;
                        }
                        default: {
                            throw new Error('不支持的题目类型');
                            break;
                        }
                    }

                    await new Promise(r => setTimeout(r, 2000));
                }
            }, questionList);
        } catch (error) {
            await infoLogger(`答题失败`);
            throw new Error('答题失败');
        }
    }

    if (taskObj.others.includes('手机端')) {
        await mainPage.evaluate(async questionList => {
            function handleImgs(s) {
                let imgEs = s.match(/(<img([^>]*)>)/gi);
                if (imgEs) {
                    for (let j = 0, k = imgEs.length; j < k; j++) {
                        let urls = imgEs[j].match(/http[s]?:\/\/(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+/),
                            url;
                        if (urls) {
                            url = urls[0].replace(/http[s]?:\/\//, '');
                            s = s.replaceAll(imgEs[j], url);
                        }
                    }
                }
                return s;
            }

            function trim(s) {
                return (
                    s
                        //删除多余字符串
                        .replace(/(<([^>]+)>)/gi, '') //去掉所有的html标记 <a> </a>
                        .replace(/^\d+[\.、]/, '') //删除所有1.、1、的内容
                        .replace(/\(.{3}\)/, '') //删除所有（xx题）的内容
                        .replaceAll('&nbsp;', '')
                        .replaceAll('\n', '') //删除所有的换行符
                        .replace(/\s{2,}/g, ' ') //删除两个及以上的空格，保留单个空格
                        .trim()
                );
            }

            function getLastTextNode(titElement) {
                // 获取所有子节点
                const childNodes = titElement.childNodes;

                // 过滤并保存文本节点
                const textNodes = Array.from(childNodes).filter(node => node.nodeType === Node.TEXT_NODE && node.nodeValue.trim() !== '');

                // 获取最后一个文本节点并返回其修剪后的内容
                if (textNodes.length > 0) {
                    return textNodes[textNodes.length - 1].nodeValue.trim();
                }

                return '';
            }

            function singleSelect(ele) {
                // var $ele = $(ele);
                $(ele).addClass('on').siblings().removeClass('on');
                var answer = $(ele).attr('name');
                var questionId = operatioQuestionId(ele);
                $('#answer' + questionId).val(answer);
                fireAnswerChangeEvent(ele);
            }

            function multiSelect(ele) {
                if (!checkEvaluationQuesNum(ele)) {
                    return;
                }
                var questionId = operatioQuestionId(ele);
                $(ele).toggleClass('on');
                var answer = [];
                var questionWrap = $(ele).parents('.questionWrap');
                questionWrap.find('.mulChoice').each(function () {
                    if ($(this).hasClass('on')) {
                        answer.push($(this).attr('name') || '');
                    }
                });
                var answerStr = answer.join('');
                var randomOptions = $('#randomOptions').val();
                answerStr = randomOptions ? sortMultiAnswer(answerStr) : answerStr;
                $('#answers' + questionId).val(answerStr);
                fireAnswerChangeEvent(ele);
            }

            //生成一个唯一id  #fanyaMarking > div.marking_content.ans-cc > div.mark_table > div.whiteDiv > div.questionLi
            function generateUniqueId() {
                return 'id-' + new Date().getTime().toString(36) + '-' + Math.random().toString(36).substr(2, 9);
            }

            // #ext-gen1001 > div.answerMain.previewPage > div > div > div.questionWrap
            let TimuList = document.querySelectorAll('#ext-gen1001 > div.answerMain.previewPage > div > div > div.questionWrap');
            for (let i = 0; i < TimuList.length; i++) {
                let timuDom = TimuList[i];

                // 题目内容
                let titDiv = timuDom.querySelector(':scope>form>div.tit');
                let questionFull = getLastTextNode(titDiv);
                let content = trim(handleImgs(questionFull));

                //题目类型
                let type = timuDom.querySelector(':scope>form>div.tit>h3').innerHTML;
                type = type.match(/^(.*?)（.*?分）$/)[1];

                //获取题目id
                let questionId = timuDom.getAttribute('data');

                //查找答案
                let answers;
                questionList.forEach(questionObj => {
                    if (questionObj.id.slice(21) === questionId && questionObj.answers) {
                        answers = questionObj.answers;
                    }
                });
                if (!answers) {
                    continue;
                }

                //2.遍历这个题目dom的所有选项dom
                switch (type) {
                    case '单选题': {
                        //遍历这个题目dom的所有选项dom，收集正确答案，放到数组中
                        let answerTmpArr = timuDom.querySelectorAll(':scope>form>div.answerList');
                        answerTmpArr.forEach(divTag => {
                            let spanTag = divTag.querySelector('span');
                            //获取一个选项dom的值
                            let optionKey = trim(handleImgs(spanTag.innerText));
                            if (optionKey == answers) {
                                //如果包含check_answer 这个css样式说明已经选择了
                                let domClass = divTag.getAttribute('class');
                                // 这里不是click 而是tap 但是怎么模拟都不行，最后直接复制源码
                                if (!domClass.includes('on')) singleSelect(divTag);
                            }
                        });
                        break;
                    }
                    case '多选题': {
                        //遍历这个题目dom的所有选项dom，收集正确答案，放到数组中
                        let answerTmpArr = timuDom.querySelectorAll(':scope>form>div.answerList');
                        for (let j = 0; j < answerTmpArr.length; j++) {
                            let divTag = answerTmpArr[j];
                            let spanTag = divTag.querySelector('span');
                            let optionKey = trim(handleImgs(spanTag.innerText));
                            if (answers.includes(optionKey)) {
                                //如果包含check_answer 这个css样式说明已经选择了
                                let domClass = divTag.getAttribute('class');
                                if (!domClass.includes('on')) {
                                    multiSelect(divTag);
                                }
                            }
                            await new Promise(r => setTimeout(r, 1000));
                        }
                        break;
                    }
                    case '判断题': {
                        answers = answers == '对' ? 'A' : 'B';
                        //遍历这个题目dom的所有选项dom，收集正确答案，放到数组中
                        let answerTmpArr = timuDom.querySelectorAll(':scope>form>div.answerList');
                        answerTmpArr.forEach(divTag => {
                            let spanTag = divTag.querySelector('span');
                            let optionKey = trim(handleImgs(spanTag.innerText));
                            if (optionKey == answers) {
                                let domClass = divTag.getAttribute('class');
                                if (!domClass.includes('on')) singleSelect(divTag);
                            }
                        });
                        break;
                    }
                    case '填空题': {
                        //遍历这个题目dom的所有选项dom，收集正确答案，放到数组中
                        let textareaList = timuDom.querySelectorAll('.stem_answer .Answer .divText .textDIV textarea');
                        let answerTmpArr = answers.split('#');
                        textareaList.forEach((textareaTag, index) => {
                            if (!(textareaTag && answerTmpArr[index])) return;
                            let id = textareaTag.getAttribute('id');
                            UE.getEditor(id).setContent(answerTmpArr[index]);
                        });
                        break;
                    }
                    case '简答题': {
                        //遍历这个题目dom的所有选项dom，收集正确答案，放到数组中
                        let textareaTag = timuDom.querySelector('.stem_answer .eidtDiv textarea');
                        if (!(textareaTag && answers)) return;
                        let id = textareaTag.getAttribute('id');
                        UE.getEditor(id).setContent(answers);
                        break;
                    }
                }

                await new Promise(r => setTimeout(r, 1000));
            }
        }, questionList);
    }
}

module.exports = saveQuestion;
