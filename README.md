# 自动刷课与答题系统

## 启动参数

| 参数   | 描述         | 默认值           |
| ------ | ------------ | ---------------- |
| `CPUS` | 任务线程数量 | 当前设备的线程数 |

## 平台功能说明

### 国家开放大学
- **跳过网课**：直接标记为完成而不实际观看
- **预跑**：处理测试的一种逻辑选择

### 继续教育在线
- **跳过网课**：包含作业，因为他们在一起完成的
- **跳过考试**：忽略考试任务
- **跳过离线作业**：不处理离线作业
- **强制考试**：忽略掉最大考试限制
- **最大考试次数<7>**：括号内填写具体值
- **补考**：只完成补考，其余考试会忽略
- **查学费**：必须指定学年和学期，不然结果不准

### 弘成教育
- **人脸识别**：考试采用人脸识别逻辑，否则默认认为不需要人脸识别

### 柠檬文采学堂
- **内置**：采用试卷返回API数据中的答案当作答案，但有时返回答案不准确，不选内置则采用传统主动搜题

### 学习通
- **手机端**：采用手机端的url进行考试，否则默认采用电脑端
- **人脸识别**：考试的时候走人脸识别逻辑
- **跳过网课**：直接标记为完成
- **跳过考试**：不处理考试任务
- **强制重考**：收集考试列表时，会把"待批阅"状态的考试科目也添加进来
- **直接考试**：跳过等待随机时间，直接进入考试
- **独立IP**：刷课的时候采用独立IP
- **跳过防沉迷**：24小时刷课

## 更新日志

### 学习通

- **2023-03-05**
  - 视频时间延长：每个视频段落从60秒改为70秒
  - 作业时间延长：模拟正常做作业时间5-10分钟
  - 夜间休息：22:50-08:05休息，不刷课

- **2023-02-28**
  - 更新文档类型的API结果处理逻辑
  - 增加'read'课件类型

- **2023-10-13**
  - 获取课程列表可以考虑用手机端页面获取
  - doPcWorkId.js获取题目选项时添加了trim()，避免空格问题
  - 注释掉收集题目的逻辑

- **2023-10-04**
  - pageTools替换courseTools
  - 进入任务页面的人脸识别添加了手机agent
  - 重构了runCourseModule
  - doWork完成一半

### 继续教育在线

- **2024-02-18**
  - 考试增加答题间隔1秒
  - 答案为"略""言之成理即可得分。"的不收集，而是重新查找答案
  - 删除用题目自带答案的逻辑
  - 更改收集题目逻辑，避免重复收集

- **2024-10-18**
  - 服务器不返回视频时长，必须通过url手动获取
  - 主观题作业一律回答'done'
  - 修改获取视频信息失败间隔为1分钟
  - 收集题目添加add_time
  - 添加容错处理：submitAnswer和getCellInfo

- **2024-10-13**
  - handleQuestion中对判断题逻辑进行修复

- **2024-10-04**
  - 修复考试列表课程过滤问题
  - 重构答题逻辑

- **2024-09-27**
  - 提交试卷时间添加随机值(60-180)，避免多任务同时交卷
  - handleQuestion更名为formatQuestion，更符合语义化
  - 统一了handleAnswer、handleExamAnswer、handleTkExamAnswer三个函数的参数

- **2024-09-26**
  - 修复网课作业填空题问题
  - 增加任务时增加课程过滤支持taskObj.coursename
  - 处理题目时添加coursename字段

- **2024-09-19**
  - 安徽继续教育在线模块更新

- **2025-05-23**
  - 视频模块增加一比一刷课
  - 修复视频答题逻辑

### 国家开放大学

- **2023-12-09**
  - 国开大改

- **2023-11-07**
  - 国开大改

- **2023-10-27**
  - 修复拦截，自动重置
  - useragent采用自动生成
  - 拦截时不直接等待promise，避免无限等待
  - 打开作业页面增加重试
  - 重写收集题库函数
  - 完善作业状态判断逻辑
  - 优化handleImgs函数

- **2023-10-23**
  - 论坛类课件用dom完成
  - 增加课程反沉迷功能
  - 课程module用拦截获取

- **2023-10-22**
  - 更新登录逻辑
  - 优化匹配题目的收集
  - 增加完形填空题

- **2023-10-21**
  - 把打开课件页面及完成课件等待全放到课件逻辑
  - 用主动拦截方式判断课件是否完成
  - 更新讨论课件逻辑，增加主动发帖

- **2023-10-18**
  - 移除courseTools.js，用pageTools.js代替
  - 用utils/axiosIns.js
  - 添加课程过滤
  - 登录条件用url判断，不用页面元素判断
  - 注释掉内存监控
  - 把api中的getAgent提取到utils作为公共工具
  - 增加多个课程过滤功能，用"-"作为分隔符

### 弘成教育

- **2023-09-26**
  - 用pageTools代替courseTools
  - 修改元素转base64编码逻辑
  - 增加任务时增加课程过滤支持taskObj.coursename
  - 把课程和考试的公共逻辑全部移动到utils目录
  - 安装缺少的模块puppeteer-extra-plugin-anonymize-ua
  - 修复进入考试界面失败问题
  - 增加查看考试后试卷的API

### 柠檬文采学堂

- **2023-10-13**
  - 人脸识别增加自动获取官方图片

- **2023-10-10**
  - 重构代码
  - 修复考试考前承诺API

- **2023-10-04**
  - 增加课程名称过滤
  - 重构答题逻辑

- **2023-09-27**
  - endModule等待30秒，因为成绩更新有延迟
  - 修复bug：page.waitForTimeout改为new Promise((r) => setTimeout())

- **2023-09-06**
  - 文采学堂考试更新

### 系统框架/公共功能

- **2023-10-29**
  - **柠檬文采**：shapr降级，不然pkg不兼容
  - **solveQuestion**：修复matchAnswerWithLocal.js中sort改变原数组的问题
  - **ws**：增加返回客户端任务列表功能

- **2023-10-23**
  - **ws客户端**：增加心跳机制代替close事件
  - **代码混淆**：用保留字替代反混淆

- **2023-10-22**
  - **框架**：增加混淆功能和授权检测

- **2023-10-21**
  - **sequelize**：增加错误重试和连接池

- **2023-10-18**
  - **getAnswerFromLocal**：增加条件选配
  - **getMainPage.js**：手动添加代理，避免国开被反爬
  - 每个模块开始前都增加随机延迟，避免同时操作

- **2023-10-13**
  - **AI匹配题目**：降低颗粒度，重构getAnswerFromAiDian，删除deepSeekAi.js和sonnetCluadeAi.js

- **2023-10-11**
  - **主进程启动Bug修复**：删除数据时where和truncate不能同时使用
  - **增加线程字段**：心跳时间(30分钟发送)、上次登录IP、上次登录地址
  - **cluster**：增加worker_id字段和重置进程功能
  - **pcId**：重新设计算法

- **2023-10-10**
  - **pkg打包**：增加授权机制
  - **增加命令引导参数**：进程数CPUS
  - **ws消息处理**：分类型wsType：runTask, delWorker, addWorker
  - **适配pkg**：移除所有path.resolve()，改为静态路径
  - **重构ws通讯结构**：增加user字段
  - **bank数据表**：增加add_time字段方便调试
  - **matchAnswer.js**：修改多选题匹配逻辑，匹配选项前先排序
  - **提取公共代码**：puppeteer实例创建单独提取

- **2023-10-04**
  - **通用**：统一设置所有平台浏览器视口和窗口尺寸为1920x1080，防止爬虫检测
  - **重构搜题模块**：
    - 搜题只用type和content两个字段
    - 收集题库移动到爱点
    - 废弃courseid字段，添加course_name字段
    - 本地题库搜题返回列表，不再是单一题目
    - 把options和answers_content字段由json改为varchar

- **2023-09-27**
  - **utils/axiosIns.js**：超时时间由10秒改为30秒

- **2023-09-21**
  - **环境变量DEV**：控制mysql以及websocket URL
  - **增加更新标记**：确保自动部署顺利完成
  - **同步数据库更新**：增加year、term、others字段

- **2023-09-19**
  - 重构axiosIns.js
  - 重构ai模块
  - 重构solveQuestion模块
  - 重构bank数据库
  - 重构sequelize模块

- **2023-09-06**
  - **通过环境变量传递启动参数**：增加MYSQL_HOST
  - **学习通考试手机端更新**

- **2023-08-30**
  - **通过环境变量传递启动参数**：CPUS、WEBSOCKET_SERVER_URL
  - **修改pcId生成方式**
  - **更改Dockerfile**：使用ghcr.io/puppeteer/puppeteer:latest镜像

- **2023-08-25**
  - 任务模块转移完成
  - WebSocket客户端搭建完成

## 待办事项

- 把所有模块的代理逻辑抽取到utils/httpProxy.js
- 把所有模块的第三方验证码逻辑抽取到utils/validate.js
- 所有模块处理题目时添加coursename字段（继续教育已完成）
- 所有模块搜索本地题库时只用(platform,type,content)三个字段（继续教育已完成）
- 所有模块pageTools替换courseTools和examTools（继续教育已完成）
- 考虑增加警告信息，保存那些不至于终止整个任务的错误信息
- 把考试和网课的错误日志error改为varchar类型

| 班级 | 学号 | 姓名 | 专业 | 成绩 | 状态 |
|---|---|---|---|---|---|
| 23级数工高(升)3洲 | 2023313045 | 陈威 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313046 | 付浩 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313047 | 武攀 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313048 | 向俊杰 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313049 | 高杨 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313050 | 付天 | 工商管理 | 及格 | ✓ |
| 23级数工高(升)3洲 | 2023313051 | 田雪 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313052 | 韩雪 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313053 | 付兴增 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313054 | 李嘉辉 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313055 | 刘江 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313056 | 杜一鸣 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313057 | 甘露 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313058 | 王霄 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313059 | 危可欣 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313060 | 叶真真 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313061 | 徐泽宇 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313062 | 曹钰涵 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313063 | 万浪 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313064 | 王梓涵 | 工商管理 | 中等 | ✓ |
| 23屏工高(升)3洲 | 2023313065 | 万伟 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313066 | 段睿 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313067 | 付艺 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313068 | 徐佳慧 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313069 | 朱洋 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313070 | 余梦婷 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313071 | 付欢 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313072 | 张涵 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313073 | 杜雪 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313074 | 陈功亮 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313075 | 覃见 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313076 | 许瑞鹏 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313077 | 杨红玛 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313078 | 陈国庆 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313079 | 付运 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313080 | 冷然 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313081 | 王力宏 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313082 | 万家乐 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313083 | 廖世豪 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313084 | 程小懒 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313085 | 赵雪健 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313086 | 余皓月 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313087 | 刘洋 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313088 | 古平举 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313089 | 向国庆 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313090 | 付杰 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313091 | 刘洋 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313092 | 段文静 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313093 | 万巧 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313094 | 余文琴 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313095 | 李依玲 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313096 | 陈杨 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313097 | 王丹 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313098 | 苏兴邦 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313099 | 王心怡 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313100 | 许雪 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313101 | 朱爱群 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313102 | 甘雨洁 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313103 | 陈江 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313104 | 万献华 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313105 | 甘智超 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313106 | 余尚阳 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313107 | 杨文涛 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313108 | 陈凌峰 | 工商管理 | 中等 | ✓ |
| 23屏工高(升)3洲 | 2023313109 | 徐园梦 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313110 | 李晶 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313111 | 陈祖佳 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313112 | 刘璐 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313113 | 马其士 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313114 | 万俊宇 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313115 | 向启航 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313116 | 孔文华 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313117 | 徐佳乐 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313118 | 苏平 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313119 | 程玉婷 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313120 | 黄国栋 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313121 | 李伟 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313122 | 程豪杰 | 工商管理 | 中等 | ✓ |
| 23屏工高(升)3洲 | 2023313123 | 付豪 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313124 | 陈川 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313125 | 李烂阳 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313126 | 李晶 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313127 | 王紫冉 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313128 | 孙高 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313129 | 欧杨 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313130 | 古博辉 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313131 | 周一丁 | 工商管理 | 及格 | ✓ |
| 23级数工高(升)3洲 | 2023313132 | 陈世诚 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313133 | 程奥 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313134 | 江宏杰 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313135 | 李杰 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313136 | 杨振宇 | 工商管理 | 中等 | ✓ |
| 23屏工高(升)3洲 | 2023313137 | 李勉 | 工商管理 | 中等 | ✓ |
| 23屏工高(升)3洲 | 2023313138 | 李梦琪 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313139 | 苏灿月 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313140 | 叶贝诺 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313141 | 冷鑫磊 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313142 | 江青山 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313143 | 程攀 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313144 | 郭津 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313145 | 吴湿 | 工商管理 | 中等 | ✓ |
| 23屏工高(升)3洲 | 2023313146 | 桂红 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313147 | 吴雨轩 | 工商管理 | 中等 | ✓ |
| 23屏工高(升)3洲 | 2023313148 | 张科 | 工商管理 | 不及格 | ✓ |
| 23屏工高(升)3洲 | 2023313149 | 李强 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313150 | 谈江 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313151 | 李江户 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313152 | 黄宇 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313153 | 方队 | 工商管理 | 中等 | ✓ |
| 23屏工高(升)3洲 | 2023313154 | 毛峰 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313155 | 段浩宇 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313156 | 陈玉 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313157 | 程永 | 工商管理 | 及格 | ✓ |
| 23级数工高(升)3洲 | 2023313158 | 李吉星 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313159 | 马松 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313160 | 王涛 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313161 | 程超群 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313162 | 曹高远 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313163 | 曹宇东 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313164 | 余东 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313165 | 陈梦想 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313166 | 李太辉 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313167 | 刘俊 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313168 | 陈冬 | 工商管理 | 中等 | ✓ |
| 23屏工高(升)3洲 | 2023313169 | 李仁浩 | 工商管理 | 中等 | ✓ |
| 23屏工高(升)3洲 | 2023313170 | 向宇 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313171 | 万代 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313172 | 王顺英 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313173 | 赵泽 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313174 | 李洪宇 | 工商管理 | 及格 | ✓ |
| 23级数工高(升)3洲 | 2023313175 | 汪中 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313176 | 索龙源 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313177 | 胡一鸣 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313178 | 王金宇 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313179 | 李壮 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313180 | 冷晨 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313181 | 王重阳 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313182 | 李文 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313183 | 陈怡文 | 工商管理 | 中等 | ✓ |
| 23屏工高(升)3洲 | 2023313184 | 陈华梅 | 工商管理 | 及格 | ✓ |
| 23级数工高(升)3洲 | 2023313185 | 向东东 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313186 | 朱杨 | 工商管理 | 中等 | ✓ |
| 23屏工高(升)3洲 | 2023313187 | 李君豪 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313188 | 陈松林 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313189 | 秦军 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313190 | 李兰斗 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313191 | 朱贝贝 | 工商管理 | 及格 | ✓ |
| 23级数工高(升)3洲 | 2023313192 | 梁帅 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313193 | 刘恒 | 工商管理 | 中等 | ✓ |
| 23屏工高(升)3洲 | 2023313194 | 马可欣 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313195 | 方马 | 工商管理 | 中等 | ✓ |
| 23屏工高(升)3洲 | 2023313196 | 王振 | 工商管理 | 中等 | ✓ |
| 23级数工高(升)3洲 | 2023313197 | 陈云飞 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313198 | 方平 | 工商管理 | 良好 | ✓ |
| 23级数工高(升)3洲 | 2023313199 | 宋凯 | 工商管理 | 良好 | ✓ |
| 23屏工高(升)3洲 | 2023313200 | 向芸芸 | 工商管理 | 中等 | ✓ |
