let api = require('../../utils/api.js');

async function getCourseListModule(infoLogger, globalStore, taskObj) {
    
    // 随学随考
    let courseListRes;
    try {
        // 'https://main.ahjxjy.cn/studentstudio/ajax-exam-sxskTable'
        courseListRes = await api.getExamList(globalStore, taskObj);
        courseListRes.list.forEach(item => (item.type = 'sxsk'));
    } catch (error) {
        await infoLogger(`获取随学随考课程列表失败`, 'red'); //记录日志
        throw error; //抛出错误，结束进程池
    }

    // 学校统考
    let onlineListRes;
    try {
        // 'https://main.ahjxjy.cn/studentstudio/ajax-exam-tkOnlineTable'
        onlineListRes = await api.getOnlineExamList(globalStore, taskObj);
        onlineListRes.list.forEach(item => (item.type = 'tk'));
    } catch (error) {
        await infoLogger(`获取学校统考课程列表失败`, 'red'); //记录日志
        throw error; //抛出错误，结束进程池
    }

    
    let bkSxskListRes = { list: [] };
    if (globalStore.taskObj.others.includes('补考')) {
        // 补考随学随考
        try {
            // 'https://main.ahjxjy.cn/studentstudio/ajax-bkExam-bkSxskTable'
            bkSxskListRes = await api.getBkSxskExamList(globalStore, taskObj);
            bkSxskListRes.list.forEach(item => (item.type = 'bkSxsk'));
        } catch (error) {
            await infoLogger(`获取补考课程列表失败`, 'red'); //记录日志
            throw error; //抛出错误，结束进程池
        }
    }

    // 补考学校统考（目前还没遇到，暂时不考虑）
    // let bkTkListRes;
    // try {
    //     // 'https://main.ahjxjy.cn/studentstudio/ajax-bkExam-bkTkTable'
    //     bkTkListRes = await api.getBkTkExamList(globalStore, taskObj);
    //     bkTkListRes.list.forEach(item=>item.type='bk')
    // } catch (error) {
    //     await infoLogger(`获取补考学校统考课程列表失败`, 'red'); //记录日志
    //     throw error; //抛出错误，结束进程池
    // }

    // 汇总课程列表
    if (taskObj.coursename) {
        courseListRes.list = courseListRes.list.filter(item => item.courseName.includes(taskObj.coursename));
        onlineListRes.list = onlineListRes.list.filter(item => item.courseName.includes(taskObj.coursename));
        bkSxskListRes.list = bkSxskListRes.list.filter(item => item.courseName.includes(taskObj.coursename));
    }
    let arr = courseListRes.list.concat(onlineListRes.list, bkSxskListRes.list);
    // 根据课程名称筛选课程列表
    arr = arr.filter(item => item.isexpired == false);
    if (arr.length == 0) {
        return []
    }

    await infoLogger(
        `获取课程列表成功，随学随考共有${courseListRes.list.length}门课程，学校统考共有：${onlineListRes.list.length}门课程，补考共有：${bkSxskListRes.list.length}门课程`
    ); //记录日志

    return arr;
}

module.exports = getCourseListModule;
