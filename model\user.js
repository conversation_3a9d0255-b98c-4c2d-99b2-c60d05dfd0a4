const Sequelize = require('sequelize');
module.exports = function(sequelize, DataTypes) {
  return sequelize.define('user', {
    user_id: {
      autoIncrement: true,
      type: DataTypes.INTEGER,
      allowNull: false,
      primaryKey: true,
      comment: "用户id"
    },
    user_name: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "用户名",
      unique: "user_name"
    },
    user_password: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "密码"
    },
    user_role_id: {
      type: DataTypes.INTEGER,
      allowNull: true,
      comment: "角色id",
      references: {
        model: 'role',
        key: 'role_id'
      }
    },
    user_email: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "邮箱",
      unique: "user_email"
    },
    user_avatar: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "头像 "
    },
    user_signup_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "注册时间"
    },
    user_signup_ip: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "注册ip"
    },
    user_signup_adress: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "注册地址"
    },
    user_signin_time: {
      type: DataTypes.DATE,
      allowNull: true,
      comment: "上次登录时间\r\n如‘2019-06-15 21:05:33’"
    },
    user_signin_ip: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "上次登录ip"
    },
    user_signin_adress: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "上次登录地址"
    },
    user_signin_system: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "上次登录操作系统"
    },
    user_signin_browser: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "上次登录浏览器版本"
    },
    user_desc: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "用户描述"
    },
    user_website: {
      type: DataTypes.STRING(255),
      allowNull: true,
      comment: "用户个人网站"
    }
  }, {
    sequelize,
    tableName: 'user',
    timestamps: false,
    indexes: [
      {
        name: "PRIMARY",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "user_id" },
        ]
      },
      {
        name: "user_name",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "user_name" },
        ]
      },
      {
        name: "user_email",
        unique: true,
        using: "BTREE",
        fields: [
          { name: "user_email" },
        ]
      },
      {
        name: "user_role_id",
        using: "BTREE",
        fields: [
          { name: "user_role_id" },
        ]
      },
    ]
  });
};
