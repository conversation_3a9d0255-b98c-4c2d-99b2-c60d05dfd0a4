const axios = require('axios');
const fs = require('fs');
let path = require('path');

const url = 'http://localhost:8000/ocr';
const imagePath = path.join(__dirname, 'test.jpg');
const imageBuffer = fs.readFileSync(imagePath);
const base64Image = imageBuffer.toString('base64');
// console.log(base64Image)
const data = {
    image: base64Image,
    probability: false,
    png_fix: false,
};

axios
    .post(url, data)
    .then(response => {
        console.log(response.data);
    })
