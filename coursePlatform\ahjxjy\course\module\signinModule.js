let pageTools = require('../../../utils/pageTools.js');

async function signinModule(infoLogger, mainPage, globalStore, taskObj) {
    //1.跳转到登录页面
    // await mainPage.goto('https://main.ahjxjy.cn/minlogin/html/home.html', {
    //     waitUntil: 'networkidle0',
    // });
    await pageTools.gotoWithRetry(mainPage, 'https://main.ahjxjy.cn/minlogin/html/home.html', { waitUntil: 'networkidle0' }, 3, infoLogger);

    //2.输入用户信息
    await mainPage.select('#indentity', '1'); //选择学生
    await mainPage.type('#account', taskObj.username); //输入用户名
    await mainPage.type('#password', taskObj.password); //输入密码

    if (!process.env.HAS_PERMISSION) {
        return;
    }

    //3.开始登录
    let signinHandle = await mainPage.$('#btnQuery'); //获取登录按钮
    await signinHandle.click(); //点击登录
    await new Promise(resolve => setTimeout(resolve, 1000)); //等待加载完成

    //判断是否登录成功，显示出来“学生空间按钮”，如果不出现这个界面，说明密码错误
    try {
        await mainPage.waitForSelector('#btnaID', {
            timeout: 3000,
        });
    } catch (error) {
        await infoLogger(`登录失败：${error.message}`, 'red'); //记录日志
        throw new Error(error); //抛出错误，结束进程池
    }
    await infoLogger(`登录成功`, 'green'); //记录日志

    //4.通过URL的方式进入“学生空间”
    // await mainPage.goto('https://main.ahjxjy.cn/studentstudio/', {
    //     waitUntil: 'networkidle0',
    // });
    await pageTools.gotoWithRetry(mainPage, 'https://main.ahjxjy.cn/studentstudio/', { waitUntil: 'networkidle0' }, 3, infoLogger);
    //5.选择学校
    let schoolCode = await mainPage.evaluate(taskObj => {
        let aTagArr = document.querySelectorAll('#menulayer_2 > a');
        let schoolCode;
        aTagArr.forEach(item => {
            if (item.textContent == taskObj.schoolname) {
                schoolCode = item.getAttribute('data-schoolcode');
            }
        });
        return schoolCode;
    }, taskObj);
    if (!schoolCode) {
        await infoLogger('获取学校代码出错', 'red'); //记录日志
        throw new Error('获取学校代码出错'); //抛出错误，结束进程池
    } else {
        await infoLogger(`学校代码：${schoolCode}`, 'green'); //记录日志
    }
    globalStore.schoolCode = schoolCode;
    // let mainUrl = `https://main.ahjxjy.cn/studentstudio/exam/sxsk?schoolCode=${globalStore.schoolCode}`;
    let mainUrl = `https://main.ahjxjy.cn/studentstudio/course/studying?schoolCode=${globalStore.schoolCode}`;
    // await mainPage.goto(mainUrl, { waitUntil: 'networkidle0' });
    await pageTools.gotoWithRetry(mainPage, mainUrl, { waitUntil: 'networkidle0' }, 3, infoLogger);
    await new Promise(r => setTimeout(r, 3000));

    //获取页面cookie
    let cookieStr = await pageTools.getPageCookies(mainPage);
    globalStore.cookieStr = cookieStr;
}

module.exports = signinModule;
