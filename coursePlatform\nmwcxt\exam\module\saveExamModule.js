let { encryptData, decryptData, decodeRequestData, encodeRequestData } = require('../../utils/aesF.js');
let api = require('../../utils/api.js');
let solveQuestion = require('../../../solveQuestion/index.js');
let { handleImgs } = require('../../../solveQuestion/format.js');
let { v4 } = require('uuid');
let pageTools = require('../../../utils/pageTools.js');
let {trim,convertHttpToHttps}=require('../../utils/tools.js');

// 结合 handleImgs 和 trim，处理题目内容
function handleContent(str) {
    let res = trim(handleImgs(str)).slice(0, 3000);
    res = convertHttpToHttps(res);
    return res;
}

// 平台升级，如果选项乱序，那么选择题的optionContent就不是正确答案了
async function saveExamModule(globalStore, infoLogger) {
    let { itemInfoList, userInfoVo, examModuleObj } = globalStore;
    //保存题目
    let saveItem = [];
    itemLoop: for (let questionObj of itemInfoList) {
        //初始化saveItemObj
        let saveItemObj = {
            examScoreDetailId: questionObj.examScoreDetailId,
            itemId: questionObj.itemId,
            itemType: questionObj.itemType,
            saveItemDetailVoList: [],
        };

        // 直接用API返回的内置答案答题
        if (globalStore.others.includes('内置')) {
            //处理答案
            questionObj.itemAnswer.forEach(item => {
                let saveItemDetailObj = {
                    optionContentKey: item.myOptionKey,
                    optionContent: item.optionContent,
                    myOption: item.optionContent,
                    imgUrl: '',
                    score: '0.0',
                };
                saveItemObj.saveItemDetailVoList.push(saveItemDetailObj);
            });
        }

        // 不用API返回的答案，自己搜题
        if (!globalStore.others.includes('内置')) {
            // 选择题特殊处理，因为答案可能是错的,3是单选题，4是多选题
            if ('34'.includes(questionObj.itemType)) {
                // 格式化题目
                //确定题目类型
                let questionType = {
                    1: '填空题',
                    2: '简答题',
                    3: '单选题',
                    4: '多选题',
                }[questionObj.itemType];

                //处理题目选项
                let optionNodes = questionObj.optionNodes;
                let options = optionNodes.map(questionObj => `${questionObj.option}:${handleContent(questionObj.optionContent)}`);

                if (Array.isArray(questionObj.optionNodes) && questionObj.optionNodes.length == 2) {
                    questionType = '判断题';
                    options = ['A:对', 'B:错'];
                }

                // 格式化题目
                let formatQuestion = {
                    id: v4(),
                    type: questionType,
                    options: options,
                    content: handleContent(questionObj.itemName),
                    platform: '柠檬文采学堂',
                    courseName: globalStore.courseName,
                    commit: '',
                };

                // 搜索答案
                let answers = await solveQuestion(formatQuestion);

                if (!answers) {
                    // console.log('没有找到答案')
                    // console.log(formatQuestion)
                    continue;
                }
                formatQuestion.answers = answers;

                //处理答案
                questionObj.itemAnswer.forEach(item => {
                    let saveItemDetailObj = {
                        optionContentKey: item.myOptionKey,
                        optionContent: item.optionContent,
                        myOption: answers,
                        imgUrl: '',
                        score: '0.0',
                    };
                    saveItemObj.saveItemDetailVoList.push(saveItemDetailObj);
                });
            }

            // 非选择题还是直接用API返回的内置答案
            if (!'34'.includes(questionObj.itemType)) {
                //处理答案
                questionObj.itemAnswer.forEach(item => {
                    let saveItemDetailObj = {
                        optionContentKey: item.myOptionKey,
                        optionContent: item.optionContent,
                        myOption: item.optionContent,
                        imgUrl: '',
                        score: '0.0',
                    };
                    saveItemObj.saveItemDetailVoList.push(saveItemDetailObj);
                });
            }
        }

        saveItem.push(saveItemObj);
    }
    //随机产生一个2-7之间的随机数，包含2和7
    // let examCount = Math.floor(Math.random() * 6 + 2);
    let examCount = pageTools.getRandomInt(1, 5);
    //删除saveItem数组中随机位置的examCount个元素
    for (let i = 0; i < examCount; i++) {
        let randomIndex = Math.floor(Math.random() * saveItem.length);
        saveItem.splice(randomIndex, 1);
    }
    await infoLogger(`共回答${saveItem.length}道题目，预计分数：${saveItem.length * 2}分`, 'green');

    //向服务器提交保存题目请求
    let examSaveObj = { user_info: JSON.stringify(userInfoVo), exam_id: examModuleObj.examId, save_item: JSON.stringify(saveItem) };
    let examSaveRes = await api.examSaveAction(examSaveObj);

    //处理服务器返回结果
    if (examSaveRes.code === 1000) {
        await infoLogger('试题保存成功', 'green');
    } else {
        await infoLogger(`试题保存失败,${JSON.stringify(examSaveRes)}`, 'red');
        throw new Error('试题保存失败'); //结束进程池任务
    }
}

module.exports = saveExamModule;
