let { encryptData, decryptData, decodeRequestData, encodeRequestData } = require('../../utils/aesF.js');
let api = require('../../utils/api.js');

async function endModule(globalStore,taskObj, infoLogger) {
    let { examModuleDate,examUrl } = globalStore;
    //获取最终分数
    examModuleRes = await api.examInfo(examModuleDate, examUrl);
    examModuleStr = decryptData(examModuleRes.data);
    examModuleArr = JSON.parse(examModuleStr);
    examModuleObj = examModuleArr[0];

    await infoLogger(`最终分数为：${examModuleObj.finalScore}`, 'green');

    let finalResult = [];
    finalResult.push({ courseName: taskObj.coursename, progress: examModuleObj.finalScore });

    return finalResult;
}

module.exports = endModule;
