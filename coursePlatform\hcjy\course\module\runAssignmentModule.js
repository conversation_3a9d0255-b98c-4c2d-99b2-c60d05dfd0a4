let { handleQuestion, collectQuestion } = require('../../utils/handleQuestion');
let pageTools = require('../../../utils/pageTools.js');
let Model = require('../../../../config/sequelize.config');
let api = require('../../utils/api');

function convertToQueryString(obj) {
    const paperAnswerResultJSON = JSON.stringify(obj.paperAnswerResult);
    const encodedPaperAnswerResult = encodeURIComponent(paperAnswerResultJSON);

    const queryParams = [];
    for (const key in obj) {
        if (key !== 'paperAnswerResult') {
            queryParams.push(`${key}=${obj[key]}`);
        }
    }
    queryParams.push(`paperAnswerResult=${encodedPaperAnswerResult}`);

    return queryParams.join('&');
}

async function runAssignmentModule(infoLogger, mainPage, courseList, globalStore) {
    // 遍历课程
    for (let i = 0; i < courseList.length; i++) {
        let courseObj = courseList[i];

        let assignmentList = courseObj.assignmentList;
        await infoLogger(`开始课程<${courseObj.courseName}>的作业`, 'blue');
        // 遍历每个课程的作业
        for (let j = 0; j < assignmentList.length; j++) {
            let assignmentObj = assignmentList[j];
            let assignmentLogStr = `[课程${i + 1}/${courseList.length}--作业[${j + 1}/${assignmentList.length}]--${assignmentObj.activityName}]`;
            let submitCount = 1;

            // 判断是否超时
            let dateString = assignmentObj.endTime;
            // 先将字符串格式化为标准的 ISO 8601 时间格式
            let formattedDateString = dateString.replace(' ', 'T') + 'Z'; // 将空格替换为 'T'，并加上 'Z' 表示 UTC 时间
            // 创建一个 Date 对象
            let date = new Date(formattedDateString);
            // 获取时间戳（以毫秒为单位）
            let  timestamp = date.getTime();
            if(Date.now()>timestamp){
                await infoLogger(`${assignmentLogStr} 作业已经超时，跳过`,'gray')
                continue;
            }

            // //获取作业url
            // let getotsurlRes = await api.getotsurl(globalStore.cookieStr, assignmentObj);
            // let assignmentUrl = getotsurlRes.data;
            // await mainPage.goto(assignmentUrl, { waitUntil: "networkidle0" });
            // await new Promise((r) => setTimeout(r, 5000));
            // let temCookie = await courseTools.getPageCookies(mainPage);

            async function doAssignment() {
                await infoLogger(`${assignmentLogStr} 开始第${submitCount}次答题`);
                submitCount++;
                assignmentObj.answerCount++;
                // 这段注释的代码，放到里面会出问题，无法跳转到考试界面，没办法，移到最外面了
                // 获取作业url
                let getotsurlRes = await api.getotsurl(globalStore.cookieStr, assignmentObj);
                let assignmentUrl = getotsurlRes.data;

                // 进入作业url页面，获取作业页面的cookie
                await mainPage.goto(assignmentUrl, { waitUntil: 'networkidle0' });
                await new Promise(r => setTimeout(r, 5000));
                let temCookie = await pageTools.getPageCookies(mainPage);

                // console.log(`当前url：${mainPage.url()}，目标url:${assignmentUrl}，当前cookie:${temCookie}`)
                // await new Promise((r) => setTimeout(r, 500000));

                //获取试题列表 https://exam.chinaedu.net/oxer/app/ots/TestActivity/StartAnswerPaper status: 1
                let getQuestionListRes = await api.getQuestionList(temCookie, assignmentObj);

                //格式化题目
                let submitData = await handleQuestion(getQuestionListRes.data, courseObj.courseName,infoLogger);

                //回答问题，提交试卷
                let submitRes = await api.submitAssignment(temCookie, submitData);
                return submitRes;
            }

            async function assignment() {
                // 情况一：没有答题
                if (assignmentObj.answerCount == null) {
                    await infoLogger(`${assignmentLogStr} 还没开始，开始答题`);
                    //1.开始答题
                    let submitRes = await doAssignment();
                    // 更改状态
                    assignmentObj.score = submitRes.data.currentScore;
                    assignmentObj.recordID = submitRes.data.answerPaperRecordId;

                    // 3.重新答题
                    await assignment();
                }

                // 情况二：已经答题，成绩合格
                if (assignmentObj.score && assignmentObj.score * 1 >= 90) {
                    await infoLogger(`${assignmentLogStr}  已经完成，分数为：${assignmentObj.score}分，跳过`, 'green');
                    return;
                }

                // 情况三：已经答题，成绩不合格
                if (assignmentObj.score && assignmentObj.score * 1 < 90) {
                    await infoLogger(
                        `${assignmentLogStr}  分数为：${assignmentObj.score}分，不及格，重做,https://exam.chinaedu.net/oxer/page/ots/OTS-UniverDetail.html?AnswerId=${assignmentObj.recordID}`
                    );
                    if (submitCount >= 4) {
                        await infoLogger(`${assignmentLogStr}已经回答了${submitCount}次，还是不及格`, 'red');
                        globalStore.warningMessage += `${assignmentLogStr}，分数:${assignmentObj.score}`;
                        return;
                    }

                    // 不能直接进入答题界面，要先进入考试界面，才可以进入查看试卷界面
                    let getotsurlRes = await api.getotsurl(globalStore.cookieStr, assignmentObj);
                    if (getotsurlRes.code !== 1) {
                        await infoLogger(`${assignmentLogStr} 获取作业url失败 msg:${getotsurlRes.message}`);
                        return;
                    }

                    let assignmentUrl = getotsurlRes.data;

                    try {
                        //进入作业url页面，获取作业页面的cookie
                        await mainPage.goto(assignmentUrl, { waitUntil: 'networkidle0' });
                    } catch (error) {
                        await infoLogger(`${assignmentLogStr} assignmentUrl：${assignmentUrl} 进入考试界面失败 message：${error.message}`);
                        throw error;
                    }
                    await new Promise(r => setTimeout(r, 3000));
                    // let realUrl=mainPage.url()
                    // let temCookie = await courseTools.getPageCookies(mainPage);
                    // console.log('temCookie',temCookie)
                    // await new Promise((r) => setTimeout(r, 3000000000));
                    // console.log('assignmentUrl',assignmentUrl)

                    //进入作业url页面，获取作业页面的cookie
                    let pageUrl = `https://exam.chinaedu.net/oxer/page/ots/OTS-UniverDetail.html?AnswerId=${assignmentObj.recordID}`;
                    await mainPage.goto(pageUrl, { waitUntil: 'networkidle0' });
                    await new Promise(r => setTimeout(r, 3000));

                    // console.log('pageUrl',pageUrl)

                    let pageUrlCookieStr = await pageTools.getPageCookies(mainPage);
                    let paperRes = await api.viewPaper(pageUrlCookieStr, assignmentObj.recordID);
                    let collectResult = await collectQuestion(paperRes, courseObj.courseName);
                    await infoLogger(`收集${collectResult.collectCount}道题目，更新${collectResult.updateCont}道题目`);

                    // //进入作业url页面，获取作业页面的cookie
                    // await mainPage.goto(realUrl, { waitUntil: "networkidle0" });
                    // await new Promise((r) => setTimeout(r, 3000));
                    // let temCookie = await courseTools.getPageCookies(mainPage);
                    // console.log(`当前url：${mainPage.url()}，目标url:${realUrl}，当前cookie:${temCookie}`)
                    // await new Promise((r) => setTimeout(r, 3000000));

                    //1.开始答题
                    let submitRes = await doAssignment();

                    // 更改状态
                    assignmentObj.score = submitRes.data.currentScore;
                    assignmentObj.recordID = submitRes.data.answerPaperRecordId;

                    await assignment();
                }
            }

            await assignment();
        }
    }
}

module.exports = runAssignmentModule;
