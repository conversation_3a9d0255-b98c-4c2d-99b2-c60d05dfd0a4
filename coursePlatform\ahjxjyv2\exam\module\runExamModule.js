let api = require('../../utils/api.js');
let { formatQuestion, answerQusetion, collectQuestion } = require('../../utils/handleQuestion.js');
let pageTools = require('../../../utils/pageTools.js');

async function runExamModule(infoLogger, mainPage, examList, globalStore) {
    let storageObj = globalStore.storageObj;
    // 声明一个常量，允许答题次数

    for (let i = 0; i < examList.length; i++) {
        let examObj = examList[i];
        examObj.answerTimes = examObj.answerTimes * 1;
        let maxAnswerTimes = 7;

        // 首次判定是否完成，每次考试之后，还需要重新判定
        if (examObj.state == 1) {
            maxAnswerTimes = 5;
        }

        // 首次判定是否完成，每次考试之后，还需要重新判定
        if (examObj.score && examObj.score > 90) {
            await infoLogger(`[${i + 1}/${examList.length}] 考试已经完成,分数为${examObj.score}分 <${examObj.courseName}>`, 'green');
            continue;
        }
        // 首次判定答题次数
        if (examObj.answerTimes >= maxAnswerTimes) {
            await infoLogger(
                `[${i + 1}/${examList.length}] 答题次数超过${maxAnswerTimes}次，分数不够，当前分数为${examObj.score}分 <${
                    examObj.courseName
                }>，是否主观题未批阅：${examObj.state == 1 ? '是' : '否'}`,
                'red'
            );
            continue;
        }

        async function main() {
            // 获取题目列表
            let questionListRes;
            try {
                questionListRes = await api.getExamQuestionList(storageObj, examObj.courseId, examObj.id);
            } catch (error) {
                if (error.message.includes('未满足学习进度')) {
                    await infoLogger(`考试失败，未满足学习进度`, 'red');
                    return;
                } else {
                    await infoLogger(`考试失败，未知错误`, 'red');
                    throw error;
                }
            }
            // 格式化题目列表
            let questionList = formatQuestion(questionListRes, examObj.courseName);

            // 处理当前试卷状态questionListRes.state 1表示已完成为批阅 0表示考试中 2表示考试完成已批阅
            let examState;
            switch (questionListRes.state) {
                case 1: {
                    examState = '已完成未批阅';
                    break;
                }
                case 0: {
                    examState = '考试中';
                    break;
                }
                case 2: {
                    examState = '已完成已批阅';
                    break;
                }
                default: {
                    examState = '未知状态';
                    throw new Error(`未知考试状态：${questionListRes.state}`);
                    break;
                }
            }

            await infoLogger(
                `课程[${i + 1}/${examList.length}]：<${examObj.courseName}> 第${questionListRes.submitCount}次考试开始,当前分数为：${questionListRes.score}分，`
            );

            // 关于试卷状态questionListRes.state 1表示已完成为批阅 0表示考试中 2表示考试完成已批阅
            // 可以答题
            if (questionListRes.state == 0) {
                await infoLogger(`当前试卷状态为：${examState}，接下来开始答题`);

                // 查找答案，回答问题
                let submitCount = await answerQusetion(questionList, storageObj);
                await infoLogger(` 共有${questionList.length}道题目，回答${submitCount}道题目`);

                // 产生一个5-15的随机值
                let randomTime = pageTools.getRandomInt(5, 12);
                await infoLogger(`等待${randomTime}分钟后提交试卷`);
                await new Promise(r => setTimeout(r, randomTime * 60 * 1000));

                // 提交考试试卷
                await api.submitExam(storageObj, examObj.courseId, examObj.id);
                await infoLogger(`考试已经提交`);
                await main();
            }

            // 不可以答题
            if (!(questionListRes.state == 0)) {
                await infoLogger(`当前试卷状态为：${examState}，接下来开始收集题目`);
                // 收集题目
                let collectCount = await collectQuestion(questionList);
                await infoLogger(`共有${questionList.length}道题目，收集${collectCount}道题目`);

                if (questionListRes.score >= 95) {
                    await infoLogger(`考试已完成,分数:${questionListRes.score}`, 'green');
                    return;
                }

                if (questionListRes.submitCount >= maxAnswerTimes) {
                    await infoLogger(
                        `[${i + 1}/${examList.length}] 答题次数超过${maxAnswerTimes}次，分数不够，当前分数为${examObj.score}分 <${
                            examObj.courseName
                        }>，试卷状态：${examState}`,
                        'red'
                    );
                    return;
                }

                // 更改答题状态
                await api.redoExam(storageObj, examObj.courseId, examObj.id, questionListRes.ruleId);
                examObj.answerTimes++;

                // 重新获取题目
                await infoLogger(`接下来重新答题`);
                await main();
            }
        }

        await main();
    }
}

module.exports = runExamModule;
