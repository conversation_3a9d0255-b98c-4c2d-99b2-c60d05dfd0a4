let api = require("../../../utils/api.js");

async function handleClickValidate(mainPage, infoLogger) {
    async function main() {
        //把div标签变成base64
        let validateDivHandle = await mainPage.$("div[class^=geetest_box_wrap_]>div[class^=geetest_box_]");
        let base64Image = await validateDivHandle.screenshot({ type: 'jpeg', encoding: 'base64' });

        let boundingBox = await validateDivHandle.boundingBox();

        let positionRes;
        try {
            positionRes = await api.clickValidate(base64Image);
        } catch (error) {
            await infoLogger("服务器识别验证码失败", "red");
            throw Error("服务器识别验证码失败");
        }
        if (false) {
            positionRes = {
                code: 0,
                message: "",
                data: {
                    captchaId: "1311-0ca1f345-3504-44bc-8e58-4c18cc91e212",
                    captchaType: "1311",
                    recognition: "166,136|48,154|258,147|186,200",
                },
            };
        }

        let positionStr = positionRes.data.recognition;
        let positionArr = positionStr.split("|").map((pair) => pair.split(",").map(Number));

        for (let position of positionArr) {
            let x = position[0] + boundingBox.x;
            let y = position[1] + boundingBox.y;

            await mainPage.mouse.move(x, y, { steps: 20 });
            await new Promise((r) => setTimeout(r, Math.random() * 100 + 50)); // 随机化延迟时间
            await mainPage.mouse.down();
            await mainPage.mouse.up();
            await new Promise((r) => setTimeout(r, Math.random() * 1000 + 500)); // 随机化延迟时间
        }
        await mainPage.click("div.geetest_submit");
        await new Promise((r) => setTimeout(r, 10000));
    }


    await main();
    let validateDivHandle = await mainPage.$("div[class^=geetest_box_wrap_]>div[class^=geetest_box_]");
    if(validateDivHandle){
        await main();
    }
}

module.exports = handleClickValidate;