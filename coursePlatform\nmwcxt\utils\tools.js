exports.trim=function trim(s) {
    return (
        s
            //删除多余字符串
            .replace(/(<([^>]+)>)/gi, '') //删除html标签  <xxx>
            .replace(/\[参考答案：.*?\]/g, ' ')
            .replace(/\(分值：\d+\)/g, '') //删除 (分值：10)
            // .replace(/分值：\d/, '') //删除 分值：5
            .replaceAll('\n', '') //删除所有的换行符
            .replaceAll('&nbsp;', ' ')
            .replace(/\s{2,}/g, ' ') //删除两个及以上的空格，保留单个空格
            .trim()
    );
}


/**
 * 将字符串中的 http:// 替换为 https://
 * @param {string} str - 原始字符串
 * @return {string} - 替换后的字符串
 */
exports.convertHttpToHttps=function convertHttpToHttps(str) {
    // 使用正则，匹配所有 http://，替换为 https://
    return str.replace(/http:\/\//g, 'https://');
}



// const str = '变截面梁的主要优点是[参考答案：加工方便]、[参考答案：加工方便]、[参考答案：合理分配载荷]。';
// const newStr = str.replace(/\[参考答案：[^\]]*\]/g, ' ');
// console.log(newStr); 

