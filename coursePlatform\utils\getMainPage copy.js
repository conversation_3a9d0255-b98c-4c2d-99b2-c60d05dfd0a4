// ========= 公共模块 =========
let UserAgent = require('user-agents');

let puppeteer = require('puppeteer-extra');
// let StealthPlugin = require('puppeteer-extra-plugin-stealth');
// puppeteer.use(StealthPlugin());

let Model = require('../../config/sequelize.config.js');

// let puppeteer=require('puppeteer');

// let AnonymizeUA = require('puppeteer-extra-plugin-anonymize-ua');
// puppeteer.use(AnonymizeUA());

// let path = require('path');
// let executablePath;
// if (process.pkg) {
//     // pkg 打包环境
//     executablePath = path.join(path.dirname(process.execPath), 'static/Chrome-bin/chrome.exe');
// } else {
//     // 开发环境
//     executablePath = path.resolve('static/Chrome-bin/chrome.exe');
// }

async function getMainPage(taskOptions) {
    //1.创建浏览器实例
    let browser;
    let proxy;
    if (taskOptions.isProxy) {
        const t = await Model.sequelize.transaction(); // 开始事务

        try {
            // 查询并加锁状态为 'idle' 的代理
            proxy = await Model.proxy.findOne({
                where: {
                    state: 'idle',
                },
                lock: t.LOCK.UPDATE, // 行级锁
                transaction: t, // 使用事务
            });

            if (!proxy) {
                throw new Error('没有可用的代理ip');
            }
            proxy = proxy.get({ plain: true });
            taskOptions.proxy = proxy;

            // 更新代理状态为 'busy'
            await Model.proxy.update(
                { state: 'busy' },
                {
                    where: {
                        ip: proxy.ip + '', // 根据代理的IP来更新
                        state: 'idle', // 确保状态仍为 idle
                    },
                    transaction: t, // 使用同一个事务
                }
            );

            await t.commit(); // 提交事务，释放锁
        } catch (error) {
            console.log(error);
            await t.rollback(); // 回滚事务，撤销所有操作
            throw new Error('更新状态出错');
        }

        taskOptions.ip = proxy.ip;

        browser = await puppeteer.launch({
            headless: taskOptions.isHeadless, //显示浏览器窗口
            defaultViewport: {
                //设置浏览器视口尺寸
                width: 1920,
                height: 1080,
            },
            args: [
                '--mute-audio', //静音
                '--window-size=1920,1080', // 设置浏览器窗口的大小
                '--use-fake-ui-for-media-stream', //绕过摄像头，麦克风权限
                // `--fingerprints=${Math.floor(Math.random() * 1000000000)}`
                `--proxy-server=http://${proxy.ip}:${proxy.port}`,
            ],
            //利用本地chrome浏览器来测试
            executablePath: `C:/Program Files/Google/Chrome/Application/chrome.exe`,
            // executablePath: executablePath,
        });
    }

    if (!taskOptions.isProxy) {
        browser = await puppeteer.launch({
            headless: taskOptions.isHeadless, //显示浏览器窗口
            defaultViewport: {
                //设置浏览器视口尺寸
                width: 1920,
                height: 1080,
            },
            args: [
                '--mute-audio', //静音
                '--window-size=1920,1080', // 设置浏览器窗口的大小
                '--use-fake-ui-for-media-stream', //绕过摄像头，麦克风权限
            ],
            //利用本地chrome浏览器来测试
            executablePath: `C:/Program Files/Google/Chrome/Application/chrome.exe`,
            // executablePath: executablePath,
        });
    }

    //创建页面实例
    // let mainPage = await browser.newPage();
    let pages = await browser.pages();
    let mainPage = pages[0];

    if (taskOptions.isProxy) {
        // 设置代理认证信息
        await mainPage.authenticate({
            username: proxy.username + '', // 代理的用户名
            password: proxy.password + '', // 代理的密码
        });
    }

    // let userAgent = new UserAgent({ deviceCategory: 'desktop' }).toString();
    // let agentStr = userAgent.toString();

    //设置页面的userAgent
    // await mainPage.setUserAgent("Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/66.0.3359.181 Safari/537.36");
    // await mainPage.setUserAgent(agentStr);
    return { mainPage, browser };
}

module.exports = getMainPage;

if (true) {
    (async () => {
        let taskOptions = {
            isHeadless: false,
            isProxy: false,
        };

        for (let i = 0; i < 5; i++) {
            let { mainPage, browser } = await getMainPage(taskOptions);
            //设置页面的userAgent
            // 设置请求头，表明接受中文内容
            await mainPage.setExtraHTTPHeaders({
                'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            });
        }

        // await mainPage.goto('http://www.baidu.com', { waitUntil: "networkidle0" });
    })();
}
