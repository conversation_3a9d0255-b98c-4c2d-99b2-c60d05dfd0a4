let pageTools = require('../../../../utils/pageTools.js');
let api = require('../../../utils/api.js');
let Model = require('../../../../../config/sequelize.config.js');
let crypto = require('crypto');

// 将字符串转换为 Unicode 编码
function decToHex(t) {
    for (var e = '', r = 0; r < t.length; r++) {
        var n = t.charCodeAt(r);
        e += n >= 19968 && n <= 40869 ? '\\u' + ('00' + t.charCodeAt(r).toString(16)).slice(-4) : t[r];
    }
    return e;
}

// 使用 MD5 算法生成哈希值
function hex_md5(input) {
    return crypto.createHash('md5').update(input, 'utf8').digest('hex');
}

// 给url进行加密
// 输入 https://lms.ouchn.cn:443/external-api/v2/virtual-experiment/70007097675/user/70000783206/score/callback
// 输出 https://lms.ouchn.cn:443/external-api/v2/virtual-experiment/70007097675/user/70000783206/score/callback?app_key=u1sSAMNPR5G45uDT5Sm2eA==&ts=1730775027&token=ab600db7e775dc5500f3
function createAuthUrl(url, app_key) {
    let secret_key = '92c904ab607eaac9d70098212e4a440f';
    // 检查 url 是否有效
    if (!url || url === '' || url === 'undefined') {
        return null; // 返回 null 以指示无效输入
    }

    // 获取当前时间戳（秒）
    let ts = Math.floor(Date.now() / 1000);
    ts = 1730775027;

    // 提取路径部分（去除协议和域名）
    let urlPath = '/' + url.split('//')[1].split('/').slice(1).join('/');

    // 构造带时间戳和 app_key 的参数字符串
    let queryString = `${urlPath}?app_key=${app_key}&ts=${ts}`;

    // 生成 token
    let token = hex_md5(queryString + secret_key).substr(0, 20);

    // 构造完整 URL
    let signedUrl = `${url}?app_key=${app_key}&ts=${ts}&token=${token}`;

    return signedUrl;
}

// 从url中提urserId和experimentId
//    'https://lmsjdsx.ouchn.cn/12054_BCeboDlE/index.html?callback_url=https%3A%2F%2Flms.ouchn.cn%3A443%2Fexternal-api%2Fv2%2Fvirtual-experiment%2F70007097401%2Fuser%2F70000783206%2Fscore%2Fcallback'
function extractValues(url) {
    // 解析 URL
    const urlObj = new URL(url);

    // 解析 callback_url 的参数
    const callbackUrl = new URL(urlObj.searchParams.get('callback_url'));

    // 使用正则表达式提取 experiment 和 user 的值
    const experimentMatch = callbackUrl.pathname.match(/virtual-experiment\/(\d+)\//);
    const userMatch = callbackUrl.pathname.match(/user\/(\d+)\//);

    // 构造返回对象
    return {
        experimentId: experimentMatch ? experimentMatch[1] : null,
        userId: userMatch ? userMatch[1] : null,
    };
}

async function experimentTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr) {
    // 跳过已完成的课件
    if (taskObj.isCompleted) {
        // 判断分数
        if (taskObj.activity_final_score >= 90) {
            await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 任务已完成，跳过，分数:${taskObj.activity_final_score}`);
            return;
        } else {
            await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 任务已完成，分数不够:${taskObj.activity_final_score}`);
        }
    } else {
        await infoLogger(`${taskLogStr}<${taskObj.title}> ${taskObj.type} 任务开始`);
    }

    // 通过被动拦截的方式获取结果
    mainPage.apiInfo.activitiesRead = null;
    mainPage.promise.activitiesReadPromise = new Promise(r => {
        mainPage.resolve.activitiesReadResolve = r;
    });
    // 通过页面打开的方式完成任务，并拦截结果
    let onActivitesRead = async request => {
        //拦截验证码
        let url = request.url();

        //拦截  保存作业10003396680
        if (url.includes(`https://lms.ouchn.cn/api/course/activities-read/${taskObj.id}`)) {
            let method = request.method();
            if (method == 'POST') {
                //处理响应体
                const response = await request.response();
                const contentType = response.headers()['content-type'];

                //响应体默认值
                let responseData = '';

                //如果响应体是json
                if (contentType && contentType.includes('application/json')) {
                    responseData = await response.json();
                }

                //获取API数据
                mainPage.apiInfo.activitiesRead = {
                    request: {
                        method: request.method(),
                        url: request.url(),
                        headers: request.headers(),
                        postData: request.postData(),
                        resourceType: request.resourceType(),
                    },
                    response: responseData,
                };

                //更改promise状态

                mainPage.resolve.activitiesReadResolve();

                // 移除监听器 用once就会自动移除监听器
                mainPage.off('requestfinished', onActivitesRead);
            }
        }
    };
    mainPage.on('requestfinished', onActivitesRead);

    // 通过被动拦截的方式获取taskObj.data.link
    mainPage.apiInfo.taskObj = null;
    mainPage.promise.taskObjPromise = new Promise(r => {
        mainPage.resolve.taskObjResolve = r;
    });
    // 通过页面打开的方式完成任务，并拦截结果
    let onTaskObj = async request => {
        //拦截验证码
        let url = request.url();

        //拦截  保存作业10003396680
        if (url == `https://lms.ouchn.cn/api/activities/${taskObj.id}`) {
            let method = request.method();
            if (method == 'GET') {
                //处理响应体
                const response = await request.response();
                const contentType = response.headers()['content-type'];

                //响应体默认值
                let responseData = '';

                //如果响应体是json
                if (contentType && contentType.includes('application/json')) {
                    responseData = await response.json();
                }

                //获取API数据
                mainPage.apiInfo.taskObj = {
                    request: {
                        method: request.method(),
                        url: request.url(),
                        headers: request.headers(),
                        postData: request.postData(),
                        resourceType: request.resourceType(),
                    },
                    response: responseData,
                };

                //更改promise状态

                mainPage.resolve.taskObjResolve();

                // 移除监听器 用once就会自动移除监听器
                mainPage.off('requestfinished', onTaskObj);
            }
        }
    };
    mainPage.on('requestfinished', onTaskObj);

    // 打开课件页面
    let taskUrl = `https://lms.ouchn.cn/course/${courseObj.id}/learning-activity/full-screen#/${taskObj.id}`;
    // await mainPage.goto(taskUrl, { waitUntil: 'networkidle2' });
    await pageTools.gotoWithRetry(mainPage, taskUrl, { waitUntil: 'networkidle0' }, 3, infoLogger);

    await new Promise(r => setTimeout(r, 5 * 1000));

    let cookieStr = await pageTools.getPageCookies(mainPage);

    // 更改页面进度
    let response = mainPage.apiInfo.activitiesRead ? mainPage.apiInfo.activitiesRead.response : null;
    if (response && response.completeness) {
        await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 通过方式一已经完成`, 'green');
    } else {
        await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 任务未完成，将通过方式二完成任务`, 'gray');
        // 学习行为分析
        await api.learningActivity(courseObj.globalData, courseObj.userAgent, taskObj.id, cookieStr);
        let res = await api.updateDuration( taskObj.id, cookieStr, '{}');
        if (res) {
            await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 已经完成`, 'green');
        } else {
            await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 任务未完成`, 'red');
        }
    }

    // 通过拦截的方式，重新获取taskObj
    let taskObjResponse = mainPage.apiInfo.taskObj ? mainPage.apiInfo.taskObj.response : null;
    if (taskObjResponse && taskObjResponse.data) {
        taskObj = taskObjResponse;
    } else {
        await infoLogger(`${taskLogStr} 没有获取实验数据 任务未完成`, 'red');
        globalStore.warningMessage += `${taskLogStr} 没有获取实验数据`;
        return;
    }

    // 获取urserId和experimentId
    let { experimentId, userId } = extractValues(taskObj.data.link);

    // 从数据库获取数据
    let requestArr = await Model.gjkfdx_experiment.findAll({
        where: {
            task_id: experimentId,
        },
    });
    if (!requestArr || requestArr.length === 0) {
        await infoLogger(`${taskLogStr} 该实验还未收集 url:${taskUrl}`, 'red');
        globalStore.warningMessage += `${taskLogStr} 该实验还未收集 url:${taskUrl}`;
        return;
    }
    for (let requestBody of requestArr) {
        // 提交数据
        let body = {
            total_score: 100,
            start_time: Date.now() - 8 * 60 * 1000,
            end_time: Date.now(),
            steps: requestBody.task_steps,
        };

        let str = decToHex(JSON.stringify(body)).replace(/:/g, ': ').replace(/,/g, ', ');
        let data_token = hex_md5(str);
        body.data_token = data_token;

        let url = `https://lms.ouchn.cn/external-api/v2/virtual-experiment/${experimentId}/user/${userId}/score/callback`;
        let url1 = createAuthUrl(url, requestBody.app_key);

        let finalRes = await api.submitExperiment(url1, body);
        await infoLogger(`${taskLogStr} 实验已提交,${JSON.stringify(finalRes)}.`, 'green');

        // 获取实验分数
        let scoreRes = await api.getExperimentScore(experimentId, userId, cookieStr);
        let lastSubmission = scoreRes.scores.submissions[0];
        let score = lastSubmission.score;
        if (score >= 90) {
            await infoLogger(`${taskLogStr} 实验得分为${score},通过`, 'green');
        } else {
            await infoLogger(`${taskLogStr} 实验得分为${score},未通过`, 'red');
            globalStore.warningMessage += `${taskLogStr} 实验得分为${score},未通过 url:${taskUrl}`;
        }

        await new Promise(r => setTimeout(r, 10 * 1000));
    }
}

module.exports = experimentTask;
