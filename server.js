let cluster = require('cluster');

// Node.js v18 中的 TextDecoder 内置模块只支持部分编码（'utf-8'、'utf-16le'、'utf-16be'），不支持 'ascii' 编码。而在您的项目中，fontkit 库使用了 TextDecoder('ascii')，这导致了错误。
// 步骤1：安装 @sinonjs/text-encoding 包  npm install @sinonjs/text-encoding
// 步骤2：在项目入口文件中引入 polyfill 在您的主文件（如 index.js 或 app.js）的顶部添加以下代码：
global.TextDecoder = require('@sinonjs/text-encoding').TextDecoder;

// 主进程
if (cluster.isMaster) {
    // 全局变量
    process.env.HAS_PERMISSION = false;

    // 引入模块
    let ClusterPool = require('./main/ClusterPool.js');
    let pcId = require('./config/pcId');
    let Model = require('./config/sequelize.config.js');
    let webSocketClient = require('./main/webSocketClient.js');
    let getCPUS = require('./utils/getCUPS.js');
    let user = require('./config/user.js');

    (async () => {
        // 用户输入线程数 通过 readline 模块 创建一个命令行窗口，让用户输入线程数量，如果没输入，就采用CPU线程数
        let cpus = await getCPUS();
        let numCPUs = cpus || require('os').cpus().length;
        numCPUs = parseInt(numCPUs);

        console.log(`${new Date().toLocaleString()}：主进程<${pcId}>启动成功`);
        console.log(`${new Date().toLocaleString()}：用户：${user}`);

        // 重置cluster进程表
        await Model.cluster.destroy({
            where: {
                pc_id: pcId,
            },
        });

        // 创建cluster进程池实例pool
        let clusterPool = new ClusterPool(numCPUs);

        // 启动webSocket客户端
        let ws = webSocketClient(clusterPool);

        // 初始化线程池
        await clusterPool.initWorkers(ws);

        // 定时发送心跳时间
        setInterval(() => {
            try {
                Model.device_licenses.update(
                    {
                        last_heartbeat: new Date(),
                    },
                    {
                        where: {
                            pc_id: pcId,
                        },
                    }
                );
            } catch (error) {
                console.log(`${new Date().toLocaleString()}：网络似乎中断了，请重启程序，或者联系管理员`);
            }
        }, 30 * 60 * 1000);
    })();
}

// 工作进程
if (!cluster.isMaster) {
    (async () => {
        // 全局变量
        process.env.HAS_PERMISSION = false;

        console.log(`${new Date().toLocaleString()}：工作进程启动`);

        // message - 接收主线程发送的数据
        let workProcessHandle = require('./main/workProcessHandle');
        process.on('message', workProcessHandle);

        // 主线程调用 worker.disconnect() 或主线程的退出导致 IPC 通道断开时，子进程会触发 disconnect 事件。
        process.on('disconnect', () => {
            console.log(`${new Date().toLocaleString()}： 主线程断开连接，子进程将自行退出`);
            process.exit();
        });
    })();
}

// $env:USER="<EMAIL>";$env:WS="ws://127.0.0.1:2000";node server.js
// $env:USER="<EMAIL>";$env:WS="ws://127.0.0.1:2000";node server.js

// pkg . --targets node18-win-x64
// pkg . --targets node18-win-x64 -o user01.exe
// javascript-obfuscator ./coursePlatform/ahjxjy/course/module/runCourseModule/courseTask/1.js --output ./coursePlatform/ahjxjy/course/module/runCourseModule/courseTask/ --config ./obfuscator-config.js