let api = require('../../../../utils/api.js');
let axiosIns = require('../../../../../utils/axiosIns.js');
const getAnswerFromLocal = require('../../../../../solveQuestion/getAnswerFromLocal.js');
const Model = require('../../../../../../config/sequelize.config.js');
let { v4 } = require('uuid');
let { lettersToNumbers, numbersToLetters } = require('../../../../../solveQuestion/format.js');
let { getVideoDurationInSeconds } = require('get-video-duration');
let signinModule = require('../../signinModule.js');

let path = require('path');
let ffprobePath = path.join(path.dirname(process.execPath), 'static/ffprobe-static/ffprobe.exe');
if (process.pkg) {
    // pkg 打包环境
    ffprobePath = path.join(path.dirname(process.execPath), 'static/ffprobe-static/ffprobe.exe');
} else {
    // 开发环境
    ffprobePath = path.resolve('static/ffprobe-static/ffprobe.exe');
}

async function videoTask(infoLogger, mainPage, courseObj, cellObj, cellLogStr, globalStore) {
    let storageObj = globalStore.storageObj;

    await infoLogger(`${cellLogStr}：开始${cellObj.extension}视频任务 《${cellObj.title}》`);

    // // 进入到课程界面
    // let cellUrl = `https://jxjynew.ahjxjy.cn/app/jxjy-student-space-web#/myCourse/courseStudy?cellId=${cellObj.id}&courseId=${courseObj.id}`;
    // await mainPage.goto(cellUrl, { waitUntil: 'networkidle0' });
    // await new Promise(r => setTimeout(r, 3000));
    // let courseUrl = 'https://jxjynew.ahjxjy.cn/app/jxjy-student-space-web#/myCourse/myCourse';
    // await mainPage.goto(courseUrl, { waitUntil: 'networkidle0' });

    // 获取视频长度
    let videoInfoRes = await api.getPreviewResource(storageObj, cellObj.relevantId);
    let previewUrl, videoUrl;
    try {
        previewUrl = JSON.parse(videoInfoRes.previewUrl);
        videoUrl = previewUrl[previewUrl.length - 1].Value;
    } catch (error) {
        videoUrl = videoInfoRes.previewUrl + '/teacher.mp4';
    }
    let duration = await getVideoDurationInSeconds(videoUrl, ffprobePath);
    duration = Math.floor(duration * 1);

    // 获取当前视频进度
    let teacherRecordRes = await api.teacherRecord(storageObj, cellObj.id, courseObj.id);
    let currentRecord = teacherRecordRes.CurrentRecord * 1;

    // 获取当前视频课件信息
    await api.studyingLenard(storageObj, courseObj.id, cellObj.id);

    let positionRes;
    for (let i = currentRecord; i <= duration; i += 60) {
        
        // 修改视频进度，添加容错机制
        for (let j = 0; j < 3; j++) {
            try {
                positionRes = await api.recordVideoPosition(storageObj, cellObj.id, courseObj.id, i);
                break;
            } catch (error) {
                await infoLogger(`修改视频进度出错，重试第${j + 1}次，重新登录`, 'gray');
                await new Promise(r => setTimeout(r, 1000));
                await signinModule(infoLogger, mainPage, globalStore);
                await new Promise(r => setTimeout(r, 1000));
                storageObj = globalStore.storageObj;
                // 最后一次尝试失败
                if (j === 2) {
                    await infoLogger(`修改视频进度出错，已尝试3次,${JSON.stringify(positionRes)}`, 'red');
                    return;
                }
            }
        }

        // 添加容错机制
        for (let k = 0; k < 3; k++) {
            if (positionRes.code == 'allow') {
                break;
            } else {
                await infoLogger(`修改视频进度失败，重新登录，重试第${k + 1}次`, 'gray');
                // 最后一次尝试失败
                if (k === 2) {
                    await infoLogger(`修改视频进度失败，已尝试3次,${JSON.stringify(positionRes)}`, 'red');
                    return;
                }
                await new Promise(r => setTimeout(r, 1000));
                await signinModule(infoLogger, mainPage, globalStore);
                await new Promise(r => setTimeout(r, 1000));
                positionRes = await api.recordVideoPosition(storageObj, cellObj.id, courseObj.id, i);
            }
        }

        if (positionRes.isStudy == true) {
            await infoLogger(`${cellLogStr}：视频已完成 《${cellObj.title}》`, 'green');
            return;
        }
        await new Promise(r => setTimeout(r, 60 * 1000));
        await infoLogger(`${cellLogStr}：视频已完成 ${i}秒 剩余 ${duration - i}秒`);

        // 确保最后一次能报告到duration
        if (i + 60 > duration && i < duration) {
            // 只有一次小循环了
            i = duration - 60; // 保证下一次i+60正好等于duration
        }
    }
}
module.exports = videoTask;
