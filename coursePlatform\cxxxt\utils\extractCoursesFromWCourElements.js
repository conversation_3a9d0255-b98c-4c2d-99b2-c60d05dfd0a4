// 通过div.w_cour选择器提取课程列表数据
function extractCoursesFromWCourElements() {
    // 选择所有课程元素
    var courseElements = document.querySelectorAll('div.w_cour');
    var coursesData = []; // 初始化一个空数组，用于存放提取到的所有课程对象

    // 遍历每个课程元素
    for (let courseElement of courseElements) {
        var course = {}; // 为当前课程创建一个空对象

        try {
            // 提取课程名称
            var courseNameElement = courseElement.querySelector('h3.w_cour_txtH');
            if (courseNameElement) {
                // 获取课程名称文本，排除span标签内容
                var courseNameText = '';
                for (let node of courseNameElement.childNodes) {
                    if (node.nodeType === Node.TEXT_NODE) {
                        courseNameText += node.textContent.trim();
                    }
                }
                course.courseName = courseNameText.trim();
            }

            // 提取课程类型（必修课/选修课等）
            var courseTypeElement = courseElement.querySelector('h3.w_cour_txtH span.w_coulogo');
            course.courseType = courseTypeElement ? courseTypeElement.textContent.trim() : null;

            // 提取课程封面图片URL
            var coverImage = courseElement.querySelector('dt.w_cour_pic img');
            course.coverImageUrl = coverImage ? coverImage.src : null;

            // 提取考核权重信息
            var examWeightElement = courseElement.querySelector('p.w_cour_txtP');
            if (examWeightElement && examWeightElement.textContent.includes('考核权重：')) {
                var weightText = examWeightElement.textContent.replace('考核权重：', '').trim();
                // 移除换行和多余空格
                weightText = weightText.replace(/\s+/g, ' ').trim();
                course.examWeight = weightText;
            }

            // 提取章节任务点信息
            var taskPointElement = courseElement.querySelector('i.w_cour_txtPitem');
            if (taskPointElement && taskPointElement.textContent.includes('章节任务点：')) {
                var taskPointSpan = taskPointElement.querySelector('span.w_cour_txtblue');
                if (taskPointSpan) {
                    var taskPointText = taskPointSpan.textContent.trim(); // 例如: "0/45"
                    var taskPointParts = taskPointText.split('/');
                    course.taskPointCompleted = taskPointParts[0] ? parseInt(taskPointParts[0]) : 0;
                    course.taskPointTotal = taskPointParts[1] ? parseInt(taskPointParts[1]) : 0;
                    course.taskPointText = taskPointText;
                }

                // 提取任务点完成百分比
                var taskPointCanvas = taskPointElement.querySelector('canvas.process');
                if (taskPointCanvas) {
                    var taskPointPercent = taskPointCanvas.textContent.trim();
                    course.taskPointPercent = taskPointPercent;
                }
            }

            // 提取章节学习次数信息
            var studyTimesElements = courseElement.querySelectorAll('i.w_cour_txtPitem');
            for (let element of studyTimesElements) {
                if (element.textContent.includes('章节学习次数：')) {
                    var studyTimesSpan = element.querySelector('span.w_cour_txtblue');
                    if (studyTimesSpan) {
                        course.studyTimesPercent = studyTimesSpan.textContent.trim();
                    }
                    break;
                }
            }

            // 提取整体学习进度
            var progressElement = courseElement.querySelector('div.percent');
            if (progressElement) {
                // 提取进度百分比文本
                var progressText = progressElement.textContent.trim();
                var progressMatch = progressText.match(/(\d+\.?\d*)%/);
                if (progressMatch) {
                    course.overallProgress = progressMatch[1] + '%';
                    course.overallProgressValue = parseFloat(progressMatch[1]);
                }

                // 提取进度条的title属性（包含详细进度信息）
                var progressSpan = progressElement.querySelector('span[title]');
                if (progressSpan) {
                    course.progressTitle = progressSpan.getAttribute('title');
                }
            }

            // 提取学习链接
            var studyLinkElement = courseElement.querySelector('a.study');
            if (studyLinkElement) {
                var onclickAttr = studyLinkElement.getAttribute('onclick');
                if (onclickAttr) {
                    // 从onclick属性中提取URL
                    var urlMatch = onclickAttr.match(/window\.open\('([^']+)'\)/);
                    if (urlMatch) {
                        course.studyUrl = urlMatch[1];
                    }
                }
                course.studyLinkText = studyLinkElement.textContent.trim();
            }

            // 提取课程ID（从图片src中获取）
            if (coverImage && coverImage.src.includes('courseId=')) {
                var courseIdMatch = coverImage.src.match(/courseId=(\d+)/);
                if (courseIdMatch) {
                    course.courseId = courseIdMatch[1];
                }
            }

            // 提取学习ID（从学习链接中获取）
            if (course.studyUrl && course.studyUrl.includes('xkid=')) {
                var xkidMatch = course.studyUrl.match(/xkid=(\d+)/);
                if (xkidMatch) {
                    course.xkid = xkidMatch[1];
                }
            }

            // 添加原始HTML（用于调试）
            course.originalHtml = courseElement.outerHTML;
        } catch (error) {
            console.error('提取课程信息时出错:', error);
            course.error = error.message;
        }

        coursesData.push(course); // 将提取到的当前课程对象添加到数组中
    }

    console.log('提取到的课程数据:', coursesData);
    return coursesData;
}

let obj = {
    courseName: '财务管理',
    courseType: '必修课',
    coverImageUrl: 'https://yangtzeucj.jxjy.chaoxing.com/face/courseFace?courseId=219663670',
    examWeight: '章节任务点:40%+章节学习次数:10%+期末考试:50%',
    taskPointCompleted: 0,
    taskPointTotal: 45,
    taskPointText: '0/45',
    taskPointPercent: '0%',
    studyTimesPercent: '7%',
    overallProgress: '0.0%',
    overallProgressValue: 0,
    progressTitle: '学习进度：0.0',
    studyUrl: '/studyApp/setViewTime?xkid=88409974',
    studyLinkText: '进入学习',
    courseId: '219663670',
    xkid: '88409974',
    originalHtml:
        '<div class="w_cour w_cournopadd">\n        <dl class="w_cour_row onexk">\n            <dd class=" w_cour_btn ddstyle">\n                                                                                                                                                    <a class="study ddsubstyle" href="javascript:;" onclick="window.open(\'/studyApp/setViewTime?xkid=88409974\')">进入学习</a>\n                                                                                                            </dd>\n                            <dt class="w_cour_pic fl" style="height: auto">\n                    <img src="/face/courseFace?courseId=219663670" width="155" height="92">\n                </dt>\n                <dd class="w_cour_txt" style="margin-left: 198px">\n                    <div class="clearfix width85">\n                        <h3 class="w_cour_txtH fl">\n                            财务管理\n                                                                                                                                                                                                                                                            <span class="w_coulogo bluebg">必修课</span>\n                                                                                                                                                                                                                                                                                            </h3>\n                    </div>\n                                                                        <p class="w_cour_txtP width85">\n                                考核权重：\n                                                                                                                章节任务点:40%+章节学习次数:10%+期末考试:50%\n                                                                                <img src="/images/wenhao.png" width="20" height="20" title="章节任务点40%\n&lt;span&gt;按完成任务点的个数计分，全部完成得满分\n\n章节学习次数10%\n章节学习次数30次为满分\n\n期末考试50%\n线下期末考试成绩得分\n\n">\n                                                                                                </p>\n                                                                                                                                                                        <p class="w_cour_txtP width85">线上学习进度：                                                                                                                        <i class="w_cour_txtPitem">章节任务点：\n                                                <span class="w_cour_txtblue">0/45</span>\n                                                <canvas class="process" style="margin-bottom: -5px;" width="20px" height="20px" runing="1">0%\n                                                </canvas>\n                                            </i>\n                                                                                            <img src="/images/wenhao.png" width="20" height="20" style="margin-left: -20px;margin-right: 20px;" title="当前课程总任务点数为45个，其中45个为考核任务点。">\n                                                                                                                                                                                                            <i class="w_cour_txtPitem">章节学习次数：\n                                                <span class="w_cour_txtblue">7%</span>\n                                                <canvas class="process" style="margin-bottom: -5px;" width="20px" height="20px" runing="1">7%\n                                                </canvas>\n                                            </i>\n                                                                                                            </p>\n                                                                                                                                                                     <span class="fl" style="margin-top: 4px;">整体学习进度：</span>\n                                        <div class="percent">\n\t\t\t\t\t\t\t\t\t\t<span title="学习进度：0.0" style="border: solid 0px #7a9f26;background: #d4d8d7;">\n\t\t\t\t\t\t\t\t\t\t\t<i style="width:0.0%;background: #3A8BFF;"></i>\n\t\t\t\t\t\t\t\t\t\t</span>\n                                            0.0%\n                                            <img src="/images/wenhao.png" width="20" height="20" title="课程进度条所表现的是所有任务点完成进度。\n注：进度条完成不代表总评成绩为满分。">\n                                        </div>\n                                                                                                                                            \n                    \n                </dd>\n                    </dl>\n    </div>',
};
