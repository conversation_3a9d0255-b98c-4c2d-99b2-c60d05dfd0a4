let handleClickValidate = require('./handleClickValidate.js');
let handleSlideValidate = require('./handleSlideValidate.js');
let pageTools = require('../../../../utils/pageTools.js');
let api = require('../../../utils/api.js');
async function signinModule(infoLogger, mainPage, globalStore) {
    let taskObj = globalStore.taskObj;
    //1.跳转到登录页面
    // await mainPage.goto(taskObj.schoolurl, { waitUntil: "networkidle0" });
    await pageTools.gotoWithRetry(mainPage, taskObj.schoolurl, { waitUntil: 'networkidle0', timeout: 50 * 1000 }, 3, infoLogger);
    await new Promise(r => setTimeout(r, 3000));

    // 为了endModule执行单独加的，因为有时候网课刷完了，身份还没过期
    let currentUrl = mainPage.url();
    if (currentUrl == 'https://menhu.pt.ouchn.cn/site/ouchnPc/index') {
        globalStore.mainPageCookie = await pageTools.getPageCookies(mainPage);
        await infoLogger('已经登录', 'green');
        return;
    }

    await infoLogger('开始登录');

    //2.输入学校名称，用户名，密码
    await mainPage.type('#loginName', taskObj.username); //输入用户名
    await mainPage.type('#password', taskObj.password); //输入密码
    await mainPage.click('#agreeCheckBox'); //点击同意协议

    //点击登录
    await mainPage.click('#form_button');
    await new Promise(r => setTimeout(r, 2000));

    // 处理滑动验证码
    await handleSlideValidate(mainPage, infoLogger);
    await infoLogger('验证码完毕');
    await new Promise(r => setTimeout(r, 5000));

    currentUrl = mainPage.url();
    if (currentUrl == 'https://menhu.pt.ouchn.cn/site/ouchnPc/index') {
        globalStore.mainPageCookie = await pageTools.getPageCookies(mainPage);
        await infoLogger('登录成功', 'green');
    } else {
        // console.log(currentUrl)
        await infoLogger('登录失败', 'red');
        throw Error('登录失败');
    }
}

module.exports = signinModule;