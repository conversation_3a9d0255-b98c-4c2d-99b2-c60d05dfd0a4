<table
	id="tblDataList"
	onselectstart="return false"
	width="100%"
	cellpadding="3"
	_default_attr="1"
	cellspacing="1"
>
	<tbody>
		<tr
			id="tr_tblDataList_0"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_0');}"
		>
			<td scoreid="367605821374201859" id="_DataListTD_tblDataList_0">
				<table
					width="100%"
					border="0"
					id="tblItem_350189604430676095"
					islabel="1"
					keylist=""
					style=""
				>
					<tbody>
						<tr>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<b>一、单选题<\/b><SPAN IsScoreRemark="1"><\/SPAN>'
										)
									);
								</script>
								<b>一、单选题</b
								><span isscoreremark="1"
									>&nbsp;&nbsp;<font color="gray"
										>(第1-4题每题5分)</font
									></span
								>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_1"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_1');}"
		>
			<td scoreid="367605821374201860" id="_DataListTD_tblDataList_1">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643283"
					islabel="0"
					keylist="lemonysoft_item_5i5ncymnsb_16"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">1.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>危险截面是（）所在的截面。<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_5i5ncymnsb_16_0 name="lemonysoft_item_5i5ncymnsb_16" ><TD>(A)<TD><label for=lemonysoft_item_5i5ncymnsb_16_0>最大面积<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_5i5ncymnsb_16_1 name="lemonysoft_item_5i5ncymnsb_16" ><TD>(B)<TD><label for=lemonysoft_item_5i5ncymnsb_16_1>最小面积<\/label><TR><TD><input type=radio value="C" id=lemonysoft_item_5i5ncymnsb_16_2 name="lemonysoft_item_5i5ncymnsb_16" ><TD>(C)<TD><label for=lemonysoft_item_5i5ncymnsb_16_2>最大应力<\/label><TR><TD><input type=radio value="D" id=lemonysoft_item_5i5ncymnsb_16_3 name="lemonysoft_item_5i5ncymnsb_16" ><TD>(D)<TD><label for=lemonysoft_item_5i5ncymnsb_16_3>最大内力<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>危险截面是（）所在的截面。</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_5i5ncymnsb_16_0"
																		name="lemonysoft_item_5i5ncymnsb_16"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_5i5ncymnsb_16_0"
																		>最大面积</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_5i5ncymnsb_16_1"
																		name="lemonysoft_item_5i5ncymnsb_16"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_5i5ncymnsb_16_1"
																		>最小面积</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="C"
																		id="lemonysoft_item_5i5ncymnsb_16_2"
																		name="lemonysoft_item_5i5ncymnsb_16"
																	/>
																</td>
																<td>(C)</td>
																<td>
																	<label
																		for="lemonysoft_item_5i5ncymnsb_16_2"
																		>最大应力</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="D"
																		id="lemonysoft_item_5i5ncymnsb_16_3"
																		name="lemonysoft_item_5i5ncymnsb_16"
																	/>
																</td>
																<td>(D)</td>
																<td>
																	<label
																		for="lemonysoft_item_5i5ncymnsb_16_3"
																		>最大内力</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_5i5ncymnsb_16',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_2"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_2');}"
		>
			<td scoreid="367605821374201861" id="_DataListTD_tblDataList_2">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643282"
					islabel="0"
					keylist="lemonysoft_item_2r8uxbb339_13"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">2.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>. 截面上的全应力的方向（　）<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_2r8uxbb339_13_0 name="lemonysoft_item_2r8uxbb339_13" ><TD>(A)<TD><label for=lemonysoft_item_2r8uxbb339_13_0>平行于截面　<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_2r8uxbb339_13_1 name="lemonysoft_item_2r8uxbb339_13" ><TD>(B)<TD><label for=lemonysoft_item_2r8uxbb339_13_1>垂直于截面　<\/label><TR><TD><input type=radio value="C" id=lemonysoft_item_2r8uxbb339_13_2 name="lemonysoft_item_2r8uxbb339_13" ><TD>(C)<TD><label for=lemonysoft_item_2r8uxbb339_13_2>可以与截面任意夹角　<\/label><TR><TD><input type=radio value="D" id=lemonysoft_item_2r8uxbb339_13_3 name="lemonysoft_item_2r8uxbb339_13" ><TD>(D)<TD><label for=lemonysoft_item_2r8uxbb339_13_3>与截面无关<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												. 截面上的全应力的方向（　）
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_2r8uxbb339_13_0"
																		name="lemonysoft_item_2r8uxbb339_13"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_2r8uxbb339_13_0"
																		>平行于截面　</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_2r8uxbb339_13_1"
																		name="lemonysoft_item_2r8uxbb339_13"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_2r8uxbb339_13_1"
																		>垂直于截面　</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="C"
																		id="lemonysoft_item_2r8uxbb339_13_2"
																		name="lemonysoft_item_2r8uxbb339_13"
																	/>
																</td>
																<td>(C)</td>
																<td>
																	<label
																		for="lemonysoft_item_2r8uxbb339_13_2"
																		>可以与截面任意夹角　</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="D"
																		id="lemonysoft_item_2r8uxbb339_13_3"
																		name="lemonysoft_item_2r8uxbb339_13"
																	/>
																</td>
																<td>(D)</td>
																<td>
																	<label
																		for="lemonysoft_item_2r8uxbb339_13_3"
																		>与截面无关</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_2r8uxbb339_13',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_3"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_3');}"
		>
			<td scoreid="367605821374201862" id="_DataListTD_tblDataList_3">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643265"
					islabel="0"
					keylist="lemonysoft_item_nkva4iiami_33"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">3.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table isitem="1"><tbody><tr><td>在下列关于梁转角的说法中，（）是错误的。\r\n<\/td><\/tr><tr><td><span><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_nkva4iiami_33_0 name="lemonysoft_item_nkva4iiami_33" ><TD>(A)<TD><label for=lemonysoft_item_nkva4iiami_33_0>转角是横截面绕中性轴转过的角位移 <\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_nkva4iiami_33_1 name="lemonysoft_item_nkva4iiami_33" ><TD>(B)<TD><label for=lemonysoft_item_nkva4iiami_33_1>转角是变形前后同一横截面间的夹角 <\/label><TR><TD><input type=radio value="C" id=lemonysoft_item_nkva4iiami_33_2 name="lemonysoft_item_nkva4iiami_33" ><TD>(C)<TD><label for=lemonysoft_item_nkva4iiami_33_2>转角是横截面绕梁轴线转过的角度 <\/label><TR><TD><input type=radio value="D" id=lemonysoft_item_nkva4iiami_33_3 name="lemonysoft_item_nkva4iiami_33" ><TD>(D)<TD><label for=lemonysoft_item_nkva4iiami_33_3>转角是挠曲线之切线与轴拘坐标轴间的夹角<\/label><\/table><\/div><\/span><\/td><\/tr><\/tbody><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												在下列关于梁转角的说法中，（）是错误的。
											</td>
										</tr>
										<tr>
											<td>
												<span
													><div>
														<table
															isitemoption="1"
															optiontype="radio"
														>
															<tbody>
																<tr>
																	<td>
																		<input
																			type="radio"
																			value="A"
																			id="lemonysoft_item_nkva4iiami_33_0"
																			name="lemonysoft_item_nkva4iiami_33"
																		/>
																	</td>
																	<td>(A)</td>
																	<td>
																		<label
																			for="lemonysoft_item_nkva4iiami_33_0"
																			>转角是横截面绕中性轴转过的角位移
																		</label>
																	</td>
																</tr>
																<tr>
																	<td>
																		<input
																			type="radio"
																			value="B"
																			id="lemonysoft_item_nkva4iiami_33_1"
																			name="lemonysoft_item_nkva4iiami_33"
																		/>
																	</td>
																	<td>(B)</td>
																	<td>
																		<label
																			for="lemonysoft_item_nkva4iiami_33_1"
																			>转角是变形前后同一横截面间的夹角
																		</label>
																	</td>
																</tr>
																<tr>
																	<td>
																		<input
																			type="radio"
																			value="C"
																			id="lemonysoft_item_nkva4iiami_33_2"
																			name="lemonysoft_item_nkva4iiami_33"
																		/>
																	</td>
																	<td>(C)</td>
																	<td>
																		<label
																			for="lemonysoft_item_nkva4iiami_33_2"
																			>转角是横截面绕梁轴线转过的角度
																		</label>
																	</td>
																</tr>
																<tr>
																	<td>
																		<input
																			type="radio"
																			value="D"
																			id="lemonysoft_item_nkva4iiami_33_3"
																			name="lemonysoft_item_nkva4iiami_33"
																		/>
																	</td>
																	<td>(D)</td>
																	<td>
																		<label
																			for="lemonysoft_item_nkva4iiami_33_3"
																			>转角是挠曲线之切线与轴拘坐标轴间的夹角</label
																		>
																	</td>
																</tr>
															</tbody>
														</table>
													</div></span
												>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_nkva4iiami_33',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_4"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_4');}"
		>
			<td scoreid="367605821374201863" id="_DataListTD_tblDataList_4">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643264"
					islabel="0"
					keylist="lemonysoft_item_b6sji1mm2v_22"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">4.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>用截面法求一水平杆某截面的内力时，是对（）建立平衡方程求解的。\r\n<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_b6sji1mm2v_22_0 name="lemonysoft_item_b6sji1mm2v_22" ><TD>(A)<TD><label for=lemonysoft_item_b6sji1mm2v_22_0>该截面左段 <\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_b6sji1mm2v_22_1 name="lemonysoft_item_b6sji1mm2v_22" ><TD>(B)<TD><label for=lemonysoft_item_b6sji1mm2v_22_1>该截面右段 <\/label><TR><TD><input type=radio value="C" id=lemonysoft_item_b6sji1mm2v_22_2 name="lemonysoft_item_b6sji1mm2v_22" ><TD>(C)<TD><label for=lemonysoft_item_b6sji1mm2v_22_2>该截面左段或右段 <\/label><TR><TD><input type=radio value="D" id=lemonysoft_item_b6sji1mm2v_22_3 name="lemonysoft_item_b6sji1mm2v_22" ><TD>(D)<TD><label for=lemonysoft_item_b6sji1mm2v_22_3>整个杆<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												用截面法求一水平杆某截面的内力时，是对（）建立平衡方程求解的。
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_b6sji1mm2v_22_0"
																		name="lemonysoft_item_b6sji1mm2v_22"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_b6sji1mm2v_22_0"
																		>该截面左段
																	</label>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_b6sji1mm2v_22_1"
																		name="lemonysoft_item_b6sji1mm2v_22"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_b6sji1mm2v_22_1"
																		>该截面右段
																	</label>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="C"
																		id="lemonysoft_item_b6sji1mm2v_22_2"
																		name="lemonysoft_item_b6sji1mm2v_22"
																	/>
																</td>
																<td>(C)</td>
																<td>
																	<label
																		for="lemonysoft_item_b6sji1mm2v_22_2"
																		>该截面左段或右段
																	</label>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="D"
																		id="lemonysoft_item_b6sji1mm2v_22_3"
																		name="lemonysoft_item_b6sji1mm2v_22"
																	/>
																</td>
																<td>(D)</td>
																<td>
																	<label
																		for="lemonysoft_item_b6sji1mm2v_22_3"
																		>整个杆</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_b6sji1mm2v_22',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_5"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_5');}"
		>
			<td scoreid="367605821374201864" id="_DataListTD_tblDataList_5">
				<table
					width="100%"
					border="0"
					id="tblItem_350189604430676096"
					islabel="1"
					keylist=""
					style=""
				>
					<script language="javascript">
						var oSpan = document.getElementsByTagName('SPAN');
						for (var i = oSpan.length - 1; i >= 0; i--) {
							if (oSpan[i].getAttribute('IsScoreRemark') == '1') {
								oSpan[i].innerHTML =
									'&nbsp;&nbsp;<font color=gray>(第1-4题每题5分)</font>';
								break;
							}
						}
					</script>
					<tbody>
						<tr>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<b>二、判断题<\/b><SPAN IsScoreRemark="1"><\/SPAN>'
										)
									);
								</script>
								<b>二、判断题</b
								><span isscoreremark="1"
									>&nbsp;&nbsp;<font color="gray"
										>(第1-4题每题4分,第5-6题每题5分,第7题题4分,第8-9题每题5分,第10-13题每题4分)</font
									></span
								>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_6"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_6');}"
		>
			<td scoreid="367605821374201865" id="_DataListTD_tblDataList_6">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643277"
					islabel="0"
					keylist="lemonysoft_item_9gfcuijhzf_1"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">1.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>圆截面杆件受扭时，横截面上的最大切应力发生在横截面离圆心最远处。(　　　　 )<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_9gfcuijhzf_1_0 name="lemonysoft_item_9gfcuijhzf_1" ><TD>(A)<TD><label for=lemonysoft_item_9gfcuijhzf_1_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_9gfcuijhzf_1_1 name="lemonysoft_item_9gfcuijhzf_1" ><TD>(B)<TD><label for=lemonysoft_item_9gfcuijhzf_1_1>错<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												圆截面杆件受扭时，横截面上的最大切应力发生在横截面离圆心最远处。(　　　　
												)
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_9gfcuijhzf_1_0"
																		name="lemonysoft_item_9gfcuijhzf_1"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_9gfcuijhzf_1_0"
																		>对</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_9gfcuijhzf_1_1"
																		name="lemonysoft_item_9gfcuijhzf_1"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_9gfcuijhzf_1_1"
																		>错</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_9gfcuijhzf_1',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_7"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_7');}"
		>
			<td scoreid="367605821374201866" id="_DataListTD_tblDataList_7">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643276"
					islabel="0"
					keylist="lemonysoft_item_1uwnzg3x0c_2"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">2.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>拉杆伸长后，横向会缩短，这是因为杆有横向应力的存在。(　　　　　 )<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_1uwnzg3x0c_2_0 name="lemonysoft_item_1uwnzg3x0c_2" ><TD>(A)<TD><label for=lemonysoft_item_1uwnzg3x0c_2_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_1uwnzg3x0c_2_1 name="lemonysoft_item_1uwnzg3x0c_2" ><TD>(B)<TD><label for=lemonysoft_item_1uwnzg3x0c_2_1>错<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												拉杆伸长后，横向会缩短，这是因为杆有横向应力的存在。(　　　　　
												)
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_1uwnzg3x0c_2_0"
																		name="lemonysoft_item_1uwnzg3x0c_2"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_1uwnzg3x0c_2_0"
																		>对</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_1uwnzg3x0c_2_1"
																		name="lemonysoft_item_1uwnzg3x0c_2"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_1uwnzg3x0c_2_1"
																		>错</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_1uwnzg3x0c_2',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_8"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_8');}"
		>
			<td scoreid="367605821374201867" id="_DataListTD_tblDataList_8">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643275"
					islabel="0"
					keylist="lemonysoft_item_b7tvlrbs8c_3"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">3.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>在轴向拉、压杆中，轴力最大的截面一定是危险截面。<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_b7tvlrbs8c_3_0 name="lemonysoft_item_b7tvlrbs8c_3" ><TD>(A)<TD><label for=lemonysoft_item_b7tvlrbs8c_3_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_b7tvlrbs8c_3_1 name="lemonysoft_item_b7tvlrbs8c_3" ><TD>(B)<TD><label for=lemonysoft_item_b7tvlrbs8c_3_1>错<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												在轴向拉、压杆中，轴力最大的截面一定是危险截面。
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_b7tvlrbs8c_3_0"
																		name="lemonysoft_item_b7tvlrbs8c_3"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_b7tvlrbs8c_3_0"
																		>对</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_b7tvlrbs8c_3_1"
																		name="lemonysoft_item_b7tvlrbs8c_3"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_b7tvlrbs8c_3_1"
																		>错</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_b7tvlrbs8c_3',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_9"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_9');}"
		>
			<td scoreid="367605821374201868" id="_DataListTD_tblDataList_9">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643274"
					islabel="0"
					keylist="lemonysoft_item_qct1klu2sh_4"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">4.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>低碳钢和铸铁试件在拉断前都有“颈缩”现象。<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_qct1klu2sh_4_0 name="lemonysoft_item_qct1klu2sh_4" ><TD>(A)<TD><label for=lemonysoft_item_qct1klu2sh_4_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_qct1klu2sh_4_1 name="lemonysoft_item_qct1klu2sh_4" ><TD>(B)<TD><label for=lemonysoft_item_qct1klu2sh_4_1>错<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												低碳钢和铸铁试件在拉断前都有“颈缩”现象。
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_qct1klu2sh_4_0"
																		name="lemonysoft_item_qct1klu2sh_4"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_qct1klu2sh_4_0"
																		>对</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_qct1klu2sh_4_1"
																		name="lemonysoft_item_qct1klu2sh_4"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_qct1klu2sh_4_1"
																		>错</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_qct1klu2sh_4',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_10"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_10');}"
		>
			<td scoreid="367605821374201869" id="_DataListTD_tblDataList_10">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643273"
					islabel="0"
					keylist="lemonysoft_item_2vew0xjbpo_5"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">5.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>平面弯曲的梁，横截面上的最大正应力，发生在离中性轴最远的上、下边缘点上<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_2vew0xjbpo_5_0 name="lemonysoft_item_2vew0xjbpo_5" ><TD>(A)<TD><label for=lemonysoft_item_2vew0xjbpo_5_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_2vew0xjbpo_5_1 name="lemonysoft_item_2vew0xjbpo_5" ><TD>(B)<TD><label for=lemonysoft_item_2vew0xjbpo_5_1>错<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												平面弯曲的梁，横截面上的最大正应力，发生在离中性轴最远的上、下边缘点上
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_2vew0xjbpo_5_0"
																		name="lemonysoft_item_2vew0xjbpo_5"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_2vew0xjbpo_5_0"
																		>对</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_2vew0xjbpo_5_1"
																		name="lemonysoft_item_2vew0xjbpo_5"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_2vew0xjbpo_5_1"
																		>错</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_2vew0xjbpo_5',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_11"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_11');}"
		>
			<td scoreid="367605821374201870" id="_DataListTD_tblDataList_11">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643272"
					islabel="0"
					keylist="lemonysoft_item_riarcbmlbx_6"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">6.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>园轴扭转角θ的大小仅由轴内扭矩大小决定。<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_riarcbmlbx_6_0 name="lemonysoft_item_riarcbmlbx_6" ><TD>(A)<TD><label for=lemonysoft_item_riarcbmlbx_6_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_riarcbmlbx_6_1 name="lemonysoft_item_riarcbmlbx_6" ><TD>(B)<TD><label for=lemonysoft_item_riarcbmlbx_6_1>错<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												园轴扭转角θ的大小仅由轴内扭矩大小决定。
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_riarcbmlbx_6_0"
																		name="lemonysoft_item_riarcbmlbx_6"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_riarcbmlbx_6_0"
																		>对</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_riarcbmlbx_6_1"
																		name="lemonysoft_item_riarcbmlbx_6"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_riarcbmlbx_6_1"
																		>错</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_riarcbmlbx_6',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_12"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_12');}"
		>
			<td scoreid="367605821374201871" id="_DataListTD_tblDataList_12">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643270"
					islabel="0"
					keylist="lemonysoft_item_9n2obkc6mf_7"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">7.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>园轴扭转危险截面一定是扭矩和横截面积均达到最大值的截面。<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_9n2obkc6mf_7_0 name="lemonysoft_item_9n2obkc6mf_7" ><TD>(A)<TD><label for=lemonysoft_item_9n2obkc6mf_7_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_9n2obkc6mf_7_1 name="lemonysoft_item_9n2obkc6mf_7" ><TD>(B)<TD><label for=lemonysoft_item_9n2obkc6mf_7_1>错<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												园轴扭转危险截面一定是扭矩和横截面积均达到最大值的截面。
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_9n2obkc6mf_7_0"
																		name="lemonysoft_item_9n2obkc6mf_7"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_9n2obkc6mf_7_0"
																		>对</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_9n2obkc6mf_7_1"
																		name="lemonysoft_item_9n2obkc6mf_7"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_9n2obkc6mf_7_1"
																		>错</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_9n2obkc6mf_7',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_13"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_13');}"
		>
			<td scoreid="367605821374201872" id="_DataListTD_tblDataList_13">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643269"
					islabel="0"
					keylist="lemonysoft_item_h2lnjrwdic_8"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">8.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>外径相同的空心园轴和实心园轴相比，空心园轴的承载能力要大些。<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_h2lnjrwdic_8_0 name="lemonysoft_item_h2lnjrwdic_8" ><TD>(A)<TD><label for=lemonysoft_item_h2lnjrwdic_8_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_h2lnjrwdic_8_1 name="lemonysoft_item_h2lnjrwdic_8" ><TD>(B)<TD><label for=lemonysoft_item_h2lnjrwdic_8_1>错<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												外径相同的空心园轴和实心园轴相比，空心园轴的承载能力要大些。
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_h2lnjrwdic_8_0"
																		name="lemonysoft_item_h2lnjrwdic_8"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_h2lnjrwdic_8_0"
																		>对</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_h2lnjrwdic_8_1"
																		name="lemonysoft_item_h2lnjrwdic_8"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_h2lnjrwdic_8_1"
																		>错</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_h2lnjrwdic_8',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_14"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_14');}"
		>
			<td scoreid="367605821374201873" id="_DataListTD_tblDataList_14">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643268"
					islabel="0"
					keylist="lemonysoft_item_vpvcskskuo_9"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">9.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>剪切和挤压总是同时产生，所以剪切面和挤压面是同一个面。<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_vpvcskskuo_9_0 name="lemonysoft_item_vpvcskskuo_9" ><TD>(A)<TD><label for=lemonysoft_item_vpvcskskuo_9_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_vpvcskskuo_9_1 name="lemonysoft_item_vpvcskskuo_9" ><TD>(B)<TD><label for=lemonysoft_item_vpvcskskuo_9_1>错<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												剪切和挤压总是同时产生，所以剪切面和挤压面是同一个面。
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_vpvcskskuo_9_0"
																		name="lemonysoft_item_vpvcskskuo_9"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_vpvcskskuo_9_0"
																		>对</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_vpvcskskuo_9_1"
																		name="lemonysoft_item_vpvcskskuo_9"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_vpvcskskuo_9_1"
																		>错</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_vpvcskskuo_9',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_15"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_15');}"
		>
			<td scoreid="367605821374201874" id="_DataListTD_tblDataList_15">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643281"
					islabel="0"
					keylist="lemonysoft_item_gfokik3yvt_13"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">10.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>正应力是指垂直于杆件横截面的应力。正应力又可分为正值正应力和负值正应力。<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_gfokik3yvt_13_0 name="lemonysoft_item_gfokik3yvt_13" ><TD>(A)<TD><label for=lemonysoft_item_gfokik3yvt_13_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_gfokik3yvt_13_1 name="lemonysoft_item_gfokik3yvt_13" ><TD>(B)<TD><label for=lemonysoft_item_gfokik3yvt_13_1>错<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												正应力是指垂直于杆件横截面的应力。正应力又可分为正值正应力和负值正应力。
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_gfokik3yvt_13_0"
																		name="lemonysoft_item_gfokik3yvt_13"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_gfokik3yvt_13_0"
																		>对</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_gfokik3yvt_13_1"
																		name="lemonysoft_item_gfokik3yvt_13"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_gfokik3yvt_13_1"
																		>错</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_gfokik3yvt_13',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_16"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_16');}"
		>
			<td scoreid="367605821374201875" id="_DataListTD_tblDataList_16">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643280"
					islabel="0"
					keylist="lemonysoft_item_ydrsskowto_12"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">11.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>构件的工作应力可以和其极限应力相等。<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_ydrsskowto_12_0 name="lemonysoft_item_ydrsskowto_12" ><TD>(A)<TD><label for=lemonysoft_item_ydrsskowto_12_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_ydrsskowto_12_1 name="lemonysoft_item_ydrsskowto_12" ><TD>(B)<TD><label for=lemonysoft_item_ydrsskowto_12_1>错<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												构件的工作应力可以和其极限应力相等。
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_ydrsskowto_12_0"
																		name="lemonysoft_item_ydrsskowto_12"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_ydrsskowto_12_0"
																		>对</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_ydrsskowto_12_1"
																		name="lemonysoft_item_ydrsskowto_12"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_ydrsskowto_12_1"
																		>错</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_ydrsskowto_12',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_17"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_17');}"
		>
			<td scoreid="367605821374201876" id="_DataListTD_tblDataList_17">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643279"
					islabel="0"
					keylist="lemonysoft_item_fxgpb8sxlf_11"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">12.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>设计构件时，须在满足安全工作的前提下尽量节省材料的要求。<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_fxgpb8sxlf_11_0 name="lemonysoft_item_fxgpb8sxlf_11" ><TD>(A)<TD><label for=lemonysoft_item_fxgpb8sxlf_11_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_fxgpb8sxlf_11_1 name="lemonysoft_item_fxgpb8sxlf_11" ><TD>(B)<TD><label for=lemonysoft_item_fxgpb8sxlf_11_1>错<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												设计构件时，须在满足安全工作的前提下尽量节省材料的要求。
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_fxgpb8sxlf_11_0"
																		name="lemonysoft_item_fxgpb8sxlf_11"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_fxgpb8sxlf_11_0"
																		>对</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_fxgpb8sxlf_11_1"
																		name="lemonysoft_item_fxgpb8sxlf_11"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_fxgpb8sxlf_11_1"
																		>错</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_fxgpb8sxlf_11',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_18"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_18');}"
		>
			<td scoreid="367605821374201877" id="_DataListTD_tblDataList_18">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643278"
					islabel="0"
					keylist="lemonysoft_item_wjskbahpdf_10"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">13.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<table IsItem=1><tr><td>挤压面的计算面积一定是实际积压的面积。<tr><td><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_wjskbahpdf_10_0 name="lemonysoft_item_wjskbahpdf_10" ><TD>(A)<TD><label for=lemonysoft_item_wjskbahpdf_10_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_wjskbahpdf_10_1 name="lemonysoft_item_wjskbahpdf_10" ><TD>(B)<TD><label for=lemonysoft_item_wjskbahpdf_10_1>错<\/label><\/table><\/div><\/table>'
										)
									);
								</script>
								<table isitem="1">
									<tbody>
										<tr>
											<td>
												挤压面的计算面积一定是实际积压的面积。
											</td>
										</tr>
										<tr>
											<td>
												<div>
													<table
														isitemoption="1"
														optiontype="radio"
													>
														<tbody>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="A"
																		id="lemonysoft_item_wjskbahpdf_10_0"
																		name="lemonysoft_item_wjskbahpdf_10"
																	/>
																</td>
																<td>(A)</td>
																<td>
																	<label
																		for="lemonysoft_item_wjskbahpdf_10_0"
																		>对</label
																	>
																</td>
															</tr>
															<tr>
																<td>
																	<input
																		type="radio"
																		value="B"
																		id="lemonysoft_item_wjskbahpdf_10_1"
																		name="lemonysoft_item_wjskbahpdf_10"
																	/>
																</td>
																<td>(B)</td>
																<td>
																	<label
																		for="lemonysoft_item_wjskbahpdf_10_1"
																		>错</label
																	>
																</td>
															</tr>
														</tbody>
													</table>
												</div>
											</td>
										</tr>
									</tbody>
								</table>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_wjskbahpdf_10',
										'',
										'3'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_19"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_19');}"
		>
			<td scoreid="367605821374201878" id="_DataListTD_tblDataList_19">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643284"
					islabel="1"
					keylist=""
					style=""
				>
					<script language="javascript">
						var oSpan = document.getElementsByTagName('SPAN');
						for (var i = oSpan.length - 1; i >= 0; i--) {
							if (oSpan[i].getAttribute('IsScoreRemark') == '1') {
								oSpan[i].innerHTML =
									'&nbsp;&nbsp;<font color=gray>(第1-4题每题4分,第5-6题每题5分,第7题题4分,第8-9题每题5分,第10-13题每题4分)</font>';
								break;
							}
						}
					</script>
					<tbody>
						<tr>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<b>三、填空题<\/b><SPAN IsScoreRemark="1"><\/SPAN>'
										)
									);
								</script>
								<b>三、填空题</b
								><span isscoreremark="1"
									>&nbsp;&nbsp;<font color="gray"
										>(第1-6题每题4分)</font
									></span
								>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_20"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_20');}"
		>
			<td scoreid="367605821374201879" id="_DataListTD_tblDataList_20">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643289"
					islabel="0"
					keylist="lemonysoft_item_key_3189774_61375,lemonysoft_item_key_3189774_44649"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">1.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<div style="line-height:20px;font-size:10pt">铸铁试件压缩时的最终破坏应力为<input IsITT001input=1 class=item_blank value=""  style="width:120;" id=lemonysoft_item_key_3189774_61375 name=lemonysoft_item_key_3189774_61375>；铸铁试件扭转的最终破坏应力为<input IsITT001input=1 class=item_blank value=""  style="width:120;" id=lemonysoft_item_key_3189774_44649 name=lemonysoft_item_key_3189774_44649>。??<\/div>'
										)
									);
								</script>
								<div style="line-height: 20px; font-size: 10pt">
									铸铁试件压缩时的最终破坏应力为<input
										isitt001input="1"
										class="item_blank"
										value=""
										style="width: 120"
										id="lemonysoft_item_key_3189774_61375"
										name="lemonysoft_item_key_3189774_61375"
									/>；铸铁试件扭转的最终破坏应力为<input
										isitt001input="1"
										class="item_blank"
										value=""
										style="width: 120"
										id="lemonysoft_item_key_3189774_44649"
										name="lemonysoft_item_key_3189774_44649"
									/>。??
								</div>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_key_3189774_61375',
										'',
										'1'
									);
								</script>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_key_3189774_44649',
										'',
										'1'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_21"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_21');}"
		>
			<td scoreid="367605821374201880" id="_DataListTD_tblDataList_21">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643288"
					islabel="0"
					keylist="lemonysoft_item_key_3189773_42007,lemonysoft_item_key_3189773_54262,lemonysoft_item_key_3189773_42415"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">2.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<div style="line-height:20px;font-size:10pt">变截面梁的主要优点是<input IsITT001input=1 class=item_blank value=""  style="width:120;" id=lemonysoft_item_key_3189773_42007 name=lemonysoft_item_key_3189773_42007>、<input IsITT001input=1 class=item_blank value=""  style="width:120;" id=lemonysoft_item_key_3189773_54262 name=lemonysoft_item_key_3189773_54262>、<input IsITT001input=1 class=item_blank value=""  style="width:132;" id=lemonysoft_item_key_3189773_42415 name=lemonysoft_item_key_3189773_42415>。<\/div>'
										)
									);
								</script>
								<div style="line-height: 20px; font-size: 10pt">
									变截面梁的主要优点是<input
										isitt001input="1"
										class="item_blank"
										value=""
										style="width: 120"
										id="lemonysoft_item_key_3189773_42007"
										name="lemonysoft_item_key_3189773_42007"
									/>、<input
										isitt001input="1"
										class="item_blank"
										value=""
										style="width: 120"
										id="lemonysoft_item_key_3189773_54262"
										name="lemonysoft_item_key_3189773_54262"
									/>、<input
										isitt001input="1"
										class="item_blank"
										value=""
										style="width: 132"
										id="lemonysoft_item_key_3189773_42415"
										name="lemonysoft_item_key_3189773_42415"
									/>。
								</div>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_key_3189773_42007',
										'',
										'1'
									);
								</script>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_key_3189773_54262',
										'',
										'1'
									);
								</script>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_key_3189773_42415',
										'',
										'1'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_22"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_22');}"
		>
			<td scoreid="367605821374201881" id="_DataListTD_tblDataList_22">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643287"
					islabel="0"
					keylist="lemonysoft_item_key_3189772_19758"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">3.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<div style="line-height:20px;font-size:10pt">主平面上的切应力等于<input IsITT001input=1 class=item_blank value=""  style="width:120;" id=lemonysoft_item_key_3189772_19758 name=lemonysoft_item_key_3189772_19758>。<\/div>'
										)
									);
								</script>
								<div style="line-height: 20px; font-size: 10pt">
									主平面上的切应力等于<input
										isitt001input="1"
										class="item_blank"
										value=""
										style="width: 120"
										id="lemonysoft_item_key_3189772_19758"
										name="lemonysoft_item_key_3189772_19758"
									/>。
								</div>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_key_3189772_19758',
										'',
										'1'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_23"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_23');}"
		>
			<td scoreid="367605821374201882" id="_DataListTD_tblDataList_23">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643286"
					islabel="0"
					keylist="lemonysoft_item_key_3189749_29568"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">4.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<div style="line-height:20px;font-size:10pt">自由落体冲击问题的动荷系数为<img src="@@IPEXP_FILES_WEB\/learning3\/media\/itembank\/2ecedb8cbe044ffa84ddb8cae704d133.png">，其中h表示<input IsITT001input=1 class=item_blank value=""  style="width:144;" id=lemonysoft_item_key_3189749_29568 name=lemonysoft_item_key_3189749_29568>。<\/div>'
										)
									);
								</script>
								<div style="line-height: 20px; font-size: 10pt">
									自由落体冲击问题的动荷系数为<img
										src="http://learning.wencaischool.net/files/learning3/media/itembank/2ecedb8cbe044ffa84ddb8cae704d133.png"
									/>，其中h表示<input
										isitt001input="1"
										class="item_blank"
										value=""
										style="width: 144"
										id="lemonysoft_item_key_3189749_29568"
										name="lemonysoft_item_key_3189749_29568"
									/>。
								</div>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_key_3189749_29568',
										'',
										'1'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>
		<tr
			id="tr_tblDataList_24"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_24');}"
		>
			<td scoreid="367605821374201883" id="_DataListTD_tblDataList_24">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643285"
					islabel="0"
					keylist="lemonysoft_item_key_3189748_13955"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">5.</td>
							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<div style="line-height:20px;font-size:10pt">若材料服从胡克定律，且物体的变形满足小变形，则该物体的变形能与载荷之间呈现<input IsITT001input=1 class=item_blank value=""  style="width:120;" id=lemonysoft_item_key_3189748_13955 name=lemonysoft_item_key_3189748_13955>关系。<\/div>'
										)
									);
								</script>
								<div style="line-height: 20px; font-size: 10pt">
									若材料服从胡克定律，且物体的变形满足小变形，则该物体的变形能与载荷之间呈现<input
										isitt001input="1"
										class="item_blank"
										value=""
										style="width: 120"
										id="lemonysoft_item_key_3189748_13955"
										name="lemonysoft_item_key_3189748_13955"
									/>关系。
								</div>
								<script language="javascript">
									showAnswer(
										'lemonysoft_item_key_3189748_13955',
										'',
										'1'
									);
								</script>
							</td>
						</tr>
					</tbody>
				</table>
			</td>
		</tr>


		<tr
			id="tr_tblDataList_25"
			onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_25');}"
		>
			<td scoreid="367605821374201884" id="_DataListTD_tblDataList_25">
				<table
					width="100%"
					border="0"
					id="tblItem_350189608725643267"
					islabel="0"
					keylist="lemonysoft_item_key_3189747_38413"
					style=""
				>
					<tbody>
						<tr>
							<td width="30" valign="top">6.</td>


							<td>
								<script language="javascript">
									document.write(
										$wapper.api.getDisplayFormat(
											'<div style="line-height:20px;font-size:10pt">一受拉弯组合变形的圆截面钢轴，若用第三强度理论设计的直径为d3，用第四强度理论设计的直径为d4，则d3<input IsITT001input=1 class=item_blank value=""  style="width:120;" id=lemonysoft_item_key_3189747_38413 name=lemonysoft_item_key_3189747_38413>d4。<\/div>'
										)
									);
								</script>


								<div style="line-height: 20px; font-size: 10pt">
									一受拉弯组合变形的圆截面钢轴，若用第三强度理论设计的直径为d3，用第四强度理论设计的直径为d4，则d3
<input
    isitt001input="1"
    class="item_blank"
    value=""
    style="width: 120"
    id="lemonysoft_item_key_3189747_38413"
    name="lemonysoft_item_key_3189747_38413"
/>
                                    d4。
								</div>


								<script language="javascript">
									showAnswer(
										'lemonysoft_item_key_3189747_38413',
										'',
										'1'
									);
								</script>
							</td>



						</tr>
					</tbody>
				</table>

				<script language="javascript">
					var oSpan = document.getElementsByTagName('SPAN');
					for (var i = oSpan.length - 1; i >= 0; i--) {
						if (oSpan[i].getAttribute('IsScoreRemark') == '1') {
							oSpan[i].innerHTML =
								'&nbsp;&nbsp;<font color=gray>(第1-6题每题4分)</font>';
							break;
						}
					}
				</script>
			</td>
		</tr>
	</tbody>
</table>
