let pageTools = require('../../../../../utils/pageTools.js');
let api = require('../../../../utils/api.js');
const crypto = require('crypto');
let qs = require('qs');

//从给定的最大值最小值之间随机选一个数值
function $n(min, max) {
    if (arguments.length == 1) {
        max = min + 1;
        if (min > 1) {
            min = min - 1;
        }
    } else if (arguments.length == 0) {
        min = 4;
        max = 6;
    }
    return parseInt(Math.random() * (max - min + 1) + min, 10) * 1000;
}

async function videoTask(taskObj,courseStore, infoLogger, attachment, defaults, attachLogStr, chapterCookieStr) {
    // 1.通过API获取视频信息 videoInfoResult
    let videoInfoPath = `https://mooc1.chaoxing.com/ananas/status/${attachment.property.objectid}`;
    let videoParams = {
        k: courseStore.$fid,
        flag: 'normal',
        _dc: String(Math.round(new Date())),
    };
    let videoInfoUrl = pageTools.buildUrl(videoInfoPath, videoParams);
    let videoInfoResult = await api.getVideoInfo(videoInfoUrl, chapterCookieStr);
    if (!videoInfoResult || !videoInfoResult.duration) {
        await infoLogger(`${attachLogStr} 获取视频信息失败，跳过任务：${attachment.property.name}`, 'red');
        return;
    }

    // 2.汇总视频信息
    // courseStore
    let classId = courseStore.classId;
    let uid = courseStore.$uid;
    let beisu = courseStore.beisu;
    // attachment
    let jobid = attachment.jobid;
    let otherInfo = attachment.otherInfo;
    let rt = attachment.property.rt || '0.9';
    let dtype = attachment.property.module.includes('audio') ? 'Audio' : 'Video';
    // videoInfoResult
    let objectid = videoInfoResult.objectid;
    let duration = videoInfoResult.duration;
    let dtoken = videoInfoResult.dtoken;
    // defaults
    let reportUrl = defaults.reportUrl;
    // others
    // let isdrag = '0'; //修改视频进度url的一个参数，3表示课开始，2表示暂停  0表示继续 4表示课件完成
    let videojs_id = String(parseInt($n(1000, 9999)));

    await infoLogger(`${attachLogStr} 视频总时长：${duration}秒`);
    // 根据playingTime生成一个API连接
    function genUrl(playingTime, isdrag) {
        // 计算额外数据
        let strEc = `[${classId}][${uid}][${jobid}][${objectid}][${playingTime * 1000}][d_yHJ!$pdA~5][${duration * 1000}][0_${duration}]`;
        let enc = crypto.createHash('md5').update(strEc).digest('hex');
        let videoPath = reportUrl + '/' + dtoken;

        // 把已有数据和计算数据进行拼接
        let videoParams = {
            clazzId: classId, //courseStore.classId
            playingTime: playingTime, //自定义
            duration: duration, // videoInfoResult.duration
            clipTime: '0_' + duration, // videoInfoResult.duration
            objectId: objectid, // videoInfoResult.objectid;
            otherInfo: otherInfo, // attachment.otherInfo
            jobid: jobid, // attachment.jobid
            userid: uid, // courseStore.$uid
            isdrag: isdrag, // 自定义
            view: 'pc',
            enc: enc, // 计算得出
            rt: rt, // attachment.property.rt || "0.9"
            dtype: dtype, // attachment.property.module.includes("audio") ? "Audio" : "Video"
            _t: Date.now(),
            attDuration: attachment.attDuration,
            attDurationEnc: attachment.attDurationEnc,
            videoFaceCaptureEnc: attachment.videoFaceCaptureEnc,
        };
        let videoUrl = videoPath + '?' + qs.stringify(videoParams, { encoder: str => str });
        return { playingTime, videoUrl, isdrag };
    }

    // 根据duration生成所有的API连接
    function genUrlList() {
        // 修改视频进度url的列表
        let videoDataList = [];

        //根据剩余时间，把剩下所有发送APIurl全部都生成
        for (let playingTime = 0; playingTime < duration; playingTime += 60 * beisu) {
            let isdrag = '0';
            //1.判断视频是否完成
            if (playingTime == 0) isdrag = '3';
            videoDataList.push(genUrl(playingTime, isdrag));
        }
        videoDataList.push(genUrl(duration, '4'));

        return videoDataList;
    }

    //2.获取‘修改视频进度’的url列表，这个是一次性把所有url全部都生成完毕，然后逐个发送请求
    let videoDataList = genUrlList();

    //3.遍历url列表，一分钟一个，发送修改视频进度请求 第四层循环，L循环
    for (let i = 0; i < videoDataList.length; i++) {
        // 取出一个视频片段
        let videoFragment = videoDataList[i];

        // 获取对应的url和isdrag
        let { playingTime, videoUrl, isdrag } = videoFragment;

        // 计算剩余时间
        let playMin = Math.round(playingTime / 60);
        let leftMin = Math.round((duration - playingTime) / 60);

        //发送请求
        let watchResult;
        try {
            watchResult = await api.updateDuration(taskObj,videoUrl, chapterCookieStr, isdrag, infoLogger);
        } catch (error) {
            await infoLogger(`${attachLogStr} 修改进度失败:${error.message}`, 'red');
            return;
        }

        // 过一分钟，发送一个准备好的url请求
        if (isdrag !== '4') {
            await infoLogger(`${attachLogStr} 视频已观看${playMin}分钟，剩余大约${leftMin}分钟`);
            await new Promise(r => setTimeout(r, 60 * 1000));
            continue;
        }

        if (isdrag == '4') {
            //判断结果
            if (watchResult.isPassed) {
                await infoLogger(`[${attachLogStr}视频任务完成：${attachment.property.name}`, 'green');
                return;
            }
            if (!watchResult.isPassed) {
                await infoLogger(`${attachLogStr} 视频任务未完成：${attachment.property.name},将进行重试`, 'red');
                for (let j = 0; j < 5; i++) {
                    await new Promise(r => setTimeout(r, 2 * 60 * 1000));
                    await infoLogger(`进行第${i + 1}次重试`);
                    watchResult = await api.updateDuration(videoUrl, chapterCookieStr, isdrag, infoLogger);
                    if (watchResult.isPassed) {
                        await infoLogger(`[${attachLogStr}视频任务完成：${attachment.property.name}`, 'green');
                        break;
                    }
                }
                await infoLogger(`[${attachLogStr}重试之后依然失败：${attachment.property.name}`, 'red');
                return;
            }
        }
    }
}

module.exports = videoTask;
