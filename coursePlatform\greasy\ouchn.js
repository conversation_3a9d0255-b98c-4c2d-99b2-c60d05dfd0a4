class ouchn_api {
    constructor(config) {
        this.config = config;
    }
    async init_button() {
        if (this.config.front_url[5] == 'ng#' || this.config.front_url[5] == 'ng') {
            GM_setValue('resource_farming_state', false); //    跨域访问，清空默认状态
            div_zhu.append("<button id='x_res' ><span>一键完成资源</span></button>");
        } else if (this.config.front_url[5] == 'learning-activity') {
            this.choice_function();
            if (GM_getValue('resource_farming_state')) {
                div_zhu.append("<button id='x_xxx' ><span>正在刷资源，请稍后</span></button>");
                aner.show('hide');
                aner.text('如需暂停请刷新上一级页面');
            } else {
                if (GM_getValue('resource_farming_main_state')) {
                    div_zhu.append('<button onclick=\'GM_setValue("resource_farming_main_state",false);location.reload()\'><span>停止翻页</span></button>');
                } else {
                    div_zhu.append('<button onclick=\'GM_setValue("resource_farming_main_state",true);location.reload()\'><span>自动翻页</span></button>');
                }
            }
        } else if (this.config.front_url[6] == 'submission') {
            // div_zhu.append("<button id='x_start' ><span>开始搜题</span></button>");
            aner.css('display', 'block');
            aner.text('正在导入题库中');
            if (!GM_getValue(this.config.front_url[7])) {
                let flag = await this.get_quiz_result(this.config.front_url[4], this.config.front_url[7]);
            } else {
                aner.text('题库已存在');
            }
        } else {
            div_zhu.append('<button onclick=\'window.open("https://d.metost.com");\'><span>MeTo题库</span></button>');
        }
    }

    get_submission(exam, submission) {
        return new Promise((resolve, rejcet) => {
            $.ajax({
                type: 'GET',
                xhrFields: {
                    withCredentials: true, //允许跨域发送cookies
                },
                url: 'https://lms.ouchn.cn/api/exams/' + exam + '/submissions/' + submission,
                success: function (res) {
                    resolve(res);
                },
            });
        });
    }

    async get_quiz_result(exams, submissions) {
        let res = await this.get_submission(exams, submissions);
        var status = 'error';
        let answers = this.reset_answer(res);
        answers.title = GM_getValue(examId) || '未命名试卷';
        if (answers == null || JSON.stringify(answers) == '{}') {
            return status;
        }
        let obj = {
            poolId: this.config.poolId,
            userId: this.config.tk_uid,
            tags: [answers.title, '国开'],
            title: 'gk_' + submissions,
            problems: [],
        };
        let data = {};

        answers.rows.forEach(row => {
            if (row.subject.length < 4 || row.answers == '') {
                console.log('题目录入有误:');
                console.log(row);
                return; //跳出循环
            }
            data = {
                tags: ['国开'],
                text: row.subject,
                answer: JSON.stringify(row.answers),
            };
            data.tags.push(row.type);
            let l = [
                'choice_A',
                'choice_B',
                'choice_C',
                'choice_D',
                'choice_E',
                'choice_F',
                'choice_G',
                'choice_H',
                'choice_I',
                'choice_J',
                'choice_K',
                'choice_L',
                'choice_M',
                'choice_N',
                'choice_O',
                'choice_P',
                'choice_Q',
                'choice_R',
                'choice_S',
                'choice_T',
                'choice_U',
                'choice_V',
                'choice_W',
                'choice_X',
                'choice_Y',
                'choice_Z',
            ];
            let i = 0;
            row.options.forEach(option => {
                data[l[i]] = option;
                i = i + 1;
            });
            obj.problems.push(data);
        });
        if (obj.problems.length == 0) {
            return;
        }
        aner.css('display', 'block');
        aner.text('因api被攻击暂不支持导入题库');
        aner.append(`</br>如需请更新完整版</a>进行更多体验`);
        return status;
    }

    reset_answer(data) {
        let newData = {};
        if (data == null) {
            return console.log('并未获取到题库数据');
        }
        if ('subjects_data' in data) {
            console.log('国开题库重组中');
            console.log(data);
            let subjects = data.subjects_data.subjects;
            let submission_score_data = data.submission_score_data;
            let submission_data = data.submission_data;
            newData.title = '暂无';
            newData.rows = [];
            subjects.forEach(row => {
                let _data = {};
                _data.id = row.id;
                let imgs = null;
                try {
                    imgs = $.parseHTML(row.description); // 去除特殊字符报错
                } catch (e) {
                    console.log(e);
                }
                row.description = window.my.HtmlUtil.htmlDecode(row.description);
                if (imgs) {
                    imgs.forEach(async function (img, index) {
                        if (img.localName == 'img') {
                            row.description += img.outerHTML;
                        }
                    });
                }
                // row.subject = row.subject.substr(0,500) //截断前500个字符
                _data.subject = row.description;
                _data.options = [];
                _data.answers = [];
                _data.type = row.type;
                let tmp_option = null;
                // if(row.options.length){
                //     tmp_option =  JSON.parse(row.options)
                // }
                // if(tmp_option){
                row.options.forEach(option => {
                    _data.options.push(window.my.HtmlUtil.htmlDecode(option.content).trim());
                    if (option.is_answer) {
                        _data.answers.push(window.my.HtmlUtil.htmlDecode(option.content).trim());
                    } else if (!('is_answer' in option)) {
                        //如果不包含这个值 则判断用户选择
                        if (!submission_score_data) {
                            return;
                        }
                        if (submission_score_data[_data.id] == row.point) {
                            //如果用户答案正确
                            submission_data.subjects.forEach(element => {
                                if (element.answers) {
                                    //填空题
                                    element.answers.forEach(answer => {
                                        _data.answers.push(window.my.HtmlUtil.htmlDecode(answer.content).trim());
                                    });
                                } else {
                                    //选择题
                                    element.answer_option_ids.forEach(answer_id => {
                                        if (option.id == answer_id) {
                                            _data.answers.push(window.my.HtmlUtil.htmlDecode(option.content).trim());
                                        }
                                    });
                                }
                            });
                        }
                    }
                });
                if (_data != null) {
                    newData.rows.push(_data);
                }
            });
        } else {
            console.log(data);
        }
        console.log(newData);
        return newData;
    }

    get_userid() {
        return new Promise((resolve, rejcet) => {
            $.ajax({
                type: 'GET',
                xhrFields: {
                    withCredentials: true, //允许跨域发送cookies
                },
                url: 'https://lms.ouchn.cn/user/settings',
                success: function (res) {
                    resolve(res);
                },
            });
        });
    }
    // get_user_inf(){
    //     return new Promise((resolve,rejcet)=>{
    //         $.ajax({
    //             type: 'GET',
    //             xhrFields: {
    //                 withCredentials: true  //允许跨域发送cookies
    //             },
    //             url:"https://lms.ouchn.cn/api/user/***********/accounts",
    //             success: function(res) {
    //                 resolve(res[0]);
    //             }
    //         });
    //     });
    // }
    async get_user_obj() {
        // let a = await this.get_userid()
        // a = $(a)
        // eval(a.find("script")[0])
        // console.log(globalData)
        // let user_inf = await(this.get_user_inf());
        // if(!user_inf){
        //     return;
        // }
        // console.log(user_inf)
        let user_inf = _this.globalData.user;
        // GM_setValue("userimg",user_inf.avatarUrl||"");
        // let img_table = $(shadowContent.querySelector("#x_set"))
        // img_table.css("background","url(" +  user_inf.avatarUrl||"" + ")")
        this.config.user_id = 'gk_' + user_inf.id;
        this.config.full_name = user_inf.name;
        let obj = {
            unionid: this.config.user_id,
            username: this.config.full_name,
            poolId: 'ec942b0b-38c6-3256-b0e1-2a33428d4bbc',
            grade: '国开',
        };
        return obj;
    }

    get_activity_reads(course) {
        return new Promise((resolve, rejcet) => {
            $.ajax({
                type: 'GET',
                xhrFields: {
                    withCredentials: true, //允许跨域发送cookies
                },
                url: 'https://lms.ouchn.cn/api/course/' + course + '/activity-reads-for-user',
                success: function (res) {
                    resolve(res);
                },
            });
        });
    }
    get_completeness(course) {
        return new Promise((resolve, rejcet) => {
            $.ajax({
                type: 'GET',
                xhrFields: {
                    withCredentials: true, //允许跨域发送cookies
                },
                url: 'https://lms.ouchn.cn/api/course/' + course + '/my-completeness',
                success: function (res) {
                    resolve(res);
                },
            });
        });
    }
    get_models(course) {
        return new Promise((resolve, rejcet) => {
            $.ajax({
                type: 'GET',
                xhrFields: {
                    withCredentials: true, //允许跨域发送cookies
                },
                url: 'https://lms.ouchn.cn/api/courses/' + course + '/modules',
                success: function (res) {
                    resolve(res);
                },
            });
        });
    }
    get_all_activities(course, str) {
        return new Promise((resolve, rejcet) => {
            $.ajax({
                type: 'GET',
                xhrFields: {
                    withCredentials: true, //允许跨域发送cookies
                },
                url: 'https://lms.ouchn.cn/api/course/' + course + '/all-activities?module_ids=' + str,
                success: function (res) {
                    resolve(res);
                },
            });
        });
    }
    async choice_function() {
        if (!GM_getValue('resource_farming_state') && !GM_getValue('resource_farming_main_state')) {
            return;
        }
        for (let i = 10; i; i--) {
            console.log($('.activity-content').text().length);
            await this.sleep(1000);
            if ($('.activity-content').find('.text-too-long').length) {
                console.log('检测到ppt按钮');
                $('.activity-content').find('.text-too-long').click();
                break;
            } else if ($('.join-button').length) {
                console.log('课堂直播');
                break;
            } else if ($('video').length && $('video')[0].duration) {
                console.log('视频加载');
                let video = $('video')[0];
                video.muted = true;
                while (1) {
                    if (video.ended) {
                        break;
                    }
                    $('.mvp-fonts-play').click();
                    // let mi  = video.duration - video.currentTime
                    // console.log(mi)
                    await this.sleep(1000);
                }

                break;
            } else if ($('.activity-content').find('.page-box').length) {
                console.log('文章加载');
                break;
            } else if ($('.exam-activity-box').length) {
                console.log('测试题加载');
                await this.sleep(1000); //测试题box加载过快，可能导致没有下一个
                break;
            }
        }

        await this.sleep(3000);
        GM_setValue('resource_farming_state', false);
        $('.next-btn').click();
    }
    sleep = delay => new Promise(resolve => setTimeout(resolve, delay));
    async resource_farming() {
        let course = this.config.front_url[4];
        let url = 'https://lms.ouchn.cn/course/' + course + '/learning-activity/full-screen#/';
        let activity_list = [];
        let divs = $('.learning-activity');
        divs.each((index, div) => {
            let type = $(div).find('.activity-summary').attr('ng-switch-when');
            if ($(div).find('.completeness').attr('class') != 'completeness full' && type != 'exam' && type != 'forum' && type != 'homework') {
                try {
                    var id = $(div).attr('id').substr(18);
                } catch {
                    return;
                }
                activity_list.push(id);
            }
        });
        if (activity_list.length) {
            console.log(activity_list);
        } else {
            aner.show('slow');
            aner.text('未检测到页面资源，请等待页面加载完毕。 ');
            aner.append('</br>若未展开资源，请点击全部展开。');
            $(shadowContent.querySelector('#x_res')).attr('disabled', false);
            return;
        }
        aner.show('slow');
        aner.text('部分浏览器默认关闭弹出窗口，请在右上角开启');

        // return
        for (let i = 0; i < activity_list.length; i++) {
            GM_setValue('resource_farming_state', true);
            let childwindow = window.open(url + activity_list[i], '_blank');
            while (1) {
                if (GM_getValue('resource_farming_state')) {
                    $(shadowContent.querySelector('#x_res')).text('剩余' + (activity_list.length - i) + '资源');
                    await this.sleep(1000);
                } else {
                    childwindow.close();
                    break;
                }
            }
        }
        $(shadowContent.querySelector('#x_res')).text('全部完成');
    }
}
