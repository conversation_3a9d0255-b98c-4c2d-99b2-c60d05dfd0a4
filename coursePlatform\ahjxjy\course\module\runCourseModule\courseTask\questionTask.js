let api = require("../../../../utils/api.js");
async function questionTask(infoLogger, globalStore, courseObj, cellObj, cellLogStr) {
    if (cellObj.status) {
        await infoLogger(`${cellLogStr}：当前${cellObj.icon}课件已经完成，不需要重复 《${cellObj.title}》`);
        return;
    }

    await infoLogger(`${cellLogStr}：开始question任务 《${cellObj.title}》`);

    // //获取questionId
    // let studyingRes2 = await axiosIns({
    //     method: "post",
    //     url: "https://main.ahjxjy.cn/study/studying/studying",
    //     headers: {
    //         cookie: globalStore.cookieStr,
    //     },
    //     data: `courseOpenId=${courseObj.courseOpenId}&cellId=${cellObj.id}&schoolCode=${globalStore.schoolCode}`,
    // });

    // //获取已回复列表
    // let replyDetailsRes = await axiosIns({
    //     method: "post",
    //     url: "https://main.ahjxjy.cn/study/questionAnswer/getReplyDetails",
    //     headers: {
    //         cookie: globalStore.cookieStr,
    //     },
    //     data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}&questionId=${studyingRes2.cell.questionId}`,
    // });

    // //获取最近一条消息
    // let lastReply=replyDetailsRes.list[0]
    // lastReply=lastReply.replace(/(<([^>]+)>)/gi, "")
    // lastReply=lastReply?lastReply:cellObj.title


    // //添加回复
    // let addReplyRes = await axiosIns({
    //     method: "post",
    //     url: "https://main.ahjxjy.cn/study/questionAnswer/addReply",
    //     headers: {
    //         cookie: globalStore.cookieStr,
    //     },
    //     data: `courseOpenId=${courseObj.courseOpenId}&schoolCode=${globalStore.schoolCode}&questionId=${
    //         studyingRes2.cell.questionId
    //     }&content=${encodeURIComponent(lastReply)}`,
    // });

    // //不知道作用
    // await axiosIns({
    //     method: "get",
    //     url: `https://main.ahjxjy.cn/study/html/content/studying/?courseOpenId=${courseObj.courseOpenId}&cellId=${cellObj.id}&schoolCode=${globalStore.schoolCode}`,
    //     headers: {
    //         cookie: globalStore.cookieStr,
    //     },
    // });

    // if (addReplyRes && addReplyRes.code == 1) {
    //     await infoLogger(`${cellLogStr}：question任务回复成功 《${cellObj.title}》`);
    // }

    await api.finishChapter(globalStore, courseObj, cellObj);
    await infoLogger(`${cellLogStr}：virtualtestnew任务状态改为已完成 《${cellObj.title}》`);
}

module.exports = questionTask;
