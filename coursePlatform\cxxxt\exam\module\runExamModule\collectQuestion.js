let path = require('path');
let Model = require('../../../../../config/sequelize.config.js');
let { v4 } = require('uuid');

async function getQuestionList(mainPage, courseName) {
    // 收集题目
    let questionList = await mainPage.evaluate(courseName => {
        function handleImgs(s) {
            let imgEs = s.match(/(<img([^>]*)>)/gi);
            if (imgEs) {
                for (let j = 0, k = imgEs.length; j < k; j++) {
                    let urls = imgEs[j].match(/http[s]?:\/\/(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\(\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+/),
                        url;
                    if (urls) {
                        url = urls[0].replace(/http[s]?:\/\//, '');
                        s = s.replaceAll(imgEs[j], url);
                    }
                }
            }
            return s;
        }

        function trim(s) {
            return (
                s
                    //删除多余字符串
                    .replace(/(<([^>]+)>)/gi, '') //去掉所有的html标记 <a></a> ==> ''
                    .replace(/^\d+[\.、]/, '') //删除开头"1." 和"1、" 的内容
                    .replace(/\(.{10}\)/, '') //删除所有 (判断题, 2.0 分) 的内容
                    .replaceAll('&nbsp;', '')
                    .replaceAll('\n', '') //删除所有的换行符
                    .replace(/\s{2,}/g, ' ') //删除两个及以上的空格，保留单个空格
                    .replace(/\([^(]*题,\s*\d+(\.\d+)?\s*分\)\s*/, '') // 删除开头(简答题, 10.0 分)
                    .replace(/\([^(]*题,\s*\d+(\.\d+)?\s*points\)\s*/, '') // 删除开头(判断题, 2.0 points)
                    .trim()
            );
        }

        function handleContent(content) {
            return trim(handleImgs(content));
        }

        let questionList = [];
        let TimuList = document.querySelectorAll('#fanyaMarking > div.fanyaMarking_left.whiteBg > div.mark_table.padTop60 > div.mark_item>div.questionLi');
        for (let i = 0; i < TimuList.length; i++) {
            let questionObj = {
                courseName,
                options: [],
                platform: '超星学习通',
            };

            let timuDom = TimuList[i];

            // 题目内容
            let questionFull = timuDom.querySelector('h3').innerText;
            let content = handleContent(questionFull);
            questionObj.content = content;

            //题目类型
            let type = timuDom.querySelector('h3>span').innerText;
            type = type.match(/\(([^,]+),/)[1];
            questionObj.type = type;

            // 题目选项 class="mark_letter colorDeep qtDetail"
            switch (type) {
                case '单选题':
                case '多选题': {
                    let liList = timuDom.querySelectorAll('.mark_letter.colorDeep.qtDetail>li');
                    liList.forEach(li => {
                        // A. 事关人民群众切身利益
                        let optionText = li.innerText;
                        let optionKey = optionText.slice(0, 1);
                        let optionContent = optionText.slice(2);
                        questionObj.options.push(`${optionKey}:${handleContent(optionContent)}`);
                    });
                    break;
                }
                case '判断题': {
                    questionObj.options = ['A:对', 'B:错'];
                    break;
                }
                default: {
                    questionObj.options = [];
                    break;
                }
            }

            // 我的答案，为了修正判断题
            switch (type) {
                case '简答题': {
                    let myAnswerTag = timuDom.querySelector('dd.selectable-text');
                    let myAnswer = myAnswerTag.innerText;
                    questionObj.myAnswer = myAnswer;
                    break;
                }
                case '填空题': {
                    questionObj.myAnswer = '填空题，未处理';
                    break;
                }
                case '判断题': {
                    let myAnswer = timuDom.querySelector('div.mark_key.clearfix > span > span').innerText;
                    questionObj.myAnswer = myAnswer == '对' ? 'A' : 'B';
                    break;
                }
                case '单选题':
                case '多选题': {
                    let myAnswer = timuDom.querySelector('div.mark_key.clearfix > span > span').innerText;
                    questionObj.myAnswer = myAnswer;
                    break;
                }
                default: {
                    questionObj.myAnswer = `未处理类型:${type}`;
                    break;
                }
            }

            //获取题目结果 有可能不存在 questionObj.isCorrect
            let spanTag = timuDom.querySelector('div.totalScore.fr > i');
            if (spanTag) {
                questionObj.score = spanTag.innerText * 1;
                questionObj.isCorrect = questionObj.score == 0 ? false : true;
            }

            // 获取正确答案 有可能不存在 questionObj.answers
            switch (type) {
                case '简答题': {
                    let answerTag = timuDom.querySelector('dl.mark_fill.colorGreen>dd');
                    if (answerTag) {
                        questionObj.answers = answerTag.innerText;
                        questionObj.answersContent = JSON.stringify([questionObj.answers]);
                    }
                    break;
                }
                case '填空题': {
                    let answerSpanTag = timuDom.querySelector('div.mark_answer dd');
                    if (answerSpanTag) {
                        let answerContent = answerSpanTag.innerText;
                        answerContent = handleContent(answerContent);
                        // 如果最后一个是句号。就删除
                        if (answerContent.endsWith('。')) {
                            answerContent = answerContent.slice(0, -1);
                        }
                        answerContent = answerContent.replace(/\s/g, '');
                        answerContent = answerContent.split('；').join('|');
                        answerContent = answerContent.split('。').join('|');
                        answerContent = answerContent.replace(/^\(\d+\)/, '');
                        questionObj.answers = answerContent;
                        questionObj.answersContent = JSON.stringify([]);
                        break;
                    }
                    break;
                }
                case '判断题': {
                    let answerTag = timuDom.querySelector('span.rightAnswerContent');
                    if (answerTag) {
                        questionObj.answers = answerTag.innerText == '对' ? 'A' : 'B';
                        questionObj.answersContent = JSON.stringify([answerTag.innerText]);
                    }
                    break;
                }
                case '单选题':
                case '多选题': {
                    let answerTag = timuDom.querySelector('span.rightAnswerContent');
                    if (answerTag) {
                        questionObj.answers = answerTag.innerText;
                        let answersContent = [];
                        for (let str of questionObj.answers) {
                            let queIndex, queValue;
                            questionObj.options.forEach(item => {
                                let firstStr = item.slice(0, 1);
                                if (firstStr == str) {
                                    [queIndex, queValue] = item.split(/:(.+)/);
                                }
                            });
                            answersContent.push(queValue);
                            answersContent = [...answersContent].sort((x, y) => x.localeCompare(y, 'zh-Hans-CN'));
                        }
                        questionObj.answersContent = JSON.stringify(answersContent);
                    }
                    break;
                }
            }

            questionList.push(questionObj);
        }
        return questionList;
    }, courseName);

    // 收集题目
    let collectCount = 0; //标记
    let updateCount = 0; // 更新
    questionLoop: for (let i = 0; i < questionList.length; i++) {
        let questionObj = questionList[i];

        let localQuestion = await Model.bank.findOne({
            where: {
                type: questionObj.type,
                content: questionObj.content,
                platform: questionObj.platform,
            },
        });
        if (!localQuestion) {
            if (questionObj.answers) {
                await Model.bank.create({
                    id: v4(),
                    type: questionObj.type,
                    content: questionObj.content,
                    platform: questionObj.platform,
                    course_name: questionObj.courseName,
                    answers: questionObj.answers,
                    answers_content: questionObj.answersContent,
                    add_time: new Date(),
                    options: JSON.stringify(questionObj.options),
                    comment: '收集',
                });
            }
            continue;
        }

        switch (questionObj.type) {
            case '单选题':
            case '多选题': {
                // 显示答案
                if (questionObj.answers) {
                    // 如果答案正确，就跳过
                    if (questionObj.answers == questionObj.myAnswer) {
                        break;
                    }

                    // 修正答案
                    let [affectedCount, affectedRows] = await Model.bank.update(
                        {
                            answers: questionObj.answers,
                            answers_content: questionObj.answersContent,
                            comment: '收集',
                        },
                        {
                            where: {
                                type: questionObj.type,
                                content: questionObj.content,
                                platform: questionObj.platform,
                                course_name: questionObj.courseName,
                            },
                        }
                    );
                    updateCount += affectedCount;
                    break;
                }

                // 显示结果
                if (questionObj.isCorrect !== undefined) {
                    if (questionObj.isCorrect === true) break;
                    if (localQuestion.comment.includes('答案错误')) break;
                    let [affectedCount, affectedRows] = await Model.bank.update(
                        {
                            comment: '答案错误',
                        },
                        {
                            where: {
                                type: questionObj.type,
                                content: questionObj.content,
                                platform: questionObj.platform,
                                course_name: questionObj.courseName,
                            },
                        }
                    );
                    collectCount += affectedCount;
                    break;
                }

                break;
            }
            case '判断题': {
                // 显示答案
                if (questionObj.answers) {
                    // 如果答案正确，就跳过
                    if (questionObj.answers == questionObj.myAnswer) {
                        break;
                    }

                    // 修正答案
                    let [affectedCount, affectedRows] = await Model.bank.update(
                        {
                            answers: questionObj.answers,
                            answers_content: questionObj.answersContent,
                            comment: '收集',
                        },
                        {
                            where: {
                                type: questionObj.type,
                                content: questionObj.content,
                                platform: questionObj.platform,
                                course_name: questionObj.courseName,
                            },
                        }
                    );
                    updateCount += affectedCount;
                    break;
                }

                // 不显示答案，但显示分数
                if (questionObj.isCorrect !== undefined) {
                    if (questionObj.isCorrect === true) break;
                    if (localQuestion.comment.includes('已经修正')) break;
                    questionObj.answers = questionObj.myAnswer == 'A' ? 'B' : 'A';
                    let answers_content = questionObj.answers == 'A' ? ['对'] : ['错'];
                    let [affectedCount, affectedRows] = await Model.bank.update(
                        {
                            comment: '已经修正',
                            answers: questionObj.answers,
                            answers_content: answers_content,
                        },
                        {
                            where: {
                                type: questionObj.type,
                                content: questionObj.content,
                                platform: questionObj.platform,
                                course_name: questionObj.courseName,
                            },
                        }
                    );

                    collectCount += affectedCount;
                    break;
                }

                break;
            }
            case '简答题': {
                if (questionObj.answers) {
                    let [affectedCount, affectedRows] = await Model.bank.update(
                        {
                            answers: questionObj.answers,
                            answers_content: questionObj.answersContent,
                            comment: '收集',
                        },
                        {
                            where: {
                                type: questionObj.type,
                                content: questionObj.content,
                                platform: questionObj.platform,
                                course_name: questionObj.courseName,
                            },
                        }
                    );
                    updateCount += affectedCount;
                    break;
                }
                break;
            }
            case '填空题': {
                if (questionObj.answers) {
                    let [affectedCount, affectedRows] = await Model.bank.update(
                        {
                            answers: questionObj.answers,
                            answers_content: questionObj.answersContent,
                            comment: '收集',
                        },
                        {
                            where: {
                                type: questionObj.type,
                                content: questionObj.content,
                                platform: questionObj.platform,
                                course_name: questionObj.courseName,
                            },
                        }
                    );
                    updateCount += affectedCount;
                    break;
                }
                break;
            }
        }
    }

    return { collectCount, updateCount };
}

module.exports = getQuestionList;

if (false) {
    (async () => {
        let puppeteer = require('puppeteer');
        //创建浏览器实例
        let browser = await puppeteer.launch({
            headless: false, //显示浏览器窗口
            defaultViewport: {
                //设置浏览器视口尺寸
                width: 1920,
                height: 1080,
            },
            args: [
                '--start-maximized', //最大化启动浏览器
                '--mute-audio', //静音
                '--use-fake-ui-for-media-stream', //绕过摄像头，麦克风权限
            ],
            //利用本地chrome浏览器来测试
            // executablePath: `C:/Program Files/Google/Chrome/Application/chrome.exe`,
        });

        //创建页面实例
        // let mainPage = await browser.newPage();
        let pages = await browser.pages();
        let mainPage = pages[0];

        let url =
            'https://mooc1-api.chaoxing.com/exam-ans/exam/test/reVersionPaperMarkContentNew?courseId=*********&classId=*********&p=1&id=*********&ut=s&newMooc=true&qbanksystem=1&qbankbackurl=%2Fexam-ans%2Fexam%2Ftest%2Flook%3FcourseId%3D*********%26classId%3D*********%26examId%3D5316295%26examAnswerId%3D*********%26cpi%3D*********%26qbanksystem%3D1%26qbankbackurl%3D%252Fexam-ans%252Fexam%252Ftest%252Fexamcode%252Fexamlist%253Fedition%253D1%2526nohead%253D0%2526fid%253D&cpi=*********&openc=3367d88f5460e89206354d2b9a070adb';
        await mainPage.goto(url, { waitUntil: 'networkidle0' });
        await new Promise(r => setTimeout(r, 30000));
        let collectCount = await getQuestionList(mainPage, '*********');
    })();
}
