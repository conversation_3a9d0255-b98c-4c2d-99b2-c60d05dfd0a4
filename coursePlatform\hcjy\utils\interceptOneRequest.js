//拦截验证码请求 http://czzycj.sccchina.net/verifycode?1451
exports.verifycodeInterception = (mainPage) => {
    mainPage.on("requestfinished", async (request) => {
        let url = request.url();

        if (url.includes("https://czzycj.sccchina.net/verifycode")) {
            // //处理重定向
            // if (response && response.status() >= 300 && response.status() <= 399) {
            //     return;
            // }
            //获取响应类型
            let response = await request.response();
            let contentType = response.headers()["content-type"];

            // 处理响应体
            let responseData;
            if (contentType && contentType.includes("application/json")) {
                //如果响应体是json
                responseData = await response.json();
            } else if (contentType && contentType.includes("image")) {
                //如果响应体是图片
                responseData = await response.buffer();
            } else {
                // 其他情况一律按照文本处理
                responseData = await response.text();
            }

            //返回数据
            mainPage.apiInfo.verifycode = {
                request: {
                    method: request.method(),
                    url: request.url(),
                    headers: request.headers(),
                    postData: request.postData(),
                    resourceType: request.resourceType(),
                },
                response: responseData,
            };
            mainPage.resolve.verifycodeResolve();
        }
    });
};

//拦截token请求 http://czzycj.sccchina.net/student/common/common/getcoursewareaddress
exports.courseTokenInterception = (mainPage) => {
    mainPage.on("requestfinished", async (request) => {
        let response = await request.response();
        let url = request.url();

        //处理重定向
        if (response && response.status() >= 300 && response.status() <= 399) {
            return;
        }

        //获取科目详细信息
        if (url.includes("https://czzycj.sccchina.net/student/common/common/getcoursewareaddress")) {
            //处理响应体
            let response = await request.response();
            let contentType = response.headers()["content-type"];

            //响应体默认值
            let responseData = await response.text();

            //如果响应体是json
            if (contentType && contentType.includes("application/json")) {
                responseData = await response.json();
            }

            //如果响应体是图片
            if (contentType && contentType.includes("image")) {
                responseData = await response.buffer();
            }

            //返回数据
            mainPage.apiInfo.courseToken = {
                request: {
                    method: request.method(),
                    url: request.url(),
                    headers: request.headers(),
                    postData: request.postData(),
                    resourceType: request.resourceType(),
                },
                response: responseData,
            };
            mainPage.resolve.courseTokenResolve();
        }
    });
};

// 拦截问题列表
exports.getQuestionList = async (mainPage) => {
    mainPage.on("requestfinished", async (request) => {
        //拦截验证码
        let url = request.url();

        if (url.includes("https://exam.chinaedu.net/oxer/app/ots/TestActivity/StartAnswerPaperWithPhoto")) {
            //处理响应体
            let response = await request.response();
            let responseData = await response.json();

            mainPage.apiInfo.getQuestionList = {
                request: {
                    method: request.method(),
                    url: request.url(),
                    headers: request.headers(),
                    postData: request.postData(),
                    resourceType: request.resourceType(),
                },
                response: responseData,
            };
            mainPage.resolve.getQuestionListResolve();
        }
    });
};