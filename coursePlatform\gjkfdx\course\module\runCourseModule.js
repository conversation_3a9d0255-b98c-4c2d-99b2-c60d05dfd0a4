let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api.js');
let Model = require('../../../../config/sequelize.config.js');
let { v4 } = require('uuid');

let pageTask = require('../courseTask/pageTask');
let videoTask = require('../courseTask/videoTask');
let meterialTask = require('../courseTask/meterialTask');
// let forumTask = require('../courseTask/forumTask');
// let experimentTask = require('../courseTask/experimentTask.js');
let examTask = require('../courseTask/examTask/index.js');
let homeWorkTask = require('../courseTask/homeWorkTask/index.js');
let mixTask = require('../courseTask/mixTask.js');

async function runCourseModule(infoLogger, mainPage, courseList, globalStore) {
    let taskObj = globalStore.taskObj;
    let courseLength = courseList.length;

    courseLoop: for (let courseIndex = 0; courseIndex < courseList.length; courseIndex++) {
        // 取出一个课程对象
        let courseObj = courseList[courseIndex];

        // 获取课程ID 从课程url中提取courseCode 如果是 防止 预跑 无法获取
        courseObj.id = courseObj.url.match(/course\/(\d+)\/content/)[1];

        // 指定课程
        if (courseObj.id != 70000136194) continue;

        let courseLogStr = `课程[${courseIndex + 1}/${courseLength}]`;
        await infoLogger(`${courseLogStr} 开始课程：${courseObj.name}-${courseObj.id}`, 'blue');

        // 判断是否已经收集过了
        if (!globalStore.others.includes('收集课程')) {
            let isCollected = await Model.gjkfdx.findOne({
                where: {
                    course_name: courseObj.name,
                },
            });
            if (!isCollected) {
                await infoLogger(`${courseLogStr} 当前课程还未收集，跳过`, 'red');
                continue courseLoop;
            }
        }

        // 跳转到课程页面
        await pageTools.gotoWithRetry(mainPage, courseObj.url, { waitUntil: 'networkidle0', timeout: 90 * 1000 }, 3, infoLogger);
        let cookieStr = await pageTools.getPageCookies(mainPage);
        globalStore.globalData = await mainPage.evaluate(() => globalData);
        globalStore.userAgent = await mainPage.evaluate(() => navigator.userAgent);
        globalStore.userId = await mainPage.evaluate(() => userId.getAttribute('value'));

        await infoLogger(`开始获取<${courseObj.name}>课程的课件信息`);

        // 获取课程模块
        let modulesRes = await api.getCourseModule(courseObj.id, cookieStr);
        let courseModules = modulesRes.modules;
        // 对课程模块进行排序
        courseModules = courseModules.sort((a, b) => a.sort - b.sort);
        let courseModuleIDArr = courseModules.map(module => module.id);

        // 获取课程所有模块的课件
        let taskRes = await api.getTask(courseObj.id, courseModuleIDArr, cookieStr);
        let learnList = taskRes.learning_activities;
        let examList = taskRes.exams;
        let taskList = learnList.concat(examList);

        // 对课件进行排序 整体构造是module(模块)->syllabus(大纲)->task(任务) 三层嵌套结构
        let sortArr = [];
        for (let moduleObj of courseModules) {
            let moduleTaskList = [];
            // 当前模块有大纲
            if (moduleObj.syllabuses && moduleObj.syllabuses.length > 0) {
                let syllabuses = moduleObj.syllabuses;
                syllabuses.sort((a, b) => a.sort - b.sort);

                for (let syllabusObj of syllabuses) {
                    let syllabusTaskList = taskList.filter(task => task.syllabus_id == syllabusObj.syllabus_id);
                    syllabusTaskList.sort((a, b) => a.sort - b.sort);
                    moduleTaskList.push(...syllabusTaskList);
                }
                sortArr.push(...moduleTaskList);
            }
            // 当前模块没有大纲
            if (!moduleObj.syllabuses || moduleObj.syllabuses.length == 0) {
                let moduleTaskList = taskList.filter(task => task.module_id == moduleObj.id);
                moduleTaskList.sort((a, b) => a.sort - b.sort);
                sortArr.push(...moduleTaskList);
            }
        }

        taskList = sortArr;
        // await infoLogger(`${courseLogStr}，获取课件成功，共有${taskList.length}个课件`);

        // 获取已完成的课件列表
        let activityReadsForUserRes = await api.activityReadsForUser(courseObj.id, cookieStr);
        let activityReadList = activityReadsForUserRes.activity_reads;
        activityReadList = activityReadList.map(item => item.activity_id);
        let completedTaskList = [];
        taskList.forEach(item => {
            if (activityReadList.includes(item.id)) {
                item.isCompleted = true;
                completedTaskList.push(item);
            }
        });
        await infoLogger(`${courseLogStr}，获取课件成功，共有${taskList.length}个课件，已经完成${completedTaskList.length}个课件`, 'green');

        // if (globalStore.taskObj.others.includes('收集课程')) {
        //     let completeRate = completedTaskList.length / taskList.length;
        //     completeRate = completeRate.toFixed(2);
        //     if (completeRate < 0.8) {
        //         await infoLogger(`${courseLogStr} 课程完成进度${completeRate}，低于80%，不予收集`, 'red');
        //         continue courseLoop;
        //     }
        // }

        taskLoop: for (let taskIndex = 0; taskIndex < taskList.length; taskIndex++) {
            let taskLogStr = `课程[${courseIndex + 1}/${courseLength}]--任务[${taskIndex + 1}/${taskList.length}]`;
            let taskObj = taskList[taskIndex];

            // 过滤
            // if (!'homework'.includes(taskObj.type)) continue taskLoop;

            let isCollect = globalStore.taskObj.others.includes('收集课程');
            let isExam = 'examhomeworkmix_task'.includes(taskObj.type);
            let isComplete = taskObj.isCompleted;

            // 收集课程并且不是考试
            if (isCollect && !isExam) {
                // await infoLogger(`${taskLogStr} <${taskObj.title}> 当前类型不用收集，跳过 ${taskObj.type}`);
                continue taskLoop;
            }

            // 收集课程并且不是考试
            if (isComplete && !isExam) {
                await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 当前类型已经完成，跳过`);
                continue taskLoop;
            }

            await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 任务开始`, 'purple');

            switch (taskObj.type) {
                // 页面
                case 'page': {
                    await pageTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr);
                    break;
                }
                //腾讯会议，直播课
                case 'tencent_meeting': {
                    await pageTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr);
                    break;
                }
                //词汇表
                case 'vocabulary': {
                    await pageTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr);
                    break;
                }
                // 链接
                case 'web_link': {
                    await pageTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr);
                    break;
                }
                // 讨论
                case 'forum': {
                    await pageTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr);
                    break;
                }

                // 参考资料
                case 'material': {
                    await meterialTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr);
                    break;
                }

                // // 虚拟实验
                // case 'virtual_experiment': {
                //     await experimentTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr);
                //     break;
                // }

                // 音视频教材
                case 'online_video': {
                    await videoTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr);
                    break;
                }

                // 测试（形考）
                case 'exam': {
                    await examTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr);
                    break;
                }

                // 大作业
                case 'homework': {
                    await homeWorkTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr);
                    // globalStore.warningMessage += `<课程：${courseObj.name}大作业：${taskObj.title}>`;
                    break;
                }

                // 复合型大作业
                case 'mix_task': {
                    await mixTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr);
                    // globalStore.warningMessage += `<课程：${courseObj.name}：${taskObj.title}> 复合大作业`;
                    break;
                }

                default: {
                    await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 未知任务类型，跳过`, 'red');
                    continue taskLoop;
                }
            }

        }

        if (globalStore.taskObj.others.includes('收集课程')) {
            // 尝试更新
            let [affectedRows] = await Model.gjkfdx.update(
                {
                    status: '收集完成',
                    add_time: new Date(),
                },
                {
                    where: {
                        course_name: courseObj.name,
                        course_id: courseObj.id,
                    },
                }
            );
            if (affectedRows == 0) {
                await Model.gjkfdx.create({
                    id: v4(),
                    major: globalStore.major,
                    layer: globalStore.layer,
                    course_name: courseObj.name,
                    course_id: courseObj.id,
                    status: '收集完成',
                    add_time: new Date(),
                });
            }
        }

        await infoLogger(`${courseLogStr} 课程完成：${courseObj.name}-${courseObj.id}`, 'purple');
    }
}

module.exports = runCourseModule;
