// coursePlatform/cxxxt/course/module/runCourseModule/loopAttachment.js

let pageTools = require('../../../../utils/pageTools.js');

// 任务模块导入
let videoTask = require('./task/videoTask.js');
let workidTask = require('./task/workidTask/index.js');
let documentTask = require('./task/documentTask.js');
let bookTask = require('./task/bookTask.js');
let hyperlinkTask = require('./task/hyperlinkTask.js');
let liveTask = require('./task/liveTask.js');
let readTask = require('./task/readTask.js');
let waitForTimeWindow = require('../../../../utils/waitForTimeWindow.js');

/**
 * 遍历并处理页面中的任务列表 (attachments)
 * @param {object} taskObj - 全局任务配置对象
 * @param {object} courseStore - 当前课程信息存储对象
 * @param {function} infoLogger - 日志记录函数
 * @param {object} mainPage - Puppeteer 的 Page 对象
 * @param {Array} attachmentList - 当前页面的任务附件列表
 * @param {object} defaults - 从 mArg 获取的默认参数对象
 * @param {string} pageLogStr - 当前页面的日志前缀字符串
 * @param {string} chapterCookieStr - 当前章节页面的cookie字符串
 * @param {string} chapterId - 当前章节ID
 * @param {object} mArgJson - 当前页面的 mArg 对象
 */
async function handleAttachment(globalStore,infoLogger, mainPage, pageLogStr, chapterId, mArgJson) {
    let courseStore = globalStore.courseStore; // 从 globalStore 中获取 courseStore
    let taskObj = globalStore.taskObj;
    let attachmentList = mArgJson.attachments;
    let defaults = mArgJson.defaults;
    
    // 获取当前页面的cookieStr
    let chapterCookieStr = await pageTools.getPageCookies(mainPage);

    loopAttachment: for (let attachmentIndex = 0; attachmentIndex < attachmentList.length; attachmentIndex++) {
        let attachLogStr = `${pageLogStr}-任务[${attachmentIndex + 1}/${attachmentList.length}]`;

        // 4.1 取出一个任务
        let attachment = attachmentList[attachmentIndex];
        let attachmentName = attachment.property.name || attachment.property.title;

        // 跳过已经完成的任务
        if (attachment.isPassed) {
            await infoLogger(`${attachLogStr}：当前任务已完成，跳过：${attachmentName}`);
            continue;
        }

        // 跳过没有任务点的任务
        if (!attachment.job) {
            await infoLogger(`${attachLogStr} 当前任务已经完成或没有任务点，跳过，任务类型：${attachment.type}，任务名称：${attachmentName}`);
            continue loopAttachment;
        }

        // 预处理insertbook类型的任务
        try {
            if (attachment.property.module == 'insertbook') {
                attachment.type = 'book';
                attachment.property.name = attachment.property.bookname;
            }
        } catch (e) {
            // 忽略错误
        }

        // 防止学习时间过长
        if (!taskObj.others.includes('跳过防沉迷')) {
            await waitForTimeWindow(07, 30, 22, 50, infoLogger, mainPage);
        }

        await infoLogger(`${attachLogStr} 开始，任务类型：${attachment.type}，任务名称：${attachmentName}`, 'blue');

        // 4.2 根据任务类型，处理任务
        switch (attachment.type) {
            // 视频任务
            case 'video':
                await videoTask(taskObj, courseStore, infoLogger, attachment, defaults, attachLogStr, chapterCookieStr);
                break;
            // 作业任务
            case 'workid':
                await workidTask(mainPage, courseStore, infoLogger, attachment, defaults, attachLogStr, chapterCookieStr, chapterId);
                break;
            // 文档任务
            case 'document':
                await documentTask(courseStore, infoLogger, attachment, mArgJson, attachLogStr, chapterCookieStr, chapterId);
                break;
            // 直播任务
            case 'live':
                await liveTask(infoLogger, mainPage, attachment, mArgJson, attachLogStr, chapterCookieStr);
                break;
            // 阅读任务
            case 'read':
                await readTask(courseStore, infoLogger, attachment, mArgJson, attachLogStr, chapterCookieStr, chapterId);
                break;
            // 图书任务
            case 'book':
                await bookTask(courseStore, infoLogger, attachment, mArgJson, attachLogStr, chapterCookieStr, chapterId);
                break;
            // 链接任务
            case 'hyperlink':
                await hyperlinkTask(courseStore, infoLogger, attachment, mArgJson, attachLogStr, chapterCookieStr, chapterId);
                break;
            // 未知任务
            default:
                await infoLogger(`${attachLogStr} 不支持的类型：${attachment.type}，跳过任务：${attachmentName}`, 'red');
                break;
        }
    }
}

module.exports = handleAttachment;
