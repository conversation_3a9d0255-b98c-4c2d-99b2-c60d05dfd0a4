let axiosInsPromise = require('./getAxiosIns.js');

// 获取图片对应的base64编码
exports.getImageBase64 = async imageUrl => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        method: 'get',
        url: imageUrl,
        responseType: 'arraybuffer',
        headers: {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            Referer: 'http://www.chaoxing.com/',
            Accept: 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9',
            'Accept-Encoding': 'gzip, deflate',
        },
    };
    let res = await axiosIns(config);
    let base64Image = Buffer.from(res, 'binary').toString('base64');
    return base64Image;
};

// 1.点击验证码
exports.clickValidate = async base64Image => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: 'https://www.bingtop.com/ocr/upload/',
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: {
            username: 'mozhi0012',
            password: 'qfQw4h4WzSKt',
            captchaData: base64Image,
            captchaType: '1311',
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        code: 0,
        message: '',
        data: {
            captchaId: '1310-c5ee0cf0-d8ce-4af7-a4b6-29c4ba924ce4',
            captchaType: '1310',
            recognition: '193',
        },
    };
}

// 获取用户主页index.html
exports.getUserIndex = async cookieStr => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: 'https://lms.ouchn.cn/user/index',
        headers: {
            accept: 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            pragma: 'no-cache',
            priority: 'u=0, i',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'document',
            'sec-fetch-mode': 'navigate',
            'sec-fetch-site': 'same-origin',
            'sec-fetch-user': '?1',
            'upgrade-insecure-requests': '1',
            cookie: cookieStr,
        },
    };
    let res = await axiosIns(config);
    return res;
};

// 下载文件
exports.downloadFile = async (url, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        method: 'get',
        url: url,
        responseType: 'arraybuffer',
        headers: {
            cookie: cookieStr,
        },
    };
    let res = await axiosIns(config);
    return res;
};

// 获取用户信息
exports.getMultiInfo = async cookieStr => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://menhu.pt.ouchn.cn/ouchnapp/wap/user/get-multi-info`,
        headers: {
            cookie: cookieStr,
        },
        method: 'GET',
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        e: 0,
        m: '操作成功',
        d: {
            identitys: [
                {
                    zzjg: '软件学院阜阳信息工程学校学习中心',
                    number: '239****250840',
                    role_type: 'student',
                    sex: 1,
                    avatar: '',
                    lxsj_year: '',
                    lxsj_day: '',
                    xjyue_year: '6',
                    xjyue_day: '94',
                    sflx: '本科(专科起点)',
                    sszy: '软件工程',
                    sfby: '0',
                },
            ],
        },
    };
}

// 获取课程列表
exports.getCourseList = async (cookieStr, tab) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        method: 'post',
        url: 'https://menhu.pt.ouchn.cn/ouchnapp/wap/course/xskc-pc',
        headers: {
            cookie: cookieStr,
            'content-type': 'application/x-www-form-urlencoded',
        },
        data: `tab=${tab}&page=1&page_size=100`,
    };
    let res = await axiosIns(config);
    if (res.e != 0) {
        throw Error(`获取课程列表失败，服务器返回数据错误 msg:${res.m},e:${res.e}`);
    }
    return res;
};
if (false) {
    res = {
        e: 0,
        m: '操作成功',
        d: {
            list: [
                {
                    name: 'Dreamweaver网页设计',
                    code: '00017',
                    type: '',
                    img_url: 'https://lms.ouchn.cn:443/api/uploads/8854735/modified-image?thumbnail=700x380',
                    num: '',
                    stu_num: '8736',
                    term: '2024-02-01至',
                    start_date: '2024年02月01日',
                    teacher: '',
                    xq: '2023-2024春季',
                    url: 'https://lms.ouchn.cn:443/course/10000049162/content?user_no=2190106451355&org_id=10000000002',
                    course_tag: '子课-国家开放大学软件学院',
                    course_state: '30',
                    completeness: '98.6', //课程进度
                    uploads_count: '0',
                    activitys: [
                        {
                            name: '形考作业',
                            type: 'formal_task',
                            num: 10,
                            completed: 8,
                            left_time: '39天',
                            infos: [
                                {
                                    activity_type: 'learning_activity',
                                    end_time: '2024-06-30T15:59:59Z',
                                    id: 10002502771,
                                    is_completed: true,
                                    name: '形考任务一',
                                    start_time: '2024-02-29T16:00:00Z',
                                    type: 'formal_task',
                                },
                                {
                                    activity_type: 'learning_activity',
                                    end_time: '2024-06-30T15:59:59Z',
                                    id: 10002502789,
                                    is_completed: true,
                                    name: '形考任务二',
                                    start_time: '2024-02-29T16:00:00Z',
                                    type: 'formal_task',
                                },
                                {
                                    activity_type: 'learning_activity',
                                    end_time: '2024-06-30T15:59:59Z',
                                    id: 10002502818,
                                    is_completed: true,
                                    name: '形考任务四',
                                    start_time: '2024-02-29T16:00:00Z',
                                    type: 'formal_task',
                                },
                                {
                                    activity_type: 'learning_activity',
                                    end_time: '2024-06-30T15:59:59Z',
                                    id: 10002502847,
                                    is_completed: true,
                                    name: '形考任务五',
                                    start_time: '2024-02-29T16:00:00Z',
                                    type: 'formal_task',
                                },
                                {
                                    activity_type: 'learning_activity',
                                    end_time: '2024-06-30T15:59:59Z',
                                    id: 10002502872,
                                    is_completed: true,
                                    name: '形考任务六',
                                    start_time: '2024-02-29T16:00:00Z',
                                    type: 'formal_task',
                                },
                                {
                                    activity_type: 'learning_activity',
                                    end_time: '2024-06-30T15:59:59Z',
                                    id: 10002502888,
                                    is_completed: true,
                                    name: '形考任务七',
                                    start_time: '2024-02-29T16:00:00Z',
                                    type: 'formal_task',
                                },
                                {
                                    activity_type: 'learning_activity',
                                    end_time: '2024-06-30T15:59:59Z',
                                    id: 10002502921,
                                    is_completed: false,
                                    name: '形考任务八',
                                    start_time: '2024-02-29T16:00:00Z',
                                    type: 'formal_task',
                                },
                                {
                                    activity_type: 'learning_activity',
                                    end_time: '2024-06-30T15:59:59Z',
                                    id: 10002502950,
                                    is_completed: true,
                                    name: '形考任务九',
                                    start_time: '2024-02-29T16:00:00Z',
                                    type: 'formal_task',
                                },
                                {
                                    activity_type: 'learning_activity',
                                    end_time: '2024-06-30T15:59:59Z',
                                    id: 10002502980,
                                    is_completed: false,
                                    name: '形考任务十',
                                    start_time: '2024-02-29T16:00:00Z',
                                    type: 'formal_task',
                                },
                                {
                                    activity_type: 'exam_activity',
                                    end_time: '2024-06-30T15:59:59Z',
                                    id: 10000263512,
                                    is_completed: true,
                                    name: '形考任务三',
                                    start_time: '2024-02-29T16:00:00Z',
                                    type: 'formal_task',
                                },
                            ],
                        },
                    ],
                    exams: [
                        {
                            name: '',
                            stime: '',
                            etime: '',
                            status: '',
                        },
                    ],
                    classworks: [
                        {
                            name: '',
                            stime: '',
                            etime: '',
                            status: '',
                        },
                    ],
                    score: {
                        last_score: '',
                        daily_score: '',
                        class_score: '',
                    },
                    info: {
                        name: 'Dreamweaver网页设计',
                        course_code: '202403-00017901',
                        code: '00017',
                        type: '',
                        img_url: 'https://lms.ouchn.cn:443/api/uploads/8854735/modified-image?thumbnail=700x380',
                        num: '',
                        stu_num: '8736',
                        start_date: '2024-02-01',
                        end_date: '',
                        term: '2024-02-01至',
                        teacher: '',
                        xq: '2023-2024春季',
                        url: 'https://lms.ouchn.cn:443/course/10000049162/content?user_no=2190106451355&org_id=10000000002',
                        course_tag: '子课-国家开放大学软件学院',
                        course_state: '30',
                        completeness: '98.6',
                        uploads_count: '0',
                        activitys: [
                            {
                                name: '形考作业',
                                type: 'formal_task',
                                num: 10,
                                completed: 8,
                                left_time: '39天',
                                infos: [
                                    {
                                        activity_type: 'learning_activity',
                                        end_time: '2024-06-30T15:59:59Z',
                                        id: 10002502771,
                                        is_completed: true,
                                        name: '形考任务一',
                                        start_time: '2024-02-29T16:00:00Z',
                                        type: 'formal_task',
                                    },
                                    {
                                        activity_type: 'learning_activity',
                                        end_time: '2024-06-30T15:59:59Z',
                                        id: 10002502789,
                                        is_completed: true,
                                        name: '形考任务二',
                                        start_time: '2024-02-29T16:00:00Z',
                                        type: 'formal_task',
                                    },
                                    {
                                        activity_type: 'learning_activity',
                                        end_time: '2024-06-30T15:59:59Z',
                                        id: 10002502818,
                                        is_completed: true,
                                        name: '形考任务四',
                                        start_time: '2024-02-29T16:00:00Z',
                                        type: 'formal_task',
                                    },
                                    {
                                        activity_type: 'learning_activity',
                                        end_time: '2024-06-30T15:59:59Z',
                                        id: 10002502847,
                                        is_completed: true,
                                        name: '形考任务五',
                                        start_time: '2024-02-29T16:00:00Z',
                                        type: 'formal_task',
                                    },
                                    {
                                        activity_type: 'learning_activity',
                                        end_time: '2024-06-30T15:59:59Z',
                                        id: 10002502872,
                                        is_completed: true,
                                        name: '形考任务六',
                                        start_time: '2024-02-29T16:00:00Z',
                                        type: 'formal_task',
                                    },
                                    {
                                        activity_type: 'learning_activity',
                                        end_time: '2024-06-30T15:59:59Z',
                                        id: 10002502888,
                                        is_completed: true,
                                        name: '形考任务七',
                                        start_time: '2024-02-29T16:00:00Z',
                                        type: 'formal_task',
                                    },
                                    {
                                        activity_type: 'learning_activity',
                                        end_time: '2024-06-30T15:59:59Z',
                                        id: 10002502921,
                                        is_completed: false,
                                        name: '形考任务八',
                                        start_time: '2024-02-29T16:00:00Z',
                                        type: 'formal_task',
                                    },
                                    {
                                        activity_type: 'learning_activity',
                                        end_time: '2024-06-30T15:59:59Z',
                                        id: 10002502950,
                                        is_completed: true,
                                        name: '形考任务九',
                                        start_time: '2024-02-29T16:00:00Z',
                                        type: 'formal_task',
                                    },
                                    {
                                        activity_type: 'learning_activity',
                                        end_time: '2024-06-30T15:59:59Z',
                                        id: 10002502980,
                                        is_completed: false,
                                        name: '形考任务十',
                                        start_time: '2024-02-29T16:00:00Z',
                                        type: 'formal_task',
                                    },
                                    {
                                        activity_type: 'exam_activity',
                                        end_time: '2024-06-30T15:59:59Z',
                                        id: 10000263512,
                                        is_completed: true,
                                        name: '形考任务三',
                                        start_time: '2024-02-29T16:00:00Z',
                                        type: 'formal_task',
                                    },
                                ],
                            },
                        ],
                        exams: [
                            {
                                name: '',
                                stime: '',
                                etime: '',
                                status: '',
                            },
                        ],
                        classworks: [
                            {
                                name: '',
                                stime: '',
                                etime: '',
                                status: '',
                            },
                        ],
                        score: {
                            last_score: '',
                            daily_score: '',
                            class_score: '',
                        },
                    },
                },
            ],
            total: 13,
            page: 1,
            page_size: '6',
            total_page: 3,
        },
    };
}

// 3.获取课程modules 首先用拦截获取
// https://lms.ouchn.cn/api/courses/${courseCode}/modules
exports.getCourseModule = async (courseId, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        method: 'get',
        url: `https://lms.ouchn.cn/api/courses/${courseId}/modules`,
        headers: {
            cookie: cookieStr,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        modules: [
            {
                from_master: true,
                hidden: false,
                id: 10000514183,
                is_hidden: 0,
                name: '课程通知',
                sort: 1,
                sticky_time: null,
                syllabuses: [],
            },
        ],
    };
}

// 4.获取每个modules的任务信息
// https://lms.ouchn.cn/api/course/${courseId}/all-activities
exports.getTask = async (courseId, moduleIdArr, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        method: 'get',
        url: `https://lms.ouchn.cn/api/course/${courseId}/all-activities`,
        headers: {
            cookie: cookieStr,
        },
        params: {
            module_ids: moduleIdArr,
            activity_types: 'learning_activities,exams,classrooms,live_records,rollcalls',
            no_loading_animation: true,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        classrooms: [],
        exams: [
            {
                // 最终分数
                activity_final_score: 100,
                // 公布答案：immediate_announce交卷后立即公布 no_announce不公布答案
                announce_answer_status: 'no_announce',
                announce_answer_time: null,
                announce_answer_type: null,
                // 公布成绩：交卷后立即公布 no_announce不公布答案
                announce_score_status: 'immediate_announce',
                announce_score_time: null,
                assign_group_ids: [],
                assign_student_ids: [],
                capture_count: null,
                completion_criterion: '提交测试',
                completion_criterion_key: 'submitted',
                completion_criterion_value: null,
                created_at: '2024-02-02T09:40:49Z',
                default_options_layout: 'vertical',
                description: '<p><span>请参加本章学后自测。</span></p>',
                disable_copy_paste: false,
                disable_right_click: false,
                enable_anti_cheat: false,
                enable_camera: false,
                enable_invigilation: false,
                // 截至日期
                end_time: '2024-07-15T15:59:59Z',
                exam_submissions: [10000263401],
                from_master: true,
                group_set_id: 0,
                group_set_name: '',
                has_assign_group: false,
                has_assign_student: false,
                has_random_subject: false,
                hidden: false,
                id: 10000263401,
                imported_from: 'moodle',
                is_announce_answer_time_passed: false,
                is_announce_score_time_passed: true,
                is_assigned_to_all: true,
                is_closed: false,
                is_final: false,
                is_formative: false,
                is_fullscreen_mode: false,
                is_ip_letrained: false,
                is_leaving_window_letrained: false,
                is_leaving_window_timeout: false,
                is_lti_mode: false,
                is_opened_catalog: false,
                is_practice_mode: false,
                is_started: true,
                is_submit_started: true,
                last_updated_at: '2024-02-05T09:47:36Z',
                last_updated_by: '张冰',
                leaving_window_limit: null,
                leaving_window_timeout: null,
                limit_answer_on_signle_client: false,
                limit_time: null,
                limited_ip: null,
                make_up_record: null,
                module_id: 0,
                module_sort: 3,
                parent_id: 0,
                prerequisites: [],
                referrer_id: 10000588765,
                referrer_type: 'syllabus',
                score_percentage: '0.0',
                score_rule: 'highest',
                sort: 20,
                start_time: '2024-02-29T16:00:00Z',
                subjects_rule: {
                    select_subjects_randomly: false,
                    shuffle_options_randomly: false,
                    shuffle_subjects_randomly: 'default',
                },
                submit_by_group: false,
                submit_start_time: '2024-02-29T16:00:00Z',
                // 允许尝试次数：999
                submit_times: 999,
                syllabus_id: 10000588765,
                syllabus_sort: null,
                title: '本章自测',
                type: 'exam',
                unique_key: 'exam-10000263401',
            },
        ],
        learning_activities: [
            {
                assign_group_ids: [],
                assign_student_ids: [],
                can_show_score: false,
                completion_criterion: '查看页面', // 完成条件 未完成的值为：空字符
                completion_criterion_key: 'view', // 完成条件key  未完成的值为 'none'
                completion_criterion_value: '0', // 完成条件值
                course_id: 10000066667, // 课程id
                created_at: '2025-02-14T08:45:37Z',
                created_by: {
                    id: 0,
                    name: null,
                },
                data: {
                    description: '',
                },
                end_time: null, //
                from_master: true,
                group_set_id: 0,
                group_set_name: '',
                has_assign_group: false,
                has_assign_student: false,
                has_submission_with_child: null,
                hidden: false,
                id: 10004413924,
                imported_from: null,
                imported_track_id: null,
                inter_review_named: null,
                inter_score_map: {
                    end_time: null,
                    id: 0,
                    is_closed: null,
                    is_started: null,
                    pieces_cnt: 0,
                    start_time: null,
                },
                intra_rubric_id: 0,
                intra_rubric_instance: {
                    conditions: null,
                    id: 0,
                    name: null,
                    rubric: {
                        conditions: null,
                        created_at: null,
                        created_by: {
                            id: 0,
                            name: null,
                        },
                        engage_number: 0,
                        id: 0,
                        name: null,
                        updated_at: null,
                    },
                },
                intra_rubric_instance_id: 0,
                intra_score_map: {
                    end_time: null,
                    id: 0,
                    is_closed: null,
                    start_time: null,
                },
                is_assigned_to_all: true, // 是否分配给所有学生
                is_closed: false, // 是否关闭
                is_final: false, // 是否最终
                is_formative: false, // 是否形成性
                is_inter_review_by_submitter: null, // 是否由提交者进行互评
                is_open: true, // 是否开放
                is_opened_catalog: false, // 是否打开目录
                is_started: true, // 是否开始
                is_submit_closed: null, // 是否提交关闭
                last_updated_at: '2025-02-14T08:45:37Z', // 最后更新时间
                last_updated_by: '张冰', // 最后更新者
                late_submission_count: 0, // 迟交次数
                module_id: 10000514183, // 模块id
                non_submit_times: null, // 未提交次数
                parent_id: 0, // 父id
                prerequisites: [], // 先决条件
                rubric_id: 0, // 评分标准id
                rubric_instance: {
                    conditions: null,
                    id: 0,
                    name: null,
                    rubric: {
                        conditions: null,
                        created_at: null,
                        created_by: {
                            id: 0,
                            name: null,
                        },
                        engage_number: 0,
                        id: 0,
                        name: null,
                        updated_at: null,
                    },
                },
                rubric_instance_id: 0, // 评分标准实例id
                score_percentage: null, // 分数百分比
                score_published: null, // 分数发布
                sort: 1, // 排序
                start_time: '2025-02-16T16:00:00Z', // 开始时间
                submission_closed: false, // 提交关闭
                submission_status: false, // 提交状态
                submit_by_group: false,
                submit_times: null,
                subtask_count: 0,
                syllabus_id: 0,
                teaching_unit_id: 10000066667,
                title: '【学习方式提醒】通知',
                type: 'page',
                unique_key: 'page-10004413924',
                updated_at: '2025-02-14T08:45:37Z',
                uploads: [],
            },
            // 大作业
            {
                activity_final_score: 83,
                assign_group_ids: [],
                assign_student_ids: [],
                can_show_score: true,
                completion_criterion: '提交作业',
                completion_criterion_key: 'submitted',
                completion_criterion_value: '0',
                course_id: 10000036250,
                created_at: '2023-09-01T05:33:46Z',
                created_by: {
                    id: 437152,
                    name: '张丽媛',
                },
                data: {
                    allow_upload_files: false,
                    announce_score_type: 2,
                    description:
                        '<p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">大作业说明</span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;"><br></span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">1.&nbsp;</span><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">学生答题完毕后，可点击保存草稿。在提交前可反复修改并保存。考生一旦提交后，不能再进行修改。</span><span class="color-E33737" style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">注意：只可提交1次！</span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">2.&nbsp;</span><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">考生请在答题框内录入试题答案，不能复制黏贴，不能以附件形式提交试卷。</span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">3.&nbsp;</span><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">答题要做到论述严谨、语句通顺，最重要的是能够理论联系实际、理论联系材料，进行深入分析，字数不少于1000字。</span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;"><br></span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="line-height: inherit; font-weight: 700;"><span class="color-E33737" style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">注意：<span style="font-size: 16pt;">答题</span>截止时间2024年1月7日</span></span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="line-height: 28px; font-family: 微软雅黑, sans-serif;">&nbsp;</span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="line-height: inherit; font-weight: 700;"><span style="font-size: 18pt; line-height: 48px; font-family: 微软雅黑, sans-serif; color: red;">如何正确认识伟大建党精神的时代价值与实践要求？</span></span></p>',
                    has_assign_target: false,
                    homework_score_percentage: {
                        instructor_score_percentage: '100.0',
                    },
                    homework_type: 'file_upload',
                    mode: 'normal',
                    other_resources: [],
                    pigai: {},
                    score_percentage: '80.0',
                    show_intra_rubric: false,
                    show_rubric: false,
                    submit_closed_time: '2024-01-07T15:59:00Z',
                    submit_times: 1,
                },
                deadline: '2024-01-07T15:59:00Z',
                end_time: null,
                from_master: true,
                group_set_id: 0,
                group_set_name: '',
                has_assign_group: false,
                has_assign_student: false,
                has_submission_with_child: true,
                hidden: false,
                id: 10001840621,
                imported_from: null,
                imported_track_id: null,
                inter_review_named: null,
                inter_score_map: {
                    end_time: null,
                    id: 0,
                    is_closed: null,
                    is_started: null,
                    pieces_cnt: 0,
                    start_time: null,
                },
                intra_rubric_id: 0,
                intra_rubric_instance: {
                    conditions: null,
                    id: 0,
                    name: null,
                    rubric: {
                        conditions: null,
                        created_at: null,
                        created_by: {
                            id: 0,
                            name: null,
                        },
                        engage_number: 0,
                        id: 0,
                        name: null,
                        updated_at: null,
                    },
                },
                intra_rubric_instance_id: 0,
                intra_score_map: {
                    end_time: null,
                    id: 0,
                    is_closed: null,
                    start_time: null,
                },
                is_assigned_to_all: true,
                is_closed: false,
                is_final: false,
                is_formative: true,
                is_inter_review_by_submitter: false,
                is_open: true,
                is_opened_catalog: false,
                is_started: true,
                is_submit_closed: true,
                last_updated_at: '2023-11-20T02:08:10Z',
                last_updated_by: '张丽媛',
                late_submission_count: 9,
                module_id: 10000217220,
                non_submit_times: false,
                parent_id: 0,
                prerequisites: [
                    {
                        activity_id: 10000194751,
                        activity_type: 'exam',
                        completion_criterion: {
                            completion_info: '已提交',
                            completion_key: 'completed',
                            completion_value: '',
                            criterion_key: 'exam.submitted',
                            criterion_text: '提交测试',
                            criterion_value: 0,
                            has_completed: true,
                        },
                        key: 'exam-10000194751',
                        title: '专题测验5（权重4%）',
                    },
                ],
                rubric_id: 0,
                rubric_instance: {
                    conditions: null,
                    id: 0,
                    name: null,
                    rubric: {
                        conditions: null,
                        created_at: null,
                        created_by: {
                            id: 0,
                            name: null,
                        },
                        engage_number: 0,
                        id: 0,
                        name: null,
                        updated_at: null,
                    },
                },
                rubric_instance_id: 0,
                score_percentage: '80.0',
                score_published: true,
                sort: 2,
                start_time: '2023-08-31T16:00:00Z',
                submission_closed: true,
                submission_status: false,
                submit_by_group: false,
                submit_times: '1', // 可以答题次数
                subtask_count: 0,
                syllabus_id: 0,
                teaching_unit_id: 10000036250,
                title: '大作业',
                type: 'homework',
                unique_key: 'homework-10001840621',
                updated_at: '2023-11-20T02:08:10Z',
                uploads: [],
            },
        ],
        live_records: [],
    };
}

// 获取课件具体信息
exports.getTaskInfo = async (taskId, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://lms.ouchn.cn/api/activities/${taskId}`,
        headers: {
            cookie: cookieStr,
            accept: 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            pragma: 'no-cache',
            priority: 'u=1, i',
            'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
        },
        method: 'GET',
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    // 大作业
    res = {
        accepting_submission: false,
        assign_group_ids: [],
        assign_student_ids: [],
        can_show_score: true,
        cc_license_references: [],
        completion_criterion: '提交作业',
        completion_criterion_key: 'submitted',
        completion_criterion_value: '0',
        course_id: 10000036250,
        created_at: '2023-09-01T05:33:46Z',
        created_by: {
            id: 437152,
            name: '张丽媛',
        },
        data: {
            allow_upload_files: false,
            announce_score_type: 2,
            description:
                '<p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">大作业说明</span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;"><br></span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">1.&nbsp;</span><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">学生答题完毕后，可点击保存草稿。在提交前可反复修改并保存。考生一旦提交后，不能再进行修改。</span><span class="color-E33737" style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">注意：只可提交1次！</span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">2.&nbsp;</span><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">考生请在答题框内录入试题答案，不能复制黏贴，不能以附件形式提交试卷。</span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">3.&nbsp;</span><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">答题要做到论述严谨、语句通顺，最重要的是能够理论联系实际、理论联系材料，进行深入分析，字数不少于1000字。</span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;"><br></span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="line-height: inherit; font-weight: 700;"><span class="color-E33737" style="font-size: 16pt; line-height: 42.6667px; font-family: 微软雅黑, sans-serif;">注意：<span style="font-size: 16pt;">答题</span>截止时间2024年1月7日</span></span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="line-height: 28px; font-family: 微软雅黑, sans-serif;">&nbsp;</span></p><p class="MsoNormal" style=\'font-family: Helvetica, "Hiragino Sans GB", "WenQuanYi Micro Hei", "Microsoft YaHei", sans-serif, "Noto Sans CJK SC", Code2001, Code2002; font-size: 14px; line-height: 28px; color: rgb(51, 51, 51);\'><span style="line-height: inherit; font-weight: 700;"><span style="font-size: 18pt; line-height: 48px; font-family: 微软雅黑, sans-serif; color: red;">如何正确认识伟大建党精神的时代价值与实践要求？</span></span></p>',
            has_assign_target: false,
            homework_score_percentage: {
                instructor_score_percentage: '100.0',
            },
            homework_type: 'file_upload',
            mode: 'normal',
            non_submit_times: false,
            other_resources: [],
            pigai: {},
            score_percentage: '80.0',
            show_intra_rubric: false,
            show_rubric: false,
            submit_closed_time: '2024-01-07T15:59:00Z',
            submit_times: 1,
        },
        end_time: null,
        from_master: true,
        group_set_id: 0,
        group_set_name: '',
        has_assign_group: false,
        has_assign_student: false,
        has_submission_with_child: true,
        hidden: false,
        id: 10001840621,
        imported_from: null,
        imported_track_id: null,
        inter_review_named: false,
        inter_score_map: {
            end_time: null,
            id: 0,
            is_closed: null,
            is_started: null,
            pieces_cnt: 0,
            start_time: null,
        },
        interaction_activity_attributes: null,
        intra_rubric_id: 0,
        intra_rubric_instance: {
            conditions: null,
            id: 0,
            name: null,
            rubric: {
                conditions: null,
                created_at: null,
                created_by: {
                    id: 0,
                    name: null,
                },
                engage_number: 0,
                id: 0,
                name: null,
                updated_at: null,
            },
        },
        intra_rubric_instance_id: 0,
        intra_score_map: {
            end_time: null,
            id: 0,
            is_closed: null,
            start_time: null,
        },
        is_announce_score_time_passed: true,
        is_assigned_to_all: true,
        is_closed: false,
        is_final: false,
        is_inter_review_by_submitter: false,
        is_open: true,
        is_opened_catalog: false,
        is_resubmit_open: true,
        is_started: true,
        is_submit_closed: true,
        late_submission_count: 9,
        module_id: 10000217220,
        need_make_up: false,
        need_remind: false,
        non_submit_times: false,
        parent_id: 0,
        prerequisites: [
            {
                activity_id: 10000194751,
                activity_type: 'exam',
                completion_criterion: {
                    completion_info: '已提交',
                    completion_key: 'completed',
                    completion_value: '',
                    criterion_key: 'exam.submitted',
                    criterion_text: '提交测试',
                    criterion_value: 0,
                    has_completed: true,
                },
                key: 'exam-10000194751',
                title: '专题测验5（权重4%）',
            },
        ],
        rubric_id: 0,
        rubric_instance: {
            conditions: null,
            id: 0,
            name: null,
            rubric: {
                conditions: null,
                created_at: null,
                created_by: {
                    id: 0,
                    name: null,
                },
                engage_number: 0,
                id: 0,
                name: null,
                updated_at: null,
            },
        },
        rubric_instance_id: 0,
        score_percentage: '80.0',
        score_published: true,
        sort: 2,
        start_time: '2023-08-31T16:00:00Z',
        submission_closed: true,
        submission_started: true,
        submit_by_group: false,
        submit_times: 1, // 允许答题次数
        subtask_count: 0,
        syllabus_id: 0,
        teaching_unit_id: 10000036250,
        title: '大作业',
        type: 'homework',
        unique_key: 'homework-10001840621',
        updated_at: '2023-11-20T02:08:10Z',
        uploads: [],
        user_submit_count: 1, // 用户提交次数
    };
}

// 24.获取examObj的详细信息
exports.getExamObj = async (examId, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://lms.ouchn.cn/api/exams/${examId}`,
        headers: {
            cookie: cookieStr,
        },
        method: 'GET',
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        // 测试标题：专题测验1（权重4%）
        title: '专题测验1（权重4%）',
        // 已经答题次数：1
        submitted_times: 1,
        // 测试开放时间：2023.08.01 00:00
        start_time: '2023-07-31T16:00:00Z',
        // 测试截止时间：2024.01.07 23:59
        end_time: '2024-01-07T15:59:00Z',
        // 测试开始时间:2023.08.01 00:00
        submit_start_time: '2023-07-31T16:00:00Z',
        // 公布成绩：交卷后立即公布 no_announce不公布答案
        announce_score_status: 'immediate_announce',
        // 公布答案：immediate_announce交卷后立即公布 no_announce不公布答案
        announce_answer_status: 'immediate_announce',
        // 成绩比例： 4.0%
        score_percentage: '4.0',
        // 允许尝试次数：999
        submit_times: 999,
        // 计分规则： 最高得分
        score_rule: 'highest',
        // 完成指标：提交测试
        completion_criterion: '提交测试',
        // 最后更新时间：2024.01.20 18:18
        last_updated_at: '2024-01-20T10:18:58Z',
        // 完成指标：提交测试
        completion_criterion_key: 'submitted',
        allow_upload_file: true,
        announce_answer_time: null,
        announce_answer_type: 'only_answer',
        announce_score_time: null,
        assign_group_ids: [],
        assign_student_ids: [],
        capture_count: 0,
        completion_criterion_value: '0',
        created_at: '2023-09-01T05:33:22Z',
        default_options_layout: 'vertical',
        description: '',
        disable_copy_paste: false,
        disable_right_click: false,
        enable_anti_cheat: false,
        enable_camera: false,
        enable_invigilation: false,
        exist_makeup_submitted_record: false,
        exist_submitted_record: false,
        from_master: true,
        group_set_id: 0,
        group_set_name: '',
        has_assign_group: false,
        has_assign_student: false,
        has_temporary_submission: false,
        hidden: false,
        id: 10000194740,
        imported_from: null,
        is_announce_answer_time_passed: true,
        is_announce_score_time_passed: true,
        is_assigned_to_all: true,
        is_assigned_to_current_user: true,
        is_closed: true,
        is_closed_for_examinee: true,
        is_final: false,
        is_fullscreen_mode: false,
        is_ip_constrained: false,
        is_leaving_window_constrained: false,
        is_leaving_window_timeout: false,
        is_lti_mode: false,
        is_makeup_exam: false,
        is_opened_catalog: false,
        is_practice_mode: false,
        is_started: true,
        is_submit_started: true,
        last_updated_by: '张丽媛',
        leaving_window_limit: null,
        leaving_window_timeout: null,
        limit_answer_on_signle_client: false,
        limit_time: null,
        limited_ip: null,
        make_up_record: null,
        makeup_exam_paper: {
            id: 10000255142,
            subjects_count: 0,
        },
        module_id: 10000217205,
        module_sort: 2,
        parent_id: 0,
        prerequisites: [
            {
                activity_id: 10001840546,
                activity_type: 'online_video',
                completion_criterion: {
                    completion_info: '已观看 100%',
                    completion_key: 'completed',
                    completion_value: 100,
                    criterion_key: 'online_video.completeness',
                    criterion_text: '需累积观看 80%(含)以上',
                    criterion_value: 80,
                    has_completed: true,
                },
                key: 'online_video-10001840546',
                title: '1-3-2  新时代坚持和发展中国特色社会主义的历史自觉',
            },
        ],
        referrer_id: 10000217205,
        referrer_type: 'module',
        sort: 9,
        subjects_count: 4,
        subjects_rule: {
            select_subjects_randomly: false,
            shuffle_options_randomly: false,
            shuffle_subjects_randomly: 'default',
        },
        submit_by_group: false,

        syllabus_id: 0,
        syllabus_sort: '1-01-01T00:00:00Z',

        total_points: 100,
        type: 'exam',
        unique_key: 'exam-10000194740',
    };
}

// 获取已完成的课件列表
exports.activityReadsForUser = async (courseId, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://lms.ouchn.cn/api/course/${courseId}/activity-reads-for-user`,
        method: 'get',
        headers: {
            cookie: cookieStr,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        activity_reads: [
            {
                activity_id: 10004413924,
                activity_type: 'learning_activity',
                completeness: 'full',
                created_by_id: 10000982576,
                created_for_id: 10000982576,
                data: {},
                id: '682eb76bef1ffe9d8db4a6d9',
                last_visited_at: '2025-05-22T07:42:15.178Z',
            },
            {
                activity_id: 10004413954,
                activity_type: 'learning_activity',
                completeness: 'full',
                created_by_id: 10000982576,
                created_for_id: 10000982576,
                data: {
                    completeness: 100,
                    upload_ids: [24608976],
                },
                id: '682eb992ef69d0a50fa8915d',
                last_visited_at: '2025-05-22T06:07:18.375Z',
            },
        ],
    };
}

// 学习行为分析
exports.learningActivity = async (globalData, userAgent, taskObj, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let data = {
        org_id: globalData.course.orgId,
        user_id: globalData.user.id + '',
        course_id: globalData.course.id + '',
        enrollment_role: 'student',
        is_teacher: false,
        activity_id: taskObj.id,
        activity_type: taskObj.type,
        activity_name: null,
        module: null,
        action: 'open',
        ts: new Date().getTime(),
        user_agent: userAgent,
        mode: 'normal',
        channel: 'web',
        target_info: {},
        master_course_id: globalData.course.id,
        org_name: globalData.user.orgName,
        org_code: globalData.user.orgCode,
        user_no: globalData.user.userNo,
        user_name: globalData.user.name,
        course_code: globalData.course.courseCode,
        course_name: globalData.course.name,
        dep_id: globalData.dept.id,
        dep_name: globalData.dept.name,
        dep_code: globalData.dept.code,
    };

    let config = {
        url: `https://lms.ouchn.cn/statistics/api/learning-activity`,
        method: 'post',
        headers: {
            accept: 'application/json, text/javascript, */*; q=0.01',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'cache-control': 'no-cache',
            'content-type': 'application/json; charset=UTF-8',
            pragma: 'no-cache',
            'sec-ch-ua': '"Chromium";v="104", " Not A;Brand";v="99", "Google Chrome";v="104"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'x-requested-with': 'XMLHttpRequest',
            cookie: cookieStr,
        },
        data: JSON.stringify(data),
    };

    let res = await axiosIns(config);
    return res;
};

// 修改课程进度
exports.updateDuration = async (taskId, cookieStr, data) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://lms.ouchn.cn/api/course/activities-read/${taskId}`,
        method: 'post',
        headers: {
            accept: '*/*',
            'accept-language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'cache-control': 'no-cache',
            'content-type': 'application/json',
            pragma: 'no-cache',
            'sec-ch-ua': '"Chromium";v="104", " Not A;Brand";v="99", "Google Chrome";v="104"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            'x-requested-with': 'XMLHttpRequest',
            cookie: cookieStr,
        },
        data: data,
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        activity_id: 10002877185,
        activity_type: 'learning_activity',
        completeness: 'full',
        created_by_id: 10000546524,
        created_for_id: 10000546524,
        data: {
            completeness: 100,
            end: 84,
            ranges: [[0, 769]],
        },
        id: '6640fcbafc53bd163efa14a1',
        last_visited_at: '2024-05-25T05:33:22Z',
    };
}

// 7.获取交卷列表 没有用代理
exports.getSubmissions = async (examId, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://lms.ouchn.cn/api/exams/${examId}/submissions`,
        method: 'get',
        headers: {
            cookie: cookieStr,
        },
    };
    let res;
    try {
        res = await axiosIns(config);
    } catch (error) {
        if (error.message.includes('You have not taken this exam and submitted yet')) {
            res = { submissions: [] };
        } else {
            throw error;
        }
    }
    return res;
};
if (false) {
    res = {
        exam_final_score: null,
        exam_score: 10,
        exam_score_rule: 'highest',
        submissions: [
            {
                created_at: '2024-05-26T01:35:11Z',
                exam_id: 10000339963,
                exam_type_text: '测试试题',
                id: 10067559704,
                score: '10.0',
                submitted_at: '2024-05-26T01:46:56Z',
            },
        ],
    };
    // 失败会返回404状态码
    fail = { message: 'You have not taken this exam and submitted yet.' };
}

// (代理 OFF) 8.查看已答试卷
exports.viewPage = async (examId, scoreId, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://lms.ouchn.cn/api/exams/${examId}/submissions/${scoreId}`,
        method: 'get',
        headers: {
            cookie: cookieStr,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        auto_mark: false,
        correct_answers_data: {
            correct_answers: [
                {
                    answer_option_ids: [10041111719],
                    point: '1.0',
                    subject_id: 10015736037,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10041111714],
                    point: '1.0',
                    subject_id: 10015736036,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10041111713],
                    point: '1.0',
                    subject_id: 10015736035,
                    type: 'single_selection',
                },
                {
                    answer_option_ids: [10041111726],
                    point: '1.0',
                    subject_id: 10015736040,
                    type: 'true_or_false',
                },
                {
                    answer_option_ids: [10041111722],
                    point: '1.0',
                    subject_id: 10015736038,
                    type: 'true_or_false',
                },
                {
                    answer_option_ids: [10041111724],
                    point: '1.0',
                    subject_id: 10015736039,
                    type: 'true_or_false',
                },
                {
                    point: '1.0',
                    subject_id: 10015736041,
                    type: 'short_answer',
                },
                {
                    point: '1.5',
                    subject_id: 10015736042,
                    type: 'short_answer',
                },
                {
                    point: '1.5',
                    subject_id: 10015736043,
                    type: 'short_answer',
                },
            ],
        },
        is_simulated: false,
        score: 10,
        subjects_data: {
            subjects: [
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p>单项选择题</p>',
                    difficulty_level: 'medium',
                    id: 10015736032,
                    last_updated_at: '2024-05-12T08:38:46Z',
                    note: null,
                    options: [],
                    parent_id: null,
                    point: '0.0',
                    settings: {
                        case_sensitive: true,
                        required: false,
                        unordered: false,
                    },
                    sort: 0,
                    sub_subjects: [],
                    type: 'text',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p>count()属于（   ）。</p>',
                    difficulty_level: 'medium',
                    id: 10015736037,
                    last_updated_at: '2024-05-12T08:38:46Z',
                    note: null,
                    options: [
                        {
                            content: '单行函数',
                            id: 10041111718,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '分组函数',
                            id: 10041111719,
                            is_answer: true,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '数值函数',
                            id: 10041111720,
                            is_answer: false,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '转换函数',
                            id: 10041111721,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '1.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 1,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p>substr()函数的作用是（   ）。</p>',
                    difficulty_level: 'medium',
                    id: 10015736036,
                    last_updated_at: '2024-05-12T08:38:46Z',
                    note: null,
                    options: [
                        {
                            content: '取字符串子串',
                            id: 10041111714,
                            is_answer: true,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '连接两个字符串',
                            id: 10041111715,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: '转换字符串',
                            id: 10041111716,
                            is_answer: false,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '复制字符串',
                            id: 10041111717,
                            is_answer: false,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '1.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 2,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p>下列函数中属于字符串函数的是（）。</p>',
                    difficulty_level: 'medium',
                    id: 10015736035,
                    last_updated_at: '2024-05-12T08:38:46Z',
                    note: null,
                    options: [
                        {
                            content: 'TO_CHAR()',
                            id: 10041111710,
                            is_answer: false,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: 'SUBSTR()',
                            id: 10041111711,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                        {
                            content: 'DECODE()',
                            id: 10041111712,
                            is_answer: false,
                            sort: 2,
                            type: 'text',
                        },
                        {
                            content: '以上皆是',
                            id: 10041111713,
                            is_answer: true,
                            sort: 3,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '1.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 3,
                    sub_subjects: [],
                    type: 'single_selection',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p>判断题</p>',
                    difficulty_level: 'medium',
                    id: 10015736033,
                    last_updated_at: '2024-05-12T08:38:46Z',
                    note: null,
                    options: [],
                    parent_id: null,
                    point: '0.0',
                    settings: {
                        case_sensitive: true,
                        required: false,
                        unordered: false,
                    },
                    sort: 4,
                    sub_subjects: [],
                    type: 'text',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p>mod(x,y)函数的作用是求x除以y的余数。</p>',
                    difficulty_level: 'medium',
                    id: 10015736040,
                    last_updated_at: '2024-05-12T08:38:46Z',
                    note: null,
                    options: [
                        {
                            content: '对',
                            id: 10041111726,
                            is_answer: true,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '错',
                            id: 10041111727,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '1.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 5,
                    sub_subjects: [],
                    type: 'true_or_false',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p>sysdate()函数可以得到系统的当前时间。</p>',
                    difficulty_level: 'medium',
                    id: 10015736038,
                    last_updated_at: '2024-05-12T08:38:46Z',
                    note: null,
                    options: [
                        {
                            content: '对',
                            id: 10041111722,
                            is_answer: true,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '错',
                            id: 10041111723,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '1.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 6,
                    sub_subjects: [],
                    type: 'true_or_false',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p>to_char(x[,f])函数把字符串或时间类型x按格式f进行格式化转换为字符串类型。</p>',
                    difficulty_level: 'medium',
                    id: 10015736039,
                    last_updated_at: '2024-05-12T08:38:46Z',
                    note: null,
                    options: [
                        {
                            content: '对',
                            id: 10041111724,
                            is_answer: true,
                            sort: 0,
                            type: 'text',
                        },
                        {
                            content: '错',
                            id: 10041111725,
                            is_answer: false,
                            sort: 1,
                            type: 'text',
                        },
                    ],
                    parent_id: null,
                    point: '1.0',
                    settings: {
                        case_sensitive: true,
                        option_type: 'text',
                        options_layout: 'vertical',
                        required: false,
                        unordered: false,
                    },
                    sort: 7,
                    sub_subjects: [],
                    type: 'true_or_false',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p>简答题</p>',
                    difficulty_level: 'medium',
                    id: 10015736034,
                    last_updated_at: '2024-05-12T08:38:46Z',
                    note: null,
                    options: [],
                    parent_id: null,
                    point: '0.0',
                    settings: {
                        case_sensitive: true,
                        required: false,
                        unordered: false,
                    },
                    sort: 8,
                    sub_subjects: [],
                    type: 'text',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p><span>简述Oracle数据库中函数的两种类型。</span></p>',
                    difficulty_level: 'medium',
                    id: 10015736041,
                    last_updated_at: '2024-05-12T08:38:46Z',
                    note: null,
                    options: [],
                    parent_id: null,
                    point: '1.0',
                    settings: {
                        case_sensitive: true,
                        required: false,
                        set_limit: false,
                        unordered: false,
                    },
                    sort: 9,
                    sub_subjects: [],
                    type: 'short_answer',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p><span>简述主要的通用函数作用。</span></p>',
                    difficulty_level: 'medium',
                    id: 10015736042,
                    last_updated_at: '2024-05-12T08:38:46Z',
                    note: null,
                    options: [],
                    parent_id: null,
                    point: '1.5',
                    settings: {
                        case_sensitive: true,
                        required: false,
                        set_limit: false,
                        unordered: false,
                    },
                    sort: 10,
                    sub_subjects: [],
                    type: 'short_answer',
                    wrong_explanation: '',
                },
                {
                    answer_explanation: '',
                    answer_number: 0,
                    correct_answers: [],
                    data: null,
                    description: '<p><span>查询clu$的size$字段，若该字段为NULL则输出0，编写实现该功能的SQL语句。</span></p>',
                    difficulty_level: 'medium',
                    id: 10015736043,
                    last_updated_at: '2024-05-12T08:38:46Z',
                    note: null,
                    options: [],
                    parent_id: null,
                    point: '1.5',
                    settings: {
                        case_sensitive: true,
                        required: false,
                        set_limit: false,
                        unordered: false,
                    },
                    sort: 11,
                    sub_subjects: [],
                    type: 'short_answer',
                    wrong_explanation: '',
                },
            ],
        },
        submission_comment_data: {},
        submission_data: {
            _fixed: true,
            progress: {
                answered_num: 6,
                total_subjects: 12,
            },
            subjects: [
                {
                    answer_option_ids: [10041111721],
                    subject_id: 10015736037,
                    subject_updated_at: '2024-05-12T08:38:46Z',
                },
                {
                    answer_option_ids: [10041111714],
                    subject_id: 10015736036,
                    subject_updated_at: '2024-05-12T08:38:46Z',
                },
                {
                    answer_option_ids: [10041111712],
                    subject_id: 10015736035,
                    subject_updated_at: '2024-05-12T08:38:46Z',
                },
                {
                    answer_option_ids: [10041111727],
                    subject_id: 10015736040,
                    subject_updated_at: '2024-05-12T08:38:46Z',
                },
                {
                    answer_option_ids: [],
                    subject_id: 10015736038,
                    subject_updated_at: '2024-05-12T08:38:46Z',
                },
                {
                    answer_option_ids: [10041111725],
                    subject_id: 10015736039,
                    subject_updated_at: '2024-05-12T08:38:46Z',
                },
                {
                    answer: '<p>可能</p>',
                    attachments: [],
                    subject_id: 10015736041,
                },
                {
                    answer: '',
                    attachments: [],
                    subject_id: 10015736042,
                },
                {
                    answer: '',
                    attachments: [],
                    subject_id: 10015736043,
                },
            ],
        },
        submission_score_data: {
            10015736035: '0',
            10015736036: '1.0',
            10015736037: '0',
            10015736038: null,
            10015736039: '0',
            10015736040: '0',
        },
    };
}

// 9.获取exam_paper_instance_id和exam_submission_id
exports.getStorage = async (examId, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://lms.ouchn.cn/api/exams/${examId}/submissions/storage`,
        method: 'get',
        headers: {
            cookie: cookieStr,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        created_at: '2024-05-26T02:47:51Z',
        exam_id: 10000339963,
        exam_type_text: '测试试题',
        examinee_id: 10000546524,
        id: 10067571513,
        instance_id: 10065768123,
        left_time: 4251132.362243,
        score: null,
        submission_data: {
            progress: {
                answered_num: 0,
                total_subjects: 12,
            },
            subjects: [
                {
                    answer_option_ids: [],
                    subject_id: 10015736037,
                    subject_updated_at: '2024-05-12T08:38:46Z',
                },
                {
                    answer_option_ids: [],
                    subject_id: 10015736036,
                    subject_updated_at: '2024-05-12T08:38:46Z',
                },
                {
                    answer_option_ids: [],
                    subject_id: 10015736035,
                    subject_updated_at: '2024-05-12T08:38:46Z',
                },
                {
                    answer_option_ids: [],
                    subject_id: 10015736040,
                    subject_updated_at: '2024-05-12T08:38:46Z',
                },
                {
                    answer_option_ids: [],
                    subject_id: 10015736038,
                    subject_updated_at: '2024-05-12T08:38:46Z',
                },
                {
                    answer_option_ids: [],
                    subject_id: 10015736039,
                    subject_updated_at: '2024-05-12T08:38:46Z',
                },
                {
                    attachments: [],
                    subject_id: 10015736041,
                },
                {
                    attachments: [],
                    subject_id: 10015736042,
                },
                {
                    attachments: [],
                    subject_id: 10015736043,
                },
            ],
        },
        submitted_at: null,
    };
}

// 10.获取作业题目数据
exports.getQuestionList = async (examId, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://lms.ouchn.cn/api/exams/${examId}/distribute`,
        method: 'get',
        headers: {
            cookie: cookieStr,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        exam_paper_instance_id: 10065756862,
        subjects: [
            // text
            {
                answer_number: 0,
                data: null,
                description: '<p>单项选择题</p>',
                difficulty_level: 'medium',
                id: 10015736032,
                last_updated_at: '2024-05-12T08:38:46Z',
                note: null,
                options: [],
                parent_id: null,
                point: '0.0',
                settings: {
                    case_sensitive: true,
                    required: false,
                    unordered: false,
                },
                sort: 0,
                sub_subjects: [],
                type: 'text', //题目类型说明，跳过
            },
            // single_selection
            {
                answer_number: 0,
                data: null,
                description: '<p>count()属于（   ）。</p>',
                difficulty_level: 'medium',
                id: 10015736037,
                last_updated_at: '2024-05-12T08:38:46Z',
                note: null,
                options: [
                    {
                        content: '单行函数',
                        id: 10041111718,
                        sort: 0,
                        type: 'text',
                    },
                    {
                        content: '分组函数',
                        id: 10041111719,
                        sort: 1,
                        type: 'text',
                    },
                    {
                        content: '数值函数',
                        id: 10041111720,
                        sort: 2,
                        type: 'text',
                    },
                    {
                        content: '转换函数',
                        id: 10041111721,
                        sort: 3,
                        type: 'text',
                    },
                ],
                parent_id: null,
                point: '1.0',
                settings: {
                    case_sensitive: true,
                    option_type: 'text',
                    options_layout: 'vertical',
                    required: false,
                    unordered: false,
                },
                sort: 1,
                sub_subjects: [],
                type: 'single_selection',
            },
            // true_or_false
            {
                answer_number: 0,
                data: null,
                description: '<p>mod(x,y)函数的作用是求x除以y的余数。</p>',
                difficulty_level: 'medium',
                id: 10015736040,
                last_updated_at: '2024-05-12T08:38:46Z',
                note: null,
                options: [
                    {
                        content: '对',
                        id: 10041111726,
                        sort: 0,
                        type: 'text',
                    },
                    {
                        content: '错',
                        id: 10041111727,
                        sort: 1,
                        type: 'text',
                    },
                ],
                parent_id: null,
                point: '1.0',
                settings: {
                    case_sensitive: true,
                    option_type: 'text',
                    options_layout: 'vertical',
                    required: false,
                    unordered: false,
                },
                sort: 5,
                sub_subjects: [],
                type: 'true_or_false',
            },
            // short_answer
            {
                answer_number: 0,
                data: null,
                description: '<p><span>查询clu$的size$字段，若该字段为NULL则输出0，编写实现该功能的SQL语句。</span></p>',
                difficulty_level: 'medium',
                id: 10015736043,
                last_updated_at: '2024-05-12T08:38:46Z',
                note: null,
                options: [],
                parent_id: null,
                point: '1.5',
                settings: {
                    case_sensitive: true,
                    required: false,
                    set_limit: false,
                    unordered: false,
                },
                sort: 11,
                sub_subjects: [],
                type: 'short_answer',
            },
        ],
    };
}

// (代理 ON) 11.提交作业
exports.submitExam = async (examId, cookieStr, data) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://lms.ouchn.cn/api/exams/${examId}/submissions`,
        method: 'post',
        headers: {
            'content-type': 'application/json;charset=UTF-8',
            cookie: cookieStr,
        },
        data: JSON.stringify(data),
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = { submission_id: 10072239013 };
}

// 12.获取作业上传列表
exports.submissionList = async (taskId, userId, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://lms.ouchn.cn/api/activities/${taskId}/students/${userId}/submission_list`,
        method: 'get',
        headers: {
            cookie: cookieStr,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        list: [
            {
                activity_id: 10002507653,
                comment: '',
                created_at: '2024-06-05T13:35:18Z',
                created_by: {
                    audit: {
                        audit_by: {
                            name: null,
                        },
                        comment: null,
                        created_at: null,
                        id: 0,
                        status: null,
                        updated_at: null,
                    },
                    avatar_big_url: '',
                    avatar_small_url: '',
                    comment: null,
                    created_at: '2022-03-21T18:01:15Z',
                    department: {
                        code: '9015900',
                        id: 10000000961,
                        name: '阜阳信息工程学习中心',
                        parent_id: 10000000960,
                    },
                    email: '<EMAIL>',
                    grade: null,
                    id: 10000546931,
                    imported_from: 'middle_db',
                    is_imported_data: true,
                    klass: {
                        code: '219015900064502',
                        id: 10000008663,
                        name: '21秋阜阳信息工程2班219015900064502',
                    },
                    mobile_phone: null,
                    name: '王巍',
                    nickname: null,
                    org: {
                        code: '901',
                        id: 10000000002,
                        name: '国家开放大学软件学院',
                        parent_id: 1,
                    },
                    org_id: 10000000002,
                    program: {
                        code: null,
                        description: null,
                        english_name: null,
                        external_id: null,
                        id: 0,
                        level: null,
                        name: null,
                        status: null,
                    },
                    program_id: 0,
                    remarks: null,
                    roles: [
                        {
                            id: 10000000002,
                            name: 'Student',
                        },
                    ],
                    storage_assigned: 0,
                    storage_used: 0,
                    updated_at: '2024-06-21T03:29:29Z',
                    user_addresses: [],
                    user_attributes: {
                        admission_date: null,
                        education: null,
                        first_job_date: null,
                        graduated_from: null,
                        id: 0,
                        identity_number: null,
                        job_name: null,
                        job_type: null,
                        nation: null,
                        native_place: null,
                        occupation_type: null,
                        portfolio_url: null,
                        teaching_date: null,
                    },
                    user_auth_externals: [],
                    user_no: '2190106451770',
                    webex_auth: false,
                },
                group: null,
                group_id: null,
                id: 10009964069,
                instructor_comment: null,
                inter_scores: [],
                is_announce_score_time_passed: true,
                is_draft: false,
                is_latest_version: true,
                is_resubmitted: false,
                marked_submitted: true,
                mode: 'normal',
                recommend: 0,
                rubric_id: null,
                rubric_score: null,
                score: null,
                score_at: '2024-06-05T13:35:18Z',
                slide_references: [],
                submission_correct: {
                    comment: null,
                    id: 10009964069,
                    instructor_score: null,
                    rubric_score: null,
                    score: null,
                    updated_at: '2024-06-05T13:35:18Z',
                    uploads: [],
                },
                submit_by_instructor: false,
                submitted_at: '2024-06-05T13:35:18Z',
                uploads: [
                    {
                        allow_download: true,
                        audio: null,
                        created_at: '2024-06-05T13:35:14Z',
                        created_by_id: 10000546931,
                        deleted: false,
                        id: 10008100073,
                        is_cc_video: false,
                        key: '10008100073',
                        link: null,
                        name: '绘制动态按钮(2).swf',
                        origin_allow_download: true,
                        owner_id: 0,
                        reference_id: 10010112659,
                        referenced_at: '2024-06-05T13:35:18Z',
                        scorm: null,
                        size: 2079,
                        source: null,
                        status: 'ready',
                        third_part_referrer_id: 0,
                        thumbnail: null,
                        type: 'swf',
                        updated_at: '2024-06-05T13:35:14Z',
                        video_src_type: 'video/mp4',
                        videos: [],
                    },
                ],
            },
        ],
    };
}

// 13.获取资源列表
exports.resources = async (cookieStr, params) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://lms.ouchn.cn/api/user/resources`,
        method: 'get',
        headers: {
            cookie: cookieStr,
        },
        params: params,
    };
    let res = await axiosIns(config);
    // let res = await axiosIns(config);
    return res;
};
if (false) {
    let res = {
        page: 1,
        pages: 7,
        uploads: [
            {
                allow_download: true,
                audio: null,
                created_at: '2024-06-05T14:15:19Z',
                created_by_id: 10000546931,
                deleted: false,
                id: 10008101196,
                is_cc_video: false,
                is_folder: false,
                is_shared: false,
                key: '10008101196',
                link: null,
                name: '移动应用界面设计形考5(2).pdf',
                origin_allow_download: null,
                owner_id: 0,
                reference_count: 1,
                reference_id: 0,
                referenced_at: null,
                resource_type: 'upload',
                scorm: null,
                size: 654693,
                source: null,
                status: 'ready',
                third_part_referrer_id: 0,
                thumbnail: null,
                type: 'document',
                updated_at: '2024-06-05T14:15:20Z',
                video_src_type: 'video/mp4',
                videos: [],
            },
        ],
    };
}

// 14.提交上传
exports.submitHomework = async (taskId, comment, uploads, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let data = {
        comment: comment,
        uploads: uploads,
        slides: [],
        is_draft: false,
        is_return: false,
        mode: 'normal',
        // submission_id: 70011276469, //可以不提供
    };
    let config = {
        url: `https://lms.ouchn.cn/api/course/activities/${taskId}/submissions`,
        method: 'post',
        headers: {
            cookie: cookieStr,
            'content-type': 'application/json;charset=UTF-8',
        },
        data: JSON.stringify(data),
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        is_announce_score_time_passed: true,
        message: '作业已经交付',
        student: {
            audit: {
                audit_by: {
                    name: null,
                },
                comment: null,
                created_at: null,
                id: 0,
                status: null,
                updated_at: null,
            },
            avatar_big_url: '',
            avatar_small_url: '',
            comment: null,
            created_at: '2022-03-21T18:01:15Z',
            department: {
                code: '9015900',
                id: 10000000961,
                name: '阜阳信息工程学习中心',
                parent_id: 10000000960,
            },
            email: '<EMAIL>',
            grade: null,
            id: 10000546931,
            imported_from: 'middle_db',
            is_imported_data: true,
            klass: {
                code: '219015900064502',
                id: 10000008663,
                name: '21秋阜阳信息工程2班219015900064502',
            },
            mobile_phone: null,
            name: '王巍',
            nickname: null,
            org: {
                code: '901',
                id: 10000000002,
                name: '国家开放大学软件学院',
                parent_id: 1,
            },
            org_id: 10000000002,
            program: {
                code: null,
                description: null,
                english_name: null,
                external_id: null,
                id: 0,
                level: null,
                name: null,
                status: null,
            },
            program_id: 0,
            remarks: null,
            roles: [
                {
                    id: 10000000002,
                    name: 'Student',
                },
            ],
            storage_assigned: 0,
            storage_used: 0,
            updated_at: '2024-06-21T03:29:29Z',
            user_addresses: [],
            user_attributes: {
                admission_date: null,
                education: null,
                first_job_date: null,
                graduated_from: null,
                id: 0,
                identity_number: null,
                job_name: null,
                job_type: null,
                nation: null,
                native_place: null,
                occupation_type: null,
                portfolio_url: null,
                teaching_date: null,
            },
            user_auth_externals: [],
            user_no: '2190106451770',
            webex_auth: false,
        },
        submission: {
            created_at: '2024-06-21T09:13:46Z',
            created_by: {
                id: 10000546931,
            },
            id: 10010617001,
            inter_scores: [],
            is_resubmitted: false,
            marked_submitted: true,
            recommend: 0,
            score: null,
            submit_by_instructor: false,
            uploads: [
                {
                    id: 10006871905,
                    key: '10006871905',
                    name: '绘制动态按钮(1).swf',
                    size: 2079,
                    type: 'swf',
                },
            ],
        },
    };
}

// 15.提交讨论
exports.submitForm = async (courseName, title, formId, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let agent = await getAgent();
    let config = {
        url: `https://lms.ouchn.cn/api/topics`,
        method: 'post',
        headers: {
            cookie: cookieStr,
            'content-type': 'application/json;charset=UTF-8',
        },
        data: `{"title":"${courseName}-${title}-我自己的感想","content":"<p>学习了这门课程：${courseName}-${title}之后，我感觉收获很多，我以后会坚持自己的理念，把学到的东西用到生活中去</p>","uploads":[],"category_id":${formId}}`,
    };
    let res;
    try {
        res = await axiosIns(config);
    } catch (error) {
        if (error.message.includes('内容重复')) {
            return '内容重复';
        }
        throw error;
    }
    return res;
};
if (false) {
}

// 16.获取form讨论的id
exports.getFormId = async (taskObj, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://lms.ouchn.cn/api/forum/${taskObj.id}/category?fields=id,title,activity(id,sort,module_id,syllabus_id,start_time,end_time,is_started,is_closed,data,can_show_score,score_percentage,title,prerequisites,submit_by_group,group_set_id,group_set_name,imported_from,parent_id),referrer_type`,
        method: 'get',
        headers: {
            cookie: cookieStr,
            'content-type': 'application/json;charset=UTF-8',
        },
    };
    let res = await axiosIns(config);

    return res;
};
if (false) {
    res = {
        topic_category: {
            activity: {
                can_show_score: true,
                data: {
                    announce_score_time: null,
                    announce_score_type: 2,
                    auto_compute: false,
                    auto_compute_rules: {
                        each_praise_score: '0.0',
                        each_reply_score: '0.0',
                        max_praise_score: '0.0',
                        max_reply_score: '0.0',
                    },
                    description:
                        '<p><span style="font-size: 16pt; line-height: 200%; font-family: 微软雅黑, sans-serif; color: rgb(229, 76, 94);">学习完这部分内容，你有什么感想或疑问？还有什么想知道的么？快来告诉我们吧~</span><br></p>',
                    limit_student_create_topic: false,
                    manual_max_score: '0.0',
                    participation_scope: 'inCourse',
                    score_percentage: '0.0',
                    set_praise_rulers: false,
                    set_reply_rulers: false,
                },
                end_time: '2024-12-29T15:59:59Z',
                group_set_id: 0,
                group_set_name: '',
                id: 70007084236,
                imported_from: null,
                is_closed: false,
                is_started: true,
                module_id: 70000842880,
                parent_id: 0,
                prerequisites: [],
                score_percentage: '0.0',
                sort: 1,
                start_time: '2024-08-31T16:00:00Z',
                submit_by_group: false,
                syllabus_id: 70001606738,
                title: '【举手提问】',
            },
            id: 70000307578,
            referrer_type: 'activity',
            title: '【举手提问】',
        },
    };
}

// 16滑动验证码
exports.slideValidate = async base64Image => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    // // 创建 URLSearchParams 对象
    // let params = new URLSearchParams();
    // params.append('username', 'mozhi0012');
    // params.append('password', 'qfQw4h4WzSKt');
    // params.append('captchaData', base64Image);
    // params.append('captchaType', '1310');
    let config = {
        url: 'https://www.bingtop.com/ocr/upload/',
        method: 'post',
        headers: {
            'Content-Type': 'application/x-www-form-urlencoded',
        },
        data: {
            username: 'mozhi0012',
            password: 'qfQw4h4WzSKt',
            captchaData: base64Image,
            captchaType: '1310',
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        code: 0,
        message: '',
        data: {
            captchaId: '1310-85289b64-8593-4cf7-9053-958b811ca023',
            captchaType: '1310',
            recognition: '183,117',
        },
    };
}

exports.mySlideValidate = async (base64Image, handleBase64) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        method: 'post',
        url: 'https://ddddocr.mozhi0012.top/ocr/slide_match',
        headers: {
            'Content-Type': 'application/json',
        },
        data: {
            background_image_base64: base64Image,
            target_image_base64: handleBase64,
            simple_target: true,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        result: {
            target: [187, 93, 267, 173],
            target_x: 0,
            target_y: 0,
        },
    };
}

// 18.提交实验
exports.submitExperiment = async (url, body) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        headers: {
            accept: 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'cache-control': 'no-cache',
            'content-type': 'application/json;charset=UTF-8',
            pragma: 'no-cache',
            priority: 'u=1, i',
            'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-site',
            Referer: 'https://lmsjdsx.ouchn.cn/',
            'Referrer-Policy': 'strict-origin-when-cross-origin',
        },
        url,
        method: 'post',
        data: JSON.stringify(body),
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = { message: '已成功保存' };
}

// 19.获取实验分数
exports.getExperimentScore = async (experimentId, userId, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        method: 'get',
        url: `https://lms.ouchn.cn/api/virtual-experiments/${experimentId}/user/${userId}/score`,
        headers: {
            accept: 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'cache-control': 'no-cache',
            pragma: 'no-cache',
            priority: 'u=1, i',
            'sec-ch-ua': '"Chromium";v="130", "Google Chrome";v="130", "Not?A_Brand";v="99"',
            'sec-ch-ua-mobile': '?0',
            'sec-ch-ua-platform': '"Windows"',
            'sec-fetch-dest': 'empty',
            'sec-fetch-mode': 'cors',
            'sec-fetch-site': 'same-origin',
            cookie: cookieStr,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        scores: {
            submissions: [
                {
                    created_at: '2024-11-05T01:48:11Z',
                    end_time: '2024-11-05T01:48:10Z',
                    from: 'other',
                    id: 213168,
                    score: 100,
                    start_time: '2024-11-05T01:45:05Z',
                    steps: [
                        {
                            answer: '',
                            cost: 180.031,
                            duration: 180,
                            is_pass: true,
                            number: 1,
                            score: 100,
                            title: '自动化生产线认知学习',
                            total_score: 100,
                        },
                    ],
                },
                {
                    created_at: '2024-10-28T00:16:47Z',
                    end_time: '2024-10-28T00:16:46Z',
                    from: 'other',
                    id: 208999,
                    score: 100,
                    start_time: '2024-10-28T00:09:19Z',
                    steps: [
                        {
                            answer: '',
                            cost: 180.002,
                            duration: 180,
                            is_pass: true,
                            number: 1,
                            score: 100,
                            title: '自动化生产线认知学习',
                            total_score: 100,
                        },
                    ],
                },
            ],
            user_info: {
                id: 70000783206,
                name: '汪志龙',
                user_no: '2234001463411',
            },
        },
    };
}

// 23.获取混合大作业任务列表
exports.getMixTaskList = async (userId, taskId, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://lms.ouchn.cn/api/activity/${taskId}/mixtask-subtask`,
        headers: {
            cookie: cookieStr,
        },
        method: 'GET',
    };
    let res = await axiosIns(config);
    return res;
};

// 创建上传任务
exports.uploads = async (fileName, fileSize, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    // 第一步：创建上传任务，获取上传凭证
    let config = {
        url: 'https://lms.ouchn.cn/api/uploads',
        method: 'POST',
        headers: {
            accept: 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'content-type': 'application/json;charset=UTF-8',
            pragma: 'no-cache',
            cookie: cookieStr,
        },
        data: {
            name: fileName,
            size: fileSize,
            parent_type: null,
            parent_id: 0,
            is_scorm: false,
            is_wmpkg: false,
            embed_material_type: '',
            use_storage: true,
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        created_at: '2025-06-01T13:52:32Z',
        id: 70010945182,
        key: 70010945182,
        name: '监督学形成性考核（一）(3).docx',
        status: 'processing',
        storage_type: 'S3',
        transcoder: 'WRPC',
        type: 'document',
        upload_url:
            'https://tencent-tronclass.obs.cn-north-4.myhuaweicloud.com/lms-media/file-storage/70010945182?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=HVVIEHSYEUO4JMOW44EP%2F20250601%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250601T135232Z&X-Amz-Expires=7200&X-Amz-SignedHeaders=host&X-Amz-Signature=a555c59943b96fe076012ed55cc3d0b52ee3eaee4ff4a4d6a73d8ed1ad9980bc',
        url: 'https://tencent-tronclass.obs.cn-north-4.myhuaweicloud.com/lms-media/file-storage/70010945182?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=HVVIEHSYEUO4JMOW44EP%2F20250601%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250601T135232Z&X-Amz-Expires=7200&X-Amz-SignedHeaders=host&X-Amz-Signature=a555c59943b96fe076012ed55cc3d0b52ee3eaee4ff4a4d6a73d8ed1ad9980bc',
    };
}

// 上传文件到华为云OBS
exports.uploadFileToObs = async (uploadUrl, contentType, md5Base64, fileBuffer) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: uploadUrl,
        method: 'PUT',
        headers: {
            accept: 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'content-type': contentType, // 根据文件类型设置
            'content-md5': md5Base64, // MD5值
            pragma: 'no-cache',
            'x-obs-meta-track-content-md5': md5Base64,
        },
        data: fileBuffer, // 文件内容
        maxBodyLength: Infinity,
        maxContentLength: Infinity,
    };
    let res = await axiosIns(config);
    return res;
};

// 上传文件回调
exports.uploadFileCallback = async (fileKey, cookieStr) => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        url: `https://lms.ouchn.cn/internal-api/upload/callback/${fileKey}`,
        method: 'POST',
        headers: {
            accept: 'application/json, text/plain, */*',
            'accept-language': 'zh-CN,zh;q=0.9',
            'cache-control': 'no-cache',
            'content-type': 'application/json;charset=UTF-8',
            pragma: 'no-cache',
            cookie: cookieStr,
        },
        data: {
            file_key: parseInt(fileKey), // 确保是数字类型
        },
    };
    let res = await axiosIns(config);
    return res;
};

// 获取用户基础信息
exports.getPcMultiInfo = async cookieStr => {
    let axiosIns = await axiosInsPromise; // 添加await来获取axios实例
    let config = {
        method: 'post',
        url: 'https://menhu.pt.ouchn.cn/ouchnapp/wap/user/pc-multi-info',
        data: '',
        headers: {
            cookie: cookieStr,
            'content-type': 'application/x-www-form-urlencoded',
            'sec-ch-ua': '"Chromium";v="136", "Google Chrome";v="136", "Not.A/Brand";v="99"',
        },
    };
    let res = await axiosIns(config);
    return res;
};
if (false) {
    res = {
        e: 0,
        m: '操作成功',
        d: {
            info: {
                header: {
                    realname: '段淳轶',
                    avatar: 'https://menhu.pt.ouchn.cn/site/static/images/ouchnPc/man.png',
                },
                base: {
                    账号: '2390101250843',
                    手机号: '186****6004',
                    组织机构: '软件学院阜阳信息工程学校学习中心',
                    邮箱: '',
                },
                other: [
                    {
                        moduleName: '学生信息',
                        xszUrl: 'http://202.205.161.130/#/PdfPreview/E8A5EECF818CCC3A4231AE9CF6B4B2F2',
                        xszDownload: 'http://202.205.161.130/student/preview/xszDownload?xh=E8A5EECF818CCC3A4231AE9CF6B4B2F2',
                        moduleItem: {
                            组织机构: '软件学院阜阳信息工程学校学习中心',
                            账号: '2390101250843',
                            学年学期: '2023秋季',
                            专业层次: '本科（专科起点）',
                            出生日期: '2001-06-06',
                            民族: '汉族',
                            政治面貌: '群众',
                            通讯地址: '安徽省阜阳市',
                            原毕业院校: '合肥职工科技大学',
                            毕业日期: '',
                            原所学专业: '旅游管理',
                            学籍状态: '在籍',
                            入学日期: '2023-09-01',
                            毕业学年学期: '',
                        },
                    },
                ],
            },
        },
    };
}
