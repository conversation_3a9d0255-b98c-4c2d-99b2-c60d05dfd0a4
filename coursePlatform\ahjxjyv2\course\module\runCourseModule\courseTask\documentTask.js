let api = require('../../../../utils/api.js');
async function documentTask(infoLogger, mainPage, courseObj, cellObj, cellLogStr, globalStore) {

    await infoLogger(`${cellLogStr}：开始${cellObj.extension}文档任务 《${cellObj.title}》`);

    let storageObj = globalStore.storageObj;

    let previewRes = await api.getPreviewResource(storageObj, cellObj.relevantId);

    // 获取当前视频课件信息
    await api.studyingLenard(storageObj, courseObj.id, cellObj.id);

    //更改课程状态为已完成
    let finishCellRes = await api.finishCell(storageObj, courseObj.id, cellObj.id);
    if (finishCellRes.code == 'allow') {
        await infoLogger(`${cellLogStr}：${cellObj.extension}文档任务完成 《${cellObj.title}》`, 'green');
    } else {
        await infoLogger(`${cellLogStr}：${cellObj.extension}文档任务失败《${cellObj.title}》,${finishCellRes.code}`, 'red');
    }

    // 等待3秒
    await new Promise(r => setTimeout(r, 30*1000));

    // // 进入到课程界面
    // let cellUrl = `https://jxjynew.ahjxjy.cn/app/jxjy-student-space-web#/myCourse/courseStudy?cellId=${cellObj.id}&courseId=${courseObj.id}`;
    // await mainPage.goto(cellUrl, { waitUntil: 'networkidle2' });
    // await new Promise(r => setTimeout(r, 2000));

    // await infoLogger(`${cellLogStr}：${cellObj.extension}文档任务完成 《${cellObj.title}》`, 'green');
}

module.exports = documentTask;
