let axios = require('axios');

let axiosIns = axios.create({
    timeout: 60 * 1000, // 超时
});

// 添加响应拦截器
axiosIns.interceptors.response.use(
    // 当相应状态码为200-299的时候走到这里
    res => res.data,
    // 其他情况都走到这里
    function (error) {
        // 请求已经发出，也收到了响应，但是状态码不在2xx范围内
        //  Axios 都会自动处理 302 重定向，不会抛出错误，也不会进入拦截器中 error 参数的处理逻辑
        if (error.response) {
            return Promise.reject(new Error(`响应状态码不在2xx范围内：status: ${error.response.status}, data: ${JSON.stringify(error.response.data)}`));
        }

        // 请求已发出但未收到响应
        if (error.request) {
            let errorMsg;
            switch (error.code) {
                case 'ECONNABORTED':
                    errorMsg = '请求超时';
                    break;
                case 'ENOTFOUND':
                    errorMsg = 'DNS 查找失败，无法找到服务器';
                    break;
                case 'ECONNREFUSED':
                    errorMsg = '连接被拒绝';
                    break;
                case 'EHOSTUNREACH':
                    errorMsg = '主机不可达';
                    break;
                case 'ECONNRESET':
                    errorMsg = '连接被重置';
                    break;
                case 'ETIMEDOUT':
                    errorMsg = '连接超时';
                    break;
                default:
                    errorMsg = '网络错误';
            }
            return Promise.reject(new Error(errorMsg));
        }
    }
);

module.exports = axiosIns;
