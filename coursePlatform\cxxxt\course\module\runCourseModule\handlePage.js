let pageTools = require('../../../../utils/pageTools.js');
let handleAttachment = require('./handleAttachment.js'); // 引入处理附件的模块

/**
 * 遍历并处理章节内的页面列表
 * @param {object} taskObj - 全局任务配置对象
 * @param {object} courseStore - 当前课程信息存储对象
 * @param {function} infoLogger - 日志记录函数
 * @param {object} mainPage - Puppeteer 的 Page 对象
 * @param {Array} pageList - 当前章节的页面URL列表
 * @param {string} chapterLogStr - 当前章节的日志前缀字符串
 * @param {string} chapterId - 当前章节ID
 * @returns {Promise<object>} - 返回一个对象，可能包含 skipChapter: true 来指示跳过当前章节
 */
async function handlePage(globalStore,infoLogger, mainPage, chapterLogStr, chapterId,pageCount) {
    let courseStore = globalStore.courseStore; // 从 globalStore 中获取 courseStore
    let taskObj = globalStore.taskObj;

    // 生成页面page列表
    // 页面URL通常是通过API或特定逻辑动态获取的，这里保持原有的固定数量页面逻辑
    // 实际应用中，这里可能需要更复杂的逻辑来确定一个章节到底有多少个页面（任务卡片）
    let pageList = [];
    for (let i = 0; i < pageCount; i++) {
        // 原代码固定循环5次获取页面，可能代表最多5个任务卡片页
        let chapterPath = 'https://mooc1.chaoxing.com/mooc-ans/knowledge/cards';
        let chapterParams = {
            cpi: courseStore.cpi,
            clazzid: courseStore.classId,
            courseid: courseStore.courseId,
            knowledgeid: chapterId, // 使用当前章节ID
            num: i, // 页面编号或索引
            isMicroCourse: 'false',
            mooc2: '1',
            ut: 's',
            v: '20160407-3',
        };
        let pageUrl = pageTools.buildUrl(chapterPath, chapterParams);
        pageList.push(pageUrl);
    }

    // 遍历页面page列表
    loopPage: for (let pageIndex = 0; pageIndex < pageList.length; pageIndex++) {
        let pageLogStr = `${chapterLogStr}-页面[${pageIndex + 1}]`;
        // 取出一个pageUrl
        let pageUrl = pageList[pageIndex];
        await infoLogger(`${pageLogStr}，进入page页面`);
        await pageTools.gotoWithRetry(mainPage, pageUrl, { waitUntil: 'networkidle0' }, 3, infoLogger);
        // await new Promise(r => setTimeout(r, 30*1000));
        // // 进入page页面
        // try {
        //     await mainPage.goto(pageUrl, { waitUntil: 'networkidle0' });
        //     await new Promise(r => setTimeout(r, 3000));
        // } catch (error) {
        //     await infoLogger(`打开章节页面出错，地址：${pageUrl}，错误message:${error.message}`, 'red');
        //     return { skipChapter: true }; // 指示上层循环跳过当前章节
        // }

  

        // 从页面中获取任务数据
        let mArgJson;
        try {
            mArgJson = await mainPage.evaluate(() => mArg);
        } catch (error) {
            await new Promise(r => setTimeout(r, 1000));
            //有可能跳转到 操作异常 页面
            let tempUrl = mainPage.url();
            if (tempUrl.includes('antispiderShowVerify.ac')) {
                await infoLogger(`操作异常，需要输入验证码 ${tempUrl}`, 'red');
                throw new Error(`操作异常，需要输入验证码 ${tempUrl}`);
                // 注意：原代码中的 processVerify 和重试逻辑已移除，错误将直接抛出
                // globalStore.isAntispider = true; // 设置一个全局标志，由上层决定是否中断整个流程或等待
                // return { error: 'antispider', message: `操作异常，需要输入验证码 ${tempUrl}` };
            } else {
                await infoLogger(`${pageLogStr}：获取mArg错误：${tempUrl}`, 'red');
                return { skipChapter: true }; // 指示上层循环跳过当前章节
            }
        }

        // 确保 mArgJson 存在
        if (!mArgJson) {
            if (pageUrl.includes('num=0')) {
                // 尝试重新加载 num=0 的页面一次
                await mainPage.goto(pageUrl, { waitUntil: 'networkidle0' });
                await new Promise(r => setTimeout(r, 3000));
                try {
                    mArgJson = await mainPage.evaluate(() => mArg);
                } catch (e) {
                    await infoLogger(`${pageLogStr}：重试获取mArg失败：${mainPage.url()}`, 'red');
                }
            }
        }

        if (!mArgJson) {
            await infoLogger(`${pageLogStr}：当前页面无任务数据(mArg)，进行下一个章节`);
            return { skipChapter: true }; // 指示上层循环跳过当前章节
        }

   

        await infoLogger(`${pageLogStr}：当前页面有${mArgJson.attachments.length}个任务`);

        // 调用 loopAttachment 处理当前页面的任务
        await handleAttachment(globalStore,infoLogger, mainPage, pageLogStr, chapterId, mArgJson);
    }
    return { skipChapter: false }; // 正常完成页面循环
}

module.exports = handlePage;
