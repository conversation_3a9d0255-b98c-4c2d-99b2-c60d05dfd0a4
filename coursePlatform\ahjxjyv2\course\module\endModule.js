let api = require('../../utils/api.js');

async function endModule(infoLogger, mainPage, globalStore) {
    let taskObj = globalStore.taskObj;
    let storageObj = globalStore.storageObj;


    let courseListRes = await api.getCourseList(storageObj);
    let courseList = courseListRes.items;

    // 如果有课程名筛选条件，就筛选课程
    if (taskObj.coursename) {
        courseList = courseList.filter(item => item.courseName.includes(taskObj.coursename));
    }

    let courseLength = courseList.length;

    let finalResult = [];
    //获取进度
    for (let i = 0; i < courseList.length; i++) {
        let courseObj = courseList[i];
        finalResult.push({
            courseName: courseObj.courseName,
            progress: courseObj.schedule * 1,
        });
    }

    await infoLogger('任务已完成','green')

    //返回数据
    return {
        finalResult,
        warningMessage: globalStore.warningMessage,
    };
}

module.exports = endModule;