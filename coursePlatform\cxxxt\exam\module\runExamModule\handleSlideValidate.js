let api = require('../../../utils/api');

async function main(mainPage, infoLogger) {
    let validateDivHandle = await mainPage.$('#cx_image_margin');
    let bigImageBase64 = await validateDivHandle.screenshot({ type: 'jpeg', encoding: 'base64' });

    //获取图片缺口坐标
    let gapPositionRes = await api.slideValidate(bigImageBase64, '');
    // await new Promise(r=>setTimeout(r,300000))
    if (gapPositionRes.code !== 0) {
        infoLogger('滑动验证码图片识别失败', 'red');
        throw Error('滑动验证码图片识别失败');
    }

    let recognition = gapPositionRes.data.recognition;
    let gapPositionX = recognition.split(',')[0];

    // 获取滑块的定位和尺寸
    let sliderHandle = await mainPage.$('#cx_image_margin > div.cx_hkinnerWrap > div.cx_rightBtn');
    let boundingBox = await sliderHandle.boundingBox();

    // 计算滑块的起始位置和目标位置
    let startX = boundingBox.x + boundingBox.width / 2;
    let startY = boundingBox.y + boundingBox.height / 2;
    let endX = startX + gapPositionX * 1 + 5; // 目标位置的X坐标，表示滑动的距离

    // 模拟鼠标拖动滑块
    await mainPage.mouse.move(startX, startY);
    await mainPage.mouse.down();
    await mainPage.mouse.move(endX, startY, { steps: 20 }); // 增加拖动的平滑度
    await mainPage.mouse.up();
}

// 当验证码不存在的时候#captcha会display:none 但是#cx_image_margin会存在
async function handleSlideValidate(page, infoLogger) {
    for (let i = 0; i < 5; i++) {
        let validateDivHandle = await page.$('#cx_image_margin');
        if (validateDivHandle) {
            let isVisible = await validateDivHandle.isVisible();
            if (validateDivHandle) {
                infoLogger(`第${i + 1}次识别验证码`);
                await main(page, infoLogger);
                await new Promise(r => setTimeout(r, 5 * 1000));
            } else {
                infoLogger('验证码识别成功', 'green');
                return;
            }
        } else {
            infoLogger('验证码识别成功', 'green');
            return;
        }
        // let isVisible = await validateDivHandle.isVisible();
        // if (validateDivHandle) {
        //     infoLogger(`第${i + 1}次识别验证码`);
        //     await main(page, infoLogger);
        //     await new Promise(r => setTimeout(r, 5 * 1000));
        // } else {
        //     infoLogger('验证码识别成功', 'green');
        //     return;
        // }
    }
    throw new Error('5次验证码识别都失败了');
}

module.exports = handleSlideValidate;
