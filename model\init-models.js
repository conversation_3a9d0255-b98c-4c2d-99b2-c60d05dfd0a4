var DataTypes = require("sequelize").DataTypes;
var _bank = require("./bank");
var _cluster = require("./cluster");
var _course = require("./course");
var _course_info_log = require("./course_info_log");
var _device_licenses = require("./device_licenses");
var _exam = require("./exam");
var _exam_info_log = require("./exam_info_log");
var _gjkfdx = require("./gjkfdx");
var _gjkfdx_experiment = require("./gjkfdx_experiment");
var _info = require("./info");
var _permission = require("./permission");
var _photo = require("./photo");
var _proxy = require("./proxy");
var _role = require("./role");
var _role_permission = require("./role_permission");
var _user = require("./user");

function initModels(sequelize) {
  var bank = _bank(sequelize, DataTypes);
  var cluster = _cluster(sequelize, DataTypes);
  var course = _course(sequelize, DataTypes);
  var course_info_log = _course_info_log(sequelize, DataTypes);
  var device_licenses = _device_licenses(sequelize, DataTypes);
  var exam = _exam(sequelize, DataTypes);
  var exam_info_log = _exam_info_log(sequelize, DataTypes);
  var gjkfdx = _gjkfdx(sequelize, DataTypes);
  var gjkfdx_experiment = _gjkfdx_experiment(sequelize, DataTypes);
  var info = _info(sequelize, DataTypes);
  var permission = _permission(sequelize, DataTypes);
  var photo = _photo(sequelize, DataTypes);
  var proxy = _proxy(sequelize, DataTypes);
  var role = _role(sequelize, DataTypes);
  var role_permission = _role_permission(sequelize, DataTypes);
  var user = _user(sequelize, DataTypes);

  course_info_log.belongsTo(course, { as: "course", foreignKey: "course_id"});
  course.hasMany(course_info_log, { as: "course_info_logs", foreignKey: "course_id"});
  exam_info_log.belongsTo(exam, { as: "exam", foreignKey: "exam_id"});
  exam.hasMany(exam_info_log, { as: "exam_info_logs", foreignKey: "exam_id"});
  role_permission.belongsTo(permission, { as: "permission", foreignKey: "permission_id"});
  permission.hasMany(role_permission, { as: "role_permissions", foreignKey: "permission_id"});
  role_permission.belongsTo(role, { as: "role", foreignKey: "role_id"});
  role.hasMany(role_permission, { as: "role_permissions", foreignKey: "role_id"});
  user.belongsTo(role, { as: "user_role", foreignKey: "user_role_id"});
  role.hasMany(user, { as: "users", foreignKey: "user_role_id"});

  return {
    bank,
    cluster,
    course,
    course_info_log,
    device_licenses,
    exam,
    exam_info_log,
    gjkfdx,
    gjkfdx_experiment,
    info,
    permission,
    photo,
    proxy,
    role,
    role_permission,
    user,
  };
}
module.exports = initModels;
module.exports.initModels = initModels;
module.exports.default = initModels;
