let api = require("../../utils/api");
async function runCourseModule(infoLogger, courseList, globalStore) {
    let isDone = true;
    while (isDone) {
        isDone = false;
        for (let i = 0; i < courseList.length; i++) {
            let courseObj = courseList[i];
            if (courseObj.timeLeft > 0) {
                isDone = true;
                //发送请求
                let addDurationRes = await api.addDuration(globalStore, courseObj);
                //刷新courseObj.timeLeft的值，不然会死循环
                courseObj.timeLeft = courseObj.timeLeft - 1;
                courseObj.playingTime = courseObj.playingTime + 1;
                await infoLogger(`课程《${courseObj.courseName}》：已经学习${courseObj.playingTime}分钟，剩余${courseObj.timeLeft}分钟`);
            } else {
                await infoLogger(`课程《${courseObj.courseName}》已经完成视频课程`, "green");
            }
        }
        await new Promise((r) => setTimeout(r, 61 * 1000));
        await infoLogger(`-----已经完成一分钟的学习-----`, "blue");
    }
    await infoLogger(`所有课程视频都已完成`, "green");
}

module.exports = runCourseModule;
