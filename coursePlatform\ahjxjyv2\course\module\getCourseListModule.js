let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api.js');

async function getCourseListModule(infoLogger, mainPage, globalStore) {
    let taskObj = globalStore.taskObj;
    let storageObj = globalStore.storageObj;

    // 从服务器获取课程列表
    let courseListRes = await api.getCourseList(storageObj);
    let courseList = courseListRes.items;

    // 如果有课程名筛选条件，就筛选课程
    if (taskObj.coursename) {
        courseList = courseList.filter(item => item.courseName.includes(taskObj.coursename));
    }

    let courseLength = courseList.length;
    await infoLogger(`获取课程列表成功，共${courseLength}门课程`, 'green'); //记录日志

    // 如果课程列表为空，则返回空数组
    if (courseList.length == 0) {
        return [];
    }

    return courseList;
}

module.exports = getCourseListModule;
