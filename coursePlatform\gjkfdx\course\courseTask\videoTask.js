let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api');
// 有两种方式，一个是用浏览器打开，修改倍速。另一种是通过axios秒过
async function videoTask(infoLogger, mainPage, globalStore, courseObj, taskObj, taskLogStr) {
    // await infoLogger(`${taskLogStr}<${taskObj.title}> ${taskObj.type} 课件开始`);

    let cookieStr = await pageTools.getPageCookies(mainPage);

    // 获取视频时常duration
    let duration;
    try {
        if (taskObj.uploads.length == 0) {
            await infoLogger(`${taskLogStr} <${taskObj.title}> ${taskObj.type} 没有视频`, 'red');
            return;
        }
        duration = taskObj.uploads[0].videos[0].duration;
        duration = Math.floor(duration) - 1;
    } catch (error) {
        // console.log(taskObj)
        await infoLogger(
            `${taskLogStr} <${taskObj.title}> ${taskObj.type} 获取参数出错，任务失败,错误消息：${error.message} videos[0]"${JSON.stringify(
                taskObj.uploads[0].videos[0]
            )}`,
            'red'
        );
        return;
    }

    // await api.learningActivity(globalStore.globalData, globalStore.userAgent, taskObj, cookieStr);
    let activitesRes = await api.updateDuration(taskObj.id, cookieStr, `{"start":1,"end":${duration}}`);
    if (activitesRes.completeness == 'full') {
        await infoLogger(`${taskLogStr}<${taskObj.title}> ${taskObj.type} 已经完成，35秒后进行下一个课件`, 'green');
    }
    await new Promise(r => setTimeout(r, 35 * 1000));
}

module.exports = videoTask;