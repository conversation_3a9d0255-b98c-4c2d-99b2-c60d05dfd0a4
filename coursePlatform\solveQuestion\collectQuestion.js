let { v4 } = require('uuid');
let Model = require('../../config/sequelize.config.js');
let { getSelectAnswerContent } = require('./format');
async function collectQuestion(questionObj, answers, source) {
    // 处理answersContent
    switch (questionObj.type) {
        case '判断题':
        case '单选题':
        case '多选题': {
            questionObj.answersContent = getSelectAnswerContent(answers, questionObj.options);
            break;
        }
        default: {
            answers = answers.slice(0, 4900);
            questionObj.answersContent = answers;
            break;
        }
    }

    // 重新格式化题目
    let quesObj = {
        id: v4(),
        content: questionObj.content,
        type: questionObj.type,
        options: JSON.stringify(questionObj.options),
        platform: questionObj.platform,
        course_name: questionObj.courseName,
        answers: answers,
        answers_content: questionObj.answersContent,
        comment: source,
        add_time: new Date(),
    };

    // 先看看数据库有没有，没有再创建，防止测试的时候多次添加
    let bank = await Model.bank.findOne({
        where: {
            type: questionObj.type,
            content: questionObj.content,
            platform: questionObj.platform,
        },
    });
    if (!bank) {
        await Model.bank.create(quesObj);
    } else {
    }
}

module.exports = collectQuestion;
