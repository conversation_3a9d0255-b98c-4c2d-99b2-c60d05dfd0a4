<table border="0" width="100%">
    <tbody>
        <tr>
            <td colspan="2">
                <script language="javascript">
                    document.write($wapper.api.getDisplayFormat('<b>一、单选题<\/b><SPAN IsScoreRemark="1"><\/SPAN>'));
                </script>
                <b>一、单选题</b><span isscoreremark="1">&nbsp;&nbsp;<font color="gray">(C第1-5题每题5分)</font></span>
            </td>
        </tr>
        <tr id="trScore_437013081282838531" correctstatus="1">
            <td valign="top" align="center" width="30">1.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">互为对偶的两个问题存在关系(　 　 )<\/div><div style="line-height:20px;font-size:10pt"><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046496_35637_0 name="lemonysoft_item_key_1046496_35637" ><TD>(A)<TD><label for=lemonysoft_item_key_1046496_35637_0>原问题无可行解，对偶问题也无可行解<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046496_35637_1 name="lemonysoft_item_key_1046496_35637" ><TD>(B)<TD><label for=lemonysoft_item_key_1046496_35637_1>对偶问题有可行解，原问题也有可行解<\/label><TR><TD><input type=radio value="C" id=lemonysoft_item_key_1046496_35637_2 name="lemonysoft_item_key_1046496_35637" ><TD>(C)<TD><label for=lemonysoft_item_key_1046496_35637_2>原问题有最优解解，对偶问题可能没有最优解<\/label><TR><TD><input type=radio value="D" id=lemonysoft_item_key_1046496_35637_3 name="lemonysoft_item_key_1046496_35637" ><TD>(D)<TD><label for=lemonysoft_item_key_1046496_35637_3>原问题无界解，对偶问题无可行解<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：D]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">互为对偶的两个问题存在关系(　 　 )</div>
                <div style="line-height: 20px; font-size: 10pt">
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046496_35637_0"
                                            name="lemonysoft_item_key_1046496_35637"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046496_35637_0">原问题无可行解，对偶问题也无可行解</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046496_35637_1"
                                            name="lemonysoft_item_key_1046496_35637"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046496_35637_1">对偶问题有可行解，原问题也有可行解</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="C"
                                            id="lemonysoft_item_key_1046496_35637_2"
                                            name="lemonysoft_item_key_1046496_35637"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(C)</td>
                                    <td><label for="lemonysoft_item_key_1046496_35637_2">原问题有最优解解，对偶问题可能没有最优解</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="D"
                                            id="lemonysoft_item_key_1046496_35637_3"
                                            name="lemonysoft_item_key_1046496_35637"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(D)</td>
                                    <td><label for="lemonysoft_item_key_1046496_35637_3">原问题无界解，对偶问题无可行解</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：D]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046496_35637", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838531">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838531"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838532" correctstatus="1">
            <td valign="top" align="center" width="30">2.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">X是线性规划的基本可行解则有(　 　 )<\/div><div style="line-height:20px;font-size:10pt"><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046495_33992_0 name="lemonysoft_item_key_1046495_33992" ><TD>(A)<TD><label for=lemonysoft_item_key_1046495_33992_0>X中的基变量非零，非基变量为零<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046495_33992_1 name="lemonysoft_item_key_1046495_33992" ><TD>(B)<TD><label for=lemonysoft_item_key_1046495_33992_1>X不一定满足约束条件<\/label><TR><TD><input type=radio value="C" id=lemonysoft_item_key_1046495_33992_2 name="lemonysoft_item_key_1046495_33992" ><TD>(C)<TD><label for=lemonysoft_item_key_1046495_33992_2>X中的基变量非负，非基变量为零<\/label><TR><TD><input type=radio value="D" id=lemonysoft_item_key_1046495_33992_3 name="lemonysoft_item_key_1046495_33992" ><TD>(D)<TD><label for=lemonysoft_item_key_1046495_33992_3>X是最优解<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：C]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">X是线性规划的基本可行解则有(　 　 )</div>
                <div style="line-height: 20px; font-size: 10pt">
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046495_33992_0"
                                            name="lemonysoft_item_key_1046495_33992"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046495_33992_0">X中的基变量非零，非基变量为零</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046495_33992_1"
                                            name="lemonysoft_item_key_1046495_33992"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046495_33992_1">X不一定满足约束条件</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="C"
                                            id="lemonysoft_item_key_1046495_33992_2"
                                            name="lemonysoft_item_key_1046495_33992"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(C)</td>
                                    <td><label for="lemonysoft_item_key_1046495_33992_2">X中的基变量非负，非基变量为零</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="D"
                                            id="lemonysoft_item_key_1046495_33992_3"
                                            name="lemonysoft_item_key_1046495_33992"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(D)</td>
                                    <td><label for="lemonysoft_item_key_1046495_33992_3">X是最优解</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：C]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046495_33992", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838532">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838532"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838533" correctstatus="1">
            <td valign="top" align="center" width="30">3.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">线性规划可行域的顶点一定是(　 　 )<\/div><div style="line-height:20px;font-size:10pt"><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046494_65440_0 name="lemonysoft_item_key_1046494_65440" ><TD>(A)<TD><label for=lemonysoft_item_key_1046494_65440_0>基本可行解<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046494_65440_1 name="lemonysoft_item_key_1046494_65440" ><TD>(B)<TD><label for=lemonysoft_item_key_1046494_65440_1>非基本解<\/label><TR><TD><input type=radio value="C" id=lemonysoft_item_key_1046494_65440_2 name="lemonysoft_item_key_1046494_65440" ><TD>(C)<TD><label for=lemonysoft_item_key_1046494_65440_2>非可行解<\/label><TR><TD><input type=radio value="D" id=lemonysoft_item_key_1046494_65440_3 name="lemonysoft_item_key_1046494_65440" ><TD>(D)<TD><label for=lemonysoft_item_key_1046494_65440_3>最优解<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：A]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">线性规划可行域的顶点一定是(　 　 )</div>
                <div style="line-height: 20px; font-size: 10pt">
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046494_65440_0"
                                            name="lemonysoft_item_key_1046494_65440"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046494_65440_0">基本可行解</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046494_65440_1"
                                            name="lemonysoft_item_key_1046494_65440"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046494_65440_1">非基本解</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="C"
                                            id="lemonysoft_item_key_1046494_65440_2"
                                            name="lemonysoft_item_key_1046494_65440"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(C)</td>
                                    <td><label for="lemonysoft_item_key_1046494_65440_2">非可行解</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="D"
                                            id="lemonysoft_item_key_1046494_65440_3"
                                            name="lemonysoft_item_key_1046494_65440"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(D)</td>
                                    <td><label for="lemonysoft_item_key_1046494_65440_3">最优解</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：A]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046494_65440", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838533">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838533"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838534" correctstatus="1">
            <td valign="top" align="center" width="30">4.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">有3个产地4个销地的平衡运输问题模型具有特征(　 　 )　 　　<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046493_17033_0 name="lemonysoft_item_key_1046493_17033" ><TD>(A)<TD><label for=lemonysoft_item_key_1046493_17033_0>有7个变量<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046493_17033_1 name="lemonysoft_item_key_1046493_17033" ><TD>(B)<TD><label for=lemonysoft_item_key_1046493_17033_1>有12个约束<\/label><TR><TD><input type=radio value="C" id=lemonysoft_item_key_1046493_17033_2 name="lemonysoft_item_key_1046493_17033" ><TD>(C)<TD><label for=lemonysoft_item_key_1046493_17033_2>有6约束<\/label><TR><TD><input type=radio value="D" id=lemonysoft_item_key_1046493_17033_3 name="lemonysoft_item_key_1046493_17033" ><TD>(D)<TD><label for=lemonysoft_item_key_1046493_17033_3>有6个基变量<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：D]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    有3个产地4个销地的平衡运输问题模型具有特征(　 　 )　 　　
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046493_17033_0"
                                            name="lemonysoft_item_key_1046493_17033"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046493_17033_0">有7个变量</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046493_17033_1"
                                            name="lemonysoft_item_key_1046493_17033"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046493_17033_1">有12个约束</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="C"
                                            id="lemonysoft_item_key_1046493_17033_2"
                                            name="lemonysoft_item_key_1046493_17033"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(C)</td>
                                    <td><label for="lemonysoft_item_key_1046493_17033_2">有6约束</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="D"
                                            id="lemonysoft_item_key_1046493_17033_3"
                                            name="lemonysoft_item_key_1046493_17033"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(D)</td>
                                    <td><label for="lemonysoft_item_key_1046493_17033_3">有6个基变量</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：D]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046493_17033", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838534">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838534"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838535" correctstatus="1">
            <td valign="top" align="center" width="30">5.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">原问题有5个变量3个约束，其对偶问题(　 　 )　 　 　<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046492_23520_0 name="lemonysoft_item_key_1046492_23520" ><TD>(A)<TD><label for=lemonysoft_item_key_1046492_23520_0>有3个变量5个约束<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046492_23520_1 name="lemonysoft_item_key_1046492_23520" ><TD>(B)<TD><label for=lemonysoft_item_key_1046492_23520_1>有5个变量3个约束<\/label><TR><TD><input type=radio value="C" id=lemonysoft_item_key_1046492_23520_2 name="lemonysoft_item_key_1046492_23520" ><TD>(C)<TD><label for=lemonysoft_item_key_1046492_23520_2>有5个变量5个约束<\/label><TR><TD><input type=radio value="D" id=lemonysoft_item_key_1046492_23520_3 name="lemonysoft_item_key_1046492_23520" ><TD>(D)<TD><label for=lemonysoft_item_key_1046492_23520_3>有3个变量3个约束<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：A]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    原问题有5个变量3个约束，其对偶问题(　 　 )　 　 　
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046492_23520_0"
                                            name="lemonysoft_item_key_1046492_23520"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046492_23520_0">有3个变量5个约束</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046492_23520_1"
                                            name="lemonysoft_item_key_1046492_23520"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046492_23520_1">有5个变量3个约束</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="C"
                                            id="lemonysoft_item_key_1046492_23520_2"
                                            name="lemonysoft_item_key_1046492_23520"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(C)</td>
                                    <td><label for="lemonysoft_item_key_1046492_23520_2">有5个变量5个约束</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="D"
                                            id="lemonysoft_item_key_1046492_23520_3"
                                            name="lemonysoft_item_key_1046492_23520"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(D)</td>
                                    <td><label for="lemonysoft_item_key_1046492_23520_3">有3个变量3个约束</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：A]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046492_23520", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838535">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838535"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr>
            <td colspan="2">
                <script language="javascript">
                    var oSpan = document.getElementsByTagName("SPAN");
                    for (var i = oSpan.length - 1; i >= 0; i--) {
                        if (oSpan[i].getAttribute("IsScoreRemark") == "1") {
                            oSpan[i].innerHTML = "&nbsp;&nbsp;<font color=gray>(C第1-5题每题5分)</font>";
                            break;
                        }
                    }
                </script>
                <script language="javascript">
                    document.write($wapper.api.getDisplayFormat('<b>二、判断题<\/b><SPAN IsScoreRemark="1"><\/SPAN>'));
                </script>
                <b>二、判断题</b><span isscoreremark="1">&nbsp;&nbsp;<font color="gray">(第1-15题每题5分)</font></span>
            </td>
        </tr>
        <tr id="trScore_437013081282838537" correctstatus="1">
            <td valign="top" align="center" width="30">1.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">若原问题具有m个约束，则它的对偶问题具有m个变量<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046488_28546_0 name="lemonysoft_item_key_1046488_28546" ><TD>(A)<TD><label for=lemonysoft_item_key_1046488_28546_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046488_28546_1 name="lemonysoft_item_key_1046488_28546" ><TD>(B)<TD><label for=lemonysoft_item_key_1046488_28546_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：A]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    若原问题具有m个约束，则它的对偶问题具有m个变量
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046488_28546_0"
                                            name="lemonysoft_item_key_1046488_28546"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046488_28546_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046488_28546_1"
                                            name="lemonysoft_item_key_1046488_28546"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046488_28546_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：A]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046488_28546", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838537">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838537"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838538" correctstatus="1">
            <td valign="top" align="center" width="30">2.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">求极大值的目标值是各分枝的上界<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046487_7404_0 name="lemonysoft_item_key_1046487_7404" ><TD>(A)<TD><label for=lemonysoft_item_key_1046487_7404_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046487_7404_1 name="lemonysoft_item_key_1046487_7404" ><TD>(B)<TD><label for=lemonysoft_item_key_1046487_7404_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：A]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    求极大值的目标值是各分枝的上界
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046487_7404_0"
                                            name="lemonysoft_item_key_1046487_7404"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046487_7404_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046487_7404_1"
                                            name="lemonysoft_item_key_1046487_7404"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046487_7404_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：A]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046487_7404", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838538">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838538"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838539" correctstatus="1">
            <td valign="top" align="center" width="30">3.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">将指派问题效率表中的每一元素同时减去一个数后最优解不变<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046486_12718_0 name="lemonysoft_item_key_1046486_12718" ><TD>(A)<TD><label for=lemonysoft_item_key_1046486_12718_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046486_12718_1 name="lemonysoft_item_key_1046486_12718" ><TD>(B)<TD><label for=lemonysoft_item_key_1046486_12718_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：A]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    将指派问题效率表中的每一元素同时减去一个数后最优解不变
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046486_12718_0"
                                            name="lemonysoft_item_key_1046486_12718"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046486_12718_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046486_12718_1"
                                            name="lemonysoft_item_key_1046486_12718"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046486_12718_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：A]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046486_12718", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838539">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838539"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838540" correctstatus="1">
            <td valign="top" align="center" width="30">4.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">人工变量出基后还可能再进基<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046485_50_0 name="lemonysoft_item_key_1046485_50" ><TD>(A)<TD><label for=lemonysoft_item_key_1046485_50_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046485_50_1 name="lemonysoft_item_key_1046485_50" ><TD>(B)<TD><label for=lemonysoft_item_key_1046485_50_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：B]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    人工变量出基后还可能再进基
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046485_50_0"
                                            name="lemonysoft_item_key_1046485_50"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046485_50_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046485_50_1"
                                            name="lemonysoft_item_key_1046485_50"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046485_50_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：B]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046485_50", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838540">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838540"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838541" correctstatus="1">
            <td valign="top" align="center" width="30">5.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">一对正负偏差变量至少一个等于零<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046484_18821_0 name="lemonysoft_item_key_1046484_18821" ><TD>(A)<TD><label for=lemonysoft_item_key_1046484_18821_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046484_18821_1 name="lemonysoft_item_key_1046484_18821" ><TD>(B)<TD><label for=lemonysoft_item_key_1046484_18821_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：A]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    一对正负偏差变量至少一个等于零
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046484_18821_0"
                                            name="lemonysoft_item_key_1046484_18821"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046484_18821_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046484_18821_1"
                                            name="lemonysoft_item_key_1046484_18821"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046484_18821_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：A]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046484_18821", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838541">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838541"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838542" correctstatus="1">
            <td valign="top" align="center" width="30">6.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">运输问题不一定存在最优解<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046483_19778_0 name="lemonysoft_item_key_1046483_19778" ><TD>(A)<TD><label for=lemonysoft_item_key_1046483_19778_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046483_19778_1 name="lemonysoft_item_key_1046483_19778" ><TD>(B)<TD><label for=lemonysoft_item_key_1046483_19778_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：B]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    运输问题不一定存在最优解
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046483_19778_0"
                                            name="lemonysoft_item_key_1046483_19778"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046483_19778_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046483_19778_1"
                                            name="lemonysoft_item_key_1046483_19778"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046483_19778_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：B]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046483_19778", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838542">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838542"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838543" correctstatus="1">
            <td valign="top" align="center" width="30">7.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">可行解是基本解<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046482_27093_0 name="lemonysoft_item_key_1046482_27093" ><TD>(A)<TD><label for=lemonysoft_item_key_1046482_27093_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046482_27093_1 name="lemonysoft_item_key_1046482_27093" ><TD>(B)<TD><label for=lemonysoft_item_key_1046482_27093_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：B]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    可行解是基本解
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046482_27093_0"
                                            name="lemonysoft_item_key_1046482_27093"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046482_27093_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046482_27093_1"
                                            name="lemonysoft_item_key_1046482_27093"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046482_27093_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：B]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046482_27093", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838543">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838543"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838544" correctstatus="1">
            <td valign="top" align="center" width="30">8.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">匈牙利法是对指派问题求最小值的一种求解方法<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046480_1807_0 name="lemonysoft_item_key_1046480_1807" ><TD>(A)<TD><label for=lemonysoft_item_key_1046480_1807_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046480_1807_1 name="lemonysoft_item_key_1046480_1807" ><TD>(B)<TD><label for=lemonysoft_item_key_1046480_1807_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：B]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    匈牙利法是对指派问题求最小值的一种求解方法
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046480_1807_0"
                                            name="lemonysoft_item_key_1046480_1807"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046480_1807_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046480_1807_1"
                                            name="lemonysoft_item_key_1046480_1807"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046480_1807_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：B]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046480_1807", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838544">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838544"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838545" correctstatus="1">
            <td valign="top" align="center" width="30">9.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">整数规划的最优解是先求相应的线性规划的最优解然后取整得到<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046479_55377_0 name="lemonysoft_item_key_1046479_55377" ><TD>(A)<TD><label for=lemonysoft_item_key_1046479_55377_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046479_55377_1 name="lemonysoft_item_key_1046479_55377" ><TD>(B)<TD><label for=lemonysoft_item_key_1046479_55377_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：B]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    整数规划的最优解是先求相应的线性规划的最优解然后取整得到
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046479_55377_0"
                                            name="lemonysoft_item_key_1046479_55377"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046479_55377_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046479_55377_1"
                                            name="lemonysoft_item_key_1046479_55377"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046479_55377_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：B]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046479_55377", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838545">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838545"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838546" correctstatus="1">
            <td valign="top" align="center" width="30">10.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">目标约束含有偏差变量<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046478_57666_0 name="lemonysoft_item_key_1046478_57666" ><TD>(A)<TD><label for=lemonysoft_item_key_1046478_57666_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046478_57666_1 name="lemonysoft_item_key_1046478_57666" ><TD>(B)<TD><label for=lemonysoft_item_key_1046478_57666_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：A]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    目标约束含有偏差变量
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046478_57666_0"
                                            name="lemonysoft_item_key_1046478_57666"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046478_57666_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046478_57666_1"
                                            name="lemonysoft_item_key_1046478_57666"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046478_57666_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：A]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046478_57666", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838546">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838546"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838547" correctstatus="1">
            <td valign="top" align="center" width="30">11.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">m+n－1个变量构成基变量组的充要条件是它们不包含闭回路<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046477_30675_0 name="lemonysoft_item_key_1046477_30675" ><TD>(A)<TD><label for=lemonysoft_item_key_1046477_30675_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046477_30675_1 name="lemonysoft_item_key_1046477_30675" ><TD>(B)<TD><label for=lemonysoft_item_key_1046477_30675_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：A]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    m+n－1个变量构成基变量组的充要条件是它们不包含闭回路
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046477_30675_0"
                                            name="lemonysoft_item_key_1046477_30675"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046477_30675_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046477_30675_1"
                                            name="lemonysoft_item_key_1046477_30675"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046477_30675_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：A]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046477_30675", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838547">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838547"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838548" correctstatus="1">
            <td valign="top" align="center" width="30">12.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">原问题具有无界解，则对偶问题不可行　<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046476_12405_0 name="lemonysoft_item_key_1046476_12405" ><TD>(A)<TD><label for=lemonysoft_item_key_1046476_12405_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046476_12405_1 name="lemonysoft_item_key_1046476_12405" ><TD>(B)<TD><label for=lemonysoft_item_key_1046476_12405_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：A]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    原问题具有无界解，则对偶问题不可行　
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046476_12405_0"
                                            name="lemonysoft_item_key_1046476_12405"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046476_12405_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046476_12405_1"
                                            name="lemonysoft_item_key_1046476_12405"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046476_12405_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：A]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046476_12405", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838548">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838548"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838549" correctstatus="1">
            <td valign="top" align="center" width="30">13.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">原问题无最优解，则对偶问题无可行解<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046491_38069_0 name="lemonysoft_item_key_1046491_38069" ><TD>(A)<TD><label for=lemonysoft_item_key_1046491_38069_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046491_38069_1 name="lemonysoft_item_key_1046491_38069" ><TD>(B)<TD><label for=lemonysoft_item_key_1046491_38069_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：B]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    原问题无最优解，则对偶问题无可行解
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046491_38069_0"
                                            name="lemonysoft_item_key_1046491_38069"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046491_38069_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046491_38069_1"
                                            name="lemonysoft_item_key_1046491_38069"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046491_38069_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：B]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046491_38069", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838549">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838549"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838550" correctstatus="1">
            <td valign="top" align="center" width="30">14.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">要求不低于目标值的目标函数是minZd<\/div><div style="line-height:20px;font-size:10pt"><div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046490_56134_0 name="lemonysoft_item_key_1046490_56134" ><TD>(A)<TD><label for=lemonysoft_item_key_1046490_56134_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046490_56134_1 name="lemonysoft_item_key_1046490_56134" ><TD>(B)<TD><label for=lemonysoft_item_key_1046490_56134_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：A]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">要求不低于目标值的目标函数是minZd</div>
                <div style="line-height: 20px; font-size: 10pt">
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046490_56134_0"
                                            name="lemonysoft_item_key_1046490_56134"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046490_56134_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046490_56134_1"
                                            name="lemonysoft_item_key_1046490_56134"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046490_56134_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：A]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046490_56134", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838550">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838550"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
        <tr id="trScore_437013081282838551" correctstatus="1">
            <td valign="top" align="center" width="30">15.</td>
            <td>
                <script language="javascript">
                    document.write(
                        $wapper.api.getDisplayFormat(
                            '<div style="line-height:20px;font-size:10pt">原问题求最大值，第i个约束是“≥”约束，则第i个对偶变量yi ≤0　<div><table IsItemOption=1 OptionType="radio"><TR><TD><input type=radio value="A" id=lemonysoft_item_key_1046489_48513_0 name="lemonysoft_item_key_1046489_48513" ><TD>(A)<TD><label for=lemonysoft_item_key_1046489_48513_0>对<\/label><TR><TD><input type=radio value="B" id=lemonysoft_item_key_1046489_48513_1 name="lemonysoft_item_key_1046489_48513" ><TD>(B)<TD><label for=lemonysoft_item_key_1046489_48513_1>错<\/label><\/table><\/div><DIV style="color:darkred;font-size:10pt">[参考答案：A]&nbsp; 分值：5<\/DIV><\/div>'
                        )
                    );
                </script>
                <div style="line-height: 20px; font-size: 10pt">
                    原问题求最大值，第i个约束是“≥”约束，则第i个对偶变量yi ≤0　
                    <div>
                        <table isitemoption="1" optiontype="radio">
                            <tbody>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="A"
                                            id="lemonysoft_item_key_1046489_48513_0"
                                            name="lemonysoft_item_key_1046489_48513"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(A)</td>
                                    <td><label for="lemonysoft_item_key_1046489_48513_0">对</label></td>
                                </tr>
                                <tr>
                                    <td>
                                        <input
                                            type="radio"
                                            value="B"
                                            id="lemonysoft_item_key_1046489_48513_1"
                                            name="lemonysoft_item_key_1046489_48513"
                                            initchecked="0"
                                        />
                                    </td>
                                    <td>(B)</td>
                                    <td><label for="lemonysoft_item_key_1046489_48513_1">错</label></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                    <div style="color: darkred; font-size: 10pt">[参考答案：A]&nbsp; 分值：5</div>
                </div>
                <script language="javascript">
                    showAnswer("lemonysoft_item_key_1046489_48513", "", "3");
                </script>
                <table width="100%" border="0">
                    <tbody>
                        <tr>
                            <td style="border-bottom: dotted gray 1px; background: #f0f0f0">
                                <table width="100%">
                                    <tbody>
                                        <tr id="trCorrect_437013081282838551">
                                            <td align="left">
                                                得分：<input
                                                    type="text"
                                                    style="text-align: center; border: none; background: none; color: red; width: 20px"
                                                    readonly=""
                                                    value="0"
                                                    name="txtScore_437013081282838551"
                                                    maxscore="5.0"
                                                    maxlength="3"
                                                />
                                                分
                                            </td>
                                            <td align="right"><font color="#303030">系统自动批改于2024年3月23日 15点54分</font></td>
                                        </tr>
                                    </tbody>
                                </table>
                            </td>
                        </tr>
                        <tr>
                            <td>&nbsp;</td>
                        </tr>
                    </tbody>
                </table>
            </td>
        </tr>
    </tbody>
</table>
