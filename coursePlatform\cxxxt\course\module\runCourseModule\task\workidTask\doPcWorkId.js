let decryptFont = require('../../../../../utils/decrypteFont/index.js');
let { traditionalized, simplized } = require('../../../../../utils/traditionalized.js');
let solveQuestion = require('../../../../../../solveQuestion/index.js');
let qs = require('qs');
let api = require('../../../../../utils/api.js');
let pageTools = require('../../../../../../utils/pageTools.js');

// 在分析直接api提交试卷的时候，url中有一个参数无法获取正确值，就是pos。所以我打算先API保存再手动提交

async function doWorkId(mainPage, courseStore, infoLogger, attachment, attachLogStr, chapterId) {
    //1.获取题目列表
    let questionList = await mainPage.evaluate(courseName => {
        function handleImgs(s) {
            // 匹配所有 img 标签，捕获 src 属性的 URL
            let imgEs = s.match(/<img\s+[^>]*src\s*=\s*['"]([^'"]+)['"][^>]*>/gi);
            if (imgEs) {
                for (let j = 0; j < imgEs.length; j++) {
                    // 匹配 img 标签中的 src 属性
                    let urlMatch = imgEs[j].match(/src\s*=\s*['"]([^'"]+)['"]/i);
                    if (urlMatch && urlMatch[1]) {
                        let url = urlMatch[1]; // 获取完整 URL
                        // 用 [URL] 格式替换 img 标签为 URL
                        s = s.replace(imgEs[j], `[URL]${url}[/URL]`);
                    }
                }
            }
            return s;
        }

        function trim(s) {
            return (
                s
                    //删除多余字符串
                    .replace(/(<([^>]+)>)/gi, '') //去掉所有的html标记 <a> </a>
                    .replaceAll('&nbsp;', '')
                    .replaceAll('\n', '') //删除所有的换行符
                    .replace(/\s{2,}/g, ' ') //删除两个及以上的空格，保留单个空格
                    .trim()
                    .replace(/^\d+\s*【.*?】/, '') //删除所有1 【单选题】
            );
        }

        function handleContent(s) {
            return trim(handleImgs(s)).slice(0, 3000);
        }

        //生成一个唯一id，不用uuid生成了
        function generateUniqueId() {
            return 'id-' + new Date().getTime().toString(36) + '-' + Math.random().toString(36).substr(2, 9);
        }

        let TimuList = document.querySelectorAll('#ZyBottom .TiMu');

        let questionList = [];
        for (let i = 0; i < TimuList.length; i++) {
            let timuDom = TimuList[i];
            let questionFull = timuDom.querySelector('.Zy_TItle').innerHTML;

            //题目类型
            let type = questionFull.match(/【(.*?)】/)[1];

            //题目内容
            let content = trim(handleImgs(questionFull));

            //获取题目id
            let questionId = '0';
            // 问题对应的答案标签 填空题的答案标签可能不止一个
            let idElements = timuDom.getElementsByTagName('input');
            //如果没有答案，就跳到下一题
            if (idElements.length == 0) {
                continue;
            }
            //通过答案标签获取问题的id
            for (let z = 0; z < idElements.length; z++) {
                try {
                    if (idElements[z].getAttribute('name').indexOf('type') !== -1) {
                        questionId = idElements[z].getAttribute('name').replace('type', '');
                        break;
                    }
                } catch (e) {
                    continue;
                }
            }
            //如果id为0或者不存在，就进行下一题
            if (questionId == '0' || content == '') {
                continue;
            }

            //4.获取题目选项
            let options = [];
            switch (type) {
                case '单选题':
                case '多选题':
                    let optionList = timuDom.querySelectorAll('.Zy_ulTop li');
                    for (let j = 0; j < optionList.length; j++) {
                        let optionTag = optionList[j];
                        let optionFull = trim(handleImgs(optionTag.textContent));
                        let optionKey = optionFull.slice(0, 1);
                        let optionValue = optionFull.slice(1).trim();
                        optionContent = optionKey + ':' + optionValue;
                        options.push(optionContent);
                    }
                    break;
                case '判断题':
                    options = ['A:对', 'B:错'];
                    break;
                default:
                    options = [];
                    break;
            }

            //收集题目对象
            questionList.push({
                id: generateUniqueId() + questionId, //id-ltf3k9bz-qnqw2bq3qanswer401584769
                content: content,
                type: type,
                options: options,
                platform: '超星学习通',
                courseName: courseName,
            });
        }
        return questionList;
    }, courseStore.courseName);

    // 2.获取加密字体的base64字符
    let base64Font = await mainPage.evaluate(() => {
        // 获取所有 style 元素
        let styles = document.querySelectorAll('style');
        let $tip = null;
        // 遍历 style 元素，查找包含 'font-cxsecret' 的元素
        styles.forEach(style => {
            if (style.textContent.includes('font-cxsecret')) {
                $tip = style;
            }
        });
        // 如果没有找到，直接返回
        if (!$tip) {
            return;
        }
        // 从 style 元素的文本内容中提取 base64 字符串
        let base64Font = $tip.textContent.match(/base64,([\w\W]+?)'/)[1];
        return base64Font;
    });

    // 3.解密字体，以及康熙部首替换
    if (base64Font) {
        for (let z = 0; z < questionList.length; z++) {
            let questionObj = questionList[z];

            questionObj.content = decryptFont(base64Font, questionObj.content);
            questionObj.content = traditionalized(questionObj.content);
            for (let y = 0; y < questionObj.options.length; y++) {
                questionObj.options[y] = decryptFont(base64Font, questionObj.options[y]);
                questionObj.options[y] = traditionalized(questionObj.options[y]);
            }
        }
    }

    // 更改页面btnBlueSubmit逻辑
    await mainPage.evaluate(async () => {
        // var submitObj;
        window.btnBlueSubmit = function btnBlueSubmit() {
            var classId = document.querySelector('#classId').value;
            var courseId = document.querySelector('#courseId').value;
            var knowledgeId = document.querySelector('#knowledgeid').value;
            // classId, courseId,knowledgeId
            validateTimeNew(classId, courseId, knowledgeId, function () {
                toadd(arguments);
            });
            var ajax_url = version(document.form1.action);
            var ajax_type = $('#form1').attr('method');
            var ajax_data = $('#form1').serialize();

            window.submitObj = {
                type: ajax_type,
                url: ajax_url,
                data: ajax_data,
                dataType: 'json',
            };
        };
        // let submitBtn = document.querySelector('a.btnSubmit.workBtnIndex');
    });

    // 获取元素位置
    const element = await mainPage.$('a.btnSubmit.workBtnIndex');
    const box = await element.boundingBox();
    // 移动鼠标到元素中心
    await mainPage.mouse.move(box.x + box.width / 2, box.y + box.height / 2);
    // 点击
    await mainPage.click('a.btnSubmit.workBtnIndex');
    await new Promise(r => setTimeout(r, 1000));

    // 获取请求数据
    let requestData = await mainPage.evaluate(() => {
        var ajax_url = version(document.form1.action);
        var ajax_type = $('#form1').attr('method');
        var ajax_data = $('#form1').serialize();
        return {
            type: ajax_type,
            url: ajax_url,
            data: ajax_data,
            dataType: 'json',
        };
    });

    // 把请求体转为请求对象
    let submitObj = qs.parse(requestData.data);
    let answerwqbid = [];
    // 查找答案
    let answerCount = 0;
    for (let i = 0; i < questionList.length; i++) {
        //1.取出一个问题对象
        let questionObj = questionList[i];
        let id = questionObj.id.slice(21); // answer403098146
        answerwqbid.push(id.replace('answer', ''));
        let answers;
        try {
            answers = await solveQuestion(questionObj);
        } catch (error) {
            continue;
        }
        if (answers) {
            questionObj.answers = answers;
            switch (questionObj.type) {
                // type:0
                case '单选题': {
                    submitObj[id] = answers;
                    break;
                }
                // type:1
                case '多选题': {
                    let numId = id.slice(6);
                    submitObj[`answercheck${numId}`] = answers.split('');
                    submitObj[id] = answers;
                    break;
                }
                // type:3
                case '判断题': {
                    submitObj[id] = answers == 'A' ? true : false;
                    break;
                }
                //
                case '计算题':
                case '简答题': {
                    submitObj[id] = `<p>${answers}</p>`;
                    break;
                }
                // type:2
                case '填空题': {
                    let numId = id.slice(6);
                    let tiankongsize = submitObj[`tiankongsize${numId}`] * 1;
                    let answerArr = answers.split('|');
                    for (let i = 0; i < tiankongsize; i++) {
                        if (!answerArr[i]) break;
                        submitObj[`answerEditor${numId}${i + 1}`] = `<p>${answerArr[i]}</p>`;
                    }
                    break;
                }
                default:
                    await infoLogger(`${attachLogStr} 未知题型：${questionObj.type}`, 'red');
                    submitObj[id] = '';
                    break;
            }
            answerCount++;
        }
    }
    // submitObj.answerwqbid = answerwqbid.join(',') + ',';
    // 把对象转回请求字符串
    let submitStr = qs.stringify(submitObj);

    // 防止检测机制，产生一个3-7的随机数
    let watiTimeCount = pageTools.getRandomInt(2,5)
    watiTimeCount = 3;
    await infoLogger(`${attachLogStr}，等待${watiTimeCount}分钟后提交`);
    await new Promise(resolve => setTimeout(resolve, 60000 * watiTimeCount));

    let cookieStr = await pageTools.getPageCookies(mainPage);
    let submitRes = await api.submitWorkidTask(requestData.url, submitStr, cookieStr);
    await infoLogger(`${attachLogStr}，作业已提交：${submitRes.msg}`);
    return submitRes;
}

module.exports = doWorkId;
