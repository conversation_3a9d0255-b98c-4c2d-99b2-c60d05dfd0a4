let axiosIns = require('../../../../utils/axiosIns.js');
let { lettersToNumbers, numbersToLetters } = require('../../../../solveQuestion/format.js');
let aiChat = require('../../../../solveQuestion/aiChat.js');
let { validateAnswer } = require('../../../../solveQuestion/format.js');
let collectQuestion = require('../../../../solveQuestion/collectQuestion.js');

// 格式化题目内容，具体说明见案例
function strToMessageArr(str) {
    // 正则表达式匹配 [URL] 和 [/URL] 之间的内容
    const urlRegex = /\[URL\](.*?)\[\/URL\]/g;

    let result = [];
    let lastIndex = 0;
    let match;

    // 查找所有的 [URL] 标签之间的内容
    while ((match = urlRegex.exec(str)) !== null) {
        // 提取 URL 内容
        const urlContent = match[1];

        // 将 [URL] 之前的文本部分作为 text 类型加入结果数组
        if (match.index > lastIndex) {
            result.push({
                type: 'text',
                text: str.slice(lastIndex, match.index),
            });
        }

        // 将 URL 内容作为 image_url 类型加入结果数组
        result.push({
            type: 'image_url',
            image_url: {
                url: urlContent,
            },
        });

        // 更新处理过的最后索引
        lastIndex = urlRegex.lastIndex;
    }

    // 添加剩余的文本部分（URL 后的文本）
    if (lastIndex < str.length) {
        result.push({
            type: 'text',
            text: str.slice(lastIndex),
        });
    }

    return result;
}
// let str = '下列函数在区间[URL]data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAqguHaqAcAxCvhdGBkxjsAAAAASUVORK5CYII=[/URL]上单调递减 的是（ ）．';
// console.log(strToMessageArr(str));

async function handleMessageArr(questionObj) {
    let questionType = questionObj.type;

    // 处理主题干内容
    let contentArr = strToMessageArr(questionObj.content);
    let messageArr = [];

    if (questionObj.type == '分析题') {
        // 1. 先添加题目类型说明和主题干
        messageArr.push({
            type: 'text',
            text: `这是一道分析题（嵌套题目），包含一个主题干和多个子题目。\n\n【主题干内容】：`,
        });

        // 添加主题干的内容（包含图片）
        messageArr.push(...contentArr);

        // 2. 处理每个子题目
        messageArr.push({
            type: 'text',
            text: `\n\n【子题目列表】：\n`,
        });

        // 遍历每个子题目，处理其中的图片
        for (let i = 0; i < questionObj.sub_subjects.length; i++) {
            let subQuestion = questionObj.sub_subjects[i];

            // 添加子题目序号和ID
            messageArr.push({
                type: 'text',
                text: `\n第${i + 1}题（ID: ${subQuestion.id}）：`,
            });

            // 处理子题目内容中的图片
            let subContentArr = strToMessageArr(subQuestion.content);
            messageArr.push(...subContentArr);

            // 添加选项
            messageArr.push({
                type: 'text',
                text: `\n选项：\n${subQuestion.options.map(opt => `  - ${opt.content} (ID: ${opt.id})`).join('\n')}\n`,
            });
        }

        // 3. 添加答题要求
        messageArr.push({
            type: 'text',
            text: `
                【答题要求】：
                1. 仔细阅读主题干，理解题目背景
                2. 根据主题干的内容，为每个子题目选择正确的答案
                3. 返回格式要求：
                - 为每个子题目构建一个对象：{"subject_id": 子题目ID, "answer_option_ids": [选中的选项ID]}
                - 将所有对象放入一个数组中
                
                最终返回JSON数组格式如下：
                [
                {"subject_id": 70027436252, "answer_option_ids": [70073118677]},
                {"subject_id": 70027436266, "answer_option_ids": [70073118730]},
                {"subject_id": 70027436285, "answer_option_ids": [70073118778]}
                ]

                请直接返回符合格式的JSON数组，不要包含任何其他内容。
                `,
        });
    }

    if (questionObj.type == '匹配题') {
        messageArr = [
            {
                type: 'text',
                text: `这是一道匹配题。\n\n【题目内容】：`,
            },
            ...contentArr,
            {
                type: 'text',
                text: `\n\n【匹配任务】：`,
            },
            {
                type: 'text',
                text: `
                    请根据题目内容，将左侧选项与右侧答案进行正确匹配。
                    
                    左侧选项列表：
                    ${questionObj.sub_subjects.map((sub, index) => `${index + 1}. ${sub.content || `选项${index + 1}`} (ID: ${sub.id})`).join('\n')}
                    
                    右侧答案池：
                    ${questionObj.rawOptions.map((opt, index) => `${String.fromCharCode(65 + index)}. ${opt.content} (ID: ${opt.id})`).join('\n')}
                    
                    【答题说明】：
                    1. 仔细阅读题目内容，理解匹配的逻辑关系
                    2. 每个左侧选项需要匹配一个右侧答案
                    3. 根据题目的具体要求进行匹配（可能是概念对应、填空匹配、因果关系等）
                    
                    【返回格式】：
                    请返回一个JSON数组，每个元素包含：
                    - subject_id: 左侧选项的ID
                    - answer_option_ids: 匹配的右侧答案ID（数组形式）
                    左侧选项列表和右侧答案池是一一对应的关系，所以选项表里面有几个选项，最终返回的数组，就会有几个元素。
                    
                    示例格式：
                    [
                    {"subject_id": ${questionObj.sub_subjects[0]?.id || '左侧选项1的ID'}, "answer_option_ids": [${
                    questionObj.rawOptions[0]?.id || '匹配的右侧答案ID'
                }]},
                    {"subject_id": ${questionObj.sub_subjects[1]?.id || '左侧选项2的ID'}, "answer_option_ids": [${
                    questionObj.rawOptions[1]?.id || '匹配的右侧答案ID'
                }]}
                    ]
                    
                    请直接返回符合格式的JSON数组，不要包含其他内容。
                `,
            },
        ];
    }

    if (questionObj.type == '填空题') {
        messageArr = [
            {
                type: 'text',
                text: `这是一道填空题。\n\n【题目内容】：`,
            },
            ...contentArr,
            {
                type: 'text',
                text: `\n\n【答题要求】：`,
            },
            {
                type: 'text',
                text: `
    请识别题目中所有需要填空的位置，并按顺序提供答案。
    
    【核心任务】：
    1. 找出题目中所有的空白处（可能是下划线、数字标记、圆圈数字或其他任何表示需要填空的标记）
    2. 根据题目内容和要求，为每个空白处填写正确答案
    3. 按照空白在题目中出现的顺序排列答案
    
    【返回格式】：
    - 多个答案用"|"符号分隔
    - 例如：答案1|答案2|答案3|答案4
    
    请直接返回答案，不要包含任何解释或其他文字。
                `,
            },
        ];
    }

    if (questionObj.type == '完形填空题') {
        // 先处理主文章内容
        messageArr = [
            {
                type: 'text',
                text: `这是一道完形填空/阅读理解题。\n\n【文章内容】：`,
            },
            ...contentArr,
            {
                type: 'text',
                text: `\n\n【题目列表】：\n`,
            },
        ];

        // 遍历每个小题目，展示题目和选项
        for (let i = 0; i < questionObj.sub_subjects.length; i++) {
            let subQuestion = questionObj.sub_subjects[i];

            messageArr.push({
                type: 'text',
                text: `\n第${i + 1}题（ID: ${subQuestion.id}）：`,
            });

            // 处理小题目内容中可能的图片
            let subContentArr = strToMessageArr(subQuestion.content || `第${i + 1}题`);
            messageArr.push(...subContentArr);

            // 添加选项
            if (subQuestion.options && subQuestion.options.length > 0) {
                messageArr.push({
                    type: 'text',
                    text: `\n选项：\n${subQuestion.options
                        .map((opt, idx) => `  ${String.fromCharCode(65 + idx)}. ${opt.content} (ID: ${opt.id})`)
                        .join('\n')}\n`,
                });
            }
        }

        // 添加答题要求
        messageArr.push({
            type: 'text',
            text: `
                
【答题要求】：
1. 仔细阅读文章，理解全文内容
2. 根据文章内容和题目要求，为每个小题选择最合适的答案
3. 注意题目可能考查：
   - 文章主旨、标题
   - 细节理解
   - 词汇/短语含义
   - 推理判断
   - 语法知识

【返回格式】：
请返回一个JSON数组，每个元素对应一道小题的答案：
[
  {"subject_id": 小题1的ID, "answer_option_ids": [正确选项的ID]},
  {"subject_id": 小题2的ID, "answer_option_ids": [正确选项的ID]},
  ...
]

示例：
[
  {"subject_id": ${questionObj.sub_subjects[0]?.id}, "answer_option_ids": [${questionObj.sub_subjects[0]?.options[0]?.id}]},
  {"subject_id": ${questionObj.sub_subjects[1]?.id}, "answer_option_ids": [${questionObj.sub_subjects[1]?.options[0]?.id}]}
]

请直接返回符合格式的JSON数组，不要包含其他内容。
            `,
        });
    }

    // 处理所有图片URL，转换为base64
    for (let item of messageArr) {
        if (item.type != 'image_url') continue;

        let url = item.image_url.url;

        // 如果已经是base64格式，跳过
        if (url.startsWith('data:image')) continue;

        // 判断url是否是相对路径
        if (url.startsWith('/')) {
            url = 'https://lms.ouchn.cn' + url;
        }

        let imageRes = await axiosIns({
            method: 'get',
            url: url,
            responseType: 'arraybuffer',
            headers: {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
                Accept: 'image/avif,image/webp,image/apng,image/svg+xml,image/*,*/*;q=0.8',
                'Accept-Language': 'zh-CN,zh;q=0.9',
                'Accept-Encoding': 'gzip, deflate',
            },
        });

        let base64Image = Buffer.from(imageRes, 'binary').toString('base64');

        // 构建 Base64 图片的 Data URL
        let imageUrl = `data:image/jpeg;base64,${base64Image}`;
        item.image_url.url = imageUrl;
    }

    return messageArr;
}

async function getAnswerFromAI(questionObj) {
    // console.dir(questionObj, { depth: 5 });

    // 修复函数调用错误
    let messageArr = await handleMessageArr(questionObj);

    let answerRes = await axiosIns({
        method: 'post',
        url: 'http://oaipro.mozhi0012.top/v1/chat/completions',
        headers: {
            'Content-Type': 'application/json',
            Authorization: `Bearer sk-JPTMbl93OqSRazlM50E9Ec94D75d48149dE46cF6Da908066`,
        },
        data: {
            model: 'gpt-4.1-2025-04-14',
            // model: 'o4-mini-2025-04-16',
            messages: [
                {
                    role: 'user',
                    content: messageArr,
                },
            ],
        },
    });
    answers = answerRes.choices[0].message.content;

    let colectAnswers;
    // 收集题目
    if (answers && answers != 'false') {
        if (questionObj.type == '分析题' || questionObj.type == '完形填空题') {
            let aiAnswersArray = JSON.parse(answers); // AI 返回的答案，格式：[{subject_id, answer_option_ids}, ...]
            let collectFormatAnswers = [];
            for (let aiAns of aiAnswersArray) {
                let subSubject = questionObj.sub_subjects.find(sub => sub.id == aiAns.subject_id);
                if (subSubject) {
                    let subjectContent = subSubject.content;
                    let chosenOption = subSubject.options.find(opt => opt.id == aiAns.answer_option_ids[0]);
                    if (chosenOption) {
                        let answerContent = chosenOption.content;
                        collectFormatAnswers.push({ subjectContent, answerContent });
                    }
                }
            }
            colectAnswers = JSON.stringify(collectFormatAnswers);
        }

        if (questionObj.type == '匹配题') {
            let aiAnswersArray = JSON.parse(answers); // AI 返回的答案，格式：[{subject_id, answer_option_ids}, ...]
            let collectFormatAnswers = [];
            for (let aiAns of aiAnswersArray) {
                let subSubject = questionObj.sub_subjects.find(sub => sub.id == aiAns.subject_id); // 左侧题目
                if (subSubject) {
                    let subjectContent = subSubject.content;
                    let chosenRawOption = questionObj.rawOptions.find(opt => opt.id == aiAns.answer_option_ids[0]); // 右侧选项
                    if (chosenRawOption) {
                        let answerContent = chosenRawOption.content;
                        collectFormatAnswers.push({ subjectContent, answerContent });
                    }
                }
            }
            colectAnswers = JSON.stringify(collectFormatAnswers);
        }

        if (questionObj.type == '填空题') {
            colectAnswers=answers
        }

        await collectQuestion(questionObj, colectAnswers, 'GK_getAnswerFromAI');
    }

    return answers;
}

module.exports = getAnswerFromAI;

if (false) {
    (async () => {
        let questionObj = {
            type: '匹配题',
            content:
                '（每小题2分，共16分）设有无穷多个信息，输入进程把信息逐个写入缓冲区，输出进程逐个从缓冲区中取出信息。设缓冲区是环形的，编号为0～n-1，in和out分别是输入进程和输出进程使用的指针，初值都是0。为使两类进程实行同步操作，设置三个信号量：两个计数信号量full和empty，一个互斥信号量mutex。full表示放有信息的缓冲区数，其初值为0。empty表示可供使用的缓冲区数，其初值为n。mutex互斥信号量，初值为1。 根据下面输入、输出进程的同步算法，填写相应的P、V操作。输入进程Input：while(TRUE) {__(1)__;_P(mutex)__;信息送往buffer(in);in=(in+1) mod n; /*以n为模*/__(2)__;__(3)__;} 输出进程Output：while(TRUE){__(4)__;__(5)__;从buffer(out)中取出信息;out=(out+1) mod n; /*以n为模*/_V(mutex)_;__(6)__;}',
            options: [],
            courseName: 'test',
            platform: '国家开放大学',
            rawOptions: [
                { id: 10044168041, content: 'P(full)' },
                { id: 10044168018, content: 'V(full)' },
                { id: 10044168062, content: 'P(mutex)' },
                { id: 10044167976, content: 'P(empty)' },
                { id: 10044168083, content: 'V(empty)' },
                { id: 10044167997, content: 'V(mutex)' },
            ],
            sub_subjects: [
                { id: 10016848937, content: '选项1' },
                { id: 10016848944, content: '选项2' },
                { id: 10016848952, content: '选项3' },
                { id: 10016848960, content: '选项4' },
                { id: 10016848967, content: '选项5' },
                { id: 10016848974, content: '选项6' },
            ],
        };

        let res = await getAnswerFromAI(questionObj);
        console.log(res);
    })();
}
