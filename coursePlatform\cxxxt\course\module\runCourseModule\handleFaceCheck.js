let axiosIns = require('../../../../utils/axiosIns.js');
let pageTools = require('../../../../utils/pageTools.js');
async function handleFaceCheck(mainPage) {
    let faceConfig = await mainPage.evaluate(() => {
        function passFace(window, fetch) {
            let axiosConfig;
            /*获取参数*/
            let uuid = window.document.getElementById('uuid').value;
            let qrcEnc = window.document.getElementById('qrcEnc').value;
            let courseId = window.document.getElementById('fccourseId').value;
            let classId = window.document.getElementById('fcclazzId').value;

            /*生成随机objectId*/
            let oidSample = 'abcdefttguhhniafunrivvalaffxafcekyu2345678';
            let oidSampleLen = oidSample.length;
            let oid = '';
            for (let i = 0; i < 32; i++) oid += oidSample.charAt(Math.floor(Math.random() * oidSampleLen));

            //判断版本，通过popElements，popVideoElements是否存在

            /*一版本人脸识别*/
            let popElements = window.document.getElementsByClassName('popDiv wid640 faceCollectQrPop popClass');
            let popVideoElements = window.document.getElementsByClassName('popDiv1 wid640 faceCollectQrPopVideo popClass');
            if (popElements.length > 0 || popVideoElements.length > 0) {
                let failTimeEs = window.document.getElementsByClassName('faceVideoCheckFailCount');
                let failCount = '0';
                if (failTimeEs.length > 0) {
                    failCount = failTimeEs[0].innerHTML;
                }

                axiosConfig = {
                    url: 'https://mooc1-api.chaoxing.com/qr/updateqrstatus',
                    method: 'post',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    data:
                        'clazzId=' +
                        classId +
                        '&courseId=' +
                        courseId +
                        '&uuid=' +
                        uuid +
                        '&objectId=' +
                        oid +
                        '&qrcEnc=' +
                        qrcEnc +
                        '&failCount=' +
                        failCount +
                        '&compareResult=0',
                };
                return axiosConfig;
            }

            /*二版本人脸识别*/
            let popElementss = window.document.getElementsByClassName('popDiv wid640');
            if (popElementss.length < 1) {
                return;
            }
            let faceElement = window.document.getElementById('fcqrimg');
            if (faceElement == null) {
                return;
            }
            let knowledgeId = '0';
            let knowledgeIdE = window.document.getElementById('chapterIdid');
            if (knowledgeIdE !== null) {
                knowledgeId = knowledgeIdE.value;
            }

            axiosConifg = {
                url: 'https://mooc1-api.chaoxing.com/knowledge/uploadInfo',
                method: 'post',
                headers: {
                    'Content-Type': 'application/x-www-form-urlencoded',
                },
                data:
                    'clazzId=' + classId + '&courseId=' + courseId + '&knowledgeId=' + knowledgeId + '&uuid=' + uuid + '&qrcEnc=' + qrcEnc + '&objectId=' + oid,
            };
            return axiosConfig;
        }
        return passFace(window, fetch);
    });

    let cookieStr = await pageTools.getPageCookies(mainPage);
    faceConfig.headers.cookie = cookieStr;
    faceConfig.headers['User-Agent'] =
        'Mozilla/5.0 (Linux; Android 11; MI 9 Build/RKQ1.200826.002; wv) AppleWebKit/537.36 (KHTML, like Gecko) Version/4.0 Chrome/109.0.5414.85 Mobile Safari/537.36 (schild:70f721684135c46d92e84815753cc043) (device:MI 9) Language/zh_CN com.chaoxing.mobile/ChaoXingStudy_3_6.3.2_android_phone_1060_235 (@Kalimdor)_693480ab70044c5e8137c1de8f594768';
    let faceRes = await axiosIns(faceConfig);
    return faceRes;
}

module.exports = handleFaceCheck;