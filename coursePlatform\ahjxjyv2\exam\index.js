// ========= 私有模块 =========
let pageTools = require('../../utils/pageTools.js');
let getMainPage = require('../../utils/getMainPage.js');

let signinModule = require('../course/module/signinModule.js');
let getExamListModule = require('./module/getExamListModule.js');
let runExamModule = require('./module/runExamModule.js');
let endModule = require('./module/endModule.js');

// ========= 任务开始 =========
async function ahjxjyExam(taskObj, taskOptions) {
    // 开启窗口
    // taskOptions.isHeadless = false;

    // 初始化日志
    let infoLogger = pageTools.getExamInfoLogger(taskObj.id, false);
    // 初始化全局对象
    let globalStore = { taskObj };
    // 初始化页面
    let { mainPage, browser } = await getMainPage(taskOptions);

    // 任务开始
    try {
        // 一、登录 schoolCode,cookieStr
        await signinModule(infoLogger, mainPage, globalStore);

        // 二、获取考试列表
        let examList = await getExamListModule(infoLogger, mainPage, globalStore);
        if (examList.length == 0) {
            await infoLogger('课程数量为0', 'red');
            await browser.close();
            browser = null;
            return {
                finalResult: [{ courseName: '课程数量为0', progress: 0 }],
                warningMessage: '',
            };
        }

        // 三、开始考试
        if (!taskObj.others.includes('跳过考试')) {
            await runExamModule(infoLogger, mainPage, examList, globalStore);
        }

        // 四、收尾工作
        let result = await endModule(infoLogger, mainPage, globalStore);
        await browser.close();
        browser = null;
        return result;
    } catch (error) {
        await browser.close();
        browser = null;
        throw error;
    } finally {
        if (browser) {
            await browser.close();
            browser = null;
        }
    }
}
module.exports = ahjxjyExam;
