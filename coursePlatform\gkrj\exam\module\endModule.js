let pageTools = require('../../../utils/pageTools.js');
let api = require('../../utils/api.js');

async function endModule(mainPage,infoLogger,taskObj){
    let cookieStr=await pageTools.getPageCookies(mainPage)
    let courseListRes=await api.getScore(cookieStr,taskObj.username)
    let courseList=courseListRes.data.records
    let finalResult=[]
    for (let courseObj of courseList) {
        finalResult.push({
            courseName: courseObj.subject,
            progress: courseObj.score,
        });
    }
    await infoLogger('全部完成','green')
    return finalResult;
}

module.exports=endModule