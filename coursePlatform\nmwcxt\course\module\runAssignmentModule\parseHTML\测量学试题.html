<table id="tblDataList" onselectstart="return false" width="100%" cellpadding="3" _default_attr="1" cellspacing="1"><tbody><tr id="tr_tblDataList_0" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_0');}">
    <td scoreid="437278583276175361" id="_DataListTD_tblDataList_0"><table width="100%" border="0" id="tblItem_419215406823112753" islabel="1" keylist="" style="">
    <tbody><tr><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<b>单选题<\/b><SPAN IsScoreRemark=\"1\"><\/SPAN>"));</script><b>单选题</b><span isscoreremark="1">&nbsp;&nbsp;<font color="gray">(第1-10题每题5分)</font></span>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_1" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_1');}">
    <td scoreid="437278583276175362" id="_DataListTD_tblDataList_1"><table width="100%" border="0" id="tblItem_419215406823112741" islabel="0" keylist="lemonysoft_item_key_1120252_6237" style="">
    <tbody><tr><td width="30" valign="top">1.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">在两点的直线上或其延长线上标定出一些点的工作，称为（　 　 ）。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120252_6237_0 name=\"lemonysoft_item_key_1120252_6237\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120252_6237_0>定向<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120252_6237_1 name=\"lemonysoft_item_key_1120252_6237\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120252_6237_1>定线<\/label><TR><TD><input type=radio value=\"C\" id=lemonysoft_item_key_1120252_6237_2 name=\"lemonysoft_item_key_1120252_6237\" ><TD>(C)<TD><label for=lemonysoft_item_key_1120252_6237_2>定段<\/label><TR><TD><input type=radio value=\"D\" id=lemonysoft_item_key_1120252_6237_3 name=\"lemonysoft_item_key_1120252_6237\" ><TD>(D)<TD><label for=lemonysoft_item_key_1120252_6237_3>定标<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">在两点的直线上或其延长线上标定出一些点的工作，称为（　 　 ）。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120252_6237_0" name="lemonysoft_item_key_1120252_6237"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120252_6237_0">定向</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120252_6237_1" name="lemonysoft_item_key_1120252_6237"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120252_6237_1">定线</label></td></tr><tr><td><input type="radio" value="C" id="lemonysoft_item_key_1120252_6237_2" name="lemonysoft_item_key_1120252_6237"></td><td>(C)</td><td><label for="lemonysoft_item_key_1120252_6237_2">定段</label></td></tr><tr><td><input type="radio" value="D" id="lemonysoft_item_key_1120252_6237_3" name="lemonysoft_item_key_1120252_6237"></td><td>(D)</td><td><label for="lemonysoft_item_key_1120252_6237_3">定标</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120252_6237","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_2" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_2');}">
    <td scoreid="437278583276175363" id="_DataListTD_tblDataList_2"><table width="100%" border="0" id="tblItem_419215406823112742" islabel="0" keylist="lemonysoft_item_key_1120251_8496" style="">
    <tbody><tr><td width="30" valign="top">2.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">在测量学中，距离测量的常用方法有钢尺量距、电磁波测距和（　 　）测距。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120251_8496_0 name=\"lemonysoft_item_key_1120251_8496\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120251_8496_0>视距法<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120251_8496_1 name=\"lemonysoft_item_key_1120251_8496\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120251_8496_1>经纬仪法<\/label><TR><TD><input type=radio value=\"C\" id=lemonysoft_item_key_1120251_8496_2 name=\"lemonysoft_item_key_1120251_8496\" ><TD>(C)<TD><label for=lemonysoft_item_key_1120251_8496_2>水准仪法<\/label><TR><TD><input type=radio value=\"D\" id=lemonysoft_item_key_1120251_8496_3 name=\"lemonysoft_item_key_1120251_8496\" ><TD>(D)<TD><label for=lemonysoft_item_key_1120251_8496_3>罗盘仪法<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">在测量学中，距离测量的常用方法有钢尺量距、电磁波测距和（　 　）测距。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120251_8496_0" name="lemonysoft_item_key_1120251_8496"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120251_8496_0">视距法</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120251_8496_1" name="lemonysoft_item_key_1120251_8496"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120251_8496_1">经纬仪法</label></td></tr><tr><td><input type="radio" value="C" id="lemonysoft_item_key_1120251_8496_2" name="lemonysoft_item_key_1120251_8496"></td><td>(C)</td><td><label for="lemonysoft_item_key_1120251_8496_2">水准仪法</label></td></tr><tr><td><input type="radio" value="D" id="lemonysoft_item_key_1120251_8496_3" name="lemonysoft_item_key_1120251_8496"></td><td>(D)</td><td><label for="lemonysoft_item_key_1120251_8496_3">罗盘仪法</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120251_8496","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_3" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_3');}">
    <td scoreid="437278583276175364" id="_DataListTD_tblDataList_3"><table width="100%" border="0" id="tblItem_419215406823112733" islabel="0" keylist="lemonysoft_item_key_1120335_30379" style="">
    <tbody><tr><td width="30" valign="top">3.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">能测定直线磁方位角的仪器，是（　 　 　）<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120335_30379_0 name=\"lemonysoft_item_key_1120335_30379\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120335_30379_0>经纬仪<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120335_30379_1 name=\"lemonysoft_item_key_1120335_30379\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120335_30379_1>全站仪<\/label><TR><TD><input type=radio value=\"C\" id=lemonysoft_item_key_1120335_30379_2 name=\"lemonysoft_item_key_1120335_30379\" ><TD>(C)<TD><label for=lemonysoft_item_key_1120335_30379_2>陀螺仪<\/label><TR><TD><input type=radio value=\"D\" id=lemonysoft_item_key_1120335_30379_3 name=\"lemonysoft_item_key_1120335_30379\" ><TD>(D)<TD><label for=lemonysoft_item_key_1120335_30379_3>罗盘仪<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">能测定直线磁方位角的仪器，是（　 　 　）</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120335_30379_0" name="lemonysoft_item_key_1120335_30379"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120335_30379_0">经纬仪</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120335_30379_1" name="lemonysoft_item_key_1120335_30379"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120335_30379_1">全站仪</label></td></tr><tr><td><input type="radio" value="C" id="lemonysoft_item_key_1120335_30379_2" name="lemonysoft_item_key_1120335_30379"></td><td>(C)</td><td><label for="lemonysoft_item_key_1120335_30379_2">陀螺仪</label></td></tr><tr><td><input type="radio" value="D" id="lemonysoft_item_key_1120335_30379_3" name="lemonysoft_item_key_1120335_30379"></td><td>(D)</td><td><label for="lemonysoft_item_key_1120335_30379_3">罗盘仪</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120335_30379","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_4" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_4');}">
    <td scoreid="437278583276175365" id="_DataListTD_tblDataList_4"><table width="100%" border="0" id="tblItem_419215406823112734" islabel="0" keylist="lemonysoft_item_key_1120259_8844" style="">
    <tbody><tr><td width="30" valign="top">4.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">坐标纵轴方向，是指（　 　 　）方向。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120259_8844_0 name=\"lemonysoft_item_key_1120259_8844\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120259_8844_0>真子午线方向<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120259_8844_1 name=\"lemonysoft_item_key_1120259_8844\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120259_8844_1>磁子午线方向<\/label><TR><TD><input type=radio value=\"C\" id=lemonysoft_item_key_1120259_8844_2 name=\"lemonysoft_item_key_1120259_8844\" ><TD>(C)<TD><label for=lemonysoft_item_key_1120259_8844_2>中央子午线方向<\/label><TR><TD><input type=radio value=\"D\" id=lemonysoft_item_key_1120259_8844_3 name=\"lemonysoft_item_key_1120259_8844\" ><TD>(D)<TD><label for=lemonysoft_item_key_1120259_8844_3>铅垂方向<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">坐标纵轴方向，是指（　 　 　）方向。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120259_8844_0" name="lemonysoft_item_key_1120259_8844"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120259_8844_0">真子午线方向</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120259_8844_1" name="lemonysoft_item_key_1120259_8844"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120259_8844_1">磁子午线方向</label></td></tr><tr><td><input type="radio" value="C" id="lemonysoft_item_key_1120259_8844_2" name="lemonysoft_item_key_1120259_8844"></td><td>(C)</td><td><label for="lemonysoft_item_key_1120259_8844_2">中央子午线方向</label></td></tr><tr><td><input type="radio" value="D" id="lemonysoft_item_key_1120259_8844_3" name="lemonysoft_item_key_1120259_8844"></td><td>(D)</td><td><label for="lemonysoft_item_key_1120259_8844_3">铅垂方向</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120259_8844","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_5" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_5');}">
    <td scoreid="437278583276175366" id="_DataListTD_tblDataList_5"><table width="100%" border="0" id="tblItem_419215406823112735" islabel="0" keylist="lemonysoft_item_key_1120258_36146" style="">
    <tbody><tr><td width="30" valign="top">5.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">过地面上某点的真子午线方向与磁子午线方向常不重合，两者之间的夹角，称为（　 　 　）。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120258_36146_0 name=\"lemonysoft_item_key_1120258_36146\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120258_36146_0>真磁角<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120258_36146_1 name=\"lemonysoft_item_key_1120258_36146\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120258_36146_1>真偏角<\/label><TR><TD><input type=radio value=\"C\" id=lemonysoft_item_key_1120258_36146_2 name=\"lemonysoft_item_key_1120258_36146\" ><TD>(C)<TD><label for=lemonysoft_item_key_1120258_36146_2>磁偏角<\/label><TR><TD><input type=radio value=\"D\" id=lemonysoft_item_key_1120258_36146_3 name=\"lemonysoft_item_key_1120258_36146\" ><TD>(D)<TD><label for=lemonysoft_item_key_1120258_36146_3>子午线偏角<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">过地面上某点的真子午线方向与磁子午线方向常不重合，两者之间的夹角，称为（　 　 　）。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120258_36146_0" name="lemonysoft_item_key_1120258_36146"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120258_36146_0">真磁角</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120258_36146_1" name="lemonysoft_item_key_1120258_36146"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120258_36146_1">真偏角</label></td></tr><tr><td><input type="radio" value="C" id="lemonysoft_item_key_1120258_36146_2" name="lemonysoft_item_key_1120258_36146"></td><td>(C)</td><td><label for="lemonysoft_item_key_1120258_36146_2">磁偏角</label></td></tr><tr><td><input type="radio" value="D" id="lemonysoft_item_key_1120258_36146_3" name="lemonysoft_item_key_1120258_36146"></td><td>(D)</td><td><label for="lemonysoft_item_key_1120258_36146_3">子午线偏角</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120258_36146","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_6" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_6');}">
    <td scoreid="437278583276175367" id="_DataListTD_tblDataList_6"><table width="100%" border="0" id="tblItem_419215406823112736" islabel="0" keylist="lemonysoft_item_key_1120257_12951" style="">
    <tbody><tr><td width="30" valign="top">6.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">精密钢尺量距，一般要进行的三项改正是尺长改正、（　 　 ）改正和倾斜改正。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120257_12951_0 name=\"lemonysoft_item_key_1120257_12951\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120257_12951_0>比例<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120257_12951_1 name=\"lemonysoft_item_key_1120257_12951\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120257_12951_1>高差<\/label><TR><TD><input type=radio value=\"C\" id=lemonysoft_item_key_1120257_12951_2 name=\"lemonysoft_item_key_1120257_12951\" ><TD>(C)<TD><label for=lemonysoft_item_key_1120257_12951_2>气压<\/label><TR><TD><input type=radio value=\"D\" id=lemonysoft_item_key_1120257_12951_3 name=\"lemonysoft_item_key_1120257_12951\" ><TD>(D)<TD><label for=lemonysoft_item_key_1120257_12951_3>温度<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">精密钢尺量距，一般要进行的三项改正是尺长改正、（　 　 ）改正和倾斜改正。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120257_12951_0" name="lemonysoft_item_key_1120257_12951"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120257_12951_0">比例</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120257_12951_1" name="lemonysoft_item_key_1120257_12951"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120257_12951_1">高差</label></td></tr><tr><td><input type="radio" value="C" id="lemonysoft_item_key_1120257_12951_2" name="lemonysoft_item_key_1120257_12951"></td><td>(C)</td><td><label for="lemonysoft_item_key_1120257_12951_2">气压</label></td></tr><tr><td><input type="radio" value="D" id="lemonysoft_item_key_1120257_12951_3" name="lemonysoft_item_key_1120257_12951"></td><td>(D)</td><td><label for="lemonysoft_item_key_1120257_12951_3">温度</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120257_12951","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_7" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_7');}">
    <td scoreid="437278583276175368" id="_DataListTD_tblDataList_7"><table width="100%" border="0" id="tblItem_419215406823112737" islabel="0" keylist="lemonysoft_item_key_1120256_51196" style="">
    <tbody><tr><td width="30" valign="top">7.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">距离丈量的结果是求得两点间的（　 　 ）。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120256_51196_0 name=\"lemonysoft_item_key_1120256_51196\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120256_51196_0>线距离<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120256_51196_1 name=\"lemonysoft_item_key_1120256_51196\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120256_51196_1>水平距离<\/label><TR><TD><input type=radio value=\"C\" id=lemonysoft_item_key_1120256_51196_2 name=\"lemonysoft_item_key_1120256_51196\" ><TD>(C)<TD><label for=lemonysoft_item_key_1120256_51196_2>折线距离<\/label><TR><TD><input type=radio value=\"D\" id=lemonysoft_item_key_1120256_51196_3 name=\"lemonysoft_item_key_1120256_51196\" ><TD>(D)<TD><label for=lemonysoft_item_key_1120256_51196_3>坐标差值<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">距离丈量的结果是求得两点间的（　 　 ）。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120256_51196_0" name="lemonysoft_item_key_1120256_51196"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120256_51196_0">线距离</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120256_51196_1" name="lemonysoft_item_key_1120256_51196"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120256_51196_1">水平距离</label></td></tr><tr><td><input type="radio" value="C" id="lemonysoft_item_key_1120256_51196_2" name="lemonysoft_item_key_1120256_51196"></td><td>(C)</td><td><label for="lemonysoft_item_key_1120256_51196_2">折线距离</label></td></tr><tr><td><input type="radio" value="D" id="lemonysoft_item_key_1120256_51196_3" name="lemonysoft_item_key_1120256_51196"></td><td>(D)</td><td><label for="lemonysoft_item_key_1120256_51196_3">坐标差值</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120256_51196","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_8" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_8');}">
    <td scoreid="437278583276175369" id="_DataListTD_tblDataList_8"><table width="100%" border="0" id="tblItem_419215406823112738" islabel="0" keylist="lemonysoft_item_key_1120255_59905" style="">
    <tbody><tr><td width="30" valign="top">8.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">坐标方位角是以（　 　 ）为标准方向，顺时针转到测线的夹角。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120255_59905_0 name=\"lemonysoft_item_key_1120255_59905\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120255_59905_0>真子午线方向<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120255_59905_1 name=\"lemonysoft_item_key_1120255_59905\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120255_59905_1>磁子午线方向<\/label><TR><TD><input type=radio value=\"C\" id=lemonysoft_item_key_1120255_59905_2 name=\"lemonysoft_item_key_1120255_59905\" ><TD>(C)<TD><label for=lemonysoft_item_key_1120255_59905_2>假定纵轴方向<\/label><TR><TD><input type=radio value=\"D\" id=lemonysoft_item_key_1120255_59905_3 name=\"lemonysoft_item_key_1120255_59905\" ><TD>(D)<TD><label for=lemonysoft_item_key_1120255_59905_3>坐标纵轴方向<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">坐标方位角是以（　 　 ）为标准方向，顺时针转到测线的夹角。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120255_59905_0" name="lemonysoft_item_key_1120255_59905"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120255_59905_0">真子午线方向</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120255_59905_1" name="lemonysoft_item_key_1120255_59905"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120255_59905_1">磁子午线方向</label></td></tr><tr><td><input type="radio" value="C" id="lemonysoft_item_key_1120255_59905_2" name="lemonysoft_item_key_1120255_59905"></td><td>(C)</td><td><label for="lemonysoft_item_key_1120255_59905_2">假定纵轴方向</label></td></tr><tr><td><input type="radio" value="D" id="lemonysoft_item_key_1120255_59905_3" name="lemonysoft_item_key_1120255_59905"></td><td>(D)</td><td><label for="lemonysoft_item_key_1120255_59905_3">坐标纵轴方向</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120255_59905","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_9" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_9');}">
    <td scoreid="437278583276175370" id="_DataListTD_tblDataList_9"><table width="100%" border="0" id="tblItem_419215406823112739" islabel="0" keylist="lemonysoft_item_key_1120254_54232" style="">
    <tbody><tr><td width="30" valign="top">9.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">在距离丈量中衡量精度的方法是用（　 　 ）。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120254_54232_0 name=\"lemonysoft_item_key_1120254_54232\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120254_54232_0>往返较差<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120254_54232_1 name=\"lemonysoft_item_key_1120254_54232\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120254_54232_1>相对误差；<\/label><TR><TD><input type=radio value=\"C\" id=lemonysoft_item_key_1120254_54232_2 name=\"lemonysoft_item_key_1120254_54232\" ><TD>(C)<TD><label for=lemonysoft_item_key_1120254_54232_2>闭合差<\/label><TR><TD><input type=radio value=\"D\" id=lemonysoft_item_key_1120254_54232_3 name=\"lemonysoft_item_key_1120254_54232\" ><TD>(D)<TD><label for=lemonysoft_item_key_1120254_54232_3>绝对误差<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">在距离丈量中衡量精度的方法是用（　 　 ）。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120254_54232_0" name="lemonysoft_item_key_1120254_54232"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120254_54232_0">往返较差</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120254_54232_1" name="lemonysoft_item_key_1120254_54232"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120254_54232_1">相对误差；</label></td></tr><tr><td><input type="radio" value="C" id="lemonysoft_item_key_1120254_54232_2" name="lemonysoft_item_key_1120254_54232"></td><td>(C)</td><td><label for="lemonysoft_item_key_1120254_54232_2">闭合差</label></td></tr><tr><td><input type="radio" value="D" id="lemonysoft_item_key_1120254_54232_3" name="lemonysoft_item_key_1120254_54232"></td><td>(D)</td><td><label for="lemonysoft_item_key_1120254_54232_3">绝对误差</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120254_54232","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_10" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_10');}">
    <td scoreid="437278583276175371" id="_DataListTD_tblDataList_10"><table width="100%" border="0" id="tblItem_419215406823112740" islabel="0" keylist="lemonysoft_item_key_1120253_37352" style="">
    <tbody><tr><td width="30" valign="top">10.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">某段距离的平均值为100mm，其往返较差为+20mm，则相对误差为（　 　 　）。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120253_37352_0 name=\"lemonysoft_item_key_1120253_37352\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120253_37352_0>0.02\/100<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120253_37352_1 name=\"lemonysoft_item_key_1120253_37352\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120253_37352_1>0.002<\/label><TR><TD><input type=radio value=\"C\" id=lemonysoft_item_key_1120253_37352_2 name=\"lemonysoft_item_key_1120253_37352\" ><TD>(C)<TD><label for=lemonysoft_item_key_1120253_37352_2>1\/5000<\/label><TR><TD><input type=radio value=\"D\" id=lemonysoft_item_key_1120253_37352_3 name=\"lemonysoft_item_key_1120253_37352\" ><TD>(D)<TD><label for=lemonysoft_item_key_1120253_37352_3>1\/10000<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">某段距离的平均值为100mm，其往返较差为+20mm，则相对误差为（　 　 　）。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120253_37352_0" name="lemonysoft_item_key_1120253_37352"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120253_37352_0">0.02/100</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120253_37352_1" name="lemonysoft_item_key_1120253_37352"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120253_37352_1">0.002</label></td></tr><tr><td><input type="radio" value="C" id="lemonysoft_item_key_1120253_37352_2" name="lemonysoft_item_key_1120253_37352"></td><td>(C)</td><td><label for="lemonysoft_item_key_1120253_37352_2">1/5000</label></td></tr><tr><td><input type="radio" value="D" id="lemonysoft_item_key_1120253_37352_3" name="lemonysoft_item_key_1120253_37352"></td><td>(D)</td><td><label for="lemonysoft_item_key_1120253_37352_3">1/10000</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120253_37352","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_11" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_11');}">
    <td scoreid="437278583276175372" id="_DataListTD_tblDataList_11"><table width="100%" border="0" id="tblItem_419215406823112754" islabel="1" keylist="" style="">
    <script language="javascript">var oSpan=document.getElementsByTagName("SPAN");for(var i=oSpan.length-1;i>=0;i--){if(oSpan[i].getAttribute("IsScoreRemark")=="1"){oSpan[i].innerHTML="&nbsp;&nbsp;<font color=gray>(第1-10题每题5分)</font>";break;}}</script>
    <tbody><tr><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<b>判断题<\/b><SPAN IsScoreRemark=\"1\"><\/SPAN>"));</script><b>判断题</b><span isscoreremark="1">&nbsp;&nbsp;<font color="gray">(第1-10题每题5分)</font></span>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_12" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_12');}">
    <td scoreid="437278583276175373" id="_DataListTD_tblDataList_12"><table width="100%" border="0" id="tblItem_419215406823112743" islabel="0" keylist="lemonysoft_item_key_1120250_49649" style="">
    <tbody><tr><td width="30" valign="top">1.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">施工总平面图就是设计总平面图。<div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120250_49649_0 name=\"lemonysoft_item_key_1120250_49649\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120250_49649_0>对<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120250_49649_1 name=\"lemonysoft_item_key_1120250_49649\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120250_49649_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">施工总平面图就是设计总平面图。<div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120250_49649_0" name="lemonysoft_item_key_1120250_49649"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120250_49649_0">对</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120250_49649_1" name="lemonysoft_item_key_1120250_49649"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120250_49649_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120250_49649","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_13" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_13');}">
    <td scoreid="437278583276175374" id="_DataListTD_tblDataList_13"><table width="100%" border="0" id="tblItem_419215406823112744" islabel="0" keylist="lemonysoft_item_key_1120248_52392" style="">
    <tbody><tr><td width="30" valign="top">2.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">经纬仪的水平度盘刻划不均匀误差，可以通过盘左、盘右观测取平均值的方法消除。<div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120248_52392_0 name=\"lemonysoft_item_key_1120248_52392\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120248_52392_0>对<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120248_52392_1 name=\"lemonysoft_item_key_1120248_52392\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120248_52392_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">经纬仪的水平度盘刻划不均匀误差，可以通过盘左、盘右观测取平均值的方法消除。<div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120248_52392_0" name="lemonysoft_item_key_1120248_52392"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120248_52392_0">对</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120248_52392_1" name="lemonysoft_item_key_1120248_52392"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120248_52392_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120248_52392","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_14" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_14');}">
    <td scoreid="437278583276175375" id="_DataListTD_tblDataList_14"><table width="100%" border="0" id="tblItem_419215406823112745" islabel="0" keylist="lemonysoft_item_key_1120247_33912" style="">
    <tbody><tr><td width="30" valign="top">3.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">使用光学对中器或垂球进行对中时，均要求经纬仪竖轴必需竖直。<div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120247_33912_0 name=\"lemonysoft_item_key_1120247_33912\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120247_33912_0>对<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120247_33912_1 name=\"lemonysoft_item_key_1120247_33912\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120247_33912_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">使用光学对中器或垂球进行对中时，均要求经纬仪竖轴必需竖直。<div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120247_33912_0" name="lemonysoft_item_key_1120247_33912"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120247_33912_0">对</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120247_33912_1" name="lemonysoft_item_key_1120247_33912"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120247_33912_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120247_33912","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_15" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_15');}">
    <td scoreid="437278583276175376" id="_DataListTD_tblDataList_15"><table width="100%" border="0" id="tblItem_419215406823112746" islabel="0" keylist="lemonysoft_item_key_1120246_857" style="">
    <tbody><tr><td width="30" valign="top">4.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">采用方向观测法进行水平角观测，当方向数多于三个时，每半测回均应为<\/div><div style=\"line-height:20px;font-size:10pt\">零。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120246_857_0 name=\"lemonysoft_item_key_1120246_857\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120246_857_0>对<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120246_857_1 name=\"lemonysoft_item_key_1120246_857\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120246_857_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">采用方向观测法进行水平角观测，当方向数多于三个时，每半测回均应为</div><div style="line-height:20px;font-size:10pt">零。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120246_857_0" name="lemonysoft_item_key_1120246_857"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120246_857_0">对</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120246_857_1" name="lemonysoft_item_key_1120246_857"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120246_857_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120246_857","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_16" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_16');}">
    <td scoreid="437278583276175377" id="_DataListTD_tblDataList_16"><table width="100%" border="0" id="tblItem_419215406823112747" islabel="0" keylist="lemonysoft_item_key_1120245_35153" style="">
    <tbody><tr><td width="30" valign="top">5.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">地面上一点到两目标的方向线间所夹的水平角，就是过该两方向线所作两竖直面间的两面<\/div><div style=\"line-height:20px;font-size:10pt\">角。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120245_35153_0 name=\"lemonysoft_item_key_1120245_35153\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120245_35153_0>对<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120245_35153_1 name=\"lemonysoft_item_key_1120245_35153\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120245_35153_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">地面上一点到两目标的方向线间所夹的水平角，就是过该两方向线所作两竖直面间的两面</div><div style="line-height:20px;font-size:10pt">角。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120245_35153_0" name="lemonysoft_item_key_1120245_35153"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120245_35153_0">对</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120245_35153_1" name="lemonysoft_item_key_1120245_35153"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120245_35153_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120245_35153","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_17" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_17');}">
    <td scoreid="437278583276175378" id="_DataListTD_tblDataList_17"><table width="100%" border="0" id="tblItem_419215406823112748" islabel="0" keylist="lemonysoft_item_key_1120244_28692" style="">
    <tbody><tr><td width="30" valign="top">6.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">竖直角观测中，竖盘指标差对同一目标盘左、盘右两半测回竖直角影响的绝对值相等而符<\/div><div style=\"line-height:20px;font-size:10pt\">号相反。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120244_28692_0 name=\"lemonysoft_item_key_1120244_28692\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120244_28692_0>对<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120244_28692_1 name=\"lemonysoft_item_key_1120244_28692\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120244_28692_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">竖直角观测中，竖盘指标差对同一目标盘左、盘右两半测回竖直角影响的绝对值相等而符</div><div style="line-height:20px;font-size:10pt">号相反。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120244_28692_0" name="lemonysoft_item_key_1120244_28692"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120244_28692_0">对</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120244_28692_1" name="lemonysoft_item_key_1120244_28692"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120244_28692_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120244_28692","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_18" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_18');}">
    <td scoreid="437278583276175379" id="_DataListTD_tblDataList_18"><table width="100%" border="0" id="tblItem_419215406823112749" islabel="0" keylist="lemonysoft_item_key_1120243_61113" style="">
    <tbody><tr><td width="30" valign="top">7.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">用经纬仪瞄准同一竖面内不同高度的两个点，在竖盘上的读数差就是竖直角 。<div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120243_61113_0 name=\"lemonysoft_item_key_1120243_61113\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120243_61113_0>对<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120243_61113_1 name=\"lemonysoft_item_key_1120243_61113\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120243_61113_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">用经纬仪瞄准同一竖面内不同高度的两个点，在竖盘上的读数差就是竖直角 。<div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120243_61113_0" name="lemonysoft_item_key_1120243_61113"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120243_61113_0">对</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120243_61113_1" name="lemonysoft_item_key_1120243_61113"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120243_61113_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120243_61113","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_19" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_19');}">
    <td scoreid="437278583276175380" id="_DataListTD_tblDataList_19"><table width="100%" border="0" id="tblItem_419215406823112750" islabel="0" keylist="lemonysoft_item_key_1120242_5281" style="">
    <tbody><tr><td width="30" valign="top">8.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">目标偏心误差对水平角的影响与测站至目标距离有关，距离愈短，影响愈大，且与观测的<\/div><div style=\"line-height:20px;font-size:10pt\">水平角度大小有关。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120242_5281_0 name=\"lemonysoft_item_key_1120242_5281\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120242_5281_0>对<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120242_5281_1 name=\"lemonysoft_item_key_1120242_5281\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120242_5281_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">目标偏心误差对水平角的影响与测站至目标距离有关，距离愈短，影响愈大，且与观测的</div><div style="line-height:20px;font-size:10pt">水平角度大小有关。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120242_5281_0" name="lemonysoft_item_key_1120242_5281"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120242_5281_0">对</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120242_5281_1" name="lemonysoft_item_key_1120242_5281"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120242_5281_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120242_5281","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_20" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_20');}">
    <td scoreid="437278583276175381" id="_DataListTD_tblDataList_20"><table width="100%" border="0" id="tblItem_419215406823112751" islabel="0" keylist="lemonysoft_item_key_1120241_33802" style="">
    <tbody><tr><td width="30" valign="top">9.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">经纬仪对中误差对水平角的影响与测站至目标的距离有关，距离愈大，影响愈大，但与水<\/div><div style=\"line-height:20px;font-size:10pt\">平角的大小无关。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120241_33802_0 name=\"lemonysoft_item_key_1120241_33802\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120241_33802_0>对<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120241_33802_1 name=\"lemonysoft_item_key_1120241_33802\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120241_33802_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">经纬仪对中误差对水平角的影响与测站至目标的距离有关，距离愈大，影响愈大，但与水</div><div style="line-height:20px;font-size:10pt">平角的大小无关。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120241_33802_0" name="lemonysoft_item_key_1120241_33802"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120241_33802_0">对</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120241_33802_1" name="lemonysoft_item_key_1120241_33802"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120241_33802_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120241_33802","","3");</script>
    </td></tr></tbody></table>
    
    </td></tr><tr id="tr_tblDataList_21" onclick="if(typeof(webGrid)=='object'&amp;&amp;webGrid.onRowClick!=null){webGrid.onRowClick('tr_tblDataList_21');}">
    <td scoreid="437278583276175382" id="_DataListTD_tblDataList_21"><table width="100%" border="0" id="tblItem_419215406823112752" islabel="0" keylist="lemonysoft_item_key_1120240_13007" style="">
    <tbody><tr><td width="30" valign="top">10.</td><td><script language="javascript">document.write($wapper.api.getDisplayFormat("<div style=\"line-height:20px;font-size:10pt\">当经纬仪各轴间具有正确的几何关系时，观测同一方向内不同高度目标时，水平度盘的读<\/div><div style=\"line-height:20px;font-size:10pt\">数是一样的。<\/div><div style=\"line-height:20px;font-size:10pt\"><div><table IsItemOption=1 OptionType=\"radio\"><TR><TD><input type=radio value=\"A\" id=lemonysoft_item_key_1120240_13007_0 name=\"lemonysoft_item_key_1120240_13007\" ><TD>(A)<TD><label for=lemonysoft_item_key_1120240_13007_0>对<\/label><TR><TD><input type=radio value=\"B\" id=lemonysoft_item_key_1120240_13007_1 name=\"lemonysoft_item_key_1120240_13007\" ><TD>(B)<TD><label for=lemonysoft_item_key_1120240_13007_1>错<\/label><\/table><\/div><\/div>"));</script><div style="line-height:20px;font-size:10pt">当经纬仪各轴间具有正确的几何关系时，观测同一方向内不同高度目标时，水平度盘的读</div><div style="line-height:20px;font-size:10pt">数是一样的。</div><div style="line-height:20px;font-size:10pt"><div><table isitemoption="1" optiontype="radio"><tbody><tr><td><input type="radio" value="A" id="lemonysoft_item_key_1120240_13007_0" name="lemonysoft_item_key_1120240_13007"></td><td>(A)</td><td><label for="lemonysoft_item_key_1120240_13007_0">对</label></td></tr><tr><td><input type="radio" value="B" id="lemonysoft_item_key_1120240_13007_1" name="lemonysoft_item_key_1120240_13007"></td><td>(B)</td><td><label for="lemonysoft_item_key_1120240_13007_1">错</label></td></tr></tbody></table></div></div>
    <script language="javascript">showAnswer("lemonysoft_item_key_1120240_13007","","3");</script>
    </td></tr></tbody></table>
    
    <script language="javascript">var oSpan=document.getElementsByTagName("SPAN");for(var i=oSpan.length-1;i>=0;i--){if(oSpan[i].getAttribute("IsScoreRemark")=="1"){oSpan[i].innerHTML="&nbsp;&nbsp;<font color=gray>(第1-10题每题5分)</font>";break;}}</script></td></tr></tbody></table>