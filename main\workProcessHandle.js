const { v4 } = require('uuid');
let path = require('path');
let pcId = require('../config/pcId');

// ========= 私有模块 =========
let Model = require('../config/sequelize.config.js');

// ========= 内置模块 =========
let workProcessHandle = async taskObj => {
    //浏览器配置
    let taskOptions = {
        isVideoEnabled: true, //视频
        isAssignmentEnabled: true, //作业
        isHeadless: 'new', //浏览器模式 false ,'new'
    };

    //上课
    if (taskObj.type == 'course') {
        //确定平台方法
        let courseFn;
        switch (taskObj.platform) {
            // 柠檬文采学堂
            case 'nmwcxt':
                courseFn = require('../coursePlatform/nmwcxt/course/index.js');
                break;
            // 安徽继续教育
            case 'ahjxjy':
                courseFn = require('../coursePlatform/ahjxjy/course/index.js');
                break;
            // 安徽继续教育（新版）
            case 'ahjxjyv2':
                courseFn = require('../coursePlatform/ahjxjyv2/course/index.js');
                break;
            // 均睿信息
            case 'jrxx':
                courseFn = require('../coursePlatform/jrxx/course');
                break;
            // 超星学习通
            case 'cxxxt':
                courseFn = require('../coursePlatform/cxxxt/course/index');
                break;
            // 弘成教育
            case 'hcjy':
                courseFn = require('../coursePlatform/hcjy/course/index.js');
                break;
            // 国家开放大学
            case 'gjkfdx':
                courseFn = require('../coursePlatform/gjkfdx/course/index.js');
                break;
        }

        // 任务开始前：更新数据库状态
        await Model.course.upsert(
            {
                id: taskObj.id,
                state: '正在进行',
                start_time: new Date(),
            },
            {
                where: {
                    id: taskObj.id,
                },
            }
        );

        //开始任务
        try {
            //随机等待5-15秒，以免同时进行服务器检测到
            let randomNum = Math.floor(Math.random() * 10 + 5);
            await new Promise(r => setTimeout(r, randomNum * 1000));

            let courseRes = await courseFn(taskObj, taskOptions);

            //任务结束：更新数据库状态
            await Model.course.update(
                {
                    state: '完成',
                    end_time: new Date(),
                    error_info: courseRes.warningMessage,
                    final_result: courseRes.finalResult,
                    pc_id: '',
                },
                {
                    where: {
                        id: taskObj.id,
                    },
                }
            );
        } catch (error) {
            await Model.course.update(
                {
                    state: '失败',
                    end_time: new Date(),
                    error_info: JSON.stringify(error, Object.getOwnPropertyNames(error)),
                    pc_id: '',
                },
                {
                    where: {
                        id: taskObj.id,
                    },
                }
            );
        }
    }

    //考试
    if (taskObj.type == 'exam') {
        //确定平台方法
        let examFn;
        switch (taskObj.platform) {
            case 'nmwcxt':
                examFn = require('../coursePlatform/nmwcxt/exam/index.js');
                break;
            case 'ahjxjy':
                examFn = require('../coursePlatform/ahjxjy/exam/index.js');
                break;
            case 'ahjxjyv2':
                examFn = require('../coursePlatform/ahjxjyv2/exam/index.js');
                break;
            case 'cxxxt':
                examFn = require('../coursePlatform/cxxxt/exam');
                break;
            case 'hcjy':
                examFn = require('../coursePlatform/hcjy/exam');
                break;
            case 'gkrj':
                examFn = require('../coursePlatform/gkrj/exam');
                break;
        }

        // 任务开始前：更新数据库状态
        await Model.exam.update(
            {
                state: '正在进行',
                start_time: new Date(),
            },
            {
                where: {
                    id: taskObj.id,
                },
            }
        );

        //开始任务
        try {
            //随机等待5-15秒，以免同时进行服务器检测到
            let randomNum = Math.floor(Math.random() * 10 + 5);
            await new Promise(r => setTimeout(r, randomNum * 1000));

            let examRes = await examFn(taskObj, taskOptions);

            //任务结束：更新数据库状态
            await Model.exam.update(
                {
                    state: '完成',
                    end_time: new Date(),
                    error_info: examRes.warningMessage,
                    final_result: examRes.finalResult,
                    pc_id: '',
                },
                {
                    where: {
                        id: taskObj.id,
                    },
                }
            );
        } catch (error) {
            await Model.exam.update(
                {
                    state: '失败',
                    end_time: new Date(),
                    error_info: JSON.stringify(error, Object.getOwnPropertyNames(error)),
                    pc_id: '',
                },
                {
                    where: {
                        id: taskObj.id,
                    },
                }
            );
        }
    }

    process.send('done');
};

module.exports = workProcessHandle;
